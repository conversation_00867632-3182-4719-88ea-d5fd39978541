# 🆓 Auto-Grade Scribe Open-Source

**Version 4.0.0 - 100% Open-Source**

Auto-Grade Scribe est une application web moderne de correction automatique d'examens utilisant exclusivement des solutions open-source. Elle permet aux enseignants de télécharger des copies d'examens scannées et d'obtenir une correction automatique avec des commentaires détaillés, sans dépendre d'APIs payantes.

## ✨ Fonctionnalités Open-Source

- **🔍 OCR Multi-Provider** : TrOCR, EasyOCR, PaddleOCR, Tesseract
- **🤖 IA de Correction** : Sentence Transformers, modèles Hugging Face
- **📝 Analyse Sémantique** : Similarité contextuelle avec fuzzy matching
- **👥 Révision Manuelle** : Interface de validation pour les enseignants
- **📊 Audit Trail** : Traçabilité complète des corrections
- **🗄️ Base PostgreSQL** : Stockage robuste avec historique
- **🐳 Containerisé** : Déploiement Docker simplifié

## 🛠️ Technologies 100% Open-Source

### Backend
- **FastAPI** - Framework web Python moderne
- **PostgreSQL** - Base de données relationnelle
- **SQLAlchemy** - ORM Python
- **TrOCR** - OCR avec Transformers (Microsoft)
- **EasyOCR** - OCR multi-langues
- **PaddleOCR** - OCR haute performance
- **Sentence Transformers** - Embeddings sémantiques
- **Tesseract** - OCR traditionnel

### Frontend (Optionnel)
- **Next.js 14** - Framework React
- **TypeScript** - Typage statique
- **Tailwind CSS** - Framework CSS
- **Shadcn/ui** - Composants UI

## 📋 Prérequis

- **Python** 3.8+
- **Docker & Docker Compose**
- **4GB RAM minimum** (pour les modèles IA)
- **Node.js** 18+ (optionnel, pour le frontend)

## 🚀 Installation Rapide

### 1. Cloner le Repository

```bash
git clone https://github.com/votre-username/auto-grade-scribe.git
cd auto-grade-scribe
```

### 2. Démarrer PostgreSQL

```bash
docker-compose -f docker-compose-simple.yml up -d
```

### 3. Installer les Dépendances

```bash
pip install pydantic-settings==2.1.0
pip install -r backend/requirements_opensource.txt
```

### 4. Démarrer l'Application

```bash
cd backend
python -m uvicorn main:app --host 0.0.0.0 --port 8001 --reload
```

### 5. Accéder à l'Application

- **Interface** : http://127.0.0.1:8001
- **Documentation API** : http://127.0.0.1:8001/docs
- **Santé de l'API** : http://127.0.0.1:8001/health

## 📁 Structure Optimisée

```
auto-grade-scribe/
├── 📁 backend/                          # Backend FastAPI optimisé
│   ├── 📁 core/                         # Configuration centralisée
│   │   ├── config.py                    # Configuration Pydantic
│   │   └── database.py                  # Modèles PostgreSQL
│   ├── 📁 services/                     # Services open-source
│   │   ├── enhanced_ocr_service.py      # OCR multi-provider
│   │   ├── intelligent_grading_service.py # IA de correction
│   │   ├── manual_review_service.py     # Révision manuelle
│   │   └── audit_service.py             # Audit et logging
│   ├── 📁 utils/                        # Utilitaires
│   ├── main.py                          # Application FastAPI
│   └── requirements_opensource.txt     # Dépendances open-source
├── 📁 app/                              # Frontend Next.js (optionnel)
├── 📁 components/                       # Composants React
├── 📁 models/                           # Cache des modèles IA
├── 📁 uploads/                          # Fichiers uploadés
├── 📁 results/                          # Résultats de correction
├── .env                                 # Configuration
├── docker-compose-simple.yml           # PostgreSQL Docker
└── README.md                            # Cette documentation
```

## 🔧 Configuration

### Variables d'Environnement (.env)

```env
# Base de données PostgreSQL
DATABASE_URL=postgresql://autograde:autograde123@localhost:5432/gradegeniusdb

# Configuration OCR Open-Source
TESSERACT_TIMEOUT=300
OCR_CONFIDENCE_THRESHOLD=0.7
EASYOCR_GPU=false
PADDLEOCR_GPU=false

# Modèles IA Open-Source
TROCR_HANDWRITTEN_MODEL=microsoft/trocr-base-handwritten
TROCR_PRINTED_MODEL=microsoft/trocr-base-printed
SIMILARITY_MODEL=all-MiniLM-L6-v2
SEMANTIC_MODEL=all-mpnet-base-v2

# Configuration de correction
GRADING_CONFIDENCE_THRESHOLD=0.7
MANUAL_REVIEW_THRESHOLD=0.6
FUZZY_MATCHING_THRESHOLD=0.8

# Sécurité
SECRET_KEY=your-secret-key-change-in-production
JWT_ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=60
```

## 🧪 Tests et Validation

### Test de l'API

```bash
curl http://127.0.0.1:8001/health
```

**Réponse attendue :**
```json
{
  "status": "healthy",
  "version": "4.0.0",
  "services": {
    "ocr": true,
    "grading": true,
    "review": true,
    "audit": true
  }
}
```

### Test d'Upload

```bash
curl -X POST "http://127.0.0.1:8001/api/upload" \
     -H "Content-Type: multipart/form-data" \
     -F "file=@test.jpg"
```

## 📚 Documentation API

- **Swagger UI** : http://127.0.0.1:8001/docs
- **ReDoc** : http://127.0.0.1:8001/redoc

### Endpoints Principaux

- `POST /api/upload` - Upload de fichier
- `GET /api/results/{id}` - Récupérer un résultat
- `POST /api/grade` - Lancer la correction
- `GET /api/review` - Interface de révision
- `GET /health` - Santé de l'application

## 🔍 Modèles IA Utilisés

### OCR (Reconnaissance de Texte)
- **TrOCR** : microsoft/trocr-base-handwritten, microsoft/trocr-base-printed
- **EasyOCR** : Détection multi-langues
- **PaddleOCR** : Performance optimisée
- **Tesseract** : OCR traditionnel fiable

### Analyse Sémantique
- **Sentence Transformers** : all-MiniLM-L6-v2, all-mpnet-base-v2
- **Multilingual** : paraphrase-multilingual-MiniLM-L12-v2

### Avantages Open-Source
- ✅ **Aucun coût d'API**
- ✅ **Données privées** (pas d'envoi vers des services externes)
- ✅ **Contrôle total** sur les modèles
- ✅ **Déploiement offline** possible
- ✅ **Personnalisation** complète

## 🛠️ Dépannage

### Problèmes Courants

1. **Erreur Pydantic** :
   ```bash
   pip install pydantic-settings==2.1.0
   ```

2. **PostgreSQL non accessible** :
   ```bash
   docker-compose -f docker-compose-simple.yml restart
   docker logs auto-grade-postgres
   ```

3. **Modèles IA manquants** :
   ```bash
   python setup_opensource_models.py
   ```

4. **Mémoire insuffisante** :
   - Réduire `MAX_CONCURRENT_OCR`
   - Utiliser des modèles plus légers

## 🚀 Déploiement Production

### Docker Compose Complet

```bash
# Démarrer tous les services
docker-compose up -d

# Vérifier les logs
docker-compose logs -f
```

### Configuration Production

1. **Sécuriser la base de données** :
   - Changer les mots de passe par défaut
   - Configurer SSL/TLS

2. **Optimiser les performances** :
   - Ajuster `MAX_CONCURRENT_OCR` et `MAX_CONCURRENT_GRADING`
   - Configurer le cache des modèles

3. **Monitoring** :
   - Logs centralisés
   - Métriques de performance
   - Alertes de santé

## 🤝 Contribution

1. Fork le projet
2. Créer une branche feature (`git checkout -b feature/nouvelle-fonctionnalite`)
3. Commit les changements (`git commit -m 'Ajout nouvelle fonctionnalité'`)
4. Push vers la branche (`git push origin feature/nouvelle-fonctionnalite`)
5. Ouvrir une Pull Request

## 📄 Licence

Ce projet est sous licence MIT. Voir le fichier `LICENSE` pour plus de détails.

## 🙏 Remerciements

- **Microsoft** pour TrOCR
- **JaidedAI** pour EasyOCR
- **PaddlePaddle** pour PaddleOCR
- **Sentence Transformers** pour les embeddings sémantiques
- **Hugging Face** pour l'écosystème de modèles
- **FastAPI** et **PostgreSQL** pour l'infrastructure

## 📞 Support

- 📧 **Issues GitHub** : Pour les bugs et demandes de fonctionnalités
- 📖 **Documentation** : Consultez `/docs` pour plus de détails
- 💬 **Discussions** : Onglet Discussions GitHub

---

**🎯 Auto-Grade Scribe v4.0.0 - Correction d'examens 100% open-source, privée et gratuite !**
