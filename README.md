# Auto Grade Scribe

Une application pour l'extraction de texte et la correction automatique d'examens, y compris les QCM et les réponses manuscrites.

## Description

Auto Grade Scribe est un système conçu pour faciliter l'évaluation et la notation des travaux des étudiants. Ce projet utilise la reconnaissance optique de caractères (OCR) et l'intelligence artificielle pour automatiser l'extraction de texte et l'analyse des examens.

## Fonctionnalités

- Authentification des utilisateurs
- Upload et gestion des fichiers à évaluer
- Reconnaissance optique de caractères (OCR) pour les examens numérisés
- Extraction automatique des réponses QCM
- Reconnaissance de texte manuscrit avec Google Gemini AI
- Correction automatique des examens
- Interface utilisateur simple et intuitive
- Déploiement facile avec Docker

## Architecture Technique

L'application est composée de:

- **Frontend**: Next.js + TypeScript + Tailwind CSS + shadcn/ui
- **Backend**: FastAPI
- **OCR**: Tesseract + Google Gemini AI
- **Base de données**: PostgreSQL
- **Conteneurisation**: Docker et Docker Compose

## Prérequis

- Docker et Docker Compose
- Clé API Google pour Gemini AI (pour la reconnaissance de texte manuscrit améliorée)

## Installation avec Docker

### Configuration de l'environnement

1. Copier le fichier d'environnement exemple:
   ```bash
   cp .env.example .env
   ```

2. Éditer le fichier `.env` et ajouter votre clé API Google:
   ```
   GOOGLE_API_KEY=your_google_api_key_here
   ```

### Démarrer l'application

1. Lancer tous les services avec Docker Compose:
   ```bash
   docker-compose up -d
   ```

2. Accéder à l'application:
   - Frontend: http://localhost:3000
   - Backend API: http://localhost:8000
   - PgAdmin (gestion de base de données): http://localhost:5050
     - Email: <EMAIL>
     - Password: admin123

3. Comptes utilisateurs par défaut:
   - Admin:
     - Nom d'utilisateur: admin
     - Mot de passe: admin123
   - Enseignant:
     - Nom d'utilisateur: teacher
     - Mot de passe: teacher123

## Installation manuelle (pour développement)

### Backend

1. Créer et activer un environnement virtuel:

```sh
python -m venv backend-env
# Windows
.\backend-env\Scripts\activate
# macOS/Linux
source backend-env/bin/activate
```

2. Installer les dépendances:

```sh
cd backend
pip install -r requirements.txt
```

3. Installer Tesseract OCR :
   - Sur Windows : Télécharger et installer depuis [https://github.com/UB-Mannheim/tesseract/wiki](https://github.com/UB-Mannheim/tesseract/wiki)
   - Sur Linux : `sudo apt-get install tesseract-ocr`
   - Sur macOS : `brew install tesseract`

4. Démarrer le serveur backend:

```sh
python -m uvicorn app:app --reload
```

### Frontend

1. Installer les dépendances:

```sh
npm install
```

2. Démarrer le serveur de développement:

```sh
npm run dev
```

3. Accéder à l'application:
   - Frontend: http://localhost:3000
   - Backend API: http://localhost:8000

## Configuration

### Variables d'environnement

Le fichier `.env` à la racine du projet contient les variables suivantes:

```
# Configuration de la base de données
DATABASE_URL=*************************************************/gradegeniusdb

# Configuration de l'API Google pour Gemini AI
GOOGLE_API_KEY=your_google_api_key_here

# Configuration de l'application
NEXT_PUBLIC_API_URL=http://localhost:8000

# Configuration de l'authentification
JWT_SECRET=your_jwt_secret_here
JWT_ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=60
```

## Utilisation

1. Accédez à la page d'accueil (http://localhost:3000)
2. Connectez-vous avec l'un des comptes par défaut ou créez un compte
3. Téléchargez une image d'examen (JPG, PNG, PDF)
4. Choisissez le type d'examen (QCM ou manuscrit)
5. Pour les QCM, le système détectera automatiquement les réponses
6. Pour les examens manuscrits, Gemini AI sera utilisé pour la reconnaissance
7. Consultez les résultats de l'extraction et de la correction

## Services Docker

L'application est composée des services Docker suivants:

1. **Frontend**: Application Next.js servant l'interface utilisateur
2. **Backend**: Application FastAPI fournissant les points d'API
3. **PostgreSQL**: Base de données pour stocker les données utilisateur et les résultats d'examen
4. **PgAdmin**: Interface web pour gérer la base de données PostgreSQL

## Protection des routes

L'application est configurée pour empêcher l'accès direct aux pages protégées sans authentification. Les utilisateurs non authentifiés seront automatiquement redirigés vers la page de connexion.

## API Documentation

### Endpoints principaux

- `GET /` - Vérifier l'état de l'API
- `POST /api/upload` - Télécharger un fichier
- `POST /extract-text` - Extraire le texte d'un fichier
- `POST /upload-and-extract` - Télécharger et extraire le texte en une seule requête
- `POST /api/login` - Authentifier un utilisateur
- `POST /api/register` - Créer un nouvel utilisateur
- `POST /api/grade/enhanced-qcm/noauth` - Corriger un QCM avec Gemini AI
- `GET /api/ocr/{file_id}/noauth` - Extraire le texte d'un fichier avec OCR

La documentation complète de l'API est disponible à l'adresse [http://localhost:8000/docs](http://localhost:8000/docs) une fois le serveur démarré.

## Composants IA

- **Service OCR**: Extrait le texte des images à l'aide de Tesseract OCR
- **Service Gemini**: Utilise l'IA Gemini de Google pour la reconnaissance de texte manuscrit et la correction
- **Service de correction QCM**: Service spécialisé pour la correction des questions à choix multiples

## Notes importantes

- Pour les fichiers volumineux, le traitement OCR peut prendre quelques secondes.
- L'intégration avec Gemini AI nécessite une clé API Google valide.
- Tous les services sont configurés pour fonctionner ensemble via Docker Compose.
- Les données sont persistantes grâce aux volumes Docker.
