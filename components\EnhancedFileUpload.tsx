"use client"

import React, { useState, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Progress } from '@/components/ui/progress';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/hooks/use-auth';
import { Upload, FileText, CheckCircle, AlertCircle, Loader2 } from 'lucide-react';

interface EnhancedFileUploadProps {
  onUploadComplete?: (examId: string) => void;
  onProcessingComplete?: (result: any) => void;
}

interface UploadProgress {
  stage: 'idle' | 'uploading' | 'processing' | 'grading' | 'completed' | 'error';
  progress: number;
  message: string;
}

const EnhancedFileUpload: React.FC<EnhancedFileUploadProps> = ({
  onUploadComplete,
  onProcessingComplete
}) => {
  const [files, setFiles] = useState<File[]>([]);
  const [isDragging, setIsDragging] = useState(false);
  const [uploadProgress, setUploadProgress] = useState<UploadProgress>({
    stage: 'idle',
    progress: 0,
    message: ''
  });
  
  // Form data
  const [examName, setExamName] = useState('');
  const [examType, setExamType] = useState('qcm');
  const [subject, setSubject] = useState('');
  const [className, setClassName] = useState('');
  const [academicYear, setAcademicYear] = useState('');
  const [autoProcess, setAutoProcess] = useState(true);
  const [autoGrade, setAutoGrade] = useState(false);
  const [correctAnswers, setCorrectAnswers] = useState('');

  const { toast } = useToast();
  const router = useRouter();
  const { accessToken } = useAuth();

  const handleDragEnter = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(true);
  }, []);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(false);
  }, []);

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
  }, []);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(false);

    const droppedFiles = Array.from(e.dataTransfer.files);
    setFiles(droppedFiles);
  }, []);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      setFiles(Array.from(e.target.files));
    }
  };

  const updateProgress = (stage: UploadProgress['stage'], progress: number, message: string) => {
    setUploadProgress({ stage, progress, message });
  };

  const uploadExam = async (file: File): Promise<string> => {
    const formData = new FormData();
    formData.append('file', file);
    
    if (examName) formData.append('exam_name', examName);
    formData.append('exam_type', examType);
    if (subject) formData.append('subject', subject);
    if (className) formData.append('class_name', className);
    if (academicYear) formData.append('academic_year', academicYear);

    const response = await fetch('/api/v2/exams/upload', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${accessToken}`
      },
      body: formData
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.detail || 'Upload failed');
    }

    const result = await response.json();
    return result.exam_id;
  };

  const processExam = async (examId: string): Promise<any> => {
    const response = await fetch(`/api/v2/exams/${examId}/process`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ force_reprocess: false })
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.detail || 'Processing failed');
    }

    return await response.json();
  };

  const gradeExam = async (examId: string): Promise<any> => {
    if (!correctAnswers.trim()) {
      throw new Error('Correct answers are required for grading');
    }

    // Parse correct answers (format: "1:A,2:B,3:C")
    const answersObj: Record<string, string> = {};
    correctAnswers.split(',').forEach(pair => {
      const [question, answer] = pair.split(':').map(s => s.trim());
      if (question && answer) {
        answersObj[question] = answer;
      }
    });

    const response = await fetch(`/api/v2/exams/${examId}/grade`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        exam_id: examId,
        correct_answers: answersObj,
        grading_config: {
          grade_scale: 'standard'
        }
      })
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.detail || 'Grading failed');
    }

    return await response.json();
  };

  const handleSubmit = async () => {
    if (files.length === 0) {
      toast({
        title: "No files selected",
        description: "Please select at least one file to upload.",
        variant: "destructive"
      });
      return;
    }

    try {
      updateProgress('uploading', 10, 'Uploading exam file...');

      // Upload the first file (for now, handle single file)
      const file = files[0];
      const examId = await uploadExam(file);

      updateProgress('uploading', 30, 'Upload completed successfully');

      if (onUploadComplete) {
        onUploadComplete(examId);
      }

      if (autoProcess) {
        updateProgress('processing', 40, 'Processing exam with OCR...');
        
        const processResult = await processExam(examId);
        
        updateProgress('processing', 70, 'OCR processing completed');

        if (autoGrade && correctAnswers.trim()) {
          updateProgress('grading', 80, 'Grading exam...');
          
          const gradeResult = await gradeExam(examId);
          
          updateProgress('completed', 100, 'Exam graded successfully');

          if (onProcessingComplete) {
            onProcessingComplete({
              examId,
              processResult,
              gradeResult
            });
          }

          toast({
            title: "Success",
            description: `Exam processed and graded successfully. Score: ${gradeResult.grade_summary?.percentage || 0}%`,
            variant: "default"
          });

          // Navigate to results
          router.push(`/results/${examId}`);
        } else {
          updateProgress('completed', 100, 'Exam processed successfully');
          
          toast({
            title: "Success",
            description: "Exam uploaded and processed successfully.",
            variant: "default"
          });

          if (onProcessingComplete) {
            onProcessingComplete({
              examId,
              processResult
            });
          }
        }
      } else {
        updateProgress('completed', 100, 'Exam uploaded successfully');
        
        toast({
          title: "Success",
          description: "Exam uploaded successfully. You can process it later.",
          variant: "default"
        });
      }

    } catch (error) {
      console.error('Error in exam workflow:', error);
      
      updateProgress('error', 0, `Error: ${error instanceof Error ? error.message : 'Unknown error'}`);
      
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : 'An unknown error occurred',
        variant: "destructive"
      });
    }
  };

  const resetForm = () => {
    setFiles([]);
    setExamName('');
    setSubject('');
    setClassName('');
    setAcademicYear('');
    setCorrectAnswers('');
    setUploadProgress({ stage: 'idle', progress: 0, message: '' });
  };

  const isProcessing = uploadProgress.stage !== 'idle' && uploadProgress.stage !== 'completed' && uploadProgress.stage !== 'error';

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Upload className="h-6 w-6" />
          Enhanced Exam Upload
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* File Upload Area */}
        <div
          className={`border-2 border-dashed rounded-lg p-8 text-center transition-colors ${
            isDragging
              ? 'border-primary bg-primary/10'
              : 'border-gray-300 hover:border-gray-400'
          }`}
          onDragEnter={handleDragEnter}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onDrop={handleDrop}
        >
          <div className="flex flex-col items-center justify-center space-y-4">
            <div className="p-4 bg-secondary rounded-full">
              <FileText className="h-8 w-8 text-primary" />
            </div>
            <div>
              <h3 className="text-lg font-medium">Drop exam files here</h3>
              <p className="text-sm text-gray-500 mt-1">
                Support for PDF, JPG, PNG files up to 50MB
              </p>
            </div>
            <div>
              <label htmlFor="file-upload">
                <Button variant="outline" className="mt-2" disabled={isProcessing}>
                  Select Files
                </Button>
                <input
                  id="file-upload"
                  type="file"
                  accept=".pdf,.jpg,.jpeg,.png"
                  multiple
                  onChange={handleFileChange}
                  className="hidden"
                  disabled={isProcessing}
                />
              </label>
            </div>
          </div>
        </div>

        {/* Selected Files */}
        {files.length > 0 && (
          <div className="space-y-2">
            <Label>Selected Files:</Label>
            {files.map((file, index) => (
              <div key={index} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                <span className="text-sm">{file.name}</span>
                <span className="text-xs text-gray-500">{(file.size / 1024 / 1024).toFixed(2)} MB</span>
              </div>
            ))}
          </div>
        )}

        {/* Exam Metadata */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="exam-name">Exam Name</Label>
            <Input
              id="exam-name"
              value={examName}
              onChange={(e) => setExamName(e.target.value)}
              placeholder="e.g., Midterm Exam"
              disabled={isProcessing}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="exam-type">Exam Type</Label>
            <Select value={examType} onValueChange={setExamType} disabled={isProcessing}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="qcm">Multiple Choice (QCM)</SelectItem>
                <SelectItem value="handwritten">Handwritten</SelectItem>
                <SelectItem value="mixed">Mixed</SelectItem>
                <SelectItem value="essay">Essay</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="subject">Subject</Label>
            <Input
              id="subject"
              value={subject}
              onChange={(e) => setSubject(e.target.value)}
              placeholder="e.g., Mathematics"
              disabled={isProcessing}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="class-name">Class</Label>
            <Input
              id="class-name"
              value={className}
              onChange={(e) => setClassName(e.target.value)}
              placeholder="e.g., MATH101"
              disabled={isProcessing}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="academic-year">Academic Year</Label>
            <Input
              id="academic-year"
              value={academicYear}
              onChange={(e) => setAcademicYear(e.target.value)}
              placeholder="e.g., 2023-2024"
              disabled={isProcessing}
            />
          </div>
        </div>

        {/* Processing Options */}
        <div className="space-y-4">
          <div className="flex items-center space-x-2">
            <input
              type="checkbox"
              id="auto-process"
              checked={autoProcess}
              onChange={(e) => setAutoProcess(e.target.checked)}
              disabled={isProcessing}
            />
            <Label htmlFor="auto-process">Automatically process with OCR after upload</Label>
          </div>

          <div className="flex items-center space-x-2">
            <input
              type="checkbox"
              id="auto-grade"
              checked={autoGrade}
              onChange={(e) => setAutoGrade(e.target.checked)}
              disabled={isProcessing || !autoProcess}
            />
            <Label htmlFor="auto-grade">Automatically grade after processing (requires correct answers)</Label>
          </div>

          {autoGrade && (
            <div className="space-y-2">
              <Label htmlFor="correct-answers">Correct Answers</Label>
              <Textarea
                id="correct-answers"
                value={correctAnswers}
                onChange={(e) => setCorrectAnswers(e.target.value)}
                placeholder="Format: 1:A,2:B,3:C,4:D (question:answer pairs separated by commas)"
                disabled={isProcessing}
                rows={3}
              />
            </div>
          )}
        </div>

        {/* Progress */}
        {uploadProgress.stage !== 'idle' && (
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <Label>Progress</Label>
              <span className="text-sm text-gray-500">{uploadProgress.progress}%</span>
            </div>
            <Progress value={uploadProgress.progress} className="w-full" />
            <div className="flex items-center gap-2 text-sm">
              {uploadProgress.stage === 'error' ? (
                <AlertCircle className="h-4 w-4 text-red-500" />
              ) : uploadProgress.stage === 'completed' ? (
                <CheckCircle className="h-4 w-4 text-green-500" />
              ) : (
                <Loader2 className="h-4 w-4 animate-spin" />
              )}
              <span className={uploadProgress.stage === 'error' ? 'text-red-600' : ''}>
                {uploadProgress.message}
              </span>
            </div>
          </div>
        )}

        {/* Actions */}
        <div className="flex gap-4">
          <Button
            onClick={handleSubmit}
            disabled={files.length === 0 || isProcessing}
            className="flex-1"
          >
            {isProcessing ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Processing...
              </>
            ) : (
              'Upload and Process'
            )}
          </Button>

          <Button
            variant="outline"
            onClick={resetForm}
            disabled={isProcessing}
          >
            Reset
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};

export default EnhancedFileUpload;
