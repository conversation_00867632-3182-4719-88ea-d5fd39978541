"""
File Utilities for Auto-Grade Scribe
Provides file handling, validation, and processing utilities
"""

import os
import hashlib
import mimetypes
import logging
from typing import Dict, Any, List, Optional, Tuple
from PIL import Image
import magic

logger = logging.getLogger("auto-grade-scribe.file-utils")

class FileUtils:
    """Utility class for file operations"""
    
    def __init__(self):
        # Supported file types
        self.supported_image_types = {
            'image/jpeg', 'image/jpg', 'image/png', 'image/tiff', 'image/bmp'
        }
        self.supported_document_types = {
            'application/pdf'
        }
        self.all_supported_types = self.supported_image_types | self.supported_document_types
        
        # File size limits (in bytes)
        self.max_file_size = 50 * 1024 * 1024  # 50MB
        self.max_image_dimensions = (10000, 10000)  # 10000x10000 pixels
        
        logger.info("File Utils initialized")
    
    def validate_file(self, file_data: bytes, filename: str) -> Dict[str, Any]:
        """
        Validate uploaded file
        
        Args:
            file_data: Binary file data
            filename: Original filename
            
        Returns:
            Validation result with details
        """
        try:
            result = {
                "valid": False,
                "error": None,
                "file_info": {},
                "warnings": []
            }
            
            # Check file size
            file_size = len(file_data)
            if file_size == 0:
                result["error"] = "File is empty"
                return result
            
            if file_size > self.max_file_size:
                result["error"] = f"File too large. Maximum size: {self.max_file_size // (1024*1024)}MB"
                return result
            
            # Detect MIME type
            try:
                mime_type = magic.from_buffer(file_data, mime=True)
            except Exception:
                # Fallback to filename-based detection
                mime_type, _ = mimetypes.guess_type(filename)
                if not mime_type:
                    result["error"] = "Could not determine file type"
                    return result
            
            # Check if file type is supported
            if mime_type not in self.all_supported_types:
                result["error"] = f"Unsupported file type: {mime_type}. Supported types: {', '.join(self.all_supported_types)}"
                return result
            
            # Additional validation for images
            if mime_type in self.supported_image_types:
                image_validation = self._validate_image(file_data)
                if not image_validation["valid"]:
                    result["error"] = image_validation["error"]
                    return result
                result["file_info"].update(image_validation["info"])
                result["warnings"].extend(image_validation.get("warnings", []))
            
            # Calculate file hash
            file_hash = hashlib.sha256(file_data).hexdigest()
            
            result.update({
                "valid": True,
                "file_info": {
                    **result["file_info"],
                    "filename": filename,
                    "mime_type": mime_type,
                    "file_size": file_size,
                    "file_hash": file_hash
                }
            })
            
            return result
            
        except Exception as e:
            logger.error(f"Error validating file: {str(e)}")
            return {
                "valid": False,
                "error": f"Validation error: {str(e)}",
                "file_info": {}
            }
    
    def _validate_image(self, file_data: bytes) -> Dict[str, Any]:
        """Validate image-specific properties"""
        try:
            from io import BytesIO
            
            result = {
                "valid": False,
                "error": None,
                "info": {},
                "warnings": []
            }
            
            # Try to open image
            try:
                image = Image.open(BytesIO(file_data))
                image.verify()  # Verify image integrity
                
                # Reopen for getting info (verify() closes the image)
                image = Image.open(BytesIO(file_data))
                
            except Exception as e:
                result["error"] = f"Invalid image file: {str(e)}"
                return result
            
            # Check dimensions
            width, height = image.size
            if width > self.max_image_dimensions[0] or height > self.max_image_dimensions[1]:
                result["error"] = f"Image too large: {width}x{height}. Maximum: {self.max_image_dimensions[0]}x{self.max_image_dimensions[1]}"
                return result
            
            # Check if image is too small
            if width < 100 or height < 100:
                result["warnings"].append("Image is very small, OCR quality may be poor")
            
            # Check aspect ratio
            aspect_ratio = width / height
            if aspect_ratio > 5 or aspect_ratio < 0.2:
                result["warnings"].append("Unusual aspect ratio detected")
            
            # Get image info
            result.update({
                "valid": True,
                "info": {
                    "width": width,
                    "height": height,
                    "format": image.format,
                    "mode": image.mode,
                    "aspect_ratio": aspect_ratio
                }
            })
            
            return result
            
        except Exception as e:
            logger.error(f"Error validating image: {str(e)}")
            return {
                "valid": False,
                "error": f"Image validation error: {str(e)}",
                "info": {}
            }
    
    def ensure_directory(self, directory_path: str) -> bool:
        """Ensure directory exists, create if necessary"""
        try:
            os.makedirs(directory_path, exist_ok=True)
            return True
        except Exception as e:
            logger.error(f"Error creating directory {directory_path}: {str(e)}")
            return False
    
    def safe_filename(self, filename: str) -> str:
        """Generate a safe filename"""
        import re
        import unicodedata
        
        # Normalize unicode characters
        filename = unicodedata.normalize('NFKD', filename)
        
        # Remove or replace unsafe characters
        filename = re.sub(r'[^\w\s\-_\.]', '', filename)
        filename = re.sub(r'[-\s]+', '-', filename)
        
        # Limit length
        name, ext = os.path.splitext(filename)
        if len(name) > 100:
            name = name[:100]
        
        return name + ext
    
    def get_file_info(self, file_path: str) -> Dict[str, Any]:
        """Get comprehensive file information"""
        try:
            if not os.path.exists(file_path):
                return {"error": "File not found"}
            
            stat = os.stat(file_path)
            
            info = {
                "path": file_path,
                "filename": os.path.basename(file_path),
                "size": stat.st_size,
                "created": stat.st_ctime,
                "modified": stat.st_mtime,
                "accessed": stat.st_atime
            }
            
            # Get MIME type
            try:
                mime_type = magic.from_file(file_path, mime=True)
                info["mime_type"] = mime_type
            except Exception:
                mime_type, _ = mimetypes.guess_type(file_path)
                info["mime_type"] = mime_type
            
            # Additional info for images
            if info.get("mime_type", "").startswith("image/"):
                try:
                    with Image.open(file_path) as img:
                        info.update({
                            "width": img.width,
                            "height": img.height,
                            "format": img.format,
                            "mode": img.mode
                        })
                except Exception as e:
                    info["image_error"] = str(e)
            
            return info
            
        except Exception as e:
            logger.error(f"Error getting file info for {file_path}: {str(e)}")
            return {"error": str(e)}
    
    def cleanup_old_files(self, directory: str, max_age_days: int = 30) -> Dict[str, Any]:
        """Clean up old files in a directory"""
        try:
            from datetime import datetime, timedelta
            
            if not os.path.exists(directory):
                return {"error": "Directory not found"}
            
            cutoff_time = datetime.now() - timedelta(days=max_age_days)
            cutoff_timestamp = cutoff_time.timestamp()
            
            deleted_files = []
            errors = []
            total_size_freed = 0
            
            for filename in os.listdir(directory):
                file_path = os.path.join(directory, filename)
                
                try:
                    if os.path.isfile(file_path):
                        file_mtime = os.path.getmtime(file_path)
                        
                        if file_mtime < cutoff_timestamp:
                            file_size = os.path.getsize(file_path)
                            os.remove(file_path)
                            deleted_files.append(filename)
                            total_size_freed += file_size
                            
                except Exception as e:
                    errors.append(f"Error deleting {filename}: {str(e)}")
            
            return {
                "success": True,
                "deleted_files": deleted_files,
                "files_deleted": len(deleted_files),
                "total_size_freed": total_size_freed,
                "errors": errors
            }
            
        except Exception as e:
            logger.error(f"Error cleaning up directory {directory}: {str(e)}")
            return {"error": str(e)}

# Create singleton instance
file_utils = FileUtils()
