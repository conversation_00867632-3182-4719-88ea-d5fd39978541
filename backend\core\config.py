"""
Configuration centralisée pour Auto-Grade Scribe Open-Source
"""

import os
from pathlib import Path
from typing import Optional
from pydantic import BaseSettings, Field

class Settings(BaseSettings):
    """Configuration de l'application"""
    
    # Environnement
    environment: str = Field(default="development", env="ENVIRONMENT")
    debug: bool = Field(default=True, env="DEBUG")
    
    # Base de données PostgreSQL
    database_url: str = Field(
        default="postgresql://autograde:autograde123@localhost:5432/gradegeniusdb",
        env="DATABASE_URL"
    )
    database_pool_size: int = Field(default=10, env="DATABASE_POOL_SIZE")
    database_max_overflow: int = Field(default=20, env="DATABASE_MAX_OVERFLOW")
    database_pool_timeout: int = Field(default=30, env="DATABASE_POOL_TIMEOUT")
    
    # Sécurité
    secret_key: str = Field(default="your-secret-key-change-in-production", env="SECRET_KEY")
    jwt_algorithm: str = Field(default="HS256", env="JWT_ALGORITHM")
    access_token_expire_minutes: int = Field(default=60, env="ACCESS_TOKEN_EXPIRE_MINUTES")
    
    # Configuration API
    api_host: str = Field(default="0.0.0.0", env="API_HOST")
    api_port: int = Field(default=8001, env="API_PORT")
    cors_origins: str = Field(
        default="http://localhost:3000,http://127.0.0.1:3000",
        env="CORS_ORIGINS"
    )
    
    # Upload de fichiers
    max_file_size: int = Field(default=52428800, env="MAX_FILE_SIZE")  # 50MB
    upload_directory: str = Field(default="uploads", env="UPLOAD_DIRECTORY")
    results_directory: str = Field(default="results", env="RESULTS_DIRECTORY")
    temp_directory: str = Field(default="temp", env="TEMP_DIRECTORY")
    
    # Configuration OCR Open-Source
    tesseract_timeout: int = Field(default=300, env="TESSERACT_TIMEOUT")
    ocr_confidence_threshold: float = Field(default=0.7, env="OCR_CONFIDENCE_THRESHOLD")
    easyocr_gpu: bool = Field(default=False, env="EASYOCR_GPU")
    paddleocr_gpu: bool = Field(default=False, env="PADDLEOCR_GPU")
    
    # Configuration IA Open-Source
    sentence_transformers_cache: str = Field(
        default="./models/sentence_transformers",
        env="SENTENCE_TRANSFORMERS_CACHE_FOLDER"
    )
    transformers_cache: str = Field(default="./models/transformers", env="TRANSFORMERS_CACHE")
    
    # Modèles TrOCR
    trocr_handwritten_model: str = Field(
        default="microsoft/trocr-base-handwritten",
        env="TROCR_HANDWRITTEN_MODEL"
    )
    trocr_printed_model: str = Field(
        default="microsoft/trocr-base-printed",
        env="TROCR_PRINTED_MODEL"
    )
    
    # Modèles de similarité sémantique
    similarity_model: str = Field(default="all-MiniLM-L6-v2", env="SIMILARITY_MODEL")
    multilingual_model: str = Field(
        default="paraphrase-multilingual-MiniLM-L12-v2",
        env="MULTILINGUAL_MODEL"
    )
    semantic_model: str = Field(default="all-mpnet-base-v2", env="SEMANTIC_MODEL")
    
    # Configuration de correction
    grading_confidence_threshold: float = Field(default=0.7, env="GRADING_CONFIDENCE_THRESHOLD")
    manual_review_threshold: float = Field(default=0.6, env="MANUAL_REVIEW_THRESHOLD")
    fuzzy_matching_threshold: float = Field(default=0.8, env="FUZZY_MATCHING_THRESHOLD")
    
    # Google AI (optionnel)
    google_api_key: Optional[str] = Field(default=None, env="GOOGLE_API_KEY")
    gemini_model: str = Field(default="gemini-pro", env="GEMINI_MODEL")
    gemini_timeout: int = Field(default=60, env="GEMINI_TIMEOUT")
    
    # Logging
    log_level: str = Field(default="INFO", env="LOG_LEVEL")
    log_file: str = Field(default="logs/app.log", env="LOG_FILE")
    
    # Cache et performance
    enable_model_caching: bool = Field(default=True, env="ENABLE_MODEL_CACHING")
    model_cache_size: int = Field(default=1000, env="MODEL_CACHE_SIZE")
    result_cache_ttl: int = Field(default=3600, env="RESULT_CACHE_TTL")
    
    # Limites de traitement
    max_concurrent_ocr: int = Field(default=3, env="MAX_CONCURRENT_OCR")
    max_concurrent_grading: int = Field(default=5, env="MAX_CONCURRENT_GRADING")
    processing_timeout: int = Field(default=300, env="PROCESSING_TIMEOUT")
    
    # Fonctionnalités activées
    enable_semantic_analysis: bool = Field(default=True, env="ENABLE_SEMANTIC_ANALYSIS")
    enable_advanced_feedback: bool = Field(default=True, env="ENABLE_ADVANCED_FEEDBACK")
    enable_batch_processing: bool = Field(default=True, env="ENABLE_BATCH_PROCESSING")
    
    class Config:
        env_file = ".env"
        case_sensitive = False
    
    def get_cors_origins(self) -> list:
        """Retourner la liste des origines CORS"""
        return [origin.strip() for origin in self.cors_origins.split(",")]
    
    def ensure_directories(self):
        """Créer les répertoires nécessaires"""
        directories = [
            self.upload_directory,
            self.results_directory,
            self.temp_directory,
            self.sentence_transformers_cache,
            self.transformers_cache,
            Path(self.log_file).parent
        ]
        
        for directory in directories:
            Path(directory).mkdir(parents=True, exist_ok=True)

# Instance globale des paramètres
settings = Settings()

# Créer les répertoires au démarrage
settings.ensure_directories()
