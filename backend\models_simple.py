"""
Simplified models for Auto-Grade Scribe
This version avoids the table redefinition issues
"""

from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON>umn, Foreign<PERSON>ey, Integer, String, DateTime, Float, Text, Enum
from sqlalchemy.dialects.postgresql import JSON
from sqlalchemy.types import TypeDecorator, VARCHAR
import json
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from datetime import datetime
from enum import Enum as PyEnum

from database import Base

# Enums for better type safety
class UserRole(PyEnum):
    ADMIN = "admin"
    TEACHER = "teacher"
    STUDENT = "student"
    USER = "user"

class ExamStatus(PyEnum):
    UPLOADED = "uploaded"
    PROCESSING = "processing"
    PROCESSED = "processed"
    GRADED = "graded"
    COMPLETED = "completed"
    ERROR = "error"
    FAILED = "failed"

class ExamType(PyEnum):
    QCM = "qcm"
    HANDWRITTEN = "handwritten"
    MIXED = "mixed"
    ESSAY = "essay"

class GradeStatus(PyEnum):
    PASSED = "passed"
    FAILED = "failed"
    PENDING = "pending"
    REVIEW_REQUIRED = "review_required"

# Core models
class User(Base):
    __tablename__ = "users"
    __table_args__ = {'extend_existing': True}

    id = Column(Integer, primary_key=True, index=True)
    username = Column(String(50), unique=True, index=True, nullable=False)
    email = Column(String(255), unique=True, index=True, nullable=False)
    full_name = Column(String(255), nullable=True)
    hashed_password = Column(String(255), nullable=False)
    is_active = Column(Boolean, default=True, nullable=False)
    role = Column(Enum(UserRole), default=UserRole.USER, nullable=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), nullable=False)
    last_login = Column(DateTime(timezone=True), nullable=True)

    # Profile information
    department = Column(String(100), nullable=True)
    institution = Column(String(255), nullable=True)
    phone = Column(String(20), nullable=True)

class Exam(Base):
    __tablename__ = "exams"
    __table_args__ = {'extend_existing': True}

    id = Column(String(36), primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)

    # File information
    original_filename = Column(String(255), nullable=False)
    file_path = Column(String(500), nullable=False)
    file_size = Column(Integer, nullable=True)

    # Exam metadata
    exam_name = Column(String(255), nullable=True)
    exam_type = Column(Enum(ExamType), nullable=False, default=ExamType.QCM)
    subject = Column(String(100), nullable=True)
    class_name = Column(String(100), nullable=True)
    academic_year = Column(String(20), nullable=True)

    # Processing information
    upload_time = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    processing_started_at = Column(DateTime(timezone=True), nullable=True)
    processing_completed_at = Column(DateTime(timezone=True), nullable=True)
    status = Column(Enum(ExamStatus), default=ExamStatus.UPLOADED, nullable=False)

    # OCR and extraction results
    extracted_text = Column(Text, nullable=True)
    ocr_confidence = Column(Float, nullable=True)
    ocr_model_used = Column(String(50), nullable=True)

    # Error handling
    error_message = Column(Text, nullable=True)
    retry_count = Column(Integer, default=0, nullable=False)

    # Additional data
    additional_data = Column(JSON, nullable=True)

class Student(Base):
    __tablename__ = "students"
    __table_args__ = {'extend_existing': True}

    id = Column(Integer, primary_key=True, index=True)
    student_id = Column(String(50), unique=True, index=True, nullable=False)
    first_name = Column(String(100), nullable=False)
    last_name = Column(String(100), nullable=False)
    email = Column(String(255), unique=True, index=True, nullable=True)
    phone = Column(String(20), nullable=True)

    # Academic information
    class_name = Column(String(100), nullable=True)
    academic_year = Column(String(20), nullable=True)
    department = Column(String(100), nullable=True)

    # Metadata
    created_by_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), nullable=False)
    is_active = Column(Boolean, default=True, nullable=False)

    # Additional information
    notes = Column(Text, nullable=True)
    additional_data = Column(JSON, nullable=True)

class ExamResult(Base):
    __tablename__ = "exam_results"
    __table_args__ = {'extend_existing': True}

    id = Column(Integer, primary_key=True, index=True)
    exam_id = Column(String(36), ForeignKey("exams.id"), nullable=False)
    student_id = Column(Integer, ForeignKey("students.id"), nullable=True)

    # Student information (extracted from exam or manually entered)
    student_name = Column(String(255), nullable=True)
    student_identifier = Column(String(100), nullable=True)

    # Grading results
    total_score = Column(Float, nullable=True)
    max_score = Column(Float, nullable=True)
    percentage = Column(Float, nullable=True)
    letter_grade = Column(String(5), nullable=True)
    grade_status = Column(Enum(GradeStatus), default=GradeStatus.PENDING, nullable=False)

    # Detailed results
    question_results = Column(JSON, nullable=True)
    extracted_answers = Column(JSON, nullable=True)
    correct_answers = Column(JSON, nullable=True)

    # AI Analysis
    ai_confidence = Column(Float, nullable=True)
    ai_model_used = Column(String(50), nullable=True)
    ai_analysis = Column(JSON, nullable=True)

    # Manual review
    requires_manual_review = Column(Boolean, default=False, nullable=False)
    reviewed_by_id = Column(Integer, ForeignKey("users.id"), nullable=True)
    review_notes = Column(Text, nullable=True)
    review_date = Column(DateTime(timezone=True), nullable=True)

    # Timestamps
    graded_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), nullable=False)

    # Additional data
    grading_rubric = Column(JSON, nullable=True)
    feedback = Column(Text, nullable=True)
    additional_data = Column(JSON, nullable=True)

class AuditLog(Base):
    __tablename__ = "audit_logs"
    __table_args__ = {'extend_existing': True}

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=True)

    # Action information
    action = Column(String(100), nullable=False)
    resource_type = Column(String(50), nullable=False)
    resource_id = Column(String(100), nullable=True)

    # Request information
    ip_address = Column(String(45), nullable=True)
    user_agent = Column(String(500), nullable=True)
    request_method = Column(String(10), nullable=True)
    request_path = Column(String(500), nullable=True)

    # Change details
    old_values = Column(JSON, nullable=True)
    new_values = Column(JSON, nullable=True)

    # Metadata
    timestamp = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    session_id = Column(String(100), nullable=True)
    additional_data = Column(JSON, nullable=True)

class GradeHistory(Base):
    __tablename__ = "grade_history"
    __table_args__ = {'extend_existing': True}

    id = Column(Integer, primary_key=True, index=True)
    exam_result_id = Column(Integer, ForeignKey("exam_results.id"), nullable=False)

    # Previous values
    previous_score = Column(Float, nullable=True)
    previous_grade = Column(String(5), nullable=True)
    previous_status = Column(String(50), nullable=True)

    # New values
    new_score = Column(Float, nullable=True)
    new_grade = Column(String(5), nullable=True)
    new_status = Column(String(50), nullable=True)

    # Change information
    changed_by_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    change_reason = Column(String(255), nullable=True)
    change_notes = Column(Text, nullable=True)
    changed_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
