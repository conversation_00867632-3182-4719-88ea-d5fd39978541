#!/usr/bin/env python3
"""
Script de démarrage rapide pour Auto-Grade Scribe Open-Source
Version simplifiée sans vérifications complexes
"""

import os
import sys
import subprocess
import logging

# Configuration du logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def main():
    """Démarrage rapide de l'application"""
    print("🆓 Auto-Grade Scribe Open-Source v4.0.0 - Démarrage Rapide")
    print("=" * 60)
    
    # Installer pydantic-settings si nécessaire
    try:
        import pydantic_settings
        logger.info("✅ pydantic-settings disponible")
    except ImportError:
        logger.info("📦 Installation de pydantic-settings...")
        try:
            subprocess.run([sys.executable, "-m", "pip", "install", "pydantic-settings==2.1.0"], 
                         check=True, capture_output=True)
            logger.info("✅ pydantic-settings installé")
        except subprocess.CalledProcessError as e:
            logger.error("❌ Erreur installation pydantic-settings: %s", e)
            sys.exit(1)
    
    # Vérifier PostgreSQL
    try:
        import psycopg2
        conn = psycopg2.connect(
            host="localhost",
            port=5432,
            user="autograde",
            password="autograde123",
            database="gradegeniusdb"
        )
        conn.close()
        logger.info("✅ PostgreSQL accessible")
    except Exception as e:
        logger.error("❌ PostgreSQL non accessible: %s", e)
        logger.info("💡 Démarrez PostgreSQL avec: docker-compose -f docker-compose-simple.yml up -d")
        sys.exit(1)
    
    # Créer les répertoires
    directories = ["uploads", "results", "temp", "logs", "models"]
    for directory in directories:
        os.makedirs(directory, exist_ok=True)
    
    logger.info("📁 Répertoires créés")
    
    # Démarrer l'application
    logger.info("🚀 Démarrage de l'application...")
    logger.info("🌐 Application sera disponible sur: http://127.0.0.1:8001")
    logger.info("📚 Documentation API: http://127.0.0.1:8001/docs")
    
    try:
        os.chdir("backend")
        subprocess.run([
            sys.executable, "-m", "uvicorn",
            "main:app",
            "--host", "0.0.0.0",
            "--port", "8001",
            "--reload"
        ], check=True)
    except KeyboardInterrupt:
        logger.info("🛑 Application arrêtée par l'utilisateur")
    except subprocess.CalledProcessError as e:
        logger.error("❌ Erreur démarrage application: %s", e)
    except FileNotFoundError:
        logger.error("❌ Fichier main.py non trouvé dans le dossier backend")
        logger.info("💡 Assurez-vous d'être dans le bon répertoire")

if __name__ == "__main__":
    main()
