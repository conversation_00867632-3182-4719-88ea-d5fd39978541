#!/usr/bin/env python3
"""
Script pour initialiser la base de données PostgreSQL.
Ce script crée les tables nécessaires dans la base de données PostgreSQL.
"""

import os
import sys
from sqlalchemy import create_engine, text
from sqlalchemy.exc import OperationalError

# URL de connexion à PostgreSQL
DATABASE_URL = "postgresql://autograde:autograde123@localhost:5432/gradegeniusdb"

def init_db():
    """Initialiser la base de données PostgreSQL."""
    print(f"Tentative de connexion à PostgreSQL: {DATABASE_URL}")

    try:
        # Créer le moteur PostgreSQL
        engine = create_engine(DATABASE_URL, pool_pre_ping=True)

        # Tester la connexion
        connection = engine.connect()
        connection.close()
        print("Connexion à PostgreSQL réussie!")

        # Importer les modèles et la base
        sys.path.append('backend')
        from database import Base

        # Créer les tables
        print("Création des tables dans la base de données PostgreSQL...")
        Base.metadata.create_all(bind=engine)
        print("Tables créées avec succès!")

        return True
    except Exception as e:
        print(f"Erreur lors de l'initialisation de la base de données PostgreSQL: {e}")
        return False

if __name__ == "__main__":
    if init_db():
        print("Base de données PostgreSQL initialisée avec succès!")
    else:
        print("Échec de l'initialisation de la base de données PostgreSQL.")
        sys.exit(1)
