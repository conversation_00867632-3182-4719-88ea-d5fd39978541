import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useToast } from '@/components/ui/use-toast';
import { useAuth } from '@/providers/auth-provider';

interface AuthFormProps {
  isLogin?: boolean;
}

const AuthForm: React.FC<AuthFormProps> = ({ isLogin = true }) => {
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState({
    username: '',
    email: '',
    password: '',
    role: 'teacher'
  });
  const { toast } = useToast();
  const router = useRouter();
  const { login } = useAuth();

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { id, value } = e.target;

    // Extraire le nom du champ à partir de l'ID
    let field = id;
    if (id.startsWith('email-')) field = 'email';
    if (id.startsWith('password-')) field = 'password';

    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleAuth = async (e: React.FormEvent, type: 'login' | 'register') => {
    e.preventDefault();
    setLoading(true);

    try {
      // Simulation d'authentification
      setTimeout(() => {
        if (type === 'login') {
          // For demo purposes, use email as username if logging in
          login(formData.email, formData.email.split('@')[0], 'teacher', formData.password);

          toast({
            title: "Connexion réussie",
            description: "Bienvenue sur GradeGenius"
          });

          // Attendre un court délai pour permettre la mise à jour de l'état d'authentification
          setTimeout(() => {
            router.push('/dashboard');
          }, 300);
        } else {
          // For registration, use the actual username
          login(formData.email, formData.username, formData.role);

          toast({
            title: "Inscription réussie",
            description: "Votre compte a été créé avec succès"
          });

          // Attendre un court délai pour permettre la mise à jour de l'état d'authentification
          setTimeout(() => {
            router.push('/dashboard');
          }, 300);
        }
        setLoading(false);
      }, 1500);
    } catch (error: any) {
      console.error('Authentication error:', error);
      toast({
        title: type === 'login' ? "Échec de connexion" : "Échec d'inscription",
        description: "Une erreur est survenue lors de l'authentification",
        variant: "destructive"
      });
      setLoading(false);
    }
  };

  return (
    <Card className="w-full max-w-md mx-auto">
      <CardHeader className="space-y-1 text-center">
        <div className="mx-auto h-10 w-10 rounded-lg bg-gradient-to-br from-primary to-accent flex items-center justify-center mb-2">
          <span className="font-bold text-white">GG</span>
        </div>
        <CardTitle className="text-2xl">GradeGenius</CardTitle>
        <CardDescription>
          Système intelligent de correction automatique de documents
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue={isLogin ? "login" : "register"} className="w-full">
          <TabsList className="grid w-full grid-cols-2 mb-4">
            <TabsTrigger value="login">Login</TabsTrigger>
            <TabsTrigger value="register">Register</TabsTrigger>
          </TabsList>

          <TabsContent value="login">
            <form onSubmit={(e) => handleAuth(e, 'login')}>
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="email-login">Email</Label>
                  <Input
                    id="email-login"
                    placeholder="<EMAIL>"
                    type="email"
                    value={formData.email}
                    onChange={handleChange}
                    required
                    autoComplete="email"
                    disabled={loading}
                    className="focus:border-primary focus:ring-2 focus:ring-primary"
                  />
                </div>
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <Label htmlFor="password-login">Password</Label>
                    <a
                      href="#"
                      className="text-sm text-primary underline-offset-4 hover:underline"
                    >
                      Forgot password?
                    </a>
                  </div>
                  <Input
                    id="password-login"
                    placeholder="••••••••"
                    type="password"
                    value={formData.password}
                    onChange={handleChange}
                    required
                    autoComplete="current-password"
                    disabled={loading}
                    className="focus:border-primary focus:ring-2 focus:ring-primary"
                  />
                </div>
                <Button className="w-full" type="submit" disabled={loading}>
                  {loading ? "Logging in..." : "Login"}
                </Button>
              </div>
            </form>
          </TabsContent>

          <TabsContent value="register">
            <form onSubmit={(e) => handleAuth(e, 'register')}>
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="username">Full Name</Label>
                  <Input
                    id="username"
                    placeholder="John Doe"
                    value={formData.username}
                    onChange={handleChange}
                    required
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="email-register">Email</Label>
                  <Input
                    id="email-register"
                    placeholder="<EMAIL>"
                    type="email"
                    value={formData.email}
                    onChange={handleChange}
                    required
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="password-register">Password</Label>
                  <Input
                    id="password-register"
                    placeholder="••••••••"
                    type="password"
                    value={formData.password}
                    onChange={handleChange}
                    required
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="role">I am a</Label>
                  <select
                    id="role"
                    className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                    value={formData.role}
                    onChange={handleChange}
                  >
                    <option value="teacher">Teacher</option>
                    <option value="student">Student</option>
                    <option value="institution">Institution</option>
                  </select>
                </div>
                <Button className="w-full" type="submit" disabled={loading}>
                  {loading ? "Creating account..." : "Create account"}
                </Button>
              </div>
            </form>
          </TabsContent>
        </Tabs>
      </CardContent>
      <CardFooter className="text-center text-sm text-muted-foreground">
        By continuing, you agree to our Terms of Service and Privacy Policy.
      </CardFooter>
    </Card>
  );
};

export default AuthForm;
