# *** GENERATED BY `setup.py antlr`, DO NOT EDIT BY HAND ***
#
# Generated with antlr4
#    antlr4 is licensed under the BSD-3-Clause License
#    https://github.com/antlr/antlr4/blob/master/LICENSE.txt
from antlr4 import *
from io import StringIO
import sys
if sys.version_info[1] > 5:
	from typing import TextIO
else:
	from typing.io import TextIO

def serializedATN():
    return [
        4,1,49,431,2,0,7,0,2,1,7,1,2,2,7,2,2,3,7,3,2,4,7,4,2,5,7,5,2,6,7,
        6,2,7,7,7,2,8,7,8,2,9,7,9,2,10,7,10,2,11,7,11,2,12,7,12,2,13,7,13,
        2,14,7,14,2,15,7,15,2,16,7,16,2,17,7,17,2,18,7,18,2,19,7,19,2,20,
        7,20,2,21,7,21,2,22,7,22,2,23,7,23,2,24,7,24,2,25,7,25,2,26,7,26,
        2,27,7,27,1,0,4,0,58,8,0,11,0,12,0,59,1,1,1,1,1,1,1,1,1,1,1,1,1,
        1,3,1,69,8,1,1,2,1,2,1,2,1,2,1,2,1,2,1,2,1,2,1,2,1,2,1,2,1,2,1,2,
        3,2,84,8,2,1,2,1,2,1,2,3,2,89,8,2,1,3,1,3,1,4,1,4,1,4,5,4,96,8,4,
        10,4,12,4,99,9,4,1,5,4,5,102,8,5,11,5,12,5,103,1,6,1,6,1,6,1,6,1,
        6,5,6,111,8,6,10,6,12,6,114,9,6,3,6,116,8,6,1,6,1,6,1,6,1,6,1,6,
        1,6,5,6,124,8,6,10,6,12,6,127,9,6,3,6,129,8,6,1,6,3,6,132,8,6,1,
        7,1,7,1,7,1,7,5,7,138,8,7,10,7,12,7,141,9,7,1,8,1,8,1,8,1,8,1,8,
        1,8,1,8,1,8,1,8,1,8,5,8,153,8,8,10,8,12,8,156,9,8,1,8,1,8,5,8,160,
        8,8,10,8,12,8,163,9,8,3,8,165,8,8,1,9,1,9,1,9,1,9,1,9,1,9,3,9,173,
        8,9,1,9,1,9,1,9,1,9,1,9,1,9,1,9,1,9,5,9,183,8,9,10,9,12,9,186,9,
        9,1,9,3,9,189,8,9,1,9,1,9,1,9,3,9,194,8,9,1,9,3,9,197,8,9,1,9,5,
        9,200,8,9,10,9,12,9,203,9,9,1,9,1,9,3,9,207,8,9,1,10,1,10,1,10,1,
        10,1,10,1,10,1,10,1,10,5,10,217,8,10,10,10,12,10,220,9,10,1,10,1,
        10,1,11,1,11,1,11,1,11,5,11,228,8,11,10,11,12,11,231,9,11,1,12,1,
        12,1,12,1,12,1,13,1,13,1,13,1,13,1,13,3,13,242,8,13,1,13,1,13,4,
        13,246,8,13,11,13,12,13,247,1,14,1,14,1,14,1,14,5,14,254,8,14,10,
        14,12,14,257,9,14,1,14,1,14,1,15,1,15,1,15,1,15,3,15,265,8,15,1,
        15,1,15,3,15,269,8,15,1,16,1,16,1,16,1,16,1,16,3,16,276,8,16,1,17,
        1,17,3,17,280,8,17,1,18,1,18,1,18,1,18,5,18,286,8,18,10,18,12,18,
        289,9,18,1,19,1,19,1,19,1,19,5,19,295,8,19,10,19,12,19,298,9,19,
        1,20,1,20,3,20,302,8,20,1,21,1,21,1,21,1,21,3,21,308,8,21,1,22,1,
        22,1,22,1,22,5,22,314,8,22,10,22,12,22,317,9,22,1,23,1,23,3,23,321,
        8,23,1,24,1,24,1,24,1,24,1,24,1,24,5,24,329,8,24,10,24,12,24,332,
        9,24,1,24,1,24,3,24,336,8,24,1,24,1,24,1,24,1,24,1,25,1,25,1,25,
        1,25,1,25,1,25,1,25,1,25,5,25,350,8,25,10,25,12,25,353,9,25,3,25,
        355,8,25,1,26,1,26,4,26,359,8,26,11,26,12,26,360,1,26,1,26,3,26,
        365,8,26,1,27,1,27,1,27,1,27,1,27,1,27,1,27,1,27,5,27,375,8,27,10,
        27,12,27,378,9,27,1,27,1,27,1,27,1,27,1,27,1,27,5,27,386,8,27,10,
        27,12,27,389,9,27,1,27,1,27,1,27,1,27,1,27,1,27,1,27,1,27,1,27,3,
        27,400,8,27,1,27,1,27,5,27,404,8,27,10,27,12,27,407,9,27,3,27,409,
        8,27,1,27,1,27,1,27,1,27,1,27,1,27,1,27,1,27,1,27,1,27,1,27,1,27,
        1,27,1,27,1,27,5,27,426,8,27,10,27,12,27,429,9,27,1,27,0,1,54,28,
        0,2,4,6,8,10,12,14,16,18,20,22,24,26,28,30,32,34,36,38,40,42,44,
        46,48,50,52,54,0,7,1,0,3,9,1,0,27,28,1,0,17,18,2,0,10,10,19,19,1,
        0,44,45,2,0,44,46,48,48,1,0,25,26,483,0,57,1,0,0,0,2,68,1,0,0,0,
        4,88,1,0,0,0,6,90,1,0,0,0,8,92,1,0,0,0,10,101,1,0,0,0,12,131,1,0,
        0,0,14,133,1,0,0,0,16,164,1,0,0,0,18,166,1,0,0,0,20,208,1,0,0,0,
        22,223,1,0,0,0,24,232,1,0,0,0,26,236,1,0,0,0,28,249,1,0,0,0,30,268,
        1,0,0,0,32,275,1,0,0,0,34,277,1,0,0,0,36,281,1,0,0,0,38,290,1,0,
        0,0,40,299,1,0,0,0,42,303,1,0,0,0,44,309,1,0,0,0,46,318,1,0,0,0,
        48,322,1,0,0,0,50,354,1,0,0,0,52,364,1,0,0,0,54,408,1,0,0,0,56,58,
        3,2,1,0,57,56,1,0,0,0,58,59,1,0,0,0,59,57,1,0,0,0,59,60,1,0,0,0,
        60,1,1,0,0,0,61,69,3,14,7,0,62,69,3,12,6,0,63,69,3,32,16,0,64,69,
        3,22,11,0,65,69,3,26,13,0,66,69,3,4,2,0,67,69,3,34,17,0,68,61,1,
        0,0,0,68,62,1,0,0,0,68,63,1,0,0,0,68,64,1,0,0,0,68,65,1,0,0,0,68,
        66,1,0,0,0,68,67,1,0,0,0,69,3,1,0,0,0,70,71,3,52,26,0,71,72,3,6,
        3,0,72,73,3,54,27,0,73,89,1,0,0,0,74,75,5,48,0,0,75,76,5,1,0,0,76,
        77,3,8,4,0,77,78,5,2,0,0,78,79,3,6,3,0,79,80,3,54,27,0,80,89,1,0,
        0,0,81,83,5,48,0,0,82,84,3,10,5,0,83,82,1,0,0,0,83,84,1,0,0,0,84,
        85,1,0,0,0,85,86,3,6,3,0,86,87,3,54,27,0,87,89,1,0,0,0,88,70,1,0,
        0,0,88,74,1,0,0,0,88,81,1,0,0,0,89,5,1,0,0,0,90,91,7,0,0,0,91,7,
        1,0,0,0,92,97,3,54,27,0,93,94,5,10,0,0,94,96,3,54,27,0,95,93,1,0,
        0,0,96,99,1,0,0,0,97,95,1,0,0,0,97,98,1,0,0,0,98,9,1,0,0,0,99,97,
        1,0,0,0,100,102,5,11,0,0,101,100,1,0,0,0,102,103,1,0,0,0,103,101,
        1,0,0,0,103,104,1,0,0,0,104,11,1,0,0,0,105,106,5,48,0,0,106,115,
        5,12,0,0,107,112,3,54,27,0,108,109,5,10,0,0,109,111,3,54,27,0,110,
        108,1,0,0,0,111,114,1,0,0,0,112,110,1,0,0,0,112,113,1,0,0,0,113,
        116,1,0,0,0,114,112,1,0,0,0,115,107,1,0,0,0,115,116,1,0,0,0,116,
        117,1,0,0,0,117,132,5,13,0,0,118,119,7,1,0,0,119,128,5,12,0,0,120,
        125,5,48,0,0,121,122,5,10,0,0,122,124,5,48,0,0,123,121,1,0,0,0,124,
        127,1,0,0,0,125,123,1,0,0,0,125,126,1,0,0,0,126,129,1,0,0,0,127,
        125,1,0,0,0,128,120,1,0,0,0,128,129,1,0,0,0,129,130,1,0,0,0,130,
        132,5,13,0,0,131,105,1,0,0,0,131,118,1,0,0,0,132,13,1,0,0,0,133,
        134,3,16,8,0,134,139,3,18,9,0,135,136,5,10,0,0,136,138,3,18,9,0,
        137,135,1,0,0,0,138,141,1,0,0,0,139,137,1,0,0,0,139,140,1,0,0,0,
        140,15,1,0,0,0,141,139,1,0,0,0,142,165,5,34,0,0,143,165,5,35,0,0,
        144,165,5,36,0,0,145,165,5,37,0,0,146,165,5,38,0,0,147,165,5,39,
        0,0,148,165,5,40,0,0,149,165,5,41,0,0,150,154,5,42,0,0,151,153,5,
        11,0,0,152,151,1,0,0,0,153,156,1,0,0,0,154,152,1,0,0,0,154,155,1,
        0,0,0,155,165,1,0,0,0,156,154,1,0,0,0,157,161,5,43,0,0,158,160,5,
        11,0,0,159,158,1,0,0,0,160,163,1,0,0,0,161,159,1,0,0,0,161,162,1,
        0,0,0,162,165,1,0,0,0,163,161,1,0,0,0,164,142,1,0,0,0,164,143,1,
        0,0,0,164,144,1,0,0,0,164,145,1,0,0,0,164,146,1,0,0,0,164,147,1,
        0,0,0,164,148,1,0,0,0,164,149,1,0,0,0,164,150,1,0,0,0,164,157,1,
        0,0,0,165,17,1,0,0,0,166,172,5,48,0,0,167,168,5,14,0,0,168,169,5,
        44,0,0,169,170,5,10,0,0,170,171,5,44,0,0,171,173,5,15,0,0,172,167,
        1,0,0,0,172,173,1,0,0,0,173,188,1,0,0,0,174,175,5,14,0,0,175,176,
        5,44,0,0,176,177,5,16,0,0,177,184,5,44,0,0,178,179,5,10,0,0,179,
        180,5,44,0,0,180,181,5,16,0,0,181,183,5,44,0,0,182,178,1,0,0,0,183,
        186,1,0,0,0,184,182,1,0,0,0,184,185,1,0,0,0,185,187,1,0,0,0,186,
        184,1,0,0,0,187,189,5,15,0,0,188,174,1,0,0,0,188,189,1,0,0,0,189,
        193,1,0,0,0,190,191,5,14,0,0,191,192,5,44,0,0,192,194,5,15,0,0,193,
        190,1,0,0,0,193,194,1,0,0,0,194,196,1,0,0,0,195,197,7,2,0,0,196,
        195,1,0,0,0,196,197,1,0,0,0,197,201,1,0,0,0,198,200,5,11,0,0,199,
        198,1,0,0,0,200,203,1,0,0,0,201,199,1,0,0,0,201,202,1,0,0,0,202,
        206,1,0,0,0,203,201,1,0,0,0,204,205,5,3,0,0,205,207,3,54,27,0,206,
        204,1,0,0,0,206,207,1,0,0,0,207,19,1,0,0,0,208,209,5,14,0,0,209,
        210,5,44,0,0,210,211,5,16,0,0,211,218,5,44,0,0,212,213,5,10,0,0,
        213,214,5,44,0,0,214,215,5,16,0,0,215,217,5,44,0,0,216,212,1,0,0,
        0,217,220,1,0,0,0,218,216,1,0,0,0,218,219,1,0,0,0,219,221,1,0,0,
        0,220,218,1,0,0,0,221,222,5,15,0,0,222,21,1,0,0,0,223,224,5,27,0,
        0,224,229,3,24,12,0,225,226,5,10,0,0,226,228,3,24,12,0,227,225,1,
        0,0,0,228,231,1,0,0,0,229,227,1,0,0,0,229,230,1,0,0,0,230,23,1,0,
        0,0,231,229,1,0,0,0,232,233,5,48,0,0,233,234,5,3,0,0,234,235,3,54,
        27,0,235,25,1,0,0,0,236,237,5,28,0,0,237,241,5,48,0,0,238,239,5,
        12,0,0,239,240,5,48,0,0,240,242,5,13,0,0,241,238,1,0,0,0,241,242,
        1,0,0,0,242,245,1,0,0,0,243,244,5,10,0,0,244,246,3,54,27,0,245,243,
        1,0,0,0,246,247,1,0,0,0,247,245,1,0,0,0,247,248,1,0,0,0,248,27,1,
        0,0,0,249,250,5,1,0,0,250,255,3,54,27,0,251,252,7,3,0,0,252,254,
        3,54,27,0,253,251,1,0,0,0,254,257,1,0,0,0,255,253,1,0,0,0,255,256,
        1,0,0,0,256,258,1,0,0,0,257,255,1,0,0,0,258,259,5,2,0,0,259,29,1,
        0,0,0,260,261,5,48,0,0,261,262,5,48,0,0,262,264,5,3,0,0,263,265,
        7,4,0,0,264,263,1,0,0,0,264,265,1,0,0,0,265,269,1,0,0,0,266,269,
        5,45,0,0,267,269,5,44,0,0,268,260,1,0,0,0,268,266,1,0,0,0,268,267,
        1,0,0,0,269,31,1,0,0,0,270,276,3,36,18,0,271,276,3,38,19,0,272,276,
        3,44,22,0,273,276,3,48,24,0,274,276,3,50,25,0,275,270,1,0,0,0,275,
        271,1,0,0,0,275,272,1,0,0,0,275,273,1,0,0,0,275,274,1,0,0,0,276,
        33,1,0,0,0,277,279,5,48,0,0,278,280,7,5,0,0,279,278,1,0,0,0,279,
        280,1,0,0,0,280,35,1,0,0,0,281,282,5,32,0,0,282,287,5,48,0,0,283,
        284,5,10,0,0,284,286,5,48,0,0,285,283,1,0,0,0,286,289,1,0,0,0,287,
        285,1,0,0,0,287,288,1,0,0,0,288,37,1,0,0,0,289,287,1,0,0,0,290,291,
        5,29,0,0,291,296,3,42,21,0,292,293,5,10,0,0,293,295,3,42,21,0,294,
        292,1,0,0,0,295,298,1,0,0,0,296,294,1,0,0,0,296,297,1,0,0,0,297,
        39,1,0,0,0,298,296,1,0,0,0,299,301,5,48,0,0,300,302,3,10,5,0,301,
        300,1,0,0,0,301,302,1,0,0,0,302,41,1,0,0,0,303,304,3,40,20,0,304,
        305,5,3,0,0,305,307,3,54,27,0,306,308,3,54,27,0,307,306,1,0,0,0,
        307,308,1,0,0,0,308,43,1,0,0,0,309,310,5,30,0,0,310,315,3,46,23,
        0,311,312,5,10,0,0,312,314,3,46,23,0,313,311,1,0,0,0,314,317,1,0,
        0,0,315,313,1,0,0,0,315,316,1,0,0,0,316,45,1,0,0,0,317,315,1,0,0,
        0,318,320,3,54,27,0,319,321,3,54,27,0,320,319,1,0,0,0,320,321,1,
        0,0,0,321,47,1,0,0,0,322,323,5,48,0,0,323,335,3,12,6,0,324,325,5,
        1,0,0,325,330,3,30,15,0,326,327,5,10,0,0,327,329,3,30,15,0,328,326,
        1,0,0,0,329,332,1,0,0,0,330,328,1,0,0,0,330,331,1,0,0,0,331,333,
        1,0,0,0,332,330,1,0,0,0,333,334,5,2,0,0,334,336,1,0,0,0,335,324,
        1,0,0,0,335,336,1,0,0,0,336,337,1,0,0,0,337,338,5,48,0,0,338,339,
        5,20,0,0,339,340,5,48,0,0,340,49,1,0,0,0,341,342,5,31,0,0,342,343,
        5,48,0,0,343,344,5,20,0,0,344,355,5,48,0,0,345,346,5,33,0,0,346,
        351,5,48,0,0,347,348,5,10,0,0,348,350,5,48,0,0,349,347,1,0,0,0,350,
        353,1,0,0,0,351,349,1,0,0,0,351,352,1,0,0,0,352,355,1,0,0,0,353,
        351,1,0,0,0,354,341,1,0,0,0,354,345,1,0,0,0,355,51,1,0,0,0,356,358,
        5,48,0,0,357,359,5,21,0,0,358,357,1,0,0,0,359,360,1,0,0,0,360,358,
        1,0,0,0,360,361,1,0,0,0,361,365,1,0,0,0,362,365,5,22,0,0,363,365,
        5,23,0,0,364,356,1,0,0,0,364,362,1,0,0,0,364,363,1,0,0,0,365,53,
        1,0,0,0,366,367,6,27,-1,0,367,409,5,46,0,0,368,369,5,18,0,0,369,
        409,3,54,27,12,370,409,5,45,0,0,371,409,5,44,0,0,372,376,5,48,0,
        0,373,375,5,11,0,0,374,373,1,0,0,0,375,378,1,0,0,0,376,374,1,0,0,
        0,376,377,1,0,0,0,377,409,1,0,0,0,378,376,1,0,0,0,379,409,3,52,26,
        0,380,381,5,48,0,0,381,382,5,1,0,0,382,387,3,54,27,0,383,384,5,10,
        0,0,384,386,3,54,27,0,385,383,1,0,0,0,386,389,1,0,0,0,387,385,1,
        0,0,0,387,388,1,0,0,0,388,390,1,0,0,0,389,387,1,0,0,0,390,391,5,
        2,0,0,391,409,1,0,0,0,392,409,3,12,6,0,393,409,3,28,14,0,394,395,
        5,12,0,0,395,396,3,54,27,0,396,397,5,13,0,0,397,409,1,0,0,0,398,
        400,5,48,0,0,399,398,1,0,0,0,399,400,1,0,0,0,400,401,1,0,0,0,401,
        405,3,20,10,0,402,404,5,11,0,0,403,402,1,0,0,0,404,407,1,0,0,0,405,
        403,1,0,0,0,405,406,1,0,0,0,406,409,1,0,0,0,407,405,1,0,0,0,408,
        366,1,0,0,0,408,368,1,0,0,0,408,370,1,0,0,0,408,371,1,0,0,0,408,
        372,1,0,0,0,408,379,1,0,0,0,408,380,1,0,0,0,408,392,1,0,0,0,408,
        393,1,0,0,0,408,394,1,0,0,0,408,399,1,0,0,0,409,427,1,0,0,0,410,
        411,10,16,0,0,411,412,5,24,0,0,412,426,3,54,27,17,413,414,10,15,
        0,0,414,415,7,6,0,0,415,426,3,54,27,16,416,417,10,14,0,0,417,418,
        7,2,0,0,418,426,3,54,27,15,419,420,10,3,0,0,420,421,5,3,0,0,421,
        426,3,54,27,4,422,423,10,2,0,0,423,424,5,16,0,0,424,426,3,54,27,
        3,425,410,1,0,0,0,425,413,1,0,0,0,425,416,1,0,0,0,425,419,1,0,0,
        0,425,422,1,0,0,0,426,429,1,0,0,0,427,425,1,0,0,0,427,428,1,0,0,
        0,428,55,1,0,0,0,429,427,1,0,0,0,50,59,68,83,88,97,103,112,115,125,
        128,131,139,154,161,164,172,184,188,193,196,201,206,218,229,241,
        247,255,264,268,275,279,287,296,301,307,315,320,330,335,351,354,
        360,364,376,387,399,405,408,425,427
    ]

class AutolevParser ( Parser ):

    grammarFileName = "Autolev.g4"

    atn = ATNDeserializer().deserialize(serializedATN())

    decisionsToDFA = [ DFA(ds, i) for i, ds in enumerate(atn.decisionToState) ]

    sharedContextCache = PredictionContextCache()

    literalNames = [ "<INVALID>", "'['", "']'", "'='", "'+='", "'-='", "':='",
                     "'*='", "'/='", "'^='", "','", "'''", "'('", "')'",
                     "'{'", "'}'", "':'", "'+'", "'-'", "';'", "'.'", "'>'",
                     "'0>'", "'1>>'", "'^'", "'*'", "'/'" ]

    symbolicNames = [ "<INVALID>", "<INVALID>", "<INVALID>", "<INVALID>",
                      "<INVALID>", "<INVALID>", "<INVALID>", "<INVALID>",
                      "<INVALID>", "<INVALID>", "<INVALID>", "<INVALID>",
                      "<INVALID>", "<INVALID>", "<INVALID>", "<INVALID>",
                      "<INVALID>", "<INVALID>", "<INVALID>", "<INVALID>",
                      "<INVALID>", "<INVALID>", "<INVALID>", "<INVALID>",
                      "<INVALID>", "<INVALID>", "<INVALID>", "Mass", "Inertia",
                      "Input", "Output", "Save", "UnitSystem", "Encode",
                      "Newtonian", "Frames", "Bodies", "Particles", "Points",
                      "Constants", "Specifieds", "Imaginary", "Variables",
                      "MotionVariables", "INT", "FLOAT", "EXP", "LINE_COMMENT",
                      "ID", "WS" ]

    RULE_prog = 0
    RULE_stat = 1
    RULE_assignment = 2
    RULE_equals = 3
    RULE_index = 4
    RULE_diff = 5
    RULE_functionCall = 6
    RULE_varDecl = 7
    RULE_varType = 8
    RULE_varDecl2 = 9
    RULE_ranges = 10
    RULE_massDecl = 11
    RULE_massDecl2 = 12
    RULE_inertiaDecl = 13
    RULE_matrix = 14
    RULE_matrixInOutput = 15
    RULE_codeCommands = 16
    RULE_settings = 17
    RULE_units = 18
    RULE_inputs = 19
    RULE_id_diff = 20
    RULE_inputs2 = 21
    RULE_outputs = 22
    RULE_outputs2 = 23
    RULE_codegen = 24
    RULE_commands = 25
    RULE_vec = 26
    RULE_expr = 27

    ruleNames =  [ "prog", "stat", "assignment", "equals", "index", "diff",
                   "functionCall", "varDecl", "varType", "varDecl2", "ranges",
                   "massDecl", "massDecl2", "inertiaDecl", "matrix", "matrixInOutput",
                   "codeCommands", "settings", "units", "inputs", "id_diff",
                   "inputs2", "outputs", "outputs2", "codegen", "commands",
                   "vec", "expr" ]

    EOF = Token.EOF
    T__0=1
    T__1=2
    T__2=3
    T__3=4
    T__4=5
    T__5=6
    T__6=7
    T__7=8
    T__8=9
    T__9=10
    T__10=11
    T__11=12
    T__12=13
    T__13=14
    T__14=15
    T__15=16
    T__16=17
    T__17=18
    T__18=19
    T__19=20
    T__20=21
    T__21=22
    T__22=23
    T__23=24
    T__24=25
    T__25=26
    Mass=27
    Inertia=28
    Input=29
    Output=30
    Save=31
    UnitSystem=32
    Encode=33
    Newtonian=34
    Frames=35
    Bodies=36
    Particles=37
    Points=38
    Constants=39
    Specifieds=40
    Imaginary=41
    Variables=42
    MotionVariables=43
    INT=44
    FLOAT=45
    EXP=46
    LINE_COMMENT=47
    ID=48
    WS=49

    def __init__(self, input:TokenStream, output:TextIO = sys.stdout):
        super().__init__(input, output)
        self.checkVersion("4.11.1")
        self._interp = ParserATNSimulator(self, self.atn, self.decisionsToDFA, self.sharedContextCache)
        self._predicates = None




    class ProgContext(ParserRuleContext):
        __slots__ = 'parser'

        def __init__(self, parser, parent:ParserRuleContext=None, invokingState:int=-1):
            super().__init__(parent, invokingState)
            self.parser = parser

        def stat(self, i:int=None):
            if i is None:
                return self.getTypedRuleContexts(AutolevParser.StatContext)
            else:
                return self.getTypedRuleContext(AutolevParser.StatContext,i)


        def getRuleIndex(self):
            return AutolevParser.RULE_prog

        def enterRule(self, listener:ParseTreeListener):
            if hasattr( listener, "enterProg" ):
                listener.enterProg(self)

        def exitRule(self, listener:ParseTreeListener):
            if hasattr( listener, "exitProg" ):
                listener.exitProg(self)




    def prog(self):

        localctx = AutolevParser.ProgContext(self, self._ctx, self.state)
        self.enterRule(localctx, 0, self.RULE_prog)
        self._la = 0 # Token type
        try:
            self.enterOuterAlt(localctx, 1)
            self.state = 57
            self._errHandler.sync(self)
            _la = self._input.LA(1)
            while True:
                self.state = 56
                self.stat()
                self.state = 59
                self._errHandler.sync(self)
                _la = self._input.LA(1)
                if not (((_la) & ~0x3f) == 0 and ((1 << _la) & 299067041120256) != 0):
                    break

        except RecognitionException as re:
            localctx.exception = re
            self._errHandler.reportError(self, re)
            self._errHandler.recover(self, re)
        finally:
            self.exitRule()
        return localctx


    class StatContext(ParserRuleContext):
        __slots__ = 'parser'

        def __init__(self, parser, parent:ParserRuleContext=None, invokingState:int=-1):
            super().__init__(parent, invokingState)
            self.parser = parser

        def varDecl(self):
            return self.getTypedRuleContext(AutolevParser.VarDeclContext,0)


        def functionCall(self):
            return self.getTypedRuleContext(AutolevParser.FunctionCallContext,0)


        def codeCommands(self):
            return self.getTypedRuleContext(AutolevParser.CodeCommandsContext,0)


        def massDecl(self):
            return self.getTypedRuleContext(AutolevParser.MassDeclContext,0)


        def inertiaDecl(self):
            return self.getTypedRuleContext(AutolevParser.InertiaDeclContext,0)


        def assignment(self):
            return self.getTypedRuleContext(AutolevParser.AssignmentContext,0)


        def settings(self):
            return self.getTypedRuleContext(AutolevParser.SettingsContext,0)


        def getRuleIndex(self):
            return AutolevParser.RULE_stat

        def enterRule(self, listener:ParseTreeListener):
            if hasattr( listener, "enterStat" ):
                listener.enterStat(self)

        def exitRule(self, listener:ParseTreeListener):
            if hasattr( listener, "exitStat" ):
                listener.exitStat(self)




    def stat(self):

        localctx = AutolevParser.StatContext(self, self._ctx, self.state)
        self.enterRule(localctx, 2, self.RULE_stat)
        try:
            self.state = 68
            self._errHandler.sync(self)
            la_ = self._interp.adaptivePredict(self._input,1,self._ctx)
            if la_ == 1:
                self.enterOuterAlt(localctx, 1)
                self.state = 61
                self.varDecl()
                pass

            elif la_ == 2:
                self.enterOuterAlt(localctx, 2)
                self.state = 62
                self.functionCall()
                pass

            elif la_ == 3:
                self.enterOuterAlt(localctx, 3)
                self.state = 63
                self.codeCommands()
                pass

            elif la_ == 4:
                self.enterOuterAlt(localctx, 4)
                self.state = 64
                self.massDecl()
                pass

            elif la_ == 5:
                self.enterOuterAlt(localctx, 5)
                self.state = 65
                self.inertiaDecl()
                pass

            elif la_ == 6:
                self.enterOuterAlt(localctx, 6)
                self.state = 66
                self.assignment()
                pass

            elif la_ == 7:
                self.enterOuterAlt(localctx, 7)
                self.state = 67
                self.settings()
                pass


        except RecognitionException as re:
            localctx.exception = re
            self._errHandler.reportError(self, re)
            self._errHandler.recover(self, re)
        finally:
            self.exitRule()
        return localctx


    class AssignmentContext(ParserRuleContext):
        __slots__ = 'parser'

        def __init__(self, parser, parent:ParserRuleContext=None, invokingState:int=-1):
            super().__init__(parent, invokingState)
            self.parser = parser


        def getRuleIndex(self):
            return AutolevParser.RULE_assignment


        def copyFrom(self, ctx:ParserRuleContext):
            super().copyFrom(ctx)



    class VecAssignContext(AssignmentContext):

        def __init__(self, parser, ctx:ParserRuleContext): # actually a AutolevParser.AssignmentContext
            super().__init__(parser)
            self.copyFrom(ctx)

        def vec(self):
            return self.getTypedRuleContext(AutolevParser.VecContext,0)

        def equals(self):
            return self.getTypedRuleContext(AutolevParser.EqualsContext,0)

        def expr(self):
            return self.getTypedRuleContext(AutolevParser.ExprContext,0)


        def enterRule(self, listener:ParseTreeListener):
            if hasattr( listener, "enterVecAssign" ):
                listener.enterVecAssign(self)

        def exitRule(self, listener:ParseTreeListener):
            if hasattr( listener, "exitVecAssign" ):
                listener.exitVecAssign(self)


    class RegularAssignContext(AssignmentContext):

        def __init__(self, parser, ctx:ParserRuleContext): # actually a AutolevParser.AssignmentContext
            super().__init__(parser)
            self.copyFrom(ctx)

        def ID(self):
            return self.getToken(AutolevParser.ID, 0)
        def equals(self):
            return self.getTypedRuleContext(AutolevParser.EqualsContext,0)

        def expr(self):
            return self.getTypedRuleContext(AutolevParser.ExprContext,0)

        def diff(self):
            return self.getTypedRuleContext(AutolevParser.DiffContext,0)


        def enterRule(self, listener:ParseTreeListener):
            if hasattr( listener, "enterRegularAssign" ):
                listener.enterRegularAssign(self)

        def exitRule(self, listener:ParseTreeListener):
            if hasattr( listener, "exitRegularAssign" ):
                listener.exitRegularAssign(self)


    class IndexAssignContext(AssignmentContext):

        def __init__(self, parser, ctx:ParserRuleContext): # actually a AutolevParser.AssignmentContext
            super().__init__(parser)
            self.copyFrom(ctx)

        def ID(self):
            return self.getToken(AutolevParser.ID, 0)
        def index(self):
            return self.getTypedRuleContext(AutolevParser.IndexContext,0)

        def equals(self):
            return self.getTypedRuleContext(AutolevParser.EqualsContext,0)

        def expr(self):
            return self.getTypedRuleContext(AutolevParser.ExprContext,0)


        def enterRule(self, listener:ParseTreeListener):
            if hasattr( listener, "enterIndexAssign" ):
                listener.enterIndexAssign(self)

        def exitRule(self, listener:ParseTreeListener):
            if hasattr( listener, "exitIndexAssign" ):
                listener.exitIndexAssign(self)



    def assignment(self):

        localctx = AutolevParser.AssignmentContext(self, self._ctx, self.state)
        self.enterRule(localctx, 4, self.RULE_assignment)
        self._la = 0 # Token type
        try:
            self.state = 88
            self._errHandler.sync(self)
            la_ = self._interp.adaptivePredict(self._input,3,self._ctx)
            if la_ == 1:
                localctx = AutolevParser.VecAssignContext(self, localctx)
                self.enterOuterAlt(localctx, 1)
                self.state = 70
                self.vec()
                self.state = 71
                self.equals()
                self.state = 72
                self.expr(0)
                pass

            elif la_ == 2:
                localctx = AutolevParser.IndexAssignContext(self, localctx)
                self.enterOuterAlt(localctx, 2)
                self.state = 74
                self.match(AutolevParser.ID)
                self.state = 75
                self.match(AutolevParser.T__0)
                self.state = 76
                self.index()
                self.state = 77
                self.match(AutolevParser.T__1)
                self.state = 78
                self.equals()
                self.state = 79
                self.expr(0)
                pass

            elif la_ == 3:
                localctx = AutolevParser.RegularAssignContext(self, localctx)
                self.enterOuterAlt(localctx, 3)
                self.state = 81
                self.match(AutolevParser.ID)
                self.state = 83
                self._errHandler.sync(self)
                _la = self._input.LA(1)
                if _la==11:
                    self.state = 82
                    self.diff()


                self.state = 85
                self.equals()
                self.state = 86
                self.expr(0)
                pass


        except RecognitionException as re:
            localctx.exception = re
            self._errHandler.reportError(self, re)
            self._errHandler.recover(self, re)
        finally:
            self.exitRule()
        return localctx


    class EqualsContext(ParserRuleContext):
        __slots__ = 'parser'

        def __init__(self, parser, parent:ParserRuleContext=None, invokingState:int=-1):
            super().__init__(parent, invokingState)
            self.parser = parser


        def getRuleIndex(self):
            return AutolevParser.RULE_equals

        def enterRule(self, listener:ParseTreeListener):
            if hasattr( listener, "enterEquals" ):
                listener.enterEquals(self)

        def exitRule(self, listener:ParseTreeListener):
            if hasattr( listener, "exitEquals" ):
                listener.exitEquals(self)




    def equals(self):

        localctx = AutolevParser.EqualsContext(self, self._ctx, self.state)
        self.enterRule(localctx, 6, self.RULE_equals)
        self._la = 0 # Token type
        try:
            self.enterOuterAlt(localctx, 1)
            self.state = 90
            _la = self._input.LA(1)
            if not(((_la) & ~0x3f) == 0 and ((1 << _la) & 1016) != 0):
                self._errHandler.recoverInline(self)
            else:
                self._errHandler.reportMatch(self)
                self.consume()
        except RecognitionException as re:
            localctx.exception = re
            self._errHandler.reportError(self, re)
            self._errHandler.recover(self, re)
        finally:
            self.exitRule()
        return localctx


    class IndexContext(ParserRuleContext):
        __slots__ = 'parser'

        def __init__(self, parser, parent:ParserRuleContext=None, invokingState:int=-1):
            super().__init__(parent, invokingState)
            self.parser = parser

        def expr(self, i:int=None):
            if i is None:
                return self.getTypedRuleContexts(AutolevParser.ExprContext)
            else:
                return self.getTypedRuleContext(AutolevParser.ExprContext,i)


        def getRuleIndex(self):
            return AutolevParser.RULE_index

        def enterRule(self, listener:ParseTreeListener):
            if hasattr( listener, "enterIndex" ):
                listener.enterIndex(self)

        def exitRule(self, listener:ParseTreeListener):
            if hasattr( listener, "exitIndex" ):
                listener.exitIndex(self)




    def index(self):

        localctx = AutolevParser.IndexContext(self, self._ctx, self.state)
        self.enterRule(localctx, 8, self.RULE_index)
        self._la = 0 # Token type
        try:
            self.enterOuterAlt(localctx, 1)
            self.state = 92
            self.expr(0)
            self.state = 97
            self._errHandler.sync(self)
            _la = self._input.LA(1)
            while _la==10:
                self.state = 93
                self.match(AutolevParser.T__9)
                self.state = 94
                self.expr(0)
                self.state = 99
                self._errHandler.sync(self)
                _la = self._input.LA(1)

        except RecognitionException as re:
            localctx.exception = re
            self._errHandler.reportError(self, re)
            self._errHandler.recover(self, re)
        finally:
            self.exitRule()
        return localctx


    class DiffContext(ParserRuleContext):
        __slots__ = 'parser'

        def __init__(self, parser, parent:ParserRuleContext=None, invokingState:int=-1):
            super().__init__(parent, invokingState)
            self.parser = parser


        def getRuleIndex(self):
            return AutolevParser.RULE_diff

        def enterRule(self, listener:ParseTreeListener):
            if hasattr( listener, "enterDiff" ):
                listener.enterDiff(self)

        def exitRule(self, listener:ParseTreeListener):
            if hasattr( listener, "exitDiff" ):
                listener.exitDiff(self)




    def diff(self):

        localctx = AutolevParser.DiffContext(self, self._ctx, self.state)
        self.enterRule(localctx, 10, self.RULE_diff)
        self._la = 0 # Token type
        try:
            self.enterOuterAlt(localctx, 1)
            self.state = 101
            self._errHandler.sync(self)
            _la = self._input.LA(1)
            while True:
                self.state = 100
                self.match(AutolevParser.T__10)
                self.state = 103
                self._errHandler.sync(self)
                _la = self._input.LA(1)
                if not (_la==11):
                    break

        except RecognitionException as re:
            localctx.exception = re
            self._errHandler.reportError(self, re)
            self._errHandler.recover(self, re)
        finally:
            self.exitRule()
        return localctx


    class FunctionCallContext(ParserRuleContext):
        __slots__ = 'parser'

        def __init__(self, parser, parent:ParserRuleContext=None, invokingState:int=-1):
            super().__init__(parent, invokingState)
            self.parser = parser

        def ID(self, i:int=None):
            if i is None:
                return self.getTokens(AutolevParser.ID)
            else:
                return self.getToken(AutolevParser.ID, i)

        def expr(self, i:int=None):
            if i is None:
                return self.getTypedRuleContexts(AutolevParser.ExprContext)
            else:
                return self.getTypedRuleContext(AutolevParser.ExprContext,i)


        def Mass(self):
            return self.getToken(AutolevParser.Mass, 0)

        def Inertia(self):
            return self.getToken(AutolevParser.Inertia, 0)

        def getRuleIndex(self):
            return AutolevParser.RULE_functionCall

        def enterRule(self, listener:ParseTreeListener):
            if hasattr( listener, "enterFunctionCall" ):
                listener.enterFunctionCall(self)

        def exitRule(self, listener:ParseTreeListener):
            if hasattr( listener, "exitFunctionCall" ):
                listener.exitFunctionCall(self)




    def functionCall(self):

        localctx = AutolevParser.FunctionCallContext(self, self._ctx, self.state)
        self.enterRule(localctx, 12, self.RULE_functionCall)
        self._la = 0 # Token type
        try:
            self.state = 131
            self._errHandler.sync(self)
            token = self._input.LA(1)
            if token in [48]:
                self.enterOuterAlt(localctx, 1)
                self.state = 105
                self.match(AutolevParser.ID)
                self.state = 106
                self.match(AutolevParser.T__11)
                self.state = 115
                self._errHandler.sync(self)
                _la = self._input.LA(1)
                if ((_la) & ~0x3f) == 0 and ((1 << _la) & 404620694540290) != 0:
                    self.state = 107
                    self.expr(0)
                    self.state = 112
                    self._errHandler.sync(self)
                    _la = self._input.LA(1)
                    while _la==10:
                        self.state = 108
                        self.match(AutolevParser.T__9)
                        self.state = 109
                        self.expr(0)
                        self.state = 114
                        self._errHandler.sync(self)
                        _la = self._input.LA(1)



                self.state = 117
                self.match(AutolevParser.T__12)
                pass
            elif token in [27, 28]:
                self.enterOuterAlt(localctx, 2)
                self.state = 118
                _la = self._input.LA(1)
                if not(_la==27 or _la==28):
                    self._errHandler.recoverInline(self)
                else:
                    self._errHandler.reportMatch(self)
                    self.consume()
                self.state = 119
                self.match(AutolevParser.T__11)
                self.state = 128
                self._errHandler.sync(self)
                _la = self._input.LA(1)
                if _la==48:
                    self.state = 120
                    self.match(AutolevParser.ID)
                    self.state = 125
                    self._errHandler.sync(self)
                    _la = self._input.LA(1)
                    while _la==10:
                        self.state = 121
                        self.match(AutolevParser.T__9)
                        self.state = 122
                        self.match(AutolevParser.ID)
                        self.state = 127
                        self._errHandler.sync(self)
                        _la = self._input.LA(1)



                self.state = 130
                self.match(AutolevParser.T__12)
                pass
            else:
                raise NoViableAltException(self)

        except RecognitionException as re:
            localctx.exception = re
            self._errHandler.reportError(self, re)
            self._errHandler.recover(self, re)
        finally:
            self.exitRule()
        return localctx


    class VarDeclContext(ParserRuleContext):
        __slots__ = 'parser'

        def __init__(self, parser, parent:ParserRuleContext=None, invokingState:int=-1):
            super().__init__(parent, invokingState)
            self.parser = parser

        def varType(self):
            return self.getTypedRuleContext(AutolevParser.VarTypeContext,0)


        def varDecl2(self, i:int=None):
            if i is None:
                return self.getTypedRuleContexts(AutolevParser.VarDecl2Context)
            else:
                return self.getTypedRuleContext(AutolevParser.VarDecl2Context,i)


        def getRuleIndex(self):
            return AutolevParser.RULE_varDecl

        def enterRule(self, listener:ParseTreeListener):
            if hasattr( listener, "enterVarDecl" ):
                listener.enterVarDecl(self)

        def exitRule(self, listener:ParseTreeListener):
            if hasattr( listener, "exitVarDecl" ):
                listener.exitVarDecl(self)




    def varDecl(self):

        localctx = AutolevParser.VarDeclContext(self, self._ctx, self.state)
        self.enterRule(localctx, 14, self.RULE_varDecl)
        self._la = 0 # Token type
        try:
            self.enterOuterAlt(localctx, 1)
            self.state = 133
            self.varType()
            self.state = 134
            self.varDecl2()
            self.state = 139
            self._errHandler.sync(self)
            _la = self._input.LA(1)
            while _la==10:
                self.state = 135
                self.match(AutolevParser.T__9)
                self.state = 136
                self.varDecl2()
                self.state = 141
                self._errHandler.sync(self)
                _la = self._input.LA(1)

        except RecognitionException as re:
            localctx.exception = re
            self._errHandler.reportError(self, re)
            self._errHandler.recover(self, re)
        finally:
            self.exitRule()
        return localctx


    class VarTypeContext(ParserRuleContext):
        __slots__ = 'parser'

        def __init__(self, parser, parent:ParserRuleContext=None, invokingState:int=-1):
            super().__init__(parent, invokingState)
            self.parser = parser

        def Newtonian(self):
            return self.getToken(AutolevParser.Newtonian, 0)

        def Frames(self):
            return self.getToken(AutolevParser.Frames, 0)

        def Bodies(self):
            return self.getToken(AutolevParser.Bodies, 0)

        def Particles(self):
            return self.getToken(AutolevParser.Particles, 0)

        def Points(self):
            return self.getToken(AutolevParser.Points, 0)

        def Constants(self):
            return self.getToken(AutolevParser.Constants, 0)

        def Specifieds(self):
            return self.getToken(AutolevParser.Specifieds, 0)

        def Imaginary(self):
            return self.getToken(AutolevParser.Imaginary, 0)

        def Variables(self):
            return self.getToken(AutolevParser.Variables, 0)

        def MotionVariables(self):
            return self.getToken(AutolevParser.MotionVariables, 0)

        def getRuleIndex(self):
            return AutolevParser.RULE_varType

        def enterRule(self, listener:ParseTreeListener):
            if hasattr( listener, "enterVarType" ):
                listener.enterVarType(self)

        def exitRule(self, listener:ParseTreeListener):
            if hasattr( listener, "exitVarType" ):
                listener.exitVarType(self)




    def varType(self):

        localctx = AutolevParser.VarTypeContext(self, self._ctx, self.state)
        self.enterRule(localctx, 16, self.RULE_varType)
        self._la = 0 # Token type
        try:
            self.state = 164
            self._errHandler.sync(self)
            token = self._input.LA(1)
            if token in [34]:
                self.enterOuterAlt(localctx, 1)
                self.state = 142
                self.match(AutolevParser.Newtonian)
                pass
            elif token in [35]:
                self.enterOuterAlt(localctx, 2)
                self.state = 143
                self.match(AutolevParser.Frames)
                pass
            elif token in [36]:
                self.enterOuterAlt(localctx, 3)
                self.state = 144
                self.match(AutolevParser.Bodies)
                pass
            elif token in [37]:
                self.enterOuterAlt(localctx, 4)
                self.state = 145
                self.match(AutolevParser.Particles)
                pass
            elif token in [38]:
                self.enterOuterAlt(localctx, 5)
                self.state = 146
                self.match(AutolevParser.Points)
                pass
            elif token in [39]:
                self.enterOuterAlt(localctx, 6)
                self.state = 147
                self.match(AutolevParser.Constants)
                pass
            elif token in [40]:
                self.enterOuterAlt(localctx, 7)
                self.state = 148
                self.match(AutolevParser.Specifieds)
                pass
            elif token in [41]:
                self.enterOuterAlt(localctx, 8)
                self.state = 149
                self.match(AutolevParser.Imaginary)
                pass
            elif token in [42]:
                self.enterOuterAlt(localctx, 9)
                self.state = 150
                self.match(AutolevParser.Variables)
                self.state = 154
                self._errHandler.sync(self)
                _la = self._input.LA(1)
                while _la==11:
                    self.state = 151
                    self.match(AutolevParser.T__10)
                    self.state = 156
                    self._errHandler.sync(self)
                    _la = self._input.LA(1)

                pass
            elif token in [43]:
                self.enterOuterAlt(localctx, 10)
                self.state = 157
                self.match(AutolevParser.MotionVariables)
                self.state = 161
                self._errHandler.sync(self)
                _la = self._input.LA(1)
                while _la==11:
                    self.state = 158
                    self.match(AutolevParser.T__10)
                    self.state = 163
                    self._errHandler.sync(self)
                    _la = self._input.LA(1)

                pass
            else:
                raise NoViableAltException(self)

        except RecognitionException as re:
            localctx.exception = re
            self._errHandler.reportError(self, re)
            self._errHandler.recover(self, re)
        finally:
            self.exitRule()
        return localctx


    class VarDecl2Context(ParserRuleContext):
        __slots__ = 'parser'

        def __init__(self, parser, parent:ParserRuleContext=None, invokingState:int=-1):
            super().__init__(parent, invokingState)
            self.parser = parser

        def ID(self):
            return self.getToken(AutolevParser.ID, 0)

        def INT(self, i:int=None):
            if i is None:
                return self.getTokens(AutolevParser.INT)
            else:
                return self.getToken(AutolevParser.INT, i)

        def expr(self):
            return self.getTypedRuleContext(AutolevParser.ExprContext,0)


        def getRuleIndex(self):
            return AutolevParser.RULE_varDecl2

        def enterRule(self, listener:ParseTreeListener):
            if hasattr( listener, "enterVarDecl2" ):
                listener.enterVarDecl2(self)

        def exitRule(self, listener:ParseTreeListener):
            if hasattr( listener, "exitVarDecl2" ):
                listener.exitVarDecl2(self)




    def varDecl2(self):

        localctx = AutolevParser.VarDecl2Context(self, self._ctx, self.state)
        self.enterRule(localctx, 18, self.RULE_varDecl2)
        self._la = 0 # Token type
        try:
            self.enterOuterAlt(localctx, 1)
            self.state = 166
            self.match(AutolevParser.ID)
            self.state = 172
            self._errHandler.sync(self)
            la_ = self._interp.adaptivePredict(self._input,15,self._ctx)
            if la_ == 1:
                self.state = 167
                self.match(AutolevParser.T__13)
                self.state = 168
                self.match(AutolevParser.INT)
                self.state = 169
                self.match(AutolevParser.T__9)
                self.state = 170
                self.match(AutolevParser.INT)
                self.state = 171
                self.match(AutolevParser.T__14)


            self.state = 188
            self._errHandler.sync(self)
            la_ = self._interp.adaptivePredict(self._input,17,self._ctx)
            if la_ == 1:
                self.state = 174
                self.match(AutolevParser.T__13)
                self.state = 175
                self.match(AutolevParser.INT)
                self.state = 176
                self.match(AutolevParser.T__15)
                self.state = 177
                self.match(AutolevParser.INT)
                self.state = 184
                self._errHandler.sync(self)
                _la = self._input.LA(1)
                while _la==10:
                    self.state = 178
                    self.match(AutolevParser.T__9)
                    self.state = 179
                    self.match(AutolevParser.INT)
                    self.state = 180
                    self.match(AutolevParser.T__15)
                    self.state = 181
                    self.match(AutolevParser.INT)
                    self.state = 186
                    self._errHandler.sync(self)
                    _la = self._input.LA(1)

                self.state = 187
                self.match(AutolevParser.T__14)


            self.state = 193
            self._errHandler.sync(self)
            _la = self._input.LA(1)
            if _la==14:
                self.state = 190
                self.match(AutolevParser.T__13)
                self.state = 191
                self.match(AutolevParser.INT)
                self.state = 192
                self.match(AutolevParser.T__14)


            self.state = 196
            self._errHandler.sync(self)
            _la = self._input.LA(1)
            if _la==17 or _la==18:
                self.state = 195
                _la = self._input.LA(1)
                if not(_la==17 or _la==18):
                    self._errHandler.recoverInline(self)
                else:
                    self._errHandler.reportMatch(self)
                    self.consume()


            self.state = 201
            self._errHandler.sync(self)
            _la = self._input.LA(1)
            while _la==11:
                self.state = 198
                self.match(AutolevParser.T__10)
                self.state = 203
                self._errHandler.sync(self)
                _la = self._input.LA(1)

            self.state = 206
            self._errHandler.sync(self)
            _la = self._input.LA(1)
            if _la==3:
                self.state = 204
                self.match(AutolevParser.T__2)
                self.state = 205
                self.expr(0)


        except RecognitionException as re:
            localctx.exception = re
            self._errHandler.reportError(self, re)
            self._errHandler.recover(self, re)
        finally:
            self.exitRule()
        return localctx


    class RangesContext(ParserRuleContext):
        __slots__ = 'parser'

        def __init__(self, parser, parent:ParserRuleContext=None, invokingState:int=-1):
            super().__init__(parent, invokingState)
            self.parser = parser

        def INT(self, i:int=None):
            if i is None:
                return self.getTokens(AutolevParser.INT)
            else:
                return self.getToken(AutolevParser.INT, i)

        def getRuleIndex(self):
            return AutolevParser.RULE_ranges

        def enterRule(self, listener:ParseTreeListener):
            if hasattr( listener, "enterRanges" ):
                listener.enterRanges(self)

        def exitRule(self, listener:ParseTreeListener):
            if hasattr( listener, "exitRanges" ):
                listener.exitRanges(self)




    def ranges(self):

        localctx = AutolevParser.RangesContext(self, self._ctx, self.state)
        self.enterRule(localctx, 20, self.RULE_ranges)
        self._la = 0 # Token type
        try:
            self.enterOuterAlt(localctx, 1)
            self.state = 208
            self.match(AutolevParser.T__13)
            self.state = 209
            self.match(AutolevParser.INT)
            self.state = 210
            self.match(AutolevParser.T__15)
            self.state = 211
            self.match(AutolevParser.INT)
            self.state = 218
            self._errHandler.sync(self)
            _la = self._input.LA(1)
            while _la==10:
                self.state = 212
                self.match(AutolevParser.T__9)
                self.state = 213
                self.match(AutolevParser.INT)
                self.state = 214
                self.match(AutolevParser.T__15)
                self.state = 215
                self.match(AutolevParser.INT)
                self.state = 220
                self._errHandler.sync(self)
                _la = self._input.LA(1)

            self.state = 221
            self.match(AutolevParser.T__14)
        except RecognitionException as re:
            localctx.exception = re
            self._errHandler.reportError(self, re)
            self._errHandler.recover(self, re)
        finally:
            self.exitRule()
        return localctx


    class MassDeclContext(ParserRuleContext):
        __slots__ = 'parser'

        def __init__(self, parser, parent:ParserRuleContext=None, invokingState:int=-1):
            super().__init__(parent, invokingState)
            self.parser = parser

        def Mass(self):
            return self.getToken(AutolevParser.Mass, 0)

        def massDecl2(self, i:int=None):
            if i is None:
                return self.getTypedRuleContexts(AutolevParser.MassDecl2Context)
            else:
                return self.getTypedRuleContext(AutolevParser.MassDecl2Context,i)


        def getRuleIndex(self):
            return AutolevParser.RULE_massDecl

        def enterRule(self, listener:ParseTreeListener):
            if hasattr( listener, "enterMassDecl" ):
                listener.enterMassDecl(self)

        def exitRule(self, listener:ParseTreeListener):
            if hasattr( listener, "exitMassDecl" ):
                listener.exitMassDecl(self)




    def massDecl(self):

        localctx = AutolevParser.MassDeclContext(self, self._ctx, self.state)
        self.enterRule(localctx, 22, self.RULE_massDecl)
        self._la = 0 # Token type
        try:
            self.enterOuterAlt(localctx, 1)
            self.state = 223
            self.match(AutolevParser.Mass)
            self.state = 224
            self.massDecl2()
            self.state = 229
            self._errHandler.sync(self)
            _la = self._input.LA(1)
            while _la==10:
                self.state = 225
                self.match(AutolevParser.T__9)
                self.state = 226
                self.massDecl2()
                self.state = 231
                self._errHandler.sync(self)
                _la = self._input.LA(1)

        except RecognitionException as re:
            localctx.exception = re
            self._errHandler.reportError(self, re)
            self._errHandler.recover(self, re)
        finally:
            self.exitRule()
        return localctx


    class MassDecl2Context(ParserRuleContext):
        __slots__ = 'parser'

        def __init__(self, parser, parent:ParserRuleContext=None, invokingState:int=-1):
            super().__init__(parent, invokingState)
            self.parser = parser

        def ID(self):
            return self.getToken(AutolevParser.ID, 0)

        def expr(self):
            return self.getTypedRuleContext(AutolevParser.ExprContext,0)


        def getRuleIndex(self):
            return AutolevParser.RULE_massDecl2

        def enterRule(self, listener:ParseTreeListener):
            if hasattr( listener, "enterMassDecl2" ):
                listener.enterMassDecl2(self)

        def exitRule(self, listener:ParseTreeListener):
            if hasattr( listener, "exitMassDecl2" ):
                listener.exitMassDecl2(self)




    def massDecl2(self):

        localctx = AutolevParser.MassDecl2Context(self, self._ctx, self.state)
        self.enterRule(localctx, 24, self.RULE_massDecl2)
        try:
            self.enterOuterAlt(localctx, 1)
            self.state = 232
            self.match(AutolevParser.ID)
            self.state = 233
            self.match(AutolevParser.T__2)
            self.state = 234
            self.expr(0)
        except RecognitionException as re:
            localctx.exception = re
            self._errHandler.reportError(self, re)
            self._errHandler.recover(self, re)
        finally:
            self.exitRule()
        return localctx


    class InertiaDeclContext(ParserRuleContext):
        __slots__ = 'parser'

        def __init__(self, parser, parent:ParserRuleContext=None, invokingState:int=-1):
            super().__init__(parent, invokingState)
            self.parser = parser

        def Inertia(self):
            return self.getToken(AutolevParser.Inertia, 0)

        def ID(self, i:int=None):
            if i is None:
                return self.getTokens(AutolevParser.ID)
            else:
                return self.getToken(AutolevParser.ID, i)

        def expr(self, i:int=None):
            if i is None:
                return self.getTypedRuleContexts(AutolevParser.ExprContext)
            else:
                return self.getTypedRuleContext(AutolevParser.ExprContext,i)


        def getRuleIndex(self):
            return AutolevParser.RULE_inertiaDecl

        def enterRule(self, listener:ParseTreeListener):
            if hasattr( listener, "enterInertiaDecl" ):
                listener.enterInertiaDecl(self)

        def exitRule(self, listener:ParseTreeListener):
            if hasattr( listener, "exitInertiaDecl" ):
                listener.exitInertiaDecl(self)




    def inertiaDecl(self):

        localctx = AutolevParser.InertiaDeclContext(self, self._ctx, self.state)
        self.enterRule(localctx, 26, self.RULE_inertiaDecl)
        self._la = 0 # Token type
        try:
            self.enterOuterAlt(localctx, 1)
            self.state = 236
            self.match(AutolevParser.Inertia)
            self.state = 237
            self.match(AutolevParser.ID)
            self.state = 241
            self._errHandler.sync(self)
            _la = self._input.LA(1)
            if _la==12:
                self.state = 238
                self.match(AutolevParser.T__11)
                self.state = 239
                self.match(AutolevParser.ID)
                self.state = 240
                self.match(AutolevParser.T__12)


            self.state = 245
            self._errHandler.sync(self)
            _la = self._input.LA(1)
            while True:
                self.state = 243
                self.match(AutolevParser.T__9)
                self.state = 244
                self.expr(0)
                self.state = 247
                self._errHandler.sync(self)
                _la = self._input.LA(1)
                if not (_la==10):
                    break

        except RecognitionException as re:
            localctx.exception = re
            self._errHandler.reportError(self, re)
            self._errHandler.recover(self, re)
        finally:
            self.exitRule()
        return localctx


    class MatrixContext(ParserRuleContext):
        __slots__ = 'parser'

        def __init__(self, parser, parent:ParserRuleContext=None, invokingState:int=-1):
            super().__init__(parent, invokingState)
            self.parser = parser

        def expr(self, i:int=None):
            if i is None:
                return self.getTypedRuleContexts(AutolevParser.ExprContext)
            else:
                return self.getTypedRuleContext(AutolevParser.ExprContext,i)


        def getRuleIndex(self):
            return AutolevParser.RULE_matrix

        def enterRule(self, listener:ParseTreeListener):
            if hasattr( listener, "enterMatrix" ):
                listener.enterMatrix(self)

        def exitRule(self, listener:ParseTreeListener):
            if hasattr( listener, "exitMatrix" ):
                listener.exitMatrix(self)




    def matrix(self):

        localctx = AutolevParser.MatrixContext(self, self._ctx, self.state)
        self.enterRule(localctx, 28, self.RULE_matrix)
        self._la = 0 # Token type
        try:
            self.enterOuterAlt(localctx, 1)
            self.state = 249
            self.match(AutolevParser.T__0)
            self.state = 250
            self.expr(0)
            self.state = 255
            self._errHandler.sync(self)
            _la = self._input.LA(1)
            while _la==10 or _la==19:
                self.state = 251
                _la = self._input.LA(1)
                if not(_la==10 or _la==19):
                    self._errHandler.recoverInline(self)
                else:
                    self._errHandler.reportMatch(self)
                    self.consume()
                self.state = 252
                self.expr(0)
                self.state = 257
                self._errHandler.sync(self)
                _la = self._input.LA(1)

            self.state = 258
            self.match(AutolevParser.T__1)
        except RecognitionException as re:
            localctx.exception = re
            self._errHandler.reportError(self, re)
            self._errHandler.recover(self, re)
        finally:
            self.exitRule()
        return localctx


    class MatrixInOutputContext(ParserRuleContext):
        __slots__ = 'parser'

        def __init__(self, parser, parent:ParserRuleContext=None, invokingState:int=-1):
            super().__init__(parent, invokingState)
            self.parser = parser

        def ID(self, i:int=None):
            if i is None:
                return self.getTokens(AutolevParser.ID)
            else:
                return self.getToken(AutolevParser.ID, i)

        def FLOAT(self):
            return self.getToken(AutolevParser.FLOAT, 0)

        def INT(self):
            return self.getToken(AutolevParser.INT, 0)

        def getRuleIndex(self):
            return AutolevParser.RULE_matrixInOutput

        def enterRule(self, listener:ParseTreeListener):
            if hasattr( listener, "enterMatrixInOutput" ):
                listener.enterMatrixInOutput(self)

        def exitRule(self, listener:ParseTreeListener):
            if hasattr( listener, "exitMatrixInOutput" ):
                listener.exitMatrixInOutput(self)




    def matrixInOutput(self):

        localctx = AutolevParser.MatrixInOutputContext(self, self._ctx, self.state)
        self.enterRule(localctx, 30, self.RULE_matrixInOutput)
        self._la = 0 # Token type
        try:
            self.state = 268
            self._errHandler.sync(self)
            token = self._input.LA(1)
            if token in [48]:
                self.enterOuterAlt(localctx, 1)
                self.state = 260
                self.match(AutolevParser.ID)

                self.state = 261
                self.match(AutolevParser.ID)
                self.state = 262
                self.match(AutolevParser.T__2)
                self.state = 264
                self._errHandler.sync(self)
                _la = self._input.LA(1)
                if _la==44 or _la==45:
                    self.state = 263
                    _la = self._input.LA(1)
                    if not(_la==44 or _la==45):
                        self._errHandler.recoverInline(self)
                    else:
                        self._errHandler.reportMatch(self)
                        self.consume()


                pass
            elif token in [45]:
                self.enterOuterAlt(localctx, 2)
                self.state = 266
                self.match(AutolevParser.FLOAT)
                pass
            elif token in [44]:
                self.enterOuterAlt(localctx, 3)
                self.state = 267
                self.match(AutolevParser.INT)
                pass
            else:
                raise NoViableAltException(self)

        except RecognitionException as re:
            localctx.exception = re
            self._errHandler.reportError(self, re)
            self._errHandler.recover(self, re)
        finally:
            self.exitRule()
        return localctx


    class CodeCommandsContext(ParserRuleContext):
        __slots__ = 'parser'

        def __init__(self, parser, parent:ParserRuleContext=None, invokingState:int=-1):
            super().__init__(parent, invokingState)
            self.parser = parser

        def units(self):
            return self.getTypedRuleContext(AutolevParser.UnitsContext,0)


        def inputs(self):
            return self.getTypedRuleContext(AutolevParser.InputsContext,0)


        def outputs(self):
            return self.getTypedRuleContext(AutolevParser.OutputsContext,0)


        def codegen(self):
            return self.getTypedRuleContext(AutolevParser.CodegenContext,0)


        def commands(self):
            return self.getTypedRuleContext(AutolevParser.CommandsContext,0)


        def getRuleIndex(self):
            return AutolevParser.RULE_codeCommands

        def enterRule(self, listener:ParseTreeListener):
            if hasattr( listener, "enterCodeCommands" ):
                listener.enterCodeCommands(self)

        def exitRule(self, listener:ParseTreeListener):
            if hasattr( listener, "exitCodeCommands" ):
                listener.exitCodeCommands(self)




    def codeCommands(self):

        localctx = AutolevParser.CodeCommandsContext(self, self._ctx, self.state)
        self.enterRule(localctx, 32, self.RULE_codeCommands)
        try:
            self.state = 275
            self._errHandler.sync(self)
            token = self._input.LA(1)
            if token in [32]:
                self.enterOuterAlt(localctx, 1)
                self.state = 270
                self.units()
                pass
            elif token in [29]:
                self.enterOuterAlt(localctx, 2)
                self.state = 271
                self.inputs()
                pass
            elif token in [30]:
                self.enterOuterAlt(localctx, 3)
                self.state = 272
                self.outputs()
                pass
            elif token in [48]:
                self.enterOuterAlt(localctx, 4)
                self.state = 273
                self.codegen()
                pass
            elif token in [31, 33]:
                self.enterOuterAlt(localctx, 5)
                self.state = 274
                self.commands()
                pass
            else:
                raise NoViableAltException(self)

        except RecognitionException as re:
            localctx.exception = re
            self._errHandler.reportError(self, re)
            self._errHandler.recover(self, re)
        finally:
            self.exitRule()
        return localctx


    class SettingsContext(ParserRuleContext):
        __slots__ = 'parser'

        def __init__(self, parser, parent:ParserRuleContext=None, invokingState:int=-1):
            super().__init__(parent, invokingState)
            self.parser = parser

        def ID(self, i:int=None):
            if i is None:
                return self.getTokens(AutolevParser.ID)
            else:
                return self.getToken(AutolevParser.ID, i)

        def EXP(self):
            return self.getToken(AutolevParser.EXP, 0)

        def FLOAT(self):
            return self.getToken(AutolevParser.FLOAT, 0)

        def INT(self):
            return self.getToken(AutolevParser.INT, 0)

        def getRuleIndex(self):
            return AutolevParser.RULE_settings

        def enterRule(self, listener:ParseTreeListener):
            if hasattr( listener, "enterSettings" ):
                listener.enterSettings(self)

        def exitRule(self, listener:ParseTreeListener):
            if hasattr( listener, "exitSettings" ):
                listener.exitSettings(self)




    def settings(self):

        localctx = AutolevParser.SettingsContext(self, self._ctx, self.state)
        self.enterRule(localctx, 34, self.RULE_settings)
        self._la = 0 # Token type
        try:
            self.enterOuterAlt(localctx, 1)
            self.state = 277
            self.match(AutolevParser.ID)
            self.state = 279
            self._errHandler.sync(self)
            la_ = self._interp.adaptivePredict(self._input,30,self._ctx)
            if la_ == 1:
                self.state = 278
                _la = self._input.LA(1)
                if not(((_la) & ~0x3f) == 0 and ((1 << _la) & 404620279021568) != 0):
                    self._errHandler.recoverInline(self)
                else:
                    self._errHandler.reportMatch(self)
                    self.consume()


        except RecognitionException as re:
            localctx.exception = re
            self._errHandler.reportError(self, re)
            self._errHandler.recover(self, re)
        finally:
            self.exitRule()
        return localctx


    class UnitsContext(ParserRuleContext):
        __slots__ = 'parser'

        def __init__(self, parser, parent:ParserRuleContext=None, invokingState:int=-1):
            super().__init__(parent, invokingState)
            self.parser = parser

        def UnitSystem(self):
            return self.getToken(AutolevParser.UnitSystem, 0)

        def ID(self, i:int=None):
            if i is None:
                return self.getTokens(AutolevParser.ID)
            else:
                return self.getToken(AutolevParser.ID, i)

        def getRuleIndex(self):
            return AutolevParser.RULE_units

        def enterRule(self, listener:ParseTreeListener):
            if hasattr( listener, "enterUnits" ):
                listener.enterUnits(self)

        def exitRule(self, listener:ParseTreeListener):
            if hasattr( listener, "exitUnits" ):
                listener.exitUnits(self)




    def units(self):

        localctx = AutolevParser.UnitsContext(self, self._ctx, self.state)
        self.enterRule(localctx, 36, self.RULE_units)
        self._la = 0 # Token type
        try:
            self.enterOuterAlt(localctx, 1)
            self.state = 281
            self.match(AutolevParser.UnitSystem)
            self.state = 282
            self.match(AutolevParser.ID)
            self.state = 287
            self._errHandler.sync(self)
            _la = self._input.LA(1)
            while _la==10:
                self.state = 283
                self.match(AutolevParser.T__9)
                self.state = 284
                self.match(AutolevParser.ID)
                self.state = 289
                self._errHandler.sync(self)
                _la = self._input.LA(1)

        except RecognitionException as re:
            localctx.exception = re
            self._errHandler.reportError(self, re)
            self._errHandler.recover(self, re)
        finally:
            self.exitRule()
        return localctx


    class InputsContext(ParserRuleContext):
        __slots__ = 'parser'

        def __init__(self, parser, parent:ParserRuleContext=None, invokingState:int=-1):
            super().__init__(parent, invokingState)
            self.parser = parser

        def Input(self):
            return self.getToken(AutolevParser.Input, 0)

        def inputs2(self, i:int=None):
            if i is None:
                return self.getTypedRuleContexts(AutolevParser.Inputs2Context)
            else:
                return self.getTypedRuleContext(AutolevParser.Inputs2Context,i)


        def getRuleIndex(self):
            return AutolevParser.RULE_inputs

        def enterRule(self, listener:ParseTreeListener):
            if hasattr( listener, "enterInputs" ):
                listener.enterInputs(self)

        def exitRule(self, listener:ParseTreeListener):
            if hasattr( listener, "exitInputs" ):
                listener.exitInputs(self)




    def inputs(self):

        localctx = AutolevParser.InputsContext(self, self._ctx, self.state)
        self.enterRule(localctx, 38, self.RULE_inputs)
        self._la = 0 # Token type
        try:
            self.enterOuterAlt(localctx, 1)
            self.state = 290
            self.match(AutolevParser.Input)
            self.state = 291
            self.inputs2()
            self.state = 296
            self._errHandler.sync(self)
            _la = self._input.LA(1)
            while _la==10:
                self.state = 292
                self.match(AutolevParser.T__9)
                self.state = 293
                self.inputs2()
                self.state = 298
                self._errHandler.sync(self)
                _la = self._input.LA(1)

        except RecognitionException as re:
            localctx.exception = re
            self._errHandler.reportError(self, re)
            self._errHandler.recover(self, re)
        finally:
            self.exitRule()
        return localctx


    class Id_diffContext(ParserRuleContext):
        __slots__ = 'parser'

        def __init__(self, parser, parent:ParserRuleContext=None, invokingState:int=-1):
            super().__init__(parent, invokingState)
            self.parser = parser

        def ID(self):
            return self.getToken(AutolevParser.ID, 0)

        def diff(self):
            return self.getTypedRuleContext(AutolevParser.DiffContext,0)


        def getRuleIndex(self):
            return AutolevParser.RULE_id_diff

        def enterRule(self, listener:ParseTreeListener):
            if hasattr( listener, "enterId_diff" ):
                listener.enterId_diff(self)

        def exitRule(self, listener:ParseTreeListener):
            if hasattr( listener, "exitId_diff" ):
                listener.exitId_diff(self)




    def id_diff(self):

        localctx = AutolevParser.Id_diffContext(self, self._ctx, self.state)
        self.enterRule(localctx, 40, self.RULE_id_diff)
        self._la = 0 # Token type
        try:
            self.enterOuterAlt(localctx, 1)
            self.state = 299
            self.match(AutolevParser.ID)
            self.state = 301
            self._errHandler.sync(self)
            _la = self._input.LA(1)
            if _la==11:
                self.state = 300
                self.diff()


        except RecognitionException as re:
            localctx.exception = re
            self._errHandler.reportError(self, re)
            self._errHandler.recover(self, re)
        finally:
            self.exitRule()
        return localctx


    class Inputs2Context(ParserRuleContext):
        __slots__ = 'parser'

        def __init__(self, parser, parent:ParserRuleContext=None, invokingState:int=-1):
            super().__init__(parent, invokingState)
            self.parser = parser

        def id_diff(self):
            return self.getTypedRuleContext(AutolevParser.Id_diffContext,0)


        def expr(self, i:int=None):
            if i is None:
                return self.getTypedRuleContexts(AutolevParser.ExprContext)
            else:
                return self.getTypedRuleContext(AutolevParser.ExprContext,i)


        def getRuleIndex(self):
            return AutolevParser.RULE_inputs2

        def enterRule(self, listener:ParseTreeListener):
            if hasattr( listener, "enterInputs2" ):
                listener.enterInputs2(self)

        def exitRule(self, listener:ParseTreeListener):
            if hasattr( listener, "exitInputs2" ):
                listener.exitInputs2(self)




    def inputs2(self):

        localctx = AutolevParser.Inputs2Context(self, self._ctx, self.state)
        self.enterRule(localctx, 42, self.RULE_inputs2)
        try:
            self.enterOuterAlt(localctx, 1)
            self.state = 303
            self.id_diff()
            self.state = 304
            self.match(AutolevParser.T__2)
            self.state = 305
            self.expr(0)
            self.state = 307
            self._errHandler.sync(self)
            la_ = self._interp.adaptivePredict(self._input,34,self._ctx)
            if la_ == 1:
                self.state = 306
                self.expr(0)


        except RecognitionException as re:
            localctx.exception = re
            self._errHandler.reportError(self, re)
            self._errHandler.recover(self, re)
        finally:
            self.exitRule()
        return localctx


    class OutputsContext(ParserRuleContext):
        __slots__ = 'parser'

        def __init__(self, parser, parent:ParserRuleContext=None, invokingState:int=-1):
            super().__init__(parent, invokingState)
            self.parser = parser

        def Output(self):
            return self.getToken(AutolevParser.Output, 0)

        def outputs2(self, i:int=None):
            if i is None:
                return self.getTypedRuleContexts(AutolevParser.Outputs2Context)
            else:
                return self.getTypedRuleContext(AutolevParser.Outputs2Context,i)


        def getRuleIndex(self):
            return AutolevParser.RULE_outputs

        def enterRule(self, listener:ParseTreeListener):
            if hasattr( listener, "enterOutputs" ):
                listener.enterOutputs(self)

        def exitRule(self, listener:ParseTreeListener):
            if hasattr( listener, "exitOutputs" ):
                listener.exitOutputs(self)




    def outputs(self):

        localctx = AutolevParser.OutputsContext(self, self._ctx, self.state)
        self.enterRule(localctx, 44, self.RULE_outputs)
        self._la = 0 # Token type
        try:
            self.enterOuterAlt(localctx, 1)
            self.state = 309
            self.match(AutolevParser.Output)
            self.state = 310
            self.outputs2()
            self.state = 315
            self._errHandler.sync(self)
            _la = self._input.LA(1)
            while _la==10:
                self.state = 311
                self.match(AutolevParser.T__9)
                self.state = 312
                self.outputs2()
                self.state = 317
                self._errHandler.sync(self)
                _la = self._input.LA(1)

        except RecognitionException as re:
            localctx.exception = re
            self._errHandler.reportError(self, re)
            self._errHandler.recover(self, re)
        finally:
            self.exitRule()
        return localctx


    class Outputs2Context(ParserRuleContext):
        __slots__ = 'parser'

        def __init__(self, parser, parent:ParserRuleContext=None, invokingState:int=-1):
            super().__init__(parent, invokingState)
            self.parser = parser

        def expr(self, i:int=None):
            if i is None:
                return self.getTypedRuleContexts(AutolevParser.ExprContext)
            else:
                return self.getTypedRuleContext(AutolevParser.ExprContext,i)


        def getRuleIndex(self):
            return AutolevParser.RULE_outputs2

        def enterRule(self, listener:ParseTreeListener):
            if hasattr( listener, "enterOutputs2" ):
                listener.enterOutputs2(self)

        def exitRule(self, listener:ParseTreeListener):
            if hasattr( listener, "exitOutputs2" ):
                listener.exitOutputs2(self)




    def outputs2(self):

        localctx = AutolevParser.Outputs2Context(self, self._ctx, self.state)
        self.enterRule(localctx, 46, self.RULE_outputs2)
        try:
            self.enterOuterAlt(localctx, 1)
            self.state = 318
            self.expr(0)
            self.state = 320
            self._errHandler.sync(self)
            la_ = self._interp.adaptivePredict(self._input,36,self._ctx)
            if la_ == 1:
                self.state = 319
                self.expr(0)


        except RecognitionException as re:
            localctx.exception = re
            self._errHandler.reportError(self, re)
            self._errHandler.recover(self, re)
        finally:
            self.exitRule()
        return localctx


    class CodegenContext(ParserRuleContext):
        __slots__ = 'parser'

        def __init__(self, parser, parent:ParserRuleContext=None, invokingState:int=-1):
            super().__init__(parent, invokingState)
            self.parser = parser

        def ID(self, i:int=None):
            if i is None:
                return self.getTokens(AutolevParser.ID)
            else:
                return self.getToken(AutolevParser.ID, i)

        def functionCall(self):
            return self.getTypedRuleContext(AutolevParser.FunctionCallContext,0)


        def matrixInOutput(self, i:int=None):
            if i is None:
                return self.getTypedRuleContexts(AutolevParser.MatrixInOutputContext)
            else:
                return self.getTypedRuleContext(AutolevParser.MatrixInOutputContext,i)


        def getRuleIndex(self):
            return AutolevParser.RULE_codegen

        def enterRule(self, listener:ParseTreeListener):
            if hasattr( listener, "enterCodegen" ):
                listener.enterCodegen(self)

        def exitRule(self, listener:ParseTreeListener):
            if hasattr( listener, "exitCodegen" ):
                listener.exitCodegen(self)




    def codegen(self):

        localctx = AutolevParser.CodegenContext(self, self._ctx, self.state)
        self.enterRule(localctx, 48, self.RULE_codegen)
        self._la = 0 # Token type
        try:
            self.enterOuterAlt(localctx, 1)
            self.state = 322
            self.match(AutolevParser.ID)
            self.state = 323
            self.functionCall()
            self.state = 335
            self._errHandler.sync(self)
            _la = self._input.LA(1)
            if _la==1:
                self.state = 324
                self.match(AutolevParser.T__0)
                self.state = 325
                self.matrixInOutput()
                self.state = 330
                self._errHandler.sync(self)
                _la = self._input.LA(1)
                while _la==10:
                    self.state = 326
                    self.match(AutolevParser.T__9)
                    self.state = 327
                    self.matrixInOutput()
                    self.state = 332
                    self._errHandler.sync(self)
                    _la = self._input.LA(1)

                self.state = 333
                self.match(AutolevParser.T__1)


            self.state = 337
            self.match(AutolevParser.ID)
            self.state = 338
            self.match(AutolevParser.T__19)
            self.state = 339
            self.match(AutolevParser.ID)
        except RecognitionException as re:
            localctx.exception = re
            self._errHandler.reportError(self, re)
            self._errHandler.recover(self, re)
        finally:
            self.exitRule()
        return localctx


    class CommandsContext(ParserRuleContext):
        __slots__ = 'parser'

        def __init__(self, parser, parent:ParserRuleContext=None, invokingState:int=-1):
            super().__init__(parent, invokingState)
            self.parser = parser

        def Save(self):
            return self.getToken(AutolevParser.Save, 0)

        def ID(self, i:int=None):
            if i is None:
                return self.getTokens(AutolevParser.ID)
            else:
                return self.getToken(AutolevParser.ID, i)

        def Encode(self):
            return self.getToken(AutolevParser.Encode, 0)

        def getRuleIndex(self):
            return AutolevParser.RULE_commands

        def enterRule(self, listener:ParseTreeListener):
            if hasattr( listener, "enterCommands" ):
                listener.enterCommands(self)

        def exitRule(self, listener:ParseTreeListener):
            if hasattr( listener, "exitCommands" ):
                listener.exitCommands(self)




    def commands(self):

        localctx = AutolevParser.CommandsContext(self, self._ctx, self.state)
        self.enterRule(localctx, 50, self.RULE_commands)
        self._la = 0 # Token type
        try:
            self.state = 354
            self._errHandler.sync(self)
            token = self._input.LA(1)
            if token in [31]:
                self.enterOuterAlt(localctx, 1)
                self.state = 341
                self.match(AutolevParser.Save)
                self.state = 342
                self.match(AutolevParser.ID)
                self.state = 343
                self.match(AutolevParser.T__19)
                self.state = 344
                self.match(AutolevParser.ID)
                pass
            elif token in [33]:
                self.enterOuterAlt(localctx, 2)
                self.state = 345
                self.match(AutolevParser.Encode)
                self.state = 346
                self.match(AutolevParser.ID)
                self.state = 351
                self._errHandler.sync(self)
                _la = self._input.LA(1)
                while _la==10:
                    self.state = 347
                    self.match(AutolevParser.T__9)
                    self.state = 348
                    self.match(AutolevParser.ID)
                    self.state = 353
                    self._errHandler.sync(self)
                    _la = self._input.LA(1)

                pass
            else:
                raise NoViableAltException(self)

        except RecognitionException as re:
            localctx.exception = re
            self._errHandler.reportError(self, re)
            self._errHandler.recover(self, re)
        finally:
            self.exitRule()
        return localctx


    class VecContext(ParserRuleContext):
        __slots__ = 'parser'

        def __init__(self, parser, parent:ParserRuleContext=None, invokingState:int=-1):
            super().__init__(parent, invokingState)
            self.parser = parser

        def ID(self):
            return self.getToken(AutolevParser.ID, 0)

        def getRuleIndex(self):
            return AutolevParser.RULE_vec

        def enterRule(self, listener:ParseTreeListener):
            if hasattr( listener, "enterVec" ):
                listener.enterVec(self)

        def exitRule(self, listener:ParseTreeListener):
            if hasattr( listener, "exitVec" ):
                listener.exitVec(self)




    def vec(self):

        localctx = AutolevParser.VecContext(self, self._ctx, self.state)
        self.enterRule(localctx, 52, self.RULE_vec)
        try:
            self.state = 364
            self._errHandler.sync(self)
            token = self._input.LA(1)
            if token in [48]:
                self.enterOuterAlt(localctx, 1)
                self.state = 356
                self.match(AutolevParser.ID)
                self.state = 358
                self._errHandler.sync(self)
                _alt = 1
                while _alt!=2 and _alt!=ATN.INVALID_ALT_NUMBER:
                    if _alt == 1:
                        self.state = 357
                        self.match(AutolevParser.T__20)

                    else:
                        raise NoViableAltException(self)
                    self.state = 360
                    self._errHandler.sync(self)
                    _alt = self._interp.adaptivePredict(self._input,41,self._ctx)

                pass
            elif token in [22]:
                self.enterOuterAlt(localctx, 2)
                self.state = 362
                self.match(AutolevParser.T__21)
                pass
            elif token in [23]:
                self.enterOuterAlt(localctx, 3)
                self.state = 363
                self.match(AutolevParser.T__22)
                pass
            else:
                raise NoViableAltException(self)

        except RecognitionException as re:
            localctx.exception = re
            self._errHandler.reportError(self, re)
            self._errHandler.recover(self, re)
        finally:
            self.exitRule()
        return localctx


    class ExprContext(ParserRuleContext):
        __slots__ = 'parser'

        def __init__(self, parser, parent:ParserRuleContext=None, invokingState:int=-1):
            super().__init__(parent, invokingState)
            self.parser = parser


        def getRuleIndex(self):
            return AutolevParser.RULE_expr


        def copyFrom(self, ctx:ParserRuleContext):
            super().copyFrom(ctx)


    class ParensContext(ExprContext):

        def __init__(self, parser, ctx:ParserRuleContext): # actually a AutolevParser.ExprContext
            super().__init__(parser)
            self.copyFrom(ctx)

        def expr(self):
            return self.getTypedRuleContext(AutolevParser.ExprContext,0)


        def enterRule(self, listener:ParseTreeListener):
            if hasattr( listener, "enterParens" ):
                listener.enterParens(self)

        def exitRule(self, listener:ParseTreeListener):
            if hasattr( listener, "exitParens" ):
                listener.exitParens(self)


    class VectorOrDyadicContext(ExprContext):

        def __init__(self, parser, ctx:ParserRuleContext): # actually a AutolevParser.ExprContext
            super().__init__(parser)
            self.copyFrom(ctx)

        def vec(self):
            return self.getTypedRuleContext(AutolevParser.VecContext,0)


        def enterRule(self, listener:ParseTreeListener):
            if hasattr( listener, "enterVectorOrDyadic" ):
                listener.enterVectorOrDyadic(self)

        def exitRule(self, listener:ParseTreeListener):
            if hasattr( listener, "exitVectorOrDyadic" ):
                listener.exitVectorOrDyadic(self)


    class ExponentContext(ExprContext):

        def __init__(self, parser, ctx:ParserRuleContext): # actually a AutolevParser.ExprContext
            super().__init__(parser)
            self.copyFrom(ctx)

        def expr(self, i:int=None):
            if i is None:
                return self.getTypedRuleContexts(AutolevParser.ExprContext)
            else:
                return self.getTypedRuleContext(AutolevParser.ExprContext,i)


        def enterRule(self, listener:ParseTreeListener):
            if hasattr( listener, "enterExponent" ):
                listener.enterExponent(self)

        def exitRule(self, listener:ParseTreeListener):
            if hasattr( listener, "exitExponent" ):
                listener.exitExponent(self)


    class MulDivContext(ExprContext):

        def __init__(self, parser, ctx:ParserRuleContext): # actually a AutolevParser.ExprContext
            super().__init__(parser)
            self.copyFrom(ctx)

        def expr(self, i:int=None):
            if i is None:
                return self.getTypedRuleContexts(AutolevParser.ExprContext)
            else:
                return self.getTypedRuleContext(AutolevParser.ExprContext,i)


        def enterRule(self, listener:ParseTreeListener):
            if hasattr( listener, "enterMulDiv" ):
                listener.enterMulDiv(self)

        def exitRule(self, listener:ParseTreeListener):
            if hasattr( listener, "exitMulDiv" ):
                listener.exitMulDiv(self)


    class AddSubContext(ExprContext):

        def __init__(self, parser, ctx:ParserRuleContext): # actually a AutolevParser.ExprContext
            super().__init__(parser)
            self.copyFrom(ctx)

        def expr(self, i:int=None):
            if i is None:
                return self.getTypedRuleContexts(AutolevParser.ExprContext)
            else:
                return self.getTypedRuleContext(AutolevParser.ExprContext,i)


        def enterRule(self, listener:ParseTreeListener):
            if hasattr( listener, "enterAddSub" ):
                listener.enterAddSub(self)

        def exitRule(self, listener:ParseTreeListener):
            if hasattr( listener, "exitAddSub" ):
                listener.exitAddSub(self)


    class FloatContext(ExprContext):

        def __init__(self, parser, ctx:ParserRuleContext): # actually a AutolevParser.ExprContext
            super().__init__(parser)
            self.copyFrom(ctx)

        def FLOAT(self):
            return self.getToken(AutolevParser.FLOAT, 0)

        def enterRule(self, listener:ParseTreeListener):
            if hasattr( listener, "enterFloat" ):
                listener.enterFloat(self)

        def exitRule(self, listener:ParseTreeListener):
            if hasattr( listener, "exitFloat" ):
                listener.exitFloat(self)


    class IntContext(ExprContext):

        def __init__(self, parser, ctx:ParserRuleContext): # actually a AutolevParser.ExprContext
            super().__init__(parser)
            self.copyFrom(ctx)

        def INT(self):
            return self.getToken(AutolevParser.INT, 0)

        def enterRule(self, listener:ParseTreeListener):
            if hasattr( listener, "enterInt" ):
                listener.enterInt(self)

        def exitRule(self, listener:ParseTreeListener):
            if hasattr( listener, "exitInt" ):
                listener.exitInt(self)


    class IdEqualsExprContext(ExprContext):

        def __init__(self, parser, ctx:ParserRuleContext): # actually a AutolevParser.ExprContext
            super().__init__(parser)
            self.copyFrom(ctx)

        def expr(self, i:int=None):
            if i is None:
                return self.getTypedRuleContexts(AutolevParser.ExprContext)
            else:
                return self.getTypedRuleContext(AutolevParser.ExprContext,i)


        def enterRule(self, listener:ParseTreeListener):
            if hasattr( listener, "enterIdEqualsExpr" ):
                listener.enterIdEqualsExpr(self)

        def exitRule(self, listener:ParseTreeListener):
            if hasattr( listener, "exitIdEqualsExpr" ):
                listener.exitIdEqualsExpr(self)


    class NegativeOneContext(ExprContext):

        def __init__(self, parser, ctx:ParserRuleContext): # actually a AutolevParser.ExprContext
            super().__init__(parser)
            self.copyFrom(ctx)

        def expr(self):
            return self.getTypedRuleContext(AutolevParser.ExprContext,0)


        def enterRule(self, listener:ParseTreeListener):
            if hasattr( listener, "enterNegativeOne" ):
                listener.enterNegativeOne(self)

        def exitRule(self, listener:ParseTreeListener):
            if hasattr( listener, "exitNegativeOne" ):
                listener.exitNegativeOne(self)


    class FunctionContext(ExprContext):

        def __init__(self, parser, ctx:ParserRuleContext): # actually a AutolevParser.ExprContext
            super().__init__(parser)
            self.copyFrom(ctx)

        def functionCall(self):
            return self.getTypedRuleContext(AutolevParser.FunctionCallContext,0)


        def enterRule(self, listener:ParseTreeListener):
            if hasattr( listener, "enterFunction" ):
                listener.enterFunction(self)

        def exitRule(self, listener:ParseTreeListener):
            if hasattr( listener, "exitFunction" ):
                listener.exitFunction(self)


    class RangessContext(ExprContext):

        def __init__(self, parser, ctx:ParserRuleContext): # actually a AutolevParser.ExprContext
            super().__init__(parser)
            self.copyFrom(ctx)

        def ranges(self):
            return self.getTypedRuleContext(AutolevParser.RangesContext,0)

        def ID(self):
            return self.getToken(AutolevParser.ID, 0)

        def enterRule(self, listener:ParseTreeListener):
            if hasattr( listener, "enterRangess" ):
                listener.enterRangess(self)

        def exitRule(self, listener:ParseTreeListener):
            if hasattr( listener, "exitRangess" ):
                listener.exitRangess(self)


    class ColonContext(ExprContext):

        def __init__(self, parser, ctx:ParserRuleContext): # actually a AutolevParser.ExprContext
            super().__init__(parser)
            self.copyFrom(ctx)

        def expr(self, i:int=None):
            if i is None:
                return self.getTypedRuleContexts(AutolevParser.ExprContext)
            else:
                return self.getTypedRuleContext(AutolevParser.ExprContext,i)


        def enterRule(self, listener:ParseTreeListener):
            if hasattr( listener, "enterColon" ):
                listener.enterColon(self)

        def exitRule(self, listener:ParseTreeListener):
            if hasattr( listener, "exitColon" ):
                listener.exitColon(self)


    class IdContext(ExprContext):

        def __init__(self, parser, ctx:ParserRuleContext): # actually a AutolevParser.ExprContext
            super().__init__(parser)
            self.copyFrom(ctx)

        def ID(self):
            return self.getToken(AutolevParser.ID, 0)

        def enterRule(self, listener:ParseTreeListener):
            if hasattr( listener, "enterId" ):
                listener.enterId(self)

        def exitRule(self, listener:ParseTreeListener):
            if hasattr( listener, "exitId" ):
                listener.exitId(self)


    class ExpContext(ExprContext):

        def __init__(self, parser, ctx:ParserRuleContext): # actually a AutolevParser.ExprContext
            super().__init__(parser)
            self.copyFrom(ctx)

        def EXP(self):
            return self.getToken(AutolevParser.EXP, 0)

        def enterRule(self, listener:ParseTreeListener):
            if hasattr( listener, "enterExp" ):
                listener.enterExp(self)

        def exitRule(self, listener:ParseTreeListener):
            if hasattr( listener, "exitExp" ):
                listener.exitExp(self)


    class MatricesContext(ExprContext):

        def __init__(self, parser, ctx:ParserRuleContext): # actually a AutolevParser.ExprContext
            super().__init__(parser)
            self.copyFrom(ctx)

        def matrix(self):
            return self.getTypedRuleContext(AutolevParser.MatrixContext,0)


        def enterRule(self, listener:ParseTreeListener):
            if hasattr( listener, "enterMatrices" ):
                listener.enterMatrices(self)

        def exitRule(self, listener:ParseTreeListener):
            if hasattr( listener, "exitMatrices" ):
                listener.exitMatrices(self)


    class IndexingContext(ExprContext):

        def __init__(self, parser, ctx:ParserRuleContext): # actually a AutolevParser.ExprContext
            super().__init__(parser)
            self.copyFrom(ctx)

        def ID(self):
            return self.getToken(AutolevParser.ID, 0)
        def expr(self, i:int=None):
            if i is None:
                return self.getTypedRuleContexts(AutolevParser.ExprContext)
            else:
                return self.getTypedRuleContext(AutolevParser.ExprContext,i)


        def enterRule(self, listener:ParseTreeListener):
            if hasattr( listener, "enterIndexing" ):
                listener.enterIndexing(self)

        def exitRule(self, listener:ParseTreeListener):
            if hasattr( listener, "exitIndexing" ):
                listener.exitIndexing(self)



    def expr(self, _p:int=0):
        _parentctx = self._ctx
        _parentState = self.state
        localctx = AutolevParser.ExprContext(self, self._ctx, _parentState)
        _prevctx = localctx
        _startState = 54
        self.enterRecursionRule(localctx, 54, self.RULE_expr, _p)
        self._la = 0 # Token type
        try:
            self.enterOuterAlt(localctx, 1)
            self.state = 408
            self._errHandler.sync(self)
            la_ = self._interp.adaptivePredict(self._input,47,self._ctx)
            if la_ == 1:
                localctx = AutolevParser.ExpContext(self, localctx)
                self._ctx = localctx
                _prevctx = localctx

                self.state = 367
                self.match(AutolevParser.EXP)
                pass

            elif la_ == 2:
                localctx = AutolevParser.NegativeOneContext(self, localctx)
                self._ctx = localctx
                _prevctx = localctx
                self.state = 368
                self.match(AutolevParser.T__17)
                self.state = 369
                self.expr(12)
                pass

            elif la_ == 3:
                localctx = AutolevParser.FloatContext(self, localctx)
                self._ctx = localctx
                _prevctx = localctx
                self.state = 370
                self.match(AutolevParser.FLOAT)
                pass

            elif la_ == 4:
                localctx = AutolevParser.IntContext(self, localctx)
                self._ctx = localctx
                _prevctx = localctx
                self.state = 371
                self.match(AutolevParser.INT)
                pass

            elif la_ == 5:
                localctx = AutolevParser.IdContext(self, localctx)
                self._ctx = localctx
                _prevctx = localctx
                self.state = 372
                self.match(AutolevParser.ID)
                self.state = 376
                self._errHandler.sync(self)
                _alt = self._interp.adaptivePredict(self._input,43,self._ctx)
                while _alt!=2 and _alt!=ATN.INVALID_ALT_NUMBER:
                    if _alt==1:
                        self.state = 373
                        self.match(AutolevParser.T__10)
                    self.state = 378
                    self._errHandler.sync(self)
                    _alt = self._interp.adaptivePredict(self._input,43,self._ctx)

                pass

            elif la_ == 6:
                localctx = AutolevParser.VectorOrDyadicContext(self, localctx)
                self._ctx = localctx
                _prevctx = localctx
                self.state = 379
                self.vec()
                pass

            elif la_ == 7:
                localctx = AutolevParser.IndexingContext(self, localctx)
                self._ctx = localctx
                _prevctx = localctx
                self.state = 380
                self.match(AutolevParser.ID)
                self.state = 381
                self.match(AutolevParser.T__0)
                self.state = 382
                self.expr(0)
                self.state = 387
                self._errHandler.sync(self)
                _la = self._input.LA(1)
                while _la==10:
                    self.state = 383
                    self.match(AutolevParser.T__9)
                    self.state = 384
                    self.expr(0)
                    self.state = 389
                    self._errHandler.sync(self)
                    _la = self._input.LA(1)

                self.state = 390
                self.match(AutolevParser.T__1)
                pass

            elif la_ == 8:
                localctx = AutolevParser.FunctionContext(self, localctx)
                self._ctx = localctx
                _prevctx = localctx
                self.state = 392
                self.functionCall()
                pass

            elif la_ == 9:
                localctx = AutolevParser.MatricesContext(self, localctx)
                self._ctx = localctx
                _prevctx = localctx
                self.state = 393
                self.matrix()
                pass

            elif la_ == 10:
                localctx = AutolevParser.ParensContext(self, localctx)
                self._ctx = localctx
                _prevctx = localctx
                self.state = 394
                self.match(AutolevParser.T__11)
                self.state = 395
                self.expr(0)
                self.state = 396
                self.match(AutolevParser.T__12)
                pass

            elif la_ == 11:
                localctx = AutolevParser.RangessContext(self, localctx)
                self._ctx = localctx
                _prevctx = localctx
                self.state = 399
                self._errHandler.sync(self)
                _la = self._input.LA(1)
                if _la==48:
                    self.state = 398
                    self.match(AutolevParser.ID)


                self.state = 401
                self.ranges()
                self.state = 405
                self._errHandler.sync(self)
                _alt = self._interp.adaptivePredict(self._input,46,self._ctx)
                while _alt!=2 and _alt!=ATN.INVALID_ALT_NUMBER:
                    if _alt==1:
                        self.state = 402
                        self.match(AutolevParser.T__10)
                    self.state = 407
                    self._errHandler.sync(self)
                    _alt = self._interp.adaptivePredict(self._input,46,self._ctx)

                pass


            self._ctx.stop = self._input.LT(-1)
            self.state = 427
            self._errHandler.sync(self)
            _alt = self._interp.adaptivePredict(self._input,49,self._ctx)
            while _alt!=2 and _alt!=ATN.INVALID_ALT_NUMBER:
                if _alt==1:
                    if self._parseListeners is not None:
                        self.triggerExitRuleEvent()
                    _prevctx = localctx
                    self.state = 425
                    self._errHandler.sync(self)
                    la_ = self._interp.adaptivePredict(self._input,48,self._ctx)
                    if la_ == 1:
                        localctx = AutolevParser.ExponentContext(self, AutolevParser.ExprContext(self, _parentctx, _parentState))
                        self.pushNewRecursionContext(localctx, _startState, self.RULE_expr)
                        self.state = 410
                        if not self.precpred(self._ctx, 16):
                            from antlr4.error.Errors import FailedPredicateException
                            raise FailedPredicateException(self, "self.precpred(self._ctx, 16)")
                        self.state = 411
                        self.match(AutolevParser.T__23)
                        self.state = 412
                        self.expr(17)
                        pass

                    elif la_ == 2:
                        localctx = AutolevParser.MulDivContext(self, AutolevParser.ExprContext(self, _parentctx, _parentState))
                        self.pushNewRecursionContext(localctx, _startState, self.RULE_expr)
                        self.state = 413
                        if not self.precpred(self._ctx, 15):
                            from antlr4.error.Errors import FailedPredicateException
                            raise FailedPredicateException(self, "self.precpred(self._ctx, 15)")
                        self.state = 414
                        _la = self._input.LA(1)
                        if not(_la==25 or _la==26):
                            self._errHandler.recoverInline(self)
                        else:
                            self._errHandler.reportMatch(self)
                            self.consume()
                        self.state = 415
                        self.expr(16)
                        pass

                    elif la_ == 3:
                        localctx = AutolevParser.AddSubContext(self, AutolevParser.ExprContext(self, _parentctx, _parentState))
                        self.pushNewRecursionContext(localctx, _startState, self.RULE_expr)
                        self.state = 416
                        if not self.precpred(self._ctx, 14):
                            from antlr4.error.Errors import FailedPredicateException
                            raise FailedPredicateException(self, "self.precpred(self._ctx, 14)")
                        self.state = 417
                        _la = self._input.LA(1)
                        if not(_la==17 or _la==18):
                            self._errHandler.recoverInline(self)
                        else:
                            self._errHandler.reportMatch(self)
                            self.consume()
                        self.state = 418
                        self.expr(15)
                        pass

                    elif la_ == 4:
                        localctx = AutolevParser.IdEqualsExprContext(self, AutolevParser.ExprContext(self, _parentctx, _parentState))
                        self.pushNewRecursionContext(localctx, _startState, self.RULE_expr)
                        self.state = 419
                        if not self.precpred(self._ctx, 3):
                            from antlr4.error.Errors import FailedPredicateException
                            raise FailedPredicateException(self, "self.precpred(self._ctx, 3)")
                        self.state = 420
                        self.match(AutolevParser.T__2)
                        self.state = 421
                        self.expr(4)
                        pass

                    elif la_ == 5:
                        localctx = AutolevParser.ColonContext(self, AutolevParser.ExprContext(self, _parentctx, _parentState))
                        self.pushNewRecursionContext(localctx, _startState, self.RULE_expr)
                        self.state = 422
                        if not self.precpred(self._ctx, 2):
                            from antlr4.error.Errors import FailedPredicateException
                            raise FailedPredicateException(self, "self.precpred(self._ctx, 2)")
                        self.state = 423
                        self.match(AutolevParser.T__15)
                        self.state = 424
                        self.expr(3)
                        pass


                self.state = 429
                self._errHandler.sync(self)
                _alt = self._interp.adaptivePredict(self._input,49,self._ctx)

        except RecognitionException as re:
            localctx.exception = re
            self._errHandler.reportError(self, re)
            self._errHandler.recover(self, re)
        finally:
            self.unrollRecursionContexts(_parentctx)
        return localctx



    def sempred(self, localctx:RuleContext, ruleIndex:int, predIndex:int):
        if self._predicates == None:
            self._predicates = dict()
        self._predicates[27] = self.expr_sempred
        pred = self._predicates.get(ruleIndex, None)
        if pred is None:
            raise Exception("No predicate with index:" + str(ruleIndex))
        else:
            return pred(localctx, predIndex)

    def expr_sempred(self, localctx:ExprContext, predIndex:int):
            if predIndex == 0:
                return self.precpred(self._ctx, 16)


            if predIndex == 1:
                return self.precpred(self._ctx, 15)


            if predIndex == 2:
                return self.precpred(self._ctx, 14)


            if predIndex == 3:
                return self.precpred(self._ctx, 3)


            if predIndex == 4:
                return self.precpred(self._ctx, 2)





