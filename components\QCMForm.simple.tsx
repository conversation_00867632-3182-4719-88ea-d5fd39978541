"use client"

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { useToast } from '@/components/ui/use-toast';

const QCMForm: React.FC = () => {
  const { toast } = useToast();
  
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    toast({
      title: "Formulaire soumis",
      description: "Ceci est un test de formulaire simplifié",
      variant: "default"
    });
  };

  return (
    <form onSubmit={handleSubmit} className="w-full space-y-6">
      <div className="space-y-4">
        <Label className="text-base font-medium">Formulaire QCM Simplifié</Label>
        <p>Version simplifiée pour tester la compilation</p>
      </div>
      
      <Button type="submit" className="w-full">
        Tester le formulaire
      </Button>
    </form>
  );
};

export default QCMForm;
