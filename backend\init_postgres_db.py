import os
import sys
import logging
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker
from sqlalchemy.exc import SQLAlchemyError
from dotenv import load_dotenv
from models import Base, User
from auth import get_password_hash

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler()]
)
logger = logging.getLogger("init-postgres-db")

# Load environment variables
load_dotenv()

def init_db():
    """Initialize the PostgreSQL database with tables and default data"""
    try:
        # Get database URL from environment
        database_url = os.getenv("DATABASE_URL")
        if not database_url:
            logger.error("DATABASE_URL environment variable not set")
            sys.exit(1)
        
        logger.info(f"Connecting to database: {database_url}")
        
        # Create engine
        engine = create_engine(database_url)
        
        # Test connection
        try:
            with engine.connect() as conn:
                conn.execute(text("SELECT 1"))
            logger.info("Database connection successful")
        except SQLAlchemyError as e:
            logger.error(f"Database connection failed: {e}")
            sys.exit(1)
        
        # Create tables
        logger.info("Creating database tables...")
        Base.metadata.create_all(engine)
        logger.info("Tables created successfully")
        
        # Create session
        SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
        db = SessionLocal()
        
        # Check if admin user exists
        admin_exists = db.query(User).filter(User.username == "admin").first()
        
        if not admin_exists:
            # Create default admin user
            logger.info("Creating default admin user...")
            admin_user = User(
                username="admin",
                email="<EMAIL>",
                hashed_password=get_password_hash("admin123"),
                role="admin",
                is_active=True
            )
            
            db.add(admin_user)
            db.commit()
            logger.info("Default admin user created successfully")
        else:
            logger.info("Admin user already exists")
        
        # Create default teacher user if not exists
        teacher_exists = db.query(User).filter(User.username == "teacher").first()
        
        if not teacher_exists:
            logger.info("Creating default teacher user...")
            teacher_user = User(
                username="teacher",
                email="<EMAIL>",
                hashed_password=get_password_hash("teacher123"),
                role="teacher",
                is_active=True
            )
            
            db.add(teacher_user)
            db.commit()
            logger.info("Default teacher user created successfully")
        else:
            logger.info("Teacher user already exists")
        
        logger.info("Database initialization completed successfully")
        
    except Exception as e:
        logger.error(f"Error initializing database: {e}")
        sys.exit(1)

if __name__ == "__main__":
    init_db()
