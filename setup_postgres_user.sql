-- Script pour créer l'utilisateur et la base de données PostgreSQL
-- Exécutez ce script en tant qu'utilisateur postgres

-- C<PERSON>er l'utilisateur autograde
CREATE USER autograde WITH PASSWORD 'autograde123';

-- C<PERSON>er la base de données
CREATE DATABASE gradegeniusdb OWNER autograde;

-- Donner tous les privilèges à l'utilisateur
GRANT ALL PRIVILEGES ON DATABASE gradegeniusdb TO autograde;

-- Se connecter à la base de données
\c gradegeniusdb

-- Donner les privilèges sur le schéma public
GRANT ALL ON SCHEMA public TO autograde;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO autograde;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO autograde;

-- Permettre la création de tables
ALTER USER autograde CREATEDB;

-- Afficher un message de confirmation
SELECT 'Utilisateur autograde et base de données gradegeniusdb créés avec succès!' as message;
