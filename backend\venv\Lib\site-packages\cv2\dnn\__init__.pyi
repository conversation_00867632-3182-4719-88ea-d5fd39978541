import cv2
import cv2.typing
import numpy
import typing


# Enumerations
DNN_BACKEND_DEFAULT: int
DNN_BACKEND_HALIDE: int
DNN_BACKEND_INFERENCE_ENGINE: int
DNN_BACKEND_OPENCV: int
DNN_BACKEND_VKCOM: int
DNN_BACKEND_CUDA: int
DNN_BACKEND_WEBNN: int
DNN_BACKEND_TIMVX: int
DNN_BACKEND_CANN: int
Backend = int
"""One of [DNN_BACKEND_DEFAULT, DNN_BACKEND_HALIDE, DNN_BACKEND_INFERENCE_ENGINE, DNN_BACKEND_OPENCV, DNN_BACKEND_VKCOM, DNN_BACKEND_CUDA, DNN_BACKEND_WEBNN, DNN_BACKEND_TIMVX, DNN_BACKEND_CANN]"""

DNN_TARGET_CPU: int
DNN_TARGET_OPENCL: int
DNN_TARGET_OPENCL_FP16: int
DNN_TARGET_MYRIAD: int
DNN_TARGET_VULKAN: int
DNN_TARGET_FPGA: int
DNN_TARGET_CUDA: int
DNN_TARGET_CUDA_FP16: int
DNN_TARGET_HDDL: int
DNN_TARGET_NPU: int
DNN_TARGET_CPU_FP16: int
Target = int
"""One of [DNN_TARGET_CPU, DNN_TARGET_OPENCL, DNN_TARGET_OPENCL_FP16, DNN_TARGET_MYRIAD, DNN_TARGET_VULKAN, DNN_TARGET_FPGA, DNN_TARGET_CUDA, DNN_TARGET_CUDA_FP16, DNN_TARGET_HDDL, DNN_TARGET_NPU, DNN_TARGET_CPU_FP16]"""

DNN_LAYOUT_UNKNOWN: int
DNN_LAYOUT_ND: int
DNN_LAYOUT_NCHW: int
DNN_LAYOUT_NCDHW: int
DNN_LAYOUT_NHWC: int
DNN_LAYOUT_NDHWC: int
DNN_LAYOUT_PLANAR: int
DataLayout = int
"""One of [DNN_LAYOUT_UNKNOWN, DNN_LAYOUT_ND, DNN_LAYOUT_NCHW, DNN_LAYOUT_NCDHW, DNN_LAYOUT_NHWC, DNN_LAYOUT_NDHWC, DNN_LAYOUT_PLANAR]"""

DNN_PMODE_NULL: int
DNN_PMODE_CROP_CENTER: int
DNN_PMODE_LETTERBOX: int
ImagePaddingMode = int
"""One of [DNN_PMODE_NULL, DNN_PMODE_CROP_CENTER, DNN_PMODE_LETTERBOX]"""

SoftNMSMethod_SOFTNMS_LINEAR: int
SOFT_NMSMETHOD_SOFTNMS_LINEAR: int
SoftNMSMethod_SOFTNMS_GAUSSIAN: int
SOFT_NMSMETHOD_SOFTNMS_GAUSSIAN: int
SoftNMSMethod = int
"""One of [SoftNMSMethod_SOFTNMS_LINEAR, SOFT_NMSMETHOD_SOFTNMS_LINEAR, SoftNMSMethod_SOFTNMS_GAUSSIAN, SOFT_NMSMETHOD_SOFTNMS_GAUSSIAN]"""



# Classes
class DictValue:
    # Functions
    @typing.overload
    def __init__(self, i: int) -> None: ...
    @typing.overload
    def __init__(self, p: float) -> None: ...
    @typing.overload
    def __init__(self, s: str) -> None: ...

    def isInt(self) -> bool: ...

    def isString(self) -> bool: ...

    def isReal(self) -> bool: ...

    def getIntValue(self, idx: int = ...) -> int: ...

    def getRealValue(self, idx: int = ...) -> float: ...

    def getStringValue(self, idx: int = ...) -> str: ...


class Net:
    # Functions
    def __init__(self) -> None: ...

    @classmethod
    @typing.overload
    def readFromModelOptimizer(cls, xml: str, bin: str) -> Net: ...
    @classmethod
    @typing.overload
    def readFromModelOptimizer(cls, bufferModelConfig: numpy.ndarray[typing.Any, numpy.dtype[numpy.uint8]], bufferWeights: numpy.ndarray[typing.Any, numpy.dtype[numpy.uint8]]) -> Net: ...

    def empty(self) -> bool: ...

    def dump(self) -> str: ...

    def dumpToFile(self, path: str) -> None: ...

    def getLayerId(self, layer: str) -> int: ...

    def getLayerNames(self) -> typing.Sequence[str]: ...

    @typing.overload
    def getLayer(self, layerId: int) -> Layer: ...
    @typing.overload
    def getLayer(self, layerName: str) -> Layer: ...
    @typing.overload
    def getLayer(self, layerId: cv2.typing.LayerId) -> Layer: ...

    def connect(self, outPin: str, inpPin: str) -> None: ...

    def setInputsNames(self, inputBlobNames: typing.Sequence[str]) -> None: ...

    def setInputShape(self, inputName: str, shape: cv2.typing.MatShape) -> None: ...

    @typing.overload
    def forward(self, outputName: str = ...) -> cv2.typing.MatLike: ...
    @typing.overload
    def forward(self, outputBlobs: typing.Sequence[cv2.typing.MatLike] | None = ..., outputName: str = ...) -> typing.Sequence[cv2.typing.MatLike]: ...
    @typing.overload
    def forward(self, outputBlobs: typing.Sequence[cv2.UMat] | None = ..., outputName: str = ...) -> typing.Sequence[cv2.UMat]: ...
    @typing.overload
    def forward(self, outBlobNames: typing.Sequence[str], outputBlobs: typing.Sequence[cv2.typing.MatLike] | None = ...) -> typing.Sequence[cv2.typing.MatLike]: ...
    @typing.overload
    def forward(self, outBlobNames: typing.Sequence[str], outputBlobs: typing.Sequence[cv2.UMat] | None = ...) -> typing.Sequence[cv2.UMat]: ...

    def forwardAsync(self, outputName: str = ...) -> cv2.AsyncArray: ...

    def forwardAndRetrieve(self, outBlobNames: typing.Sequence[str]) -> typing.Sequence[typing.Sequence[cv2.typing.MatLike]]: ...

    @typing.overload
    def quantize(self, calibData: typing.Sequence[cv2.typing.MatLike], inputsDtype: int, outputsDtype: int, perChannel: bool = ...) -> Net: ...
    @typing.overload
    def quantize(self, calibData: typing.Sequence[cv2.UMat], inputsDtype: int, outputsDtype: int, perChannel: bool = ...) -> Net: ...

    def getInputDetails(self) -> tuple[typing.Sequence[float], typing.Sequence[int]]: ...

    def getOutputDetails(self) -> tuple[typing.Sequence[float], typing.Sequence[int]]: ...

    def setHalideScheduler(self, scheduler: str) -> None: ...

    def setPreferableBackend(self, backendId: int) -> None: ...

    def setPreferableTarget(self, targetId: int) -> None: ...

    @typing.overload
    def setInput(self, blob: cv2.typing.MatLike, name: str = ..., scalefactor: float = ..., mean: cv2.typing.Scalar = ...) -> None: ...
    @typing.overload
    def setInput(self, blob: cv2.UMat, name: str = ..., scalefactor: float = ..., mean: cv2.typing.Scalar = ...) -> None: ...

    @typing.overload
    def setParam(self, layer: int, numParam: int, blob: cv2.typing.MatLike) -> None: ...
    @typing.overload
    def setParam(self, layerName: str, numParam: int, blob: cv2.typing.MatLike) -> None: ...

    @typing.overload
    def getParam(self, layer: int, numParam: int = ...) -> cv2.typing.MatLike: ...
    @typing.overload
    def getParam(self, layerName: str, numParam: int = ...) -> cv2.typing.MatLike: ...

    def getUnconnectedOutLayers(self) -> typing.Sequence[int]: ...

    def getUnconnectedOutLayersNames(self) -> typing.Sequence[str]: ...

    @typing.overload
    def getLayersShapes(self, netInputShapes: typing.Sequence[cv2.typing.MatShape]) -> tuple[typing.Sequence[int], typing.Sequence[typing.Sequence[cv2.typing.MatShape]], typing.Sequence[typing.Sequence[cv2.typing.MatShape]]]: ...
    @typing.overload
    def getLayersShapes(self, netInputShape: cv2.typing.MatShape) -> tuple[typing.Sequence[int], typing.Sequence[typing.Sequence[cv2.typing.MatShape]], typing.Sequence[typing.Sequence[cv2.typing.MatShape]]]: ...

    @typing.overload
    def getFLOPS(self, netInputShapes: typing.Sequence[cv2.typing.MatShape]) -> int: ...
    @typing.overload
    def getFLOPS(self, netInputShape: cv2.typing.MatShape) -> int: ...
    @typing.overload
    def getFLOPS(self, layerId: int, netInputShapes: typing.Sequence[cv2.typing.MatShape]) -> int: ...
    @typing.overload
    def getFLOPS(self, layerId: int, netInputShape: cv2.typing.MatShape) -> int: ...

    def getLayerTypes(self) -> typing.Sequence[str]: ...

    def getLayersCount(self, layerType: str) -> int: ...

    @typing.overload
    def getMemoryConsumption(self, netInputShape: cv2.typing.MatShape) -> tuple[int, int]: ...
    @typing.overload
    def getMemoryConsumption(self, layerId: int, netInputShapes: typing.Sequence[cv2.typing.MatShape]) -> tuple[int, int]: ...
    @typing.overload
    def getMemoryConsumption(self, layerId: int, netInputShape: cv2.typing.MatShape) -> tuple[int, int]: ...

    def enableFusion(self, fusion: bool) -> None: ...

    def enableWinograd(self, useWinograd: bool) -> None: ...

    def getPerfProfile(self) -> tuple[int, typing.Sequence[float]]: ...


class Image2BlobParams:
    scalefactor: cv2.typing.Scalar
    size: cv2.typing.Size
    mean: cv2.typing.Scalar
    swapRB: bool
    ddepth: int
    datalayout: DataLayout
    paddingmode: ImagePaddingMode

    # Functions
    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, scalefactor: cv2.typing.Scalar, size: cv2.typing.Size = ..., mean: cv2.typing.Scalar = ..., swapRB: bool = ..., ddepth: int = ..., datalayout: DataLayout = ..., mode: ImagePaddingMode = ...) -> None: ...


class Model:
    # Functions
    @typing.overload
    def __init__(self, model: str, config: str = ...) -> None: ...
    @typing.overload
    def __init__(self, network: Net) -> None: ...

    @typing.overload
    def setInputSize(self, size: cv2.typing.Size) -> Model: ...
    @typing.overload
    def setInputSize(self, width: int, height: int) -> Model: ...

    def setInputMean(self, mean: cv2.typing.Scalar) -> Model: ...

    def setInputScale(self, scale: cv2.typing.Scalar) -> Model: ...

    def setInputCrop(self, crop: bool) -> Model: ...

    def setInputSwapRB(self, swapRB: bool) -> Model: ...

    def setInputParams(self, scale: float = ..., size: cv2.typing.Size = ..., mean: cv2.typing.Scalar = ..., swapRB: bool = ..., crop: bool = ...) -> None: ...

    @typing.overload
    def predict(self, frame: cv2.typing.MatLike, outs: typing.Sequence[cv2.typing.MatLike] | None = ...) -> typing.Sequence[cv2.typing.MatLike]: ...
    @typing.overload
    def predict(self, frame: cv2.UMat, outs: typing.Sequence[cv2.UMat] | None = ...) -> typing.Sequence[cv2.UMat]: ...

    def setPreferableBackend(self, backendId: Backend) -> Model: ...

    def setPreferableTarget(self, targetId: Target) -> Model: ...


class Layer(cv2.Algorithm):
    blobs: typing.Sequence[cv2.typing.MatLike]
    @property
    def name(self) -> str: ...
    @property
    def type(self) -> str: ...
    @property
    def preferableTarget(self) -> int: ...

    # Functions
    @typing.overload
    def finalize(self, inputs: typing.Sequence[cv2.typing.MatLike], outputs: typing.Sequence[cv2.typing.MatLike] | None = ...) -> typing.Sequence[cv2.typing.MatLike]: ...
    @typing.overload
    def finalize(self, inputs: typing.Sequence[cv2.UMat], outputs: typing.Sequence[cv2.UMat] | None = ...) -> typing.Sequence[cv2.UMat]: ...

    def run(self, inputs: typing.Sequence[cv2.typing.MatLike], internals: typing.Sequence[cv2.typing.MatLike], outputs: typing.Sequence[cv2.typing.MatLike] | None = ...) -> tuple[typing.Sequence[cv2.typing.MatLike], typing.Sequence[cv2.typing.MatLike]]: ...

    def outputNameToIndex(self, outputName: str) -> int: ...


class ClassificationModel(Model):
    # Functions
    @typing.overload
    def __init__(self, model: str, config: str = ...) -> None: ...
    @typing.overload
    def __init__(self, network: Net) -> None: ...

    def setEnableSoftmaxPostProcessing(self, enable: bool) -> ClassificationModel: ...

    def getEnableSoftmaxPostProcessing(self) -> bool: ...

    @typing.overload
    def classify(self, frame: cv2.typing.MatLike) -> tuple[int, float]: ...
    @typing.overload
    def classify(self, frame: cv2.UMat) -> tuple[int, float]: ...


class KeypointsModel(Model):
    # Functions
    @typing.overload
    def __init__(self, model: str, config: str = ...) -> None: ...
    @typing.overload
    def __init__(self, network: Net) -> None: ...

    @typing.overload
    def estimate(self, frame: cv2.typing.MatLike, thresh: float = ...) -> typing.Sequence[cv2.typing.Point2f]: ...
    @typing.overload
    def estimate(self, frame: cv2.UMat, thresh: float = ...) -> typing.Sequence[cv2.typing.Point2f]: ...


class SegmentationModel(Model):
    # Functions
    @typing.overload
    def __init__(self, model: str, config: str = ...) -> None: ...
    @typing.overload
    def __init__(self, network: Net) -> None: ...

    @typing.overload
    def segment(self, frame: cv2.typing.MatLike, mask: cv2.typing.MatLike | None = ...) -> cv2.typing.MatLike: ...
    @typing.overload
    def segment(self, frame: cv2.UMat, mask: cv2.UMat | None = ...) -> cv2.UMat: ...


class DetectionModel(Model):
    # Functions
    @typing.overload
    def __init__(self, model: str, config: str = ...) -> None: ...
    @typing.overload
    def __init__(self, network: Net) -> None: ...

    def setNmsAcrossClasses(self, value: bool) -> DetectionModel: ...

    def getNmsAcrossClasses(self) -> bool: ...

    @typing.overload
    def detect(self, frame: cv2.typing.MatLike, confThreshold: float = ..., nmsThreshold: float = ...) -> tuple[typing.Sequence[int], typing.Sequence[float], typing.Sequence[cv2.typing.Rect]]: ...
    @typing.overload
    def detect(self, frame: cv2.UMat, confThreshold: float = ..., nmsThreshold: float = ...) -> tuple[typing.Sequence[int], typing.Sequence[float], typing.Sequence[cv2.typing.Rect]]: ...


class TextRecognitionModel(Model):
    # Functions
    @typing.overload
    def __init__(self, network: Net) -> None: ...
    @typing.overload
    def __init__(self, model: str, config: str = ...) -> None: ...

    def setDecodeType(self, decodeType: str) -> TextRecognitionModel: ...

    def getDecodeType(self) -> str: ...

    def setDecodeOptsCTCPrefixBeamSearch(self, beamSize: int, vocPruneSize: int = ...) -> TextRecognitionModel: ...

    def setVocabulary(self, vocabulary: typing.Sequence[str]) -> TextRecognitionModel: ...

    def getVocabulary(self) -> typing.Sequence[str]: ...

    @typing.overload
    def recognize(self, frame: cv2.typing.MatLike) -> str: ...
    @typing.overload
    def recognize(self, frame: cv2.UMat) -> str: ...
    @typing.overload
    def recognize(self, frame: cv2.typing.MatLike, roiRects: typing.Sequence[cv2.typing.MatLike]) -> typing.Sequence[str]: ...
    @typing.overload
    def recognize(self, frame: cv2.UMat, roiRects: typing.Sequence[cv2.UMat]) -> typing.Sequence[str]: ...


class TextDetectionModel(Model):
    # Functions
    @typing.overload
    def detect(self, frame: cv2.typing.MatLike) -> tuple[typing.Sequence[typing.Sequence[cv2.typing.Point]], typing.Sequence[float]]: ...
    @typing.overload
    def detect(self, frame: cv2.UMat) -> tuple[typing.Sequence[typing.Sequence[cv2.typing.Point]], typing.Sequence[float]]: ...
    @typing.overload
    def detect(self, frame: cv2.typing.MatLike) -> typing.Sequence[typing.Sequence[cv2.typing.Point]]: ...
    @typing.overload
    def detect(self, frame: cv2.UMat) -> typing.Sequence[typing.Sequence[cv2.typing.Point]]: ...

    @typing.overload
    def detectTextRectangles(self, frame: cv2.typing.MatLike) -> tuple[typing.Sequence[cv2.typing.RotatedRect], typing.Sequence[float]]: ...
    @typing.overload
    def detectTextRectangles(self, frame: cv2.UMat) -> tuple[typing.Sequence[cv2.typing.RotatedRect], typing.Sequence[float]]: ...
    @typing.overload
    def detectTextRectangles(self, frame: cv2.typing.MatLike) -> typing.Sequence[cv2.typing.RotatedRect]: ...
    @typing.overload
    def detectTextRectangles(self, frame: cv2.UMat) -> typing.Sequence[cv2.typing.RotatedRect]: ...


class TextDetectionModel_EAST(TextDetectionModel):
    # Functions
    @typing.overload
    def __init__(self, network: Net) -> None: ...
    @typing.overload
    def __init__(self, model: str, config: str = ...) -> None: ...

    def setConfidenceThreshold(self, confThreshold: float) -> TextDetectionModel_EAST: ...

    def getConfidenceThreshold(self) -> float: ...

    def setNMSThreshold(self, nmsThreshold: float) -> TextDetectionModel_EAST: ...

    def getNMSThreshold(self) -> float: ...


class TextDetectionModel_DB(TextDetectionModel):
    # Functions
    @typing.overload
    def __init__(self, network: Net) -> None: ...
    @typing.overload
    def __init__(self, model: str, config: str = ...) -> None: ...

    def setBinaryThreshold(self, binaryThreshold: float) -> TextDetectionModel_DB: ...

    def getBinaryThreshold(self) -> float: ...

    def setPolygonThreshold(self, polygonThreshold: float) -> TextDetectionModel_DB: ...

    def getPolygonThreshold(self) -> float: ...

    def setUnclipRatio(self, unclipRatio: float) -> TextDetectionModel_DB: ...

    def getUnclipRatio(self) -> float: ...

    def setMaxCandidates(self, maxCandidates: int) -> TextDetectionModel_DB: ...

    def getMaxCandidates(self) -> int: ...



# Functions
def NMSBoxes(bboxes: typing.Sequence[cv2.typing.Rect2d], scores: typing.Sequence[float], score_threshold: float, nms_threshold: float, eta: float = ..., top_k: int = ...) -> typing.Sequence[int]: ...

def NMSBoxesBatched(bboxes: typing.Sequence[cv2.typing.Rect2d], scores: typing.Sequence[float], class_ids: typing.Sequence[int], score_threshold: float, nms_threshold: float, eta: float = ..., top_k: int = ...) -> typing.Sequence[int]: ...

def NMSBoxesRotated(bboxes: typing.Sequence[cv2.typing.RotatedRect], scores: typing.Sequence[float], score_threshold: float, nms_threshold: float, eta: float = ..., top_k: int = ...) -> typing.Sequence[int]: ...

@typing.overload
def blobFromImage(image: cv2.typing.MatLike, scalefactor: float = ..., size: cv2.typing.Size = ..., mean: cv2.typing.Scalar = ..., swapRB: bool = ..., crop: bool = ..., ddepth: int = ...) -> cv2.typing.MatLike: ...
@typing.overload
def blobFromImage(image: cv2.UMat, scalefactor: float = ..., size: cv2.typing.Size = ..., mean: cv2.typing.Scalar = ..., swapRB: bool = ..., crop: bool = ..., ddepth: int = ...) -> cv2.typing.MatLike: ...

@typing.overload
def blobFromImageWithParams(image: cv2.typing.MatLike, param: Image2BlobParams = ...) -> cv2.typing.MatLike: ...
@typing.overload
def blobFromImageWithParams(image: cv2.UMat, param: Image2BlobParams = ...) -> cv2.typing.MatLike: ...
@typing.overload
def blobFromImageWithParams(image: cv2.typing.MatLike, blob: cv2.typing.MatLike | None = ..., param: Image2BlobParams = ...) -> cv2.typing.MatLike: ...
@typing.overload
def blobFromImageWithParams(image: cv2.UMat, blob: cv2.UMat | None = ..., param: Image2BlobParams = ...) -> cv2.UMat: ...

@typing.overload
def blobFromImages(images: typing.Sequence[cv2.typing.MatLike], scalefactor: float = ..., size: cv2.typing.Size = ..., mean: cv2.typing.Scalar = ..., swapRB: bool = ..., crop: bool = ..., ddepth: int = ...) -> cv2.typing.MatLike: ...
@typing.overload
def blobFromImages(images: typing.Sequence[cv2.UMat], scalefactor: float = ..., size: cv2.typing.Size = ..., mean: cv2.typing.Scalar = ..., swapRB: bool = ..., crop: bool = ..., ddepth: int = ...) -> cv2.typing.MatLike: ...

@typing.overload
def blobFromImagesWithParams(images: typing.Sequence[cv2.typing.MatLike], param: Image2BlobParams = ...) -> cv2.typing.MatLike: ...
@typing.overload
def blobFromImagesWithParams(images: typing.Sequence[cv2.UMat], param: Image2BlobParams = ...) -> cv2.typing.MatLike: ...
@typing.overload
def blobFromImagesWithParams(images: typing.Sequence[cv2.typing.MatLike], blob: cv2.typing.MatLike | None = ..., param: Image2BlobParams = ...) -> cv2.typing.MatLike: ...
@typing.overload
def blobFromImagesWithParams(images: typing.Sequence[cv2.UMat], blob: cv2.UMat | None = ..., param: Image2BlobParams = ...) -> cv2.UMat: ...

def getAvailableTargets(be: Backend) -> typing.Sequence[Target]: ...

@typing.overload
def imagesFromBlob(blob_: cv2.typing.MatLike, images_: typing.Sequence[cv2.typing.MatLike] | None = ...) -> typing.Sequence[cv2.typing.MatLike]: ...
@typing.overload
def imagesFromBlob(blob_: cv2.typing.MatLike, images_: typing.Sequence[cv2.UMat] | None = ...) -> typing.Sequence[cv2.UMat]: ...

@typing.overload
def readNet(model: str, config: str = ..., framework: str = ...) -> Net: ...
@typing.overload
def readNet(framework: str, bufferModel: numpy.ndarray[typing.Any, numpy.dtype[numpy.uint8]], bufferConfig: numpy.ndarray[typing.Any, numpy.dtype[numpy.uint8]] = ...) -> Net: ...

@typing.overload
def readNetFromCaffe(prototxt: str, caffeModel: str = ...) -> Net: ...
@typing.overload
def readNetFromCaffe(bufferProto: numpy.ndarray[typing.Any, numpy.dtype[numpy.uint8]], bufferModel: numpy.ndarray[typing.Any, numpy.dtype[numpy.uint8]] = ...) -> Net: ...

@typing.overload
def readNetFromDarknet(cfgFile: str, darknetModel: str = ...) -> Net: ...
@typing.overload
def readNetFromDarknet(bufferCfg: numpy.ndarray[typing.Any, numpy.dtype[numpy.uint8]], bufferModel: numpy.ndarray[typing.Any, numpy.dtype[numpy.uint8]] = ...) -> Net: ...

@typing.overload
def readNetFromModelOptimizer(xml: str, bin: str) -> Net: ...
@typing.overload
def readNetFromModelOptimizer(bufferModelConfig: numpy.ndarray[typing.Any, numpy.dtype[numpy.uint8]], bufferWeights: numpy.ndarray[typing.Any, numpy.dtype[numpy.uint8]]) -> Net: ...

@typing.overload
def readNetFromONNX(onnxFile: str) -> Net: ...
@typing.overload
def readNetFromONNX(buffer: numpy.ndarray[typing.Any, numpy.dtype[numpy.uint8]]) -> Net: ...

@typing.overload
def readNetFromTFLite(model: str) -> Net: ...
@typing.overload
def readNetFromTFLite(bufferModel: numpy.ndarray[typing.Any, numpy.dtype[numpy.uint8]]) -> Net: ...

@typing.overload
def readNetFromTensorflow(model: str, config: str = ...) -> Net: ...
@typing.overload
def readNetFromTensorflow(bufferModel: numpy.ndarray[typing.Any, numpy.dtype[numpy.uint8]], bufferConfig: numpy.ndarray[typing.Any, numpy.dtype[numpy.uint8]] = ...) -> Net: ...

def readNetFromTorch(model: str, isBinary: bool = ..., evaluate: bool = ...) -> Net: ...

def readTensorFromONNX(path: str) -> cv2.typing.MatLike: ...

def readTorchBlob(filename: str, isBinary: bool = ...) -> cv2.typing.MatLike: ...

def shrinkCaffeModel(src: str, dst: str, layersTypes: typing.Sequence[str] = ...) -> None: ...

def softNMSBoxes(bboxes: typing.Sequence[cv2.typing.Rect], scores: typing.Sequence[float], score_threshold: float, nms_threshold: float, top_k: int = ..., sigma: float = ..., method: SoftNMSMethod = ...) -> tuple[typing.Sequence[float], typing.Sequence[int]]: ...

def writeTextGraph(model: str, output: str) -> None: ...


