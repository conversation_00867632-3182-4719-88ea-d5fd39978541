from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>te<PERSON>, String, DateTime, Float, JSO<PERSON>, Text, Enum
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from sqlalchemy.dialects.postgresql import UUID
import uuid
from datetime import datetime
from enum import Enum as PyEnum
from typing import Optional

from database import Base

# Prevent duplicate table definitions
import sys
if 'models' in sys.modules:
    # Models already loaded, skip redefinition
    from sys import modules
    current_module = modules[__name__]
    for attr_name in dir(current_module):
        attr = getattr(current_module, attr_name)
        if hasattr(attr, '__tablename__'):
            # This is a SQLAlchemy model, skip redefinition
            globals()[attr_name] = attr

# Enums for better type safety
class UserRole(PyEnum):
    ADMIN = "admin"
    TEACHER = "teacher"
    STUDENT = "student"
    USER = "user"

class ExamStatus(PyEnum):
    UPLOADED = "uploaded"
    PROCESSING = "processing"
    PROCESSED = "processed"
    GRADED = "graded"
    COMPLETED = "completed"
    ERROR = "error"
    FAILED = "failed"

class ExamType(PyEnum):
    QCM = "qcm"
    HANDWRITTEN = "handwritten"
    MIXED = "mixed"
    ESSAY = "essay"

class GradeStatus(PyEnum):
    PASSED = "passed"
    FAILED = "failed"
    PENDING = "pending"
    REVIEW_REQUIRED = "review_required"

class User(Base):
    __tablename__ = "users"

    id = Column(Integer, primary_key=True, index=True)
    username = Column(String(50), unique=True, index=True, nullable=False)
    email = Column(String(255), unique=True, index=True, nullable=False)
    full_name = Column(String(255), nullable=True)
    hashed_password = Column(String(255), nullable=False)
    is_active = Column(Boolean, default=True, nullable=False)
    role = Column(Enum(UserRole), default=UserRole.USER, nullable=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), nullable=False)
    last_login = Column(DateTime(timezone=True), nullable=True)

    # Profile information
    department = Column(String(100), nullable=True)
    institution = Column(String(255), nullable=True)
    phone = Column(String(20), nullable=True)

    # Settings
    preferences = Column(JSON, nullable=True)

    # Relations
    exams = relationship("Exam", back_populates="owner", cascade="all, delete-orphan")
    created_students = relationship("Student", back_populates="created_by", cascade="all, delete-orphan")
    audit_logs = relationship("AuditLog", back_populates="user", cascade="all, delete-orphan")

class Student(Base):
    __tablename__ = "students"

    id = Column(Integer, primary_key=True, index=True)
    student_id = Column(String(50), unique=True, index=True, nullable=False)
    first_name = Column(String(100), nullable=False)
    last_name = Column(String(100), nullable=False)
    email = Column(String(255), unique=True, index=True, nullable=True)
    phone = Column(String(20), nullable=True)

    # Academic information
    class_name = Column(String(100), nullable=True)
    academic_year = Column(String(20), nullable=True)
    department = Column(String(100), nullable=True)

    # Metadata
    created_by_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), nullable=False)
    is_active = Column(Boolean, default=True, nullable=False)

    # Additional information
    notes = Column(Text, nullable=True)
    metadata = Column(JSON, nullable=True)

    # Relations
    created_by = relationship("User", back_populates="created_students")
    exam_results = relationship("ExamResult", back_populates="student", cascade="all, delete-orphan")

class Exam(Base):
    __tablename__ = "exams"

    id = Column(String(36), primary_key=True, index=True, default=lambda: str(uuid.uuid4()))
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)

    # File information
    original_filename = Column(String(255), nullable=False)
    file_path = Column(String(500), nullable=False)
    file_size = Column(Integer, nullable=True)  # in bytes
    file_hash = Column(String(64), nullable=True)  # SHA-256 hash for integrity

    # Exam metadata
    exam_name = Column(String(255), nullable=True)
    exam_type = Column(Enum(ExamType), nullable=False, default=ExamType.QCM)
    subject = Column(String(100), nullable=True)
    class_name = Column(String(100), nullable=True)
    academic_year = Column(String(20), nullable=True)

    # Processing information
    upload_time = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    processing_started_at = Column(DateTime(timezone=True), nullable=True)
    processing_completed_at = Column(DateTime(timezone=True), nullable=True)
    status = Column(Enum(ExamStatus), default=ExamStatus.UPLOADED, nullable=False)

    # OCR and extraction results
    extracted_text = Column(Text, nullable=True)
    ocr_confidence = Column(Float, nullable=True)
    ocr_model_used = Column(String(50), nullable=True)  # tesseract, gemini, etc.

    # Processing configuration
    processing_config = Column(JSON, nullable=True)

    # Error handling
    error_message = Column(Text, nullable=True)
    retry_count = Column(Integer, default=0, nullable=False)

    # Metadata
    metadata = Column(JSON, nullable=True)

    # Relations
    owner = relationship("User", back_populates="exams")
    exam_results = relationship("ExamResult", back_populates="exam", cascade="all, delete-orphan")
    processing_logs = relationship("ProcessingLog", back_populates="exam", cascade="all, delete-orphan")

class ExamResult(Base):
    __tablename__ = "exam_results"

    id = Column(Integer, primary_key=True, index=True)
    exam_id = Column(String(36), ForeignKey("exams.id"), nullable=False)
    student_id = Column(Integer, ForeignKey("students.id"), nullable=True)

    # Student information (extracted from exam or manually entered)
    student_name = Column(String(255), nullable=True)
    student_identifier = Column(String(100), nullable=True)  # student ID from exam

    # Grading results
    total_score = Column(Float, nullable=True)
    max_score = Column(Float, nullable=True)
    percentage = Column(Float, nullable=True)
    letter_grade = Column(String(5), nullable=True)  # A, B, C, D, F
    grade_status = Column(Enum(GradeStatus), default=GradeStatus.PENDING, nullable=False)

    # Detailed results
    question_results = Column(JSON, nullable=True)  # Question-by-question breakdown
    extracted_answers = Column(JSON, nullable=True)  # Student's extracted answers
    correct_answers = Column(JSON, nullable=True)   # Correct answers used for grading

    # AI Analysis
    ai_confidence = Column(Float, nullable=True)
    ai_model_used = Column(String(50), nullable=True)
    ai_analysis = Column(JSON, nullable=True)

    # Manual review
    requires_manual_review = Column(Boolean, default=False, nullable=False)
    reviewed_by_id = Column(Integer, ForeignKey("users.id"), nullable=True)
    review_notes = Column(Text, nullable=True)
    review_date = Column(DateTime(timezone=True), nullable=True)

    # Timestamps
    graded_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), nullable=False)

    # Additional metadata
    grading_rubric = Column(JSON, nullable=True)
    feedback = Column(Text, nullable=True)
    metadata = Column(JSON, nullable=True)

    # Relations
    exam = relationship("Exam", back_populates="exam_results")
    student = relationship("Student", back_populates="exam_results")
    reviewed_by = relationship("User", foreign_keys=[reviewed_by_id])
    grade_history = relationship("GradeHistory", back_populates="exam_result", cascade="all, delete-orphan")

class GradeHistory(Base):
    __tablename__ = "grade_history"

    id = Column(Integer, primary_key=True, index=True)
    exam_result_id = Column(Integer, ForeignKey("exam_results.id"), nullable=False)

    # Previous values
    previous_score = Column(Float, nullable=True)
    previous_grade = Column(String(5), nullable=True)
    previous_status = Column(String(50), nullable=True)

    # New values
    new_score = Column(Float, nullable=True)
    new_grade = Column(String(5), nullable=True)
    new_status = Column(String(50), nullable=True)

    # Change information
    changed_by_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    change_reason = Column(String(255), nullable=True)
    change_notes = Column(Text, nullable=True)
    changed_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)

    # Relations
    exam_result = relationship("ExamResult", back_populates="grade_history")
    changed_by = relationship("User", foreign_keys=[changed_by_id])

class ProcessingLog(Base):
    __tablename__ = "processing_logs"

    id = Column(Integer, primary_key=True, index=True)
    exam_id = Column(String(36), ForeignKey("exams.id"), nullable=False)

    # Processing step information
    step_name = Column(String(100), nullable=False)  # upload, ocr, grading, etc.
    step_status = Column(String(50), nullable=False)  # started, completed, failed

    # Timing
    started_at = Column(DateTime(timezone=True), nullable=False)
    completed_at = Column(DateTime(timezone=True), nullable=True)
    duration_seconds = Column(Float, nullable=True)

    # Results and errors
    success = Column(Boolean, nullable=False)
    error_message = Column(Text, nullable=True)
    error_code = Column(String(50), nullable=True)

    # Processing details
    input_data = Column(JSON, nullable=True)
    output_data = Column(JSON, nullable=True)
    processing_config = Column(JSON, nullable=True)

    # System information
    server_info = Column(JSON, nullable=True)

    # Relations
    exam = relationship("Exam", back_populates="processing_logs")

class AuditLog(Base):
    __tablename__ = "audit_logs"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=True)

    # Action information
    action = Column(String(100), nullable=False)  # create, update, delete, login, etc.
    resource_type = Column(String(50), nullable=False)  # exam, user, grade, etc.
    resource_id = Column(String(100), nullable=True)

    # Request information
    ip_address = Column(String(45), nullable=True)  # IPv6 compatible
    user_agent = Column(String(500), nullable=True)
    request_method = Column(String(10), nullable=True)
    request_path = Column(String(500), nullable=True)

    # Change details
    old_values = Column(JSON, nullable=True)
    new_values = Column(JSON, nullable=True)

    # Metadata
    timestamp = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    session_id = Column(String(100), nullable=True)
    additional_data = Column(JSON, nullable=True)

    # Relations
    user = relationship("User", back_populates="audit_logs")

class ExamTemplate(Base):
    __tablename__ = "exam_templates"

    id = Column(Integer, primary_key=True, index=True)
    created_by_id = Column(Integer, ForeignKey("users.id"), nullable=False)

    # Template information
    name = Column(String(255), nullable=False)
    description = Column(Text, nullable=True)
    exam_type = Column(Enum(ExamType), nullable=False)
    subject = Column(String(100), nullable=True)

    # Template configuration
    question_count = Column(Integer, nullable=True)
    answer_choices = Column(JSON, nullable=True)  # For QCM: ["A", "B", "C", "D"]
    grading_rubric = Column(JSON, nullable=True)
    correct_answers = Column(JSON, nullable=True)

    # Processing settings
    ocr_settings = Column(JSON, nullable=True)
    grading_settings = Column(JSON, nullable=True)

    # Metadata
    is_active = Column(Boolean, default=True, nullable=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), nullable=False)

    # Relations
    created_by = relationship("User", foreign_keys=[created_by_id])