from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON>umn, <PERSON><PERSON><PERSON>, Integer, String, DateTime, Float, JSON
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
import uuid
from datetime import datetime

from database import Base

class User(Base):
    __tablename__ = "users"

    id = Column(Integer, primary_key=True, index=True)
    username = Column(String, unique=True, index=True)
    email = Column(String, unique=True, index=True)
    full_name = Column(String, nullable=True)
    hashed_password = Column(String)
    is_active = Column(Boolean, default=True)
    role = Column(String, default="user")  # user, admin, etc.
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())

    # Relation avec les examens
    exams = relationship("Exam", back_populates="owner")


class Exam(Base):
    __tablename__ = "exams"

    id = Column(String, primary_key=True, index=True, default=lambda: str(uuid.uuid4()))
    user_id = Column(Integer, ForeignKey("users.id"))
    original_filename = Column(String)
    file_path = Column(String)
    upload_time = Column(DateTime, default=datetime.now)
    status = Column(String, default="uploaded")  # uploaded, processing, completed, error
    exam_type = Column(String, nullable=True)  # handwritten, qcm, etc.
    extracted_text = Column(String, nullable=True)
    score = Column(Float, nullable=True)
    grade = Column(String, nullable=True)
    summary = Column(String, nullable=True)
    details = Column(JSON, nullable=True)

    # Relation avec l'utilisateur
    owner = relationship("User", back_populates="exams") 