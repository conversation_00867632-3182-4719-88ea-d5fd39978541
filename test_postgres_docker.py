#!/usr/bin/env python3
"""
Test simple pour vérifier la configuration PostgreSQL avec Docker
"""

import subprocess
import time
import sys
import os

def run_command(command, timeout=30):
    """Exécuter une commande avec timeout"""
    try:
        result = subprocess.run(
            command, 
            shell=True, 
            capture_output=True, 
            text=True, 
            timeout=timeout
        )
        return result.returncode == 0, result.stdout, result.stderr
    except subprocess.TimeoutExpired:
        return False, "", "Timeout"
    except Exception as e:
        return False, "", str(e)

def test_docker():
    """Tester si Docker est disponible"""
    print("🐳 Test Docker...")
    success, stdout, stderr = run_command("docker --version")
    if success:
        print(f"  ✅ Docker trouvé: {stdout.strip()}")
        return True
    else:
        print(f"  ❌ Docker non trouvé: {stderr}")
        return False

def test_docker_compose():
    """Tester si Docker Compose est disponible"""
    print("🐳 Test Docker Compose...")
    success, stdout, stderr = run_command("docker-compose --version")
    if success:
        print(f"  ✅ Docker Compose trouvé: {stdout.strip()}")
        return True
    else:
        print(f"  ❌ Docker Compose non trouvé: {stderr}")
        return False

def cleanup_postgres():
    """Nettoyer les conteneurs PostgreSQL existants"""
    print("🧹 Nettoyage des conteneurs PostgreSQL existants...")
    
    # Arrêter les conteneurs
    run_command("docker stop auto-grade-postgres", timeout=10)
    run_command("docker rm auto-grade-postgres", timeout=10)
    
    print("  ✅ Nettoyage terminé")

def start_postgres():
    """Démarrer PostgreSQL avec Docker Compose"""
    print("🚀 Démarrage PostgreSQL...")
    
    success, stdout, stderr = run_command(
        "docker-compose -f docker-compose-db-only.yml up -d", 
        timeout=120
    )
    
    if success:
        print("  ✅ PostgreSQL démarré")
        return True
    else:
        print(f"  ❌ Erreur démarrage PostgreSQL: {stderr}")
        return False

def wait_for_postgres():
    """Attendre que PostgreSQL soit prêt"""
    print("⏳ Attente que PostgreSQL soit prêt...")
    
    max_attempts = 30
    for attempt in range(1, max_attempts + 1):
        success, stdout, stderr = run_command(
            "docker exec auto-grade-postgres pg_isready -U autograde -d gradegeniusdb",
            timeout=5
        )
        
        if success:
            print("  ✅ PostgreSQL est prêt!")
            return True
        
        print(f"  ⏳ Tentative {attempt}/{max_attempts}...")
        time.sleep(2)
    
    print("  ❌ Timeout: PostgreSQL pas prêt")
    return False

def test_postgres_connection():
    """Tester la connexion PostgreSQL"""
    print("🧪 Test connexion PostgreSQL...")
    
    success, stdout, stderr = run_command(
        'docker exec auto-grade-postgres psql -U autograde -d gradegeniusdb -c "SELECT version();"',
        timeout=10
    )
    
    if success:
        print("  ✅ Connexion PostgreSQL réussie!")
        print(f"  📊 Version: {stdout.strip()}")
        return True
    else:
        print(f"  ❌ Erreur connexion: {stderr}")
        return False

def test_database_creation():
    """Tester la création de tables"""
    print("🗄️ Test création de tables...")
    
    # Test simple de création de table
    sql_command = '''
    CREATE TABLE IF NOT EXISTS test_table (
        id SERIAL PRIMARY KEY,
        name VARCHAR(100),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );
    INSERT INTO test_table (name) VALUES ('test');
    SELECT COUNT(*) FROM test_table;
    DROP TABLE test_table;
    '''
    
    success, stdout, stderr = run_command(
        f'docker exec auto-grade-postgres psql -U autograde -d gradegeniusdb -c "{sql_command}"',
        timeout=10
    )
    
    if success:
        print("  ✅ Création de tables réussie!")
        return True
    else:
        print(f"  ❌ Erreur création tables: {stderr}")
        return False

def create_env_file():
    """Créer le fichier .env"""
    print("⚙️ Création fichier .env...")
    
    env_content = """# Configuration PostgreSQL pour Auto-Grade Scribe
ENVIRONMENT=development
DEBUG=true
DATABASE_URL=postgresql://autograde:autograde123@localhost:5432/gradegeniusdb
SECRET_KEY=your-secret-key-change-in-production
API_HOST=0.0.0.0
API_PORT=8000
"""
    
    try:
        with open(".env", "w", encoding="utf-8") as f:
            f.write(env_content)
        print("  ✅ Fichier .env créé")
        return True
    except Exception as e:
        print(f"  ❌ Erreur création .env: {e}")
        return False

def show_status():
    """Afficher le statut final"""
    print("\n" + "="*50)
    print("📊 Statut PostgreSQL")
    print("="*50)
    
    # Statut des conteneurs
    success, stdout, stderr = run_command("docker ps --filter name=auto-grade-postgres")
    if success and "auto-grade-postgres" in stdout:
        print("✅ Conteneur PostgreSQL: En cours d'exécution")
    else:
        print("❌ Conteneur PostgreSQL: Arrêté")
    
    # Test de connexion rapide
    success, stdout, stderr = run_command(
        "docker exec auto-grade-postgres pg_isready -U autograde -d gradegeniusdb",
        timeout=5
    )
    if success:
        print("✅ Service PostgreSQL: Accessible")
    else:
        print("❌ Service PostgreSQL: Non accessible")
    
    print("\n📋 Informations de connexion:")
    print("  Host: localhost")
    print("  Port: 5432")
    print("  Database: gradegeniusdb")
    print("  User: autograde")
    print("  Password: autograde123")
    
    print("\n🚀 Pour démarrer l'application:")
    print("  cd backend")
    print("  python -m uvicorn app:app --host 0.0.0.0 --port 8000 --reload")

def main():
    """Fonction principale"""
    print("🐘 Test Configuration PostgreSQL Docker")
    print("="*50)
    
    tests = [
        ("Docker", test_docker),
        ("Docker Compose", test_docker_compose),
        ("Nettoyage", cleanup_postgres),
        ("Démarrage PostgreSQL", start_postgres),
        ("Attente PostgreSQL", wait_for_postgres),
        ("Connexion PostgreSQL", test_postgres_connection),
        ("Création tables", test_database_creation),
        ("Fichier .env", create_env_file)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
            else:
                print(f"❌ {test_name} a échoué")
        except Exception as e:
            print(f"❌ {test_name} a planté: {e}")
    
    print(f"\n📊 Résultats: {passed}/{total} tests réussis")
    
    if passed == total:
        print("🎉 Configuration PostgreSQL réussie!")
        show_status()
    else:
        print("⚠️ Certains tests ont échoué.")
        print("\n🔧 Solutions possibles:")
        print("  1. Vérifiez que Docker Desktop est démarré")
        print("  2. Redémarrez Docker Desktop")
        print("  3. Exécutez: docker system prune -f")
        print("  4. Réessayez le script")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
