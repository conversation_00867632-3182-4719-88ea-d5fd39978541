# DO NOT EDIT! This file was generated by jschema_to_python version 0.0.1.dev29,
# with extension for dataclasses and type annotation.

from __future__ import annotations

import dataclasses
from typing import Optional

from torch.onnx._internal.diagnostics.infra.sarif import (
    _artifact_location,
    _property_bag,
)


@dataclasses.dataclass
class ExternalPropertyFileReference(object):
    """Contains information that enables a SARIF consumer to locate the external property file that contains the value of an externalized property associated with the run."""

    guid: Optional[str] = dataclasses.field(
        default=None, metadata={"schema_property_name": "guid"}
    )
    item_count: int = dataclasses.field(
        default=-1, metadata={"schema_property_name": "itemCount"}
    )
    location: Optional[_artifact_location.ArtifactLocation] = dataclasses.field(
        default=None, metadata={"schema_property_name": "location"}
    )
    properties: Optional[_property_bag.PropertyBag] = dataclasses.field(
        default=None, metadata={"schema_property_name": "properties"}
    )


# flake8: noqa
