#pragma once

// @generated by torchgen/gen.py from Function.h

#include <ATen/Context.h>
#include <ATen/DeviceGuard.h>
#include <ATen/TensorUtils.h>
#include <ATen/TracerMode.h>
#include <ATen/core/Generator.h>
#include <ATen/core/Reduction.h>
#include <ATen/core/Tensor.h>
#include <c10/core/Scalar.h>
#include <c10/core/Storage.h>
#include <c10/core/TensorOptions.h>
#include <c10/util/Deprecated.h>
#include <c10/util/Optional.h>



#include <ATen/ops/log10_ops.h>

namespace at {


// aten::log10(Tensor self) -> Tensor
inline at::Tensor log10(const at::Tensor & self) {
    return at::_ops::log10::call(self);
}

// aten::log10_(Tensor(a!) self) -> Tensor(a!)
inline at::Tensor & log10_(at::Tensor & self) {
    return at::_ops::log10_::call(self);
}

// aten::log10.out(Tensor self, *, Tensor(a!) out) -> Tensor(a!)
inline at::Tensor & log10_out(at::Tensor & out, const at::Tensor & self) {
    return at::_ops::log10_out::call(self, out);
}
// aten::log10.out(Tensor self, *, Tensor(a!) out) -> Tensor(a!)
inline at::Tensor & log10_outf(const at::Tensor & self, at::Tensor & out) {
    return at::_ops::log10_out::call(self, out);
}

}
