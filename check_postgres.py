#!/usr/bin/env python3
"""
Script pour vérifier l'état de PostgreSQL.
Ce script tente de se connecter à PostgreSQL et affiche des informations de diagnostic.
"""

import os
import sys
import time
import socket
import subprocess
from sqlalchemy import create_engine, text
from sqlalchemy.exc import OperationalError

# URL de connexion à PostgreSQL
DATABASE_URL = "*************************************************/gradegeniusdb"

def check_port_open(host, port):
    """Vérifier si un port est ouvert sur un hôte."""
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(2)
        result = sock.connect_ex((host, port))
        sock.close()
        return result == 0
    except Exception as e:
        print(f"Erreur lors de la vérification du port: {e}")
        return False

def check_docker_running():
    """Vérifier si Docker est en cours d'exécution."""
    try:
        result = subprocess.run(["docker", "ps"], capture_output=True, text=True)
        return result.returncode == 0
    except Exception as e:
        print(f"Erreur lors de la vérification de Docker: {e}")
        return False

def check_postgres_container():
    """Vérifier si le conteneur PostgreSQL est en cours d'exécution."""
    try:
        result = subprocess.run(["docker", "ps", "--filter", "name=autogradedb"], capture_output=True, text=True)
        return "autogradedb" in result.stdout
    except Exception as e:
        print(f"Erreur lors de la vérification du conteneur PostgreSQL: {e}")
        return False

def check_postgres_connection():
    """Vérifier la connexion à PostgreSQL."""
    try:
        # Créer le moteur PostgreSQL
        engine = create_engine(DATABASE_URL, pool_pre_ping=True)

        # Tester la connexion
        connection = engine.connect()
        
        # Exécuter une requête simple
        result = connection.execute(text("SELECT version()"))
        version = result.fetchone()[0]
        
        connection.close()
        return True, version
    except Exception as e:
        return False, str(e)

def main():
    """Fonction principale."""
    print("=== Vérification de l'état de PostgreSQL ===")
    print(f"URL de connexion: {DATABASE_URL}")
    
    # Vérifier si Docker est en cours d'exécution
    docker_running = check_docker_running()
    print(f"Docker en cours d'exécution: {docker_running}")
    
    if not docker_running:
        print("ERREUR: Docker n'est pas en cours d'exécution.")
        print("Veuillez démarrer Docker Desktop et réessayer.")
        return
    
    # Vérifier si le conteneur PostgreSQL est en cours d'exécution
    postgres_container_running = check_postgres_container()
    print(f"Conteneur PostgreSQL en cours d'exécution: {postgres_container_running}")
    
    if not postgres_container_running:
        print("ERREUR: Le conteneur PostgreSQL n'est pas en cours d'exécution.")
        print("Veuillez démarrer le conteneur avec la commande suivante:")
        print("docker-compose up -d")
        return
    
    # Vérifier si le port PostgreSQL est ouvert
    port_open = check_port_open("localhost", 5432)
    print(f"Port PostgreSQL (5432) ouvert: {port_open}")
    
    if not port_open:
        print("ERREUR: Le port PostgreSQL n'est pas ouvert.")
        print("Vérifiez que le conteneur PostgreSQL est correctement configuré.")
        return
    
    # Vérifier la connexion à PostgreSQL
    connection_ok, connection_info = check_postgres_connection()
    print(f"Connexion à PostgreSQL: {connection_ok}")
    
    if connection_ok:
        print(f"Version PostgreSQL: {connection_info}")
        print("La connexion à PostgreSQL fonctionne correctement!")
    else:
        print(f"ERREUR: Impossible de se connecter à PostgreSQL: {connection_info}")
        print("Vérifiez les identifiants et le nom de la base de données.")
    
    print("\n=== Recommandations ===")
    if not docker_running:
        print("1. Démarrez Docker Desktop")
    if not postgres_container_running:
        print("2. Démarrez le conteneur PostgreSQL avec la commande:")
        print("   docker-compose up -d")
    if not port_open:
        print("3. Vérifiez que le port 5432 n'est pas bloqué par un pare-feu")
    if not connection_ok:
        print("4. Vérifiez les identifiants et le nom de la base de données")
        print("5. Initialisez la base de données avec la commande:")
        print("   python init_postgres_db.py")

if __name__ == "__main__":
    main()
