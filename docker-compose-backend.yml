services:
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: autograde-backend
    restart: always
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=****************************************************/gradegeniusdb
      - GEMINI_API_KEY=${GEMINI_API_KEY}
    volumes:
      - ./backend:/app
    networks:
      - autograde-network

networks:
  autograde-network:
    name: auto-grade-scribe-main_autograde-network
    external: true
