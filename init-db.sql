-- Script d'initialisation de la base de données PostgreSQL pour Auto-Grade Scribe
-- Ce script est exécuté automatiquement lors du premier démarrage du conteneur PostgreSQL

-- Créer des extensions utiles
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";

-- Donner tous les privilèges à l'utilisateur autograde
GRANT ALL PRIVILEGES ON DATABASE gradegeniusdb TO autograde;
GRANT ALL ON SCHEMA public TO autograde;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO autograde;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO autograde;
GRANT ALL PRIVILEGES ON ALL FUNCTIONS IN SCHEMA public TO autograde;

-- Permettre la création de tables et d'autres objets
ALTER USER autograde CREATEDB;
ALTER USER autograde CREATEROLE;

-- Configurer les paramètres de performance
ALTER SYSTEM SET shared_preload_libraries = 'pg_stat_statements';
ALTER SYSTEM SET max_connections = 100;
ALTER SYSTEM SET shared_buffers = '256MB';
ALTER SYSTEM SET effective_cache_size = '1GB';
ALTER SYSTEM SET maintenance_work_mem = '64MB';
ALTER SYSTEM SET checkpoint_completion_target = 0.9;
ALTER SYSTEM SET wal_buffers = '16MB';
ALTER SYSTEM SET default_statistics_target = 100;

-- Créer un utilisateur de lecture seule (optionnel)
CREATE USER readonly_user WITH PASSWORD 'readonly123';
GRANT CONNECT ON DATABASE gradegeniusdb TO readonly_user;
GRANT USAGE ON SCHEMA public TO readonly_user;
GRANT SELECT ON ALL TABLES IN SCHEMA public TO readonly_user;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT SELECT ON TABLES TO readonly_user;

-- Message de confirmation
SELECT 'Base de données PostgreSQL initialisée avec succès pour Auto-Grade Scribe!' as message;
