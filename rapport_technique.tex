\documentclass[a4paper,12pt]{report}

% Packages nécessaires
\usepackage[utf8]{inputenc}
\usepackage[T1]{fontenc}
\usepackage[french]{babel}
\usepackage{graphicx}
\usepackage{hyperref}
\usepackage{listings}
\usepackage{xcolor}
\usepackage{geometry}
\usepackage{fancyhdr}
\usepackage{titlesec}
\usepackage{tcolorbox}
\usepackage{enumitem}

% Configuration de la géométrie de la page
\geometry{margin=2.5cm}

% Configuration des liens hypertexte
\hypersetup{
    colorlinks=true,
    linkcolor=blue,
    filecolor=magenta,
    urlcolor=cyan,
}

% Configuration des listings de code
\definecolor{codegreen}{rgb}{0,0.6,0}
\definecolor{codegray}{rgb}{0.5,0.5,0.5}
\definecolor{codepurple}{rgb}{0.58,0,0.82}
\definecolor{backcolour}{rgb}{0.95,0.95,0.92}

\lstdefinestyle{mystyle}{
    backgroundcolor=\color{backcolour},
    commentstyle=\color{codegreen},
    keywordstyle=\color{magenta},
    numberstyle=\tiny\color{codegray},
    stringstyle=\color{codepurple},
    basicstyle=\ttfamily\footnotesize,
    breakatwhitespace=false,
    breaklines=true,
    captionpos=b,
    keepspaces=true,
    numbers=left,
    numbersep=5pt,
    showspaces=false,
    showstringspaces=false,
    showtabs=false,
    tabsize=2
}

\lstset{style=mystyle}

% Configuration des en-têtes et pieds de page
\pagestyle{fancy}
\fancyhf{}
\fancyhead[L]{Auto-Grade Scribe}
\fancyhead[R]{\thepage}
\fancyfoot[C]{Rapport Technique}

% Configuration des titres
\titleformat{\chapter}[display]
{\normalfont\huge\bfseries}{\chaptertitlename\ \thechapter}{20pt}{\Huge}
\titlespacing*{\chapter}{0pt}{50pt}{40pt}

% Début du document
\begin{document}

\begin{titlepage}
    \centering
    \vspace*{1cm}
    {\Huge\bfseries Auto-Grade Scribe\par}
    \vspace{1cm}
    {\Large Rapport Technique\par}
    \vspace{1.5cm}
    {\large\textbf{Système de Correction Automatique d'Examens}\par}
    \vspace{2cm}
    \includegraphics[width=0.5\textwidth]{logo.png}
    \vspace{2cm}
    \par
    {\Large\itshape Projet de Fin d'Année\par}
    \vfill
    {\large \today\par}
\end{titlepage}

\tableofcontents
\newpage

\chapter{Introduction}

\section{Présentation du Projet}
Auto-Grade Scribe est une application web conçue pour automatiser la correction d'examens manuscrits et à choix multiples (QCM). Le système utilise des technologies avancées de reconnaissance optique de caractères (OCR) et d'intelligence artificielle pour extraire le texte des images d'examens, analyser les réponses et fournir une évaluation détaillée.

\section{Objectifs}
Les principaux objectifs du projet sont :
\begin{itemize}
    \item Automatiser le processus de correction d'examens pour réduire la charge de travail des enseignants
    \item Fournir une évaluation objective et cohérente des réponses des étudiants
    \item Offrir un retour détaillé et personnalisé aux étudiants
    \item Créer une interface utilisateur intuitive et accessible
    \item Assurer la sécurité et la confidentialité des données
\end{itemize}

\section{Technologies Utilisées}
Le projet utilise les technologies suivantes :
\begin{itemize}
    \item \textbf{Frontend} : Next.js, TypeScript, Tailwind CSS, shadcn/ui
    \item \textbf{Backend} : FastAPI (Python)
    \item \textbf{Base de données} : SQLite
    \item \textbf{OCR} : Tesseract
    \item \textbf{IA} : TinyLlama (modèle de langage)
    \item \textbf{Authentification} : JWT (JSON Web Tokens)
\end{itemize}

\chapter{Architecture du Système}

\section{Vue d'Ensemble}
L'architecture d'Auto-Grade Scribe suit un modèle client-serveur classique avec une séparation claire entre le frontend et le backend. Le frontend est responsable de l'interface utilisateur et de l'interaction avec l'utilisateur, tandis que le backend gère le traitement des données, l'OCR, l'analyse par IA et la persistance des données.

\begin{figure}[h]
    \centering
    \includegraphics[width=0.8\textwidth]{architecture.png}
    \caption{Architecture générale du système}
\end{figure}

\section{Architecture Frontend}
Le frontend est développé avec Next.js, un framework React qui offre des fonctionnalités avancées comme le rendu côté serveur (SSR) et la génération de sites statiques (SSG). L'interface utilisateur est construite avec Tailwind CSS et la bibliothèque de composants shadcn/ui.

\subsection{Structure des Composants}
Les principaux composants du frontend sont :
\begin{itemize}
    \item \textbf{Layout} : Structure générale de l'application (Header, Footer, etc.)
    \item \textbf{FileUpload} : Gestion du téléchargement des fichiers
    \item \textbf{GradingResult} : Affichage des résultats de correction
    \item \textbf{Dashboard} : Tableau de bord utilisateur
    \item \textbf{RouteGuard} : Protection des routes nécessitant une authentification
\end{itemize}

\subsection{Gestion de l'État}
L'état de l'application est géré à l'aide de React Context API, notamment pour l'authentification des utilisateurs. Les états locaux des composants sont gérés avec les hooks useState et useEffect.

\section{Architecture Backend}
Le backend est développé avec FastAPI, un framework Python moderne et performant pour la création d'APIs. Il est organisé en plusieurs modules :

\subsection{Structure des Modules}
\begin{itemize}
    \item \textbf{app.py} : Point d'entrée de l'application et définition des routes API
    \item \textbf{models.py} : Définition des modèles de données SQLAlchemy
    \item \textbf{schemas.py} : Schémas Pydantic pour la validation des données
    \item \textbf{auth.py} : Gestion de l'authentification et des autorisations
    \item \textbf{ai/} : Services d'intelligence artificielle
    \begin{itemize}
        \item \textbf{ocr\_service.py} : Service OCR avec Tesseract
        \item \textbf{handwritten\_service.py} : Service d'analyse des examens manuscrits
        \item \textbf{enhanced\_qcm\_service.py} : Service d'analyse des QCM
    \end{itemize}
\end{itemize}

\subsection{Base de Données}
La base de données SQLite est utilisée pour stocker les informations sur les utilisateurs, les examens téléchargés et les résultats des corrections. Les principales tables sont :
\begin{itemize}
    \item \textbf{users} : Informations sur les utilisateurs
    \item \textbf{exams} : Informations sur les examens téléchargés
\end{itemize}

\chapter{Fonctionnalités Principales}

\section{Authentification et Autorisation}
Le système implémente une authentification basée sur JWT (JSON Web Tokens) pour sécuriser l'accès aux fonctionnalités. Les utilisateurs doivent s'authentifier pour accéder à certaines parties de l'application.

\subsection{Protection des Routes}
Un composant RouteGuard a été implémenté pour protéger les routes nécessitant une authentification. Ce composant vérifie si l'utilisateur est authentifié et le redirige vers la page de connexion si nécessaire.

\begin{lstlisting}[language=JavaScript, caption=Extrait du composant RouteGuard]
const RouteGuard: React.FC<RouteGuardProps> = ({ children }) => {
  const { isAuthenticated, isLoading } = useAuth();
  const router = useRouter();
  const pathname = usePathname();
  const [authorized, setAuthorized] = useState(false);

  useEffect(() => {
    // Fonction pour vérifier si l'utilisateur est autorisé à accéder à la page
    const checkAuth = () => {
      // Si le chemin est public, autoriser l'accès
      if (publicPaths.includes(pathname)) {
        setAuthorized(true);
        return;
      }

      // Si l'utilisateur n'est pas authentifié et que le chemin n'est pas public, rediriger vers la page de connexion
      if (!isAuthenticated) {
        setAuthorized(false);
        router.push('/login');
        return;
      }

      // Si l'utilisateur est authentifié, autoriser l'accès
      setAuthorized(true);
    };

    // Vérifier l'authentification lorsque le chemin change ou que l'état d'authentification change
    if (!isLoading) {
      checkAuth();
    }
  }, [isAuthenticated, isLoading, pathname, router]);

  // Afficher le contenu si l'utilisateur est autorisé
  return authorized ? <>{children}</> : null;
};
\end{lstlisting}

\section{Téléchargement et Traitement des Fichiers}
Le système permet aux utilisateurs de télécharger des images d'examens (manuscrits ou QCM) pour analyse. Le processus de téléchargement et de traitement est géré par le composant FileUpload.

\subsection{Processus de Téléchargement}
\begin{enumerate}
    \item L'utilisateur sélectionne un fichier à télécharger
    \item Le fichier est envoyé au backend via une requête POST à l'endpoint /api/upload/noauth
    \item Le backend génère un identifiant unique pour le fichier et le stocke dans le dossier uploads/
    \item Le backend crée une entrée dans la base de données pour suivre le statut du fichier
    \item L'identifiant du fichier est renvoyé au frontend
\end{enumerate}

\subsection{Processus de Correction}
\begin{enumerate}
    \item Le frontend envoie une requête POST à l'endpoint /api/grade/noauth avec l'identifiant du fichier
    \item Le backend lance un processus de correction en arrière-plan avec les étapes suivantes :
    \begin{enumerate}
        \item Prétraitement du document
        \item Extraction du texte par OCR
        \item Analyse du texte par IA
        \item Finalisation de la correction
    \end{enumerate}
    \item Le frontend affiche une barre de progression pour indiquer l'avancement du processus
    \item Une fois le processus terminé, les résultats sont affichés à l'utilisateur
\end{enumerate}

\section{Reconnaissance Optique de Caractères (OCR)}
Le système utilise Tesseract OCR pour extraire le texte des images d'examens. Le service OCR est implémenté dans le module ocr\_service.py.

\begin{lstlisting}[language=Python, caption=Extrait du service OCR]
def extract_text(image_path, mode="standard"):
    """
    Extrait le texte d'une image en utilisant Tesseract OCR
    """
    try:
        # Vérifier que le fichier existe
        if not os.path.exists(image_path):
            return {
                "success": False,
                "error": f"File not found: {image_path}"
            }
        
        # Charger l'image avec OpenCV
        image = cv2.imread(image_path)
        if image is None:
            return {
                "success": False,
                "error": f"Failed to load image: {image_path}"
            }
        
        # Prétraitement de l'image
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        thresh = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY_INV + cv2.THRESH_OTSU)[1]
        
        # Configuration de Tesseract
        config = "--oem 3 --psm 6"
        if mode == "structured":
            config = "--oem 3 --psm 4"  # Mode structuré pour les tableaux et formulaires
        
        # Extraction du texte
        text = pytesseract.image_to_string(thresh, config=config, lang="fra")
        
        return {
            "success": True,
            "extracted_text": text
        }
    except Exception as e:
        return {
            "success": False,
            "error": str(e)
        }
\end{lstlisting}

\section{Analyse par Intelligence Artificielle}
Le système utilise le modèle TinyLlama pour analyser le texte extrait et générer une évaluation. Le service d'analyse est implémenté dans les modules handwritten\_service.py et enhanced\_qcm\_service.py.

\subsection{Analyse des Examens Manuscrits}
Pour les examens manuscrits, le système :
\begin{enumerate}
    \item Extrait le texte de l'image avec OCR
    \item Analyse le texte avec TinyLlama en fonction d'une rubrique d'évaluation
    \item Génère une note, des commentaires par question et un commentaire général
\end{enumerate}

\subsection{Analyse des QCM}
Pour les QCM, le système :
\begin{enumerate}
    \item Détecte les cases cochées dans l'image
    \item Compare les réponses avec les réponses correctes
    \item Calcule un score et génère un feedback
\end{enumerate}

\section{Affichage des Résultats}
Les résultats de la correction sont affichés dans le composant GradingResult. Ce composant présente :
\begin{itemize}
    \item Le statut du processus de correction
    \item La note générée par l'IA
    \item Le texte extrait de l'image
    \item Les détails d'évaluation par question
\end{itemize}

\chapter{Aspects Techniques}

\section{Gestion des Erreurs}
Le système implémente une gestion robuste des erreurs à la fois côté frontend et backend :
\begin{itemize}
    \item \textbf{Frontend} : Utilisation de blocs try/catch pour capturer et afficher les erreurs
    \item \textbf{Backend} : Utilisation d'exceptions HTTP pour renvoyer des codes d'erreur appropriés
\end{itemize}

\section{Optimisation des Performances}
Plusieurs techniques sont utilisées pour optimiser les performances :
\begin{itemize}
    \item \textbf{Traitement asynchrone} : Les tâches longues comme l'OCR et l'analyse par IA sont exécutées en arrière-plan
    \item \textbf{Mise en cache} : Les résultats d'OCR sont stockés pour éviter de répéter l'extraction
    \item \textbf{Chargement paresseux} : Les composants et les images sont chargés à la demande
\end{itemize}

\section{Sécurité}
Le système implémente plusieurs mesures de sécurité :
\begin{itemize}
    \item \textbf{Authentification JWT} : Protection des routes et des API
    \item \textbf{Validation des entrées} : Utilisation de schémas Pydantic pour valider les données
    \item \textbf{Protection CSRF} : Mesures contre les attaques Cross-Site Request Forgery
    \item \textbf{Sanitisation des fichiers} : Vérification des types de fichiers téléchargés
\end{itemize}

\chapter{Conclusion}

\section{Résumé}
Auto-Grade Scribe est une application web moderne qui combine des technologies avancées d'OCR et d'IA pour automatiser la correction d'examens. Le système offre une interface utilisateur intuitive, un traitement robuste des fichiers et une analyse précise des réponses.

\section{Perspectives d'Évolution}
Plusieurs améliorations sont envisagées pour les versions futures :
\begin{itemize}
    \item \textbf{Support de formats supplémentaires} : PDF multi-pages, documents Word, etc.
    \item \textbf{Amélioration de l'OCR} : Utilisation de modèles de deep learning pour améliorer la précision
    \item \textbf{Intégration avec des LMS} : Connexion avec des systèmes de gestion d'apprentissage comme Moodle
    \item \textbf{Analyse statistique} : Outils d'analyse des performances des étudiants
    \item \textbf{Interface mobile} : Application mobile pour les enseignants et les étudiants
\end{itemize}

\section{Remerciements}
Nous tenons à remercier tous ceux qui ont contribué à ce projet, notamment :
\begin{itemize}
    \item Les enseignants et tuteurs pour leur guidance
    \item Les testeurs qui ont fourni des retours précieux
    \item Les communautés open-source des technologies utilisées
\end{itemize}

\end{document}
