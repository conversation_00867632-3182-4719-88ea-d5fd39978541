#!/usr/bin/env python3
"""
Test script pour vérifier la configuration PostgreSQL d'Auto-Grade Scribe
"""

import sys
import os
from pathlib import Path
import subprocess

def test_postgres_connection():
    """Test de connexion PostgreSQL"""
    print("🐘 Test de connexion PostgreSQL...")
    
    try:
        # Ajouter le backend au path
        backend_path = Path(__file__).parent / "backend"
        sys.path.insert(0, str(backend_path))
        
        # Importer la configuration de base de données
        from database import DATABASE_URL, engine
        
        print(f"  📍 URL de connexion: {DATABASE_URL}")
        
        # Tester la connexion
        connection = engine.connect()
        
        # Exécuter une requête simple
        result = connection.execute("SELECT version();")
        version = result.fetchone()[0]
        print(f"  ✅ PostgreSQL connecté: {version}")
        
        connection.close()
        return True
        
    except Exception as e:
        print(f"  ❌ Erreur de connexion PostgreSQL: {e}")
        print("  💡 Suggestions:")
        print("     - Vérifiez que PostgreSQL est démarré")
        print("     - Vérifiez les paramètres de connexion dans .env")
        print("     - Exécutez: .\\setup_postgres_local.ps1")
        return False

def test_postgres_models():
    """Test des modèles avec PostgreSQL"""
    print("\n📊 Test des modèles PostgreSQL...")
    
    try:
        backend_path = Path(__file__).parent / "backend"
        sys.path.insert(0, str(backend_path))
        
        from database import engine, Base
        from models_simple import User, Exam, Student, ExamResult, AuditLog
        
        # Créer les tables
        Base.metadata.create_all(bind=engine)
        print("  ✅ Tables PostgreSQL créées avec succès")
        
        # Test d'insertion simple
        from sqlalchemy.orm import sessionmaker
        SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
        db = SessionLocal()
        
        try:
            # Compter les utilisateurs existants
            user_count = db.query(User).count()
            print(f"  ✅ Requête réussie: {user_count} utilisateurs dans la base")
            
            return True
            
        finally:
            db.close()
        
    except Exception as e:
        print(f"  ❌ Erreur avec les modèles PostgreSQL: {e}")
        return False

def test_postgres_performance():
    """Test de performance PostgreSQL"""
    print("\n⚡ Test de performance PostgreSQL...")
    
    try:
        backend_path = Path(__file__).parent / "backend"
        sys.path.insert(0, str(backend_path))
        
        from database import engine
        import time
        
        # Test de performance simple
        start_time = time.time()
        
        connection = engine.connect()
        
        # Exécuter plusieurs requêtes
        for i in range(10):
            result = connection.execute("SELECT 1;")
            result.fetchone()
        
        end_time = time.time()
        duration = (end_time - start_time) * 1000  # en millisecondes
        
        connection.close()
        
        print(f"  ✅ 10 requêtes exécutées en {duration:.2f}ms")
        
        if duration < 1000:  # moins d'une seconde
            print("  🚀 Performance excellente!")
        else:
            print("  ⚠️ Performance acceptable mais pourrait être améliorée")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Erreur de test de performance: {e}")
        return False

def test_postgres_features():
    """Test des fonctionnalités spécifiques à PostgreSQL"""
    print("\n🔧 Test des fonctionnalités PostgreSQL...")
    
    try:
        backend_path = Path(__file__).parent / "backend"
        sys.path.insert(0, str(backend_path))
        
        from database import engine
        
        connection = engine.connect()
        
        # Test JSON support
        try:
            connection.execute("SELECT '{\"test\": \"value\"}'::json;")
            print("  ✅ Support JSON activé")
        except:
            print("  ⚠️ Support JSON limité")
        
        # Test des extensions
        try:
            result = connection.execute("SELECT extname FROM pg_extension;")
            extensions = [row[0] for row in result.fetchall()]
            print(f"  ✅ Extensions disponibles: {', '.join(extensions)}")
        except:
            print("  ⚠️ Impossible de lister les extensions")
        
        # Test de la version
        try:
            result = connection.execute("SHOW server_version;")
            version = result.fetchone()[0]
            print(f"  ✅ Version PostgreSQL: {version}")
        except:
            print("  ⚠️ Impossible de récupérer la version")
        
        connection.close()
        return True
        
    except Exception as e:
        print(f"  ❌ Erreur de test des fonctionnalités: {e}")
        return False

def check_postgres_service():
    """Vérifier si le service PostgreSQL est démarré"""
    print("\n🔍 Vérification du service PostgreSQL...")
    
    try:
        # Essayer de se connecter avec psql
        result = subprocess.run(
            ["psql", "--version"], 
            capture_output=True, 
            text=True, 
            timeout=5
        )
        
        if result.returncode == 0:
            print(f"  ✅ psql disponible: {result.stdout.strip()}")
        else:
            print("  ⚠️ psql non trouvé dans le PATH")
        
        # Tester la connexion directe
        result = subprocess.run(
            ["psql", "-h", "localhost", "-p", "5432", "-U", "autograde", "-d", "gradegeniusdb", "-c", "SELECT 1;"],
            capture_output=True,
            text=True,
            timeout=10,
            env={**os.environ, "PGPASSWORD": "autograde123"}
        )
        
        if result.returncode == 0:
            print("  ✅ Service PostgreSQL accessible")
            return True
        else:
            print("  ❌ Service PostgreSQL non accessible")
            print(f"  Erreur: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        print("  ❌ Timeout lors de la connexion PostgreSQL")
        return False
    except FileNotFoundError:
        print("  ⚠️ psql non installé ou non dans le PATH")
        return False
    except Exception as e:
        print(f"  ❌ Erreur lors de la vérification: {e}")
        return False

def create_postgres_env():
    """Créer un fichier .env pour PostgreSQL"""
    print("\n⚙️ Configuration .env pour PostgreSQL...")
    
    env_content = """# Configuration PostgreSQL pour Auto-Grade Scribe
ENVIRONMENT=development
DEBUG=true

# Database PostgreSQL
DATABASE_URL=postgresql://autograde:autograde123@localhost:5432/gradegeniusdb

# Security
SECRET_KEY=your-secret-key-change-in-production
JWT_ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=60

# API Configuration
API_HOST=0.0.0.0
API_PORT=8000
CORS_ORIGINS=http://localhost:3000,http://127.0.0.1:3000

# File Upload
MAX_FILE_SIZE=52428800
UPLOAD_DIRECTORY=uploads
RESULTS_DIRECTORY=results
TEMP_DIRECTORY=temp

# OCR Configuration
TESSERACT_TIMEOUT=300
OCR_CONFIDENCE_THRESHOLD=0.6

# AI Configuration (ajoutez votre clé API Google ici)
# GOOGLE_API_KEY=your-google-api-key-here
GEMINI_MODEL=gemini-pro-vision
GEMINI_TIMEOUT=60

# Logging
LOG_LEVEL=INFO
LOG_FILE=logs/app.log

# Database Connection Pool
DATABASE_POOL_SIZE=10
DATABASE_MAX_OVERFLOW=20
DATABASE_POOL_TIMEOUT=30
"""
    
    try:
        with open(".env", "w", encoding="utf-8") as f:
            f.write(env_content)
        print("  ✅ Fichier .env créé pour PostgreSQL")
        return True
    except Exception as e:
        print(f"  ❌ Erreur lors de la création du .env: {e}")
        return False

def main():
    """Exécuter tous les tests PostgreSQL"""
    print("🐘 Test de Configuration PostgreSQL pour Auto-Grade Scribe")
    print("=" * 60)
    
    tests = [
        ("Service PostgreSQL", check_postgres_service),
        ("Configuration .env", create_postgres_env),
        ("Connexion PostgreSQL", test_postgres_connection),
        ("Modèles PostgreSQL", test_postgres_models),
        ("Performance PostgreSQL", test_postgres_performance),
        ("Fonctionnalités PostgreSQL", test_postgres_features)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"  ❌ {test_name} a échoué: {e}")
    
    print("\n" + "=" * 60)
    print(f"📊 Résultats: {passed}/{total} tests réussis")
    
    if passed == total:
        print("🎉 Configuration PostgreSQL réussie!")
        print("\n🚀 Pour démarrer l'application:")
        print("   cd backend")
        print("   python -m uvicorn app:app --host 0.0.0.0 --port 8000 --reload")
        print("\n📚 Accès:")
        print("   - API: http://localhost:8000")
        print("   - Documentation: http://localhost:8000/docs")
        print("   - Santé: http://localhost:8000/health")
    else:
        print("⚠️ Certains tests ont échoué.")
        print("\n🔧 Solutions:")
        print("   1. Installez PostgreSQL: https://www.postgresql.org/download/")
        print("   2. Exécutez: .\\setup_postgres_local.ps1")
        print("   3. Ou utilisez Docker: docker-compose -f docker-compose.postgres.yml up -d")
        print("   4. Vérifiez que le service PostgreSQL est démarré")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
