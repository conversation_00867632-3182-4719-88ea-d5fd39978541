# Script pour configurer PostgreSQL localement pour Auto-Grade Scribe
Write-Host "🐘 Configuration PostgreSQL pour Auto-Grade Scribe" -ForegroundColor Green

# Vérifier si PostgreSQL est installé
try {
    $pgVersion = psql --version 2>&1
    Write-Host "✅ PostgreSQL trouvé: $pgVersion" -ForegroundColor Green
    $pgInstalled = $true
} catch {
    Write-Host "❌ PostgreSQL non trouvé" -ForegroundColor Red
    $pgInstalled = $false
}

if (-not $pgInstalled) {
    Write-Host "📥 Installation de PostgreSQL..." -ForegroundColor Yellow
    Write-Host "Veuillez télécharger et installer PostgreSQL depuis:" -ForegroundColor Cyan
    Write-Host "https://www.postgresql.org/download/windows/" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "Ou utilisez Chocolatey:" -ForegroundColor Cyan
    Write-Host "choco install postgresql" -ForegroundColor White
    Write-Host ""
    Write-Host "Ou utilisez winget:" -ForegroundColor Cyan
    Write-Host "winget install PostgreSQL.PostgreSQL" -ForegroundColor White
    Write-Host ""
    
    $response = Read-Host "Avez-vous installé PostgreSQL? (y/n)"
    if ($response -ne 'y') {
        Write-Host "❌ Installation annulée. Veuillez installer PostgreSQL d'abord." -ForegroundColor Red
        exit 1
    }
}

# Configuration de la base de données
Write-Host "🔧 Configuration de la base de données..." -ForegroundColor Yellow

$dbName = "gradegeniusdb"
$dbUser = "autograde"
$dbPassword = "autograde123"
$dbHost = "localhost"
$dbPort = "5432"

# Créer l'utilisateur et la base de données
Write-Host "📝 Création de l'utilisateur et de la base de données..." -ForegroundColor Yellow

$createUserSQL = @"
-- Créer l'utilisateur
CREATE USER $dbUser WITH PASSWORD '$dbPassword';

-- Créer la base de données
CREATE DATABASE $dbName OWNER $dbUser;

-- Donner tous les privilèges
GRANT ALL PRIVILEGES ON DATABASE $dbName TO $dbUser;

-- Se connecter à la base de données
\c $dbName

-- Donner les privilèges sur le schéma
GRANT ALL ON SCHEMA public TO $dbUser;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO $dbUser;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO $dbUser;

-- Permettre la création de tables
ALTER USER $dbUser CREATEDB;
"@

# Sauvegarder le script SQL
$createUserSQL | Out-File -FilePath "create_db.sql" -Encoding UTF8

Write-Host "Exécution du script de création..." -ForegroundColor Yellow
Write-Host "Vous devrez peut-être entrer le mot de passe de l'utilisateur postgres" -ForegroundColor Cyan

try {
    # Essayer de se connecter en tant que postgres
    psql -U postgres -f create_db.sql
    Write-Host "✅ Base de données créée avec succès" -ForegroundColor Green
} catch {
    Write-Host "⚠️ Erreur lors de la création automatique" -ForegroundColor Yellow
    Write-Host "Veuillez exécuter manuellement:" -ForegroundColor Cyan
    Write-Host "psql -U postgres -f create_db.sql" -ForegroundColor White
}

# Créer le fichier .env avec PostgreSQL
Write-Host "⚙️ Configuration du fichier .env..." -ForegroundColor Yellow

$envContent = @"
# Environment
ENVIRONMENT=development
DEBUG=true

# Database PostgreSQL
DATABASE_URL=postgresql://$dbUser`:$dbPassword@$dbHost`:$dbPort/$dbName

# Security
SECRET_KEY=your-secret-key-change-in-production
JWT_ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=60

# API Configuration
API_HOST=0.0.0.0
API_PORT=8000
CORS_ORIGINS=http://localhost:3000,http://127.0.0.1:3000

# File Upload
MAX_FILE_SIZE=52428800
UPLOAD_DIRECTORY=uploads
RESULTS_DIRECTORY=results
TEMP_DIRECTORY=temp

# OCR Configuration
TESSERACT_TIMEOUT=300
OCR_CONFIDENCE_THRESHOLD=0.6

# AI Configuration (ajoutez votre clé API Google ici)
# GOOGLE_API_KEY=your-google-api-key-here
GEMINI_MODEL=gemini-pro-vision
GEMINI_TIMEOUT=60

# Logging
LOG_LEVEL=INFO
LOG_FILE=logs/app.log
LOG_MAX_SIZE=10485760

# Database Connection Pool
DATABASE_POOL_SIZE=10
DATABASE_MAX_OVERFLOW=20
DATABASE_POOL_TIMEOUT=30
"@

$envContent | Out-File -FilePath ".env" -Encoding UTF8
Write-Host "✅ Fichier .env créé avec PostgreSQL" -ForegroundColor Green

# Tester la connexion
Write-Host "🧪 Test de connexion à la base de données..." -ForegroundColor Yellow

try {
    $connectionString = "postgresql://$dbUser`:$dbPassword@$dbHost`:$dbPort/$dbName"
    psql $connectionString -c "SELECT version();"
    Write-Host "✅ Connexion PostgreSQL réussie!" -ForegroundColor Green
} catch {
    Write-Host "❌ Erreur de connexion PostgreSQL" -ForegroundColor Red
    Write-Host "Vérifiez que PostgreSQL est démarré et que les paramètres sont corrects" -ForegroundColor Yellow
}

# Nettoyer le fichier temporaire
Remove-Item "create_db.sql" -ErrorAction SilentlyContinue

Write-Host ""
Write-Host "🎉 Configuration PostgreSQL terminée!" -ForegroundColor Green
Write-Host ""
Write-Host "📋 Informations de connexion:" -ForegroundColor Cyan
Write-Host "  Host: $dbHost" -ForegroundColor White
Write-Host "  Port: $dbPort" -ForegroundColor White
Write-Host "  Database: $dbName" -ForegroundColor White
Write-Host "  User: $dbUser" -ForegroundColor White
Write-Host "  Password: $dbPassword" -ForegroundColor White
Write-Host ""
Write-Host "🚀 Pour démarrer l'application:" -ForegroundColor Cyan
Write-Host "  cd backend" -ForegroundColor White
Write-Host "  python -m uvicorn app:app --host 0.0.0.0 --port 8000 --reload" -ForegroundColor White
Write-Host ""
Write-Host "🌐 Accès:" -ForegroundColor Cyan
Write-Host "  API: http://localhost:8000" -ForegroundColor White
Write-Host "  Documentation: http://localhost:8000/docs" -ForegroundColor White
