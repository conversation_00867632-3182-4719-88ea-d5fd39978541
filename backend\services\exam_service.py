"""
Enhanced Exam Service for Auto-Grade Scribe
Provides comprehensive exam processing, grading, and management functionality
"""

import os
import hashlib
import logging
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, desc
from fastapi import HTTPException, status
import uuid

from models import (
    Exam, ExamResult, Student, ProcessingLog, GradeHistory, 
    ExamStatus, ExamType, GradeStatus, User
)
from services.ocr_service import EnhancedOCRService
from services.grading_service import EnhancedGradingService
from services.audit_service import AuditService
from utils.file_utils import FileUtils
from utils.validation import ValidationUtils

logger = logging.getLogger("auto-grade-scribe.exam-service")

class ExamService:
    """Enhanced service for exam processing and management"""
    
    def __init__(self):
        self.ocr_service = EnhancedOCRService()
        self.grading_service = EnhancedGradingService()
        self.audit_service = AuditService()
        self.file_utils = FileUtils()
        self.validation_utils = ValidationUtils()
    
    async def create_exam(
        self, 
        db: Session, 
        user_id: int, 
        file_data: bytes, 
        filename: str,
        exam_type: ExamType = ExamType.QCM,
        exam_name: Optional[str] = None,
        subject: Optional[str] = None,
        class_name: Optional[str] = None,
        academic_year: Optional[str] = None,
        processing_config: Optional[Dict[str, Any]] = None
    ) -> Exam:
        """
        Create a new exam with enhanced validation and processing
        
        Args:
            db: Database session
            user_id: ID of the user creating the exam
            file_data: Binary file data
            filename: Original filename
            exam_type: Type of exam (QCM, HANDWRITTEN, etc.)
            exam_name: Optional name for the exam
            subject: Subject of the exam
            class_name: Class name
            academic_year: Academic year
            processing_config: Configuration for processing
            
        Returns:
            Created Exam object
        """
        try:
            # Validate file
            validation_result = self.validation_utils.validate_exam_file(file_data, filename)
            if not validation_result["valid"]:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"Invalid file: {validation_result['error']}"
                )
            
            # Generate unique exam ID and file path
            exam_id = str(uuid.uuid4())
            file_extension = os.path.splitext(filename)[1]
            unique_filename = f"{exam_id}{file_extension}"
            file_path = os.path.join("uploads", unique_filename)
            
            # Calculate file hash for integrity
            file_hash = hashlib.sha256(file_data).hexdigest()
            
            # Save file
            os.makedirs(os.path.dirname(file_path), exist_ok=True)
            with open(file_path, "wb") as f:
                f.write(file_data)
            
            # Create exam record
            exam = Exam(
                id=exam_id,
                user_id=user_id,
                original_filename=filename,
                file_path=file_path,
                file_size=len(file_data),
                file_hash=file_hash,
                exam_name=exam_name or filename,
                exam_type=exam_type,
                subject=subject,
                class_name=class_name,
                academic_year=academic_year,
                status=ExamStatus.UPLOADED,
                processing_config=processing_config or {},
                metadata={
                    "upload_ip": None,  # Will be set by the API endpoint
                    "upload_user_agent": None,  # Will be set by the API endpoint
                    "file_validation": validation_result
                }
            )
            
            db.add(exam)
            db.commit()
            db.refresh(exam)
            
            # Log the creation
            await self._log_processing_step(
                db, exam_id, "upload", "completed", 
                success=True, 
                output_data={"file_size": len(file_data), "file_hash": file_hash}
            )
            
            # Audit log
            await self.audit_service.log_action(
                db, user_id, "create", "exam", exam_id,
                new_values={"filename": filename, "exam_type": exam_type.value}
            )
            
            logger.info(f"Created exam {exam_id} for user {user_id}")
            return exam
            
        except Exception as e:
            logger.error(f"Error creating exam: {str(e)}")
            # Clean up file if it was created
            if 'file_path' in locals() and os.path.exists(file_path):
                os.remove(file_path)
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to create exam: {str(e)}"
            )
    
    async def process_exam(
        self, 
        db: Session, 
        exam_id: str, 
        user_id: int,
        force_reprocess: bool = False
    ) -> Dict[str, Any]:
        """
        Process an exam through OCR and initial analysis
        
        Args:
            db: Database session
            exam_id: ID of the exam to process
            user_id: ID of the user requesting processing
            force_reprocess: Whether to force reprocessing if already processed
            
        Returns:
            Processing results
        """
        try:
            # Get exam
            exam = db.query(Exam).filter(Exam.id == exam_id).first()
            if not exam:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Exam not found"
                )
            
            # Check if already processed
            if exam.status in [ExamStatus.PROCESSED, ExamStatus.GRADED, ExamStatus.COMPLETED] and not force_reprocess:
                return {
                    "success": True,
                    "message": "Exam already processed",
                    "exam_id": exam_id,
                    "status": exam.status.value
                }
            
            # Update status to processing
            exam.status = ExamStatus.PROCESSING
            exam.processing_started_at = datetime.utcnow()
            db.commit()
            
            # Log processing start
            await self._log_processing_step(
                db, exam_id, "ocr", "started", 
                success=True,
                input_data={"exam_type": exam.exam_type.value, "force_reprocess": force_reprocess}
            )
            
            # Perform OCR
            ocr_result = await self.ocr_service.extract_text_enhanced(
                exam.file_path, 
                exam.exam_type,
                exam.processing_config.get("ocr_settings", {})
            )
            
            if not ocr_result["success"]:
                exam.status = ExamStatus.ERROR
                exam.error_message = ocr_result.get("error", "OCR processing failed")
                db.commit()
                
                await self._log_processing_step(
                    db, exam_id, "ocr", "failed", 
                    success=False,
                    error_message=ocr_result.get("error")
                )
                
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail=f"OCR processing failed: {ocr_result.get('error')}"
                )
            
            # Update exam with OCR results
            exam.extracted_text = ocr_result.get("extracted_text", "")
            exam.ocr_confidence = ocr_result.get("confidence", 0.0)
            exam.ocr_model_used = ocr_result.get("model", "unknown")
            exam.status = ExamStatus.PROCESSED
            exam.processing_completed_at = datetime.utcnow()
            
            db.commit()
            
            # Log successful OCR
            await self._log_processing_step(
                db, exam_id, "ocr", "completed", 
                success=True,
                output_data={
                    "extracted_text_length": len(exam.extracted_text),
                    "confidence": exam.ocr_confidence,
                    "model": exam.ocr_model_used
                }
            )
            
            # Audit log
            await self.audit_service.log_action(
                db, user_id, "process", "exam", exam_id,
                new_values={"status": exam.status.value, "ocr_model": exam.ocr_model_used}
            )
            
            logger.info(f"Successfully processed exam {exam_id}")
            
            return {
                "success": True,
                "exam_id": exam_id,
                "status": exam.status.value,
                "extracted_text": exam.extracted_text,
                "student_name": ocr_result.get("student_name", "Non détecté"),
                "student_id": ocr_result.get("student_id", ""),
                "extracted_answers": ocr_result.get("extracted_answers", {}),
                "confidence": exam.ocr_confidence,
                "model": exam.ocr_model_used
            }
            
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error processing exam {exam_id}: {str(e)}")
            
            # Update exam status to error
            if 'exam' in locals():
                exam.status = ExamStatus.ERROR
                exam.error_message = str(e)
                db.commit()
            
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to process exam: {str(e)}"
            )
    
    async def _log_processing_step(
        self,
        db: Session,
        exam_id: str,
        step_name: str,
        step_status: str,
        success: bool,
        input_data: Optional[Dict[str, Any]] = None,
        output_data: Optional[Dict[str, Any]] = None,
        error_message: Optional[str] = None,
        error_code: Optional[str] = None
    ):
        """Log a processing step"""
        try:
            log_entry = ProcessingLog(
                exam_id=exam_id,
                step_name=step_name,
                step_status=step_status,
                started_at=datetime.utcnow(),
                completed_at=datetime.utcnow() if step_status in ["completed", "failed"] else None,
                success=success,
                error_message=error_message,
                error_code=error_code,
                input_data=input_data,
                output_data=output_data,
                server_info={
                    "timestamp": datetime.utcnow().isoformat(),
                    "step": step_name
                }
            )
            
            db.add(log_entry)
            db.commit()
            
        except Exception as e:
            logger.error(f"Failed to log processing step: {str(e)}")
    
    def get_exam_by_id(self, db: Session, exam_id: str, user_id: Optional[int] = None) -> Optional[Exam]:
        """Get exam by ID with optional user filtering"""
        query = db.query(Exam).filter(Exam.id == exam_id)
        if user_id:
            query = query.filter(Exam.user_id == user_id)
        return query.first()
    
    def get_exams_by_user(
        self, 
        db: Session, 
        user_id: int, 
        skip: int = 0, 
        limit: int = 100,
        status_filter: Optional[ExamStatus] = None,
        exam_type_filter: Optional[ExamType] = None
    ) -> List[Exam]:
        """Get exams for a specific user with filtering"""
        query = db.query(Exam).filter(Exam.user_id == user_id)
        
        if status_filter:
            query = query.filter(Exam.status == status_filter)
        
        if exam_type_filter:
            query = query.filter(Exam.exam_type == exam_type_filter)
        
        return query.order_by(desc(Exam.upload_time)).offset(skip).limit(limit).all()
    
    async def delete_exam(self, db: Session, exam_id: str, user_id: int) -> bool:
        """Delete an exam and all associated data"""
        try:
            exam = self.get_exam_by_id(db, exam_id, user_id)
            if not exam:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Exam not found"
                )
            
            # Delete file
            if os.path.exists(exam.file_path):
                os.remove(exam.file_path)
            
            # Audit log before deletion
            await self.audit_service.log_action(
                db, user_id, "delete", "exam", exam_id,
                old_values={"filename": exam.original_filename, "status": exam.status.value}
            )
            
            # Delete from database (cascade will handle related records)
            db.delete(exam)
            db.commit()
            
            logger.info(f"Deleted exam {exam_id} for user {user_id}")
            return True
            
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error deleting exam {exam_id}: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to delete exam: {str(e)}"
            )

# Create singleton instance
exam_service = ExamService()
