try:
    from jose.backends.cryptography_backend import get_random_bytes  # noqa: F401
except ImportError:
    try:
        from jose.backends.pycrypto_backend import get_random_bytes  # noqa: F401
    except ImportError:
        from jose.backends.native import get_random_bytes  # noqa: F401

try:
    from jose.backends.cryptography_backend import Crypt<PERSON><PERSON><PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON>  # noqa: F401
except ImportError:
    try:
        from jose.backends.rsa_backend import RSAKey  # noqa: F401
    except ImportError:
        RSAKey = None

try:
    from jose.backends.cryptography_backend import CryptographyECK<PERSON> as <PERSON><PERSON><PERSON>  # noqa: F401
except ImportError:
    from jose.backends.ecdsa_backend import EC<PERSON>A<PERSON><PERSON><PERSON> as <PERSON>Key  # noqa: F401

try:
    from jose.backends.cryptography_backend import CryptographyAES<PERSON>ey as A<PERSON>Key  # noqa: F401
except ImportError:
    AESKey = None

try:
    from jose.backends.cryptography_backend import Cryptography<PERSON><PERSON><PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON>  # noqa: F401
except ImportError:
    from jose.backends.native import HM<PERSON><PERSON>ey  # noqa: F401

from .base import DIRKey  # noqa: F401
