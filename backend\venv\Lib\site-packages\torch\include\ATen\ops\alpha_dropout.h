#pragma once

// @generated by torchgen/gen.py from Function.h

#include <ATen/Context.h>
#include <ATen/DeviceGuard.h>
#include <ATen/TensorUtils.h>
#include <ATen/TracerMode.h>
#include <ATen/core/Generator.h>
#include <ATen/core/Reduction.h>
#include <ATen/core/Tensor.h>
#include <c10/core/Scalar.h>
#include <c10/core/Storage.h>
#include <c10/core/TensorOptions.h>
#include <c10/util/Deprecated.h>
#include <c10/util/Optional.h>



#include <ATen/ops/alpha_dropout_ops.h>

namespace at {


// aten::alpha_dropout(Tensor input, float p, bool train) -> Tensor
inline at::Tensor alpha_dropout(const at::Tensor & input, double p, bool train) {
    return at::_ops::alpha_dropout::call(input, p, train);
}

// aten::alpha_dropout_(Tensor(a!) self, float p, bool train) -> Tensor(a!)
inline at::Tensor & alpha_dropout_(at::Tensor & self, double p, bool train) {
    return at::_ops::alpha_dropout_::call(self, p, train);
}

}
