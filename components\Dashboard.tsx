"use client"

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import FileUpload from './FileUpload';
import GradingResult from './GradingResult';
import { Upload, BarChart3, History, Settings, FileText } from 'lucide-react';

type TabType = 'options' | 'handwritten' | 'results' | 'history' | 'materials' | 'settings';

const Dashboard: React.FC = () => {
  const [activeTab, setActiveTab] = useState<TabType>("options");
  const router = useRouter();

  // Gestionnaire d'erreurs global
  const handleError = (error: any) => {
    console.error('Dashboard error:', error);
    // Ici vous pouvez ajouter une notification d'erreur UI si nécessaire
  };

  // Rendu sécurisé du contenu de l'onglet
  const renderTabContent = () => {
    try {
      switch (activeTab) {
        case "options":
          return (
            <div className="space-y-6">
              <h2 className="text-2xl font-bold">Choisissez un type d'examen</h2>
              <p className="text-muted-foreground">
                Sélectionnez le type d'examen que vous souhaitez corriger
              </p>

              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mt-4">
                <Button
                  variant="outline"
                  className="h-32 flex flex-col items-center justify-center gap-3 border-2 hover:border-primary hover:bg-primary/5"
                  onClick={() => router.push('/qcm')}
                >
                  <div className="p-3 bg-secondary rounded-full">
                    <FileText className="h-6 w-6 text-primary" />
                  </div>
                  <span className="font-medium text-lg">QCM</span>
                  <span className="text-xs text-muted-foreground text-center">
                    Correction automatique des questionnaires à choix multiples
                  </span>
                </Button>

                <Button
                  variant="outline"
                  className="h-32 flex flex-col items-center justify-center gap-3 border-2 hover:border-primary hover:bg-primary/5"
                  onClick={() => setActiveTab('handwritten')}
                >
                  <div className="p-3 bg-secondary rounded-full">
                    <Upload className="h-6 w-6 text-primary" />
                  </div>
                  <span className="font-medium text-lg">Handwritten</span>
                  <span className="text-xs text-muted-foreground text-center">
                    Correction des réponses manuscrites et des dissertations
                  </span>
                </Button>
              </div>
            </div>
          );
        case "handwritten":
          return (
            <div className="space-y-4">
              <div className="flex items-center gap-2 mb-6">
                <Button
                  variant="ghost"
                  size="sm"
                  className="flex items-center gap-1"
                  onClick={() => setActiveTab('options')}
                >
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-arrow-left"><path d="m12 19-7-7 7-7"/><path d="M19 12H5"/></svg>
                  <span>Retour</span>
                </Button>
                <h2 className="text-2xl font-bold">Correction d'examen manuscrit</h2>
              </div>
              <FileUpload onUploadComplete={(resultId) => {
                console.log('Upload complete:', resultId);
                setActiveTab('results');
              }} />
            </div>
          );
        case "results":
          return <GradingResult />;
        default:
          return (
            <div className="p-4 text-center text-muted-foreground">
              Contenu en cours de développement
            </div>
          );
      }
    } catch (error) {
      handleError(error);
      return (
        <div className="p-4 text-center text-red-500">
          Une erreur est survenue lors du chargement du contenu
        </div>
      );
    }
  };

  return (
    <div className="container mx-auto py-6 max-w-7xl">
      <div className="mb-8">
        <h1 className="text-3xl font-bold gradient-heading">Bienvenue sur Auto-Grade Scribe</h1>
        <p className="text-muted-foreground mt-2">
          Système de notation automatique des examens avec analyse détaillée
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-[240px_1fr] gap-6">
        {/* Sidebar Navigation */}
        <div className="hidden md:block">
          <Card>
            <CardContent className="p-0">
              <nav className="flex flex-col p-2 space-y-1">
                {[
                  { id: "options", icon: Upload, label: "Upload Exams" },
                  { id: "results", icon: BarChart3, label: "Results" },
                  { id: "history", icon: History, label: "History" },
                  { id: "materials", icon: FileText, label: "Materials" },
                  { id: "settings", icon: Settings, label: "Settings" }
                ].map(({ id, icon: Icon, label }) => (
                  <button
                    key={id}
                    onClick={() => setActiveTab(id as TabType)}
                    className={`flex items-center gap-3 px-4 py-2 rounded-md text-left transition-colors ${
                      activeTab === id
                        ? "bg-primary text-primary-foreground"
                        : "hover:bg-secondary"
                    }`}
                  >
                    <Icon size={18} />
                    <span>{label}</span>
                  </button>
                ))}
              </nav>
            </CardContent>
          </Card>
        </div>

        {/* Main Content */}
        <div className="flex-1">
          <Card>
            <CardContent className="p-6">
              {renderTabContent()}
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
