import os
import logging
import google.generativeai as genai
from typing import Dict, Any, List, Optional
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Configure logging
logger = logging.getLogger("auto-grade-scribe.ai.gemini")

class GeminiService:
    def __init__(self):
        """Initialize the Gemini service with API key from environment variables"""
        self.api_key = os.getenv("GOOGLE_API_KEY")
        
        if not self.api_key:
            logger.warning("GOOGLE_API_KEY not found in environment variables. Gemini service will not work.")
            self.is_available = False
        else:
            try:
                # Configure the Gemini API
                genai.configure(api_key=self.api_key)
                
                # Get available models
                self.models = [m for m in genai.list_models() if 'generateContent' in m.supported_generation_methods]
                
                # Use the Gemini Pro Vision model for image analysis
                self.vision_model = genai.GenerativeModel('gemini-pro-vision')
                
                # Use the Gemini Pro model for text analysis
                self.text_model = genai.GenerativeModel('gemini-pro')
                
                self.is_available = True
                logger.info(f"Gemini service initialized successfully with models: {[m.name for m in self.models]}")
            except Exception as e:
                logger.error(f"Failed to initialize Gemini service: {str(e)}")
                self.is_available = False
    
    def analyze_handwritten_text(self, image_path: str) -> Dict[str, Any]:
        """
        Use Gemini Vision to analyze handwritten text in an image
        
        Args:
            image_path: Path to the image file
            
        Returns:
            Dictionary with extracted text and analysis
        """
        if not self.is_available:
            return {"success": False, "error": "Gemini service not available"}
        
        try:
            # Load the image
            with open(image_path, 'rb') as f:
                image_data = f.read()
            
            # Create a prompt for handwritten text extraction
            prompt = """
            Analyze this image of a handwritten exam paper. 
            Extract all text content, paying special attention to:
            1. Student name and ID if present
            2. Question numbers and answers
            3. Any marked checkboxes or selections
            
            Format the output as structured text, preserving the layout as much as possible.
            If there are multiple choice questions, clearly indicate which option was selected.
            """
            
            # Generate content with the image
            response = self.vision_model.generate_content([prompt, image_data])
            
            # Extract the text from the response
            extracted_text = response.text
            
            # Process the extracted text to identify student info and answers
            processed_result = self._process_extracted_text(extracted_text)
            
            return {
                "success": True,
                "extracted_text": extracted_text,
                "student_name": processed_result.get("student_name", "Non détecté"),
                "student_id": processed_result.get("student_id", ""),
                "extracted_answers": processed_result.get("answers", {}),
                "model": "gemini-vision"
            }
            
        except Exception as e:
            logger.error(f"Error analyzing handwritten text with Gemini: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "model": "gemini-vision"
            }
    
    def grade_exam(self, extracted_text: str, correct_answers: Dict[str, str]) -> Dict[str, Any]:
        """
        Use Gemini to grade an exam based on extracted text and correct answers
        
        Args:
            extracted_text: Text extracted from the exam
            correct_answers: Dictionary of correct answers (question_number -> answer)
            
        Returns:
            Dictionary with grading results
        """
        if not self.is_available:
            return {"success": False, "error": "Gemini service not available"}
        
        try:
            # Create a prompt for grading
            prompt = f"""
            Grade this exam based on the extracted text and the correct answers.
            
            Extracted text:
            {extracted_text}
            
            Correct answers:
            {correct_answers}
            
            For each question, determine if the student's answer matches the correct answer.
            Calculate the total score and percentage.
            Provide detailed feedback for each question.
            """
            
            # Generate content
            response = self.text_model.generate_content(prompt)
            
            # Process the grading results
            grading_text = response.text
            
            # Parse the grading text to extract score and feedback
            grading_result = self._parse_grading_results(grading_text, correct_answers)
            
            return {
                "success": True,
                "grade_summary": grading_result.get("summary", {}),
                "details": grading_result.get("details", {}),
                "model": "gemini-pro"
            }
            
        except Exception as e:
            logger.error(f"Error grading exam with Gemini: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "model": "gemini-pro"
            }
    
    def _process_extracted_text(self, text: str) -> Dict[str, Any]:
        """
        Process extracted text to identify student info and answers
        
        Args:
            text: Extracted text from the image
            
        Returns:
            Dictionary with processed information
        """
        try:
            # Use Gemini to extract structured information
            prompt = f"""
            Extract structured information from this text extracted from an exam paper:
            
            {text}
            
            Please identify and return:
            1. Student name (if present)
            2. Student ID (if present)
            3. Answers to questions (in format question_number: answer)
            
            Format your response as JSON with keys: "student_name", "student_id", and "answers".
            For answers, use a dictionary with question numbers as keys and answers as values.
            """
            
            # Generate content
            response = self.text_model.generate_content(prompt)
            
            # Try to parse the response as JSON
            response_text = response.text
            
            # Extract JSON part if it exists
            import re
            import json
            
            # Look for JSON-like content between triple backticks or curly braces
            json_match = re.search(r'```json\s*(.*?)\s*```', response_text, re.DOTALL)
            if not json_match:
                json_match = re.search(r'```\s*(.*?)\s*```', response_text, re.DOTALL)
            if not json_match:
                json_match = re.search(r'({.*})', response_text, re.DOTALL)
            
            if json_match:
                try:
                    json_str = json_match.group(1)
                    result = json.loads(json_str)
                    return result
                except json.JSONDecodeError:
                    # If JSON parsing fails, use regex to extract information
                    pass
            
            # Fallback to regex extraction
            result = {}
            
            # Extract student name
            name_match = re.search(r'[Nn]ame:?\s*([A-Za-zÀ-ÖØ-öø-ÿ\s\-\'\.]+)', text)
            if name_match:
                result["student_name"] = name_match.group(1).strip()
            else:
                result["student_name"] = "Non détecté"
            
            # Extract student ID
            id_match = re.search(r'[Ii][Dd]:?\s*(\d+)', text)
            if id_match:
                result["student_id"] = id_match.group(1).strip()
            else:
                result["student_id"] = ""
            
            # Extract answers
            answers = {}
            answer_matches = re.findall(r'(?:Question|Q)?\s*(\d+)[\.:\)]\s*([A-Za-z0-9]+)', text)
            for match in answer_matches:
                question_num = int(match[0])
                answer = match[1]
                answers[question_num] = answer
            
            result["answers"] = answers
            
            return result
            
        except Exception as e:
            logger.error(f"Error processing extracted text: {str(e)}")
            return {
                "student_name": "Non détecté",
                "student_id": "",
                "answers": {}
            }
    
    def _parse_grading_results(self, grading_text: str, correct_answers: Dict[str, str]) -> Dict[str, Any]:
        """
        Parse grading results from Gemini's response
        
        Args:
            grading_text: Text response from Gemini
            correct_answers: Dictionary of correct answers
            
        Returns:
            Dictionary with parsed grading results
        """
        try:
            # Use Gemini to parse its own output into structured format
            prompt = f"""
            Parse the following grading results into a structured format:
            
            {grading_text}
            
            Format your response as JSON with two main sections:
            1. "summary" containing:
               - score (number of correct answers)
               - total (total number of questions)
               - percentage (score as percentage)
               - status ("passed" if percentage >= 60%, otherwise "failed")
            
            2. "details" containing an array of question results, each with:
               - question_number
               - student_answer
               - correct_answer
               - is_correct (boolean)
               - feedback (if any)
            
            Ensure the JSON is properly formatted.
            """
            
            # Generate content
            response = self.text_model.generate_content(prompt)
            
            # Try to parse the response as JSON
            response_text = response.text
            
            # Extract JSON part if it exists
            import re
            import json
            
            # Look for JSON-like content between triple backticks or curly braces
            json_match = re.search(r'```json\s*(.*?)\s*```', response_text, re.DOTALL)
            if not json_match:
                json_match = re.search(r'```\s*(.*?)\s*```', response_text, re.DOTALL)
            if not json_match:
                json_match = re.search(r'({.*})', response_text, re.DOTALL)
            
            if json_match:
                try:
                    json_str = json_match.group(1)
                    result = json.loads(json_str)
                    return result
                except json.JSONDecodeError:
                    # If JSON parsing fails, create a basic result
                    pass
            
            # Fallback to creating a basic result
            total_questions = len(correct_answers)
            # Try to extract score from text
            score_match = re.search(r'score:?\s*(\d+)', grading_text, re.IGNORECASE)
            score = int(score_match.group(1)) if score_match else 0
            
            percentage = (score / total_questions) * 100 if total_questions > 0 else 0
            status = "passed" if percentage >= 60 else "failed"
            
            return {
                "summary": {
                    "score": score,
                    "total": total_questions,
                    "percentage": percentage,
                    "status": status
                },
                "details": {
                    "grading_text": grading_text,
                    "correct_answers": correct_answers
                }
            }
            
        except Exception as e:
            logger.error(f"Error parsing grading results: {str(e)}")
            return {
                "summary": {
                    "score": 0,
                    "total": len(correct_answers),
                    "percentage": 0,
                    "status": "failed"
                },
                "details": {
                    "error": str(e),
                    "grading_text": grading_text
                }
            }

# Create a singleton instance
gemini_service = GeminiService()
