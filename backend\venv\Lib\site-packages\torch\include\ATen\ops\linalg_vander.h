#pragma once

// @generated by torchgen/gen.py from Function.h

#include <ATen/Context.h>
#include <ATen/DeviceGuard.h>
#include <ATen/TensorUtils.h>
#include <ATen/TracerMode.h>
#include <ATen/core/Generator.h>
#include <ATen/core/Reduction.h>
#include <ATen/core/Tensor.h>
#include <c10/core/Scalar.h>
#include <c10/core/Storage.h>
#include <c10/core/TensorOptions.h>
#include <c10/util/Deprecated.h>
#include <c10/util/Optional.h>



#include <ATen/ops/linalg_vander_ops.h>

namespace at {


// aten::linalg_vander(Tensor x, *, SymInt? N=None) -> Tensor
inline at::Tensor linalg_vander(const at::Tensor & x, c10::optional<int64_t> N=c10::nullopt) {
    return at::_ops::linalg_vander::call(x, N.has_value() ? c10::make_optional(c10::SymInt(*N)) : c10::nullopt);
}
namespace symint {
  template <typename T, typename = std::enable_if_t<std::is_same<T, int64_t>::value>>
  at::Tensor linalg_vander(const at::Tensor & x, c10::optional<int64_t> N=c10::nullopt) {
    return at::_ops::linalg_vander::call(x, N.has_value() ? c10::make_optional(c10::SymInt(*N)) : c10::nullopt);
  }
}

// aten::linalg_vander(Tensor x, *, SymInt? N=None) -> Tensor
inline at::Tensor linalg_vander_symint(const at::Tensor & x, c10::optional<c10::SymInt> N=c10::nullopt) {
    return at::_ops::linalg_vander::call(x, N);
}
namespace symint {
  template <typename T, typename = std::enable_if_t<std::is_same<T, c10::SymInt>::value>>
  at::Tensor linalg_vander(const at::Tensor & x, c10::optional<c10::SymInt> N=c10::nullopt) {
    return at::_ops::linalg_vander::call(x, N);
  }
}

}
