'use client';

import { Suspense } from 'react';
import { useParams } from 'next/navigation';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import GradingResult from '@/components/GradingResult';
import { Loader2 } from 'lucide-react';

// Composant de chargement pour Suspense
function ResultLoader() {
  return (
    <div className="text-center">
      <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-primary" />
      <h2 className="text-xl font-medium">Chargement des résultats...</h2>
      <p className="text-muted-foreground mt-2">Veuillez patienter pendant que nous récupérons vos résultats</p>
    </div>
  );
}

// Composant principal de résultats
function ResultContent() {
  const params = useParams();
  const fileId = params.id as string;

  return (
    <div className="container mx-auto max-w-7xl">
      <h1 className="text-3xl font-bold mb-6">Résultats d'évaluation</h1>
      <GradingResult resultId={fileId} showDemo={false} />
    </div>
  );
}

// Page de résultats
export default function ResultPage() {
  return (
    <div className="flex min-h-screen flex-col">
      <Header />
      <main className="flex-1 p-4 md:p-6 flex items-center justify-center">
        <Suspense fallback={<ResultLoader />}>
          <ResultContent />
        </Suspense>
      </main>
      <Footer />
    </div>
  );
}
