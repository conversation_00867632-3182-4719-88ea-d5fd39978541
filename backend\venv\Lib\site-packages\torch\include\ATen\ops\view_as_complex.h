#pragma once

// @generated by torchgen/gen.py from Function.h

#include <ATen/Context.h>
#include <ATen/DeviceGuard.h>
#include <ATen/TensorUtils.h>
#include <ATen/TracerMode.h>
#include <ATen/core/Generator.h>
#include <ATen/core/Reduction.h>
#include <ATen/core/Tensor.h>
#include <c10/core/Scalar.h>
#include <c10/core/Storage.h>
#include <c10/core/TensorOptions.h>
#include <c10/util/Deprecated.h>
#include <c10/util/Optional.h>



#include <ATen/ops/view_as_complex_ops.h>

namespace at {


// aten::view_as_complex(Tensor(a) self) -> Tensor(a)
inline at::Tensor view_as_complex(const at::Tensor & self) {
    return at::_ops::view_as_complex::call(self);
}

}
