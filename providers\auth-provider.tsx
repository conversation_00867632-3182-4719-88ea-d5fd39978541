'use client';

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';

// Define the shape of our auth context
interface AuthContextType {
  user: User | null;
  accessToken: string | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  login: (email: string, username: string, role: string, password?: string) => void;
  logout: () => void;
  register: (email: string, password: string, name: string) => Promise<void>;
}

// User type
interface User {
  id: string;
  name: string;
  email: string;
  role: string;
}

// Create the context with a default value
const AuthContext = createContext<AuthContextType>({
  user: null,
  accessToken: null,
  isAuthenticated: false,
  isLoading: true,
  login: async () => {},
  logout: () => {},
  register: async () => {},
});

// Provider props
interface AuthProviderProps {
  children: ReactNode;
}

// Auth provider component
export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [accessToken, setAccessToken] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // Check if user is already logged in on mount
  useEffect(() => {
    const checkAuth = async () => {
      try {
        // For demo purposes, we'll just check localStorage
        const storedToken = localStorage.getItem('accessToken');
        const storedUser = localStorage.getItem('user');

        if (storedToken && storedUser) {
          setAccessToken(storedToken);
          setUser(JSON.parse(storedUser));
        }
      } catch (error) {
        console.error('Auth check failed:', error);
        // Clear any invalid data
        localStorage.removeItem('accessToken');
        localStorage.removeItem('user');
      } finally {
        setIsLoading(false);
      }
    };

    checkAuth();
  }, []);

  // Login function
  const login = (email: string, username: string, role: string, password?: string) => {
    setIsLoading(true);
    try {
      // For demo purposes, we'll simulate a successful login
      // In a real app, you would make an API call here
      const mockUser: User = {
        id: '1',
        name: username || 'Demo User',
        email: email,
        role: role || 'teacher',
      };
      const mockToken = 'mock-jwt-token';

      // Store in localStorage
      localStorage.setItem('accessToken', mockToken);
      localStorage.setItem('user', JSON.stringify(mockUser));

      // Store in cookies for middleware
      document.cookie = `accessToken=${mockToken}; path=/; max-age=86400; SameSite=Lax`;
      document.cookie = `user=${JSON.stringify(mockUser)}; path=/; max-age=86400; SameSite=Lax`;

      // Update state
      setUser(mockUser);
      setAccessToken(mockToken);
    } catch (error) {
      console.error('Login failed:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  // Logout function
  const logout = () => {
    // Clear localStorage
    localStorage.removeItem('accessToken');
    localStorage.removeItem('user');

    // Clear cookies
    document.cookie = 'accessToken=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT; SameSite=Lax';
    document.cookie = 'user=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT; SameSite=Lax';

    // Update state
    setUser(null);
    setAccessToken(null);
  };

  // Register function
  const register = async (email: string, password: string, name: string) => {
    setIsLoading(true);
    try {
      // For demo purposes, we'll simulate a successful registration
      // In a real app, you would make an API call here
      const mockUser: User = {
        id: '1',
        name: name,
        email: email,
        role: 'teacher',
      };
      const mockToken = 'mock-jwt-token';

      // Store in localStorage
      localStorage.setItem('accessToken', mockToken);
      localStorage.setItem('user', JSON.stringify(mockUser));

      // Store in cookies for middleware
      document.cookie = `accessToken=${mockToken}; path=/; max-age=86400; SameSite=Lax`;
      document.cookie = `user=${JSON.stringify(mockUser)}; path=/; max-age=86400; SameSite=Lax`;

      // Update state
      setUser(mockUser);
      setAccessToken(mockToken);
    } catch (error) {
      console.error('Registration failed:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  // Compute isAuthenticated
  const isAuthenticated = !!user && !!accessToken;

  // Provide the auth context to children
  return (
    <AuthContext.Provider
      value={{
        user,
        accessToken,
        isAuthenticated,
        isLoading,
        login,
        logout,
        register,
      }}
    >
      {children}
    </AuthContext.Provider>
  );
};

// Custom hook to use the auth context
export const useAuth = () => useContext(AuthContext);
