# Script de démarrage PostgreSQL pour Auto-Grade Scribe
Write-Host "🐘 Démarrage PostgreSQL avec Docker Compose..." -ForegroundColor Green

# Vérifier si Docker est disponible
try {
    docker --version | Out-Null
    Write-Host "✅ Docker trouvé" -ForegroundColor Green
} catch {
    Write-Host "❌ Docker non trouvé. Veuillez installer Docker Desktop." -ForegroundColor Red
    Write-Host "Téléchargez depuis: https://www.docker.com/products/docker-desktop" -ForegroundColor Cyan
    exit 1
}

# Vérifier si docker-compose est disponible
try {
    docker-compose --version | Out-Null
    Write-Host "✅ Docker Compose trouvé" -ForegroundColor Green
} catch {
    Write-Host "❌ Docker Compose non trouvé." -ForegroundColor Red
    exit 1
}

# Démarrer PostgreSQL uniquement
Write-Host "🚀 Démarrage de PostgreSQL..." -ForegroundColor Yellow
docker-compose -f docker-compose-db-only.yml up -d

if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ PostgreSQL démarré avec succès!" -ForegroundColor Green
} else {
    Write-Host "❌ Erreur lors du démarrage de PostgreSQL" -ForegroundColor Red
    exit 1
}

# Attendre que PostgreSQL soit prêt
Write-Host "⏳ Attente que PostgreSQL soit prêt..." -ForegroundColor Yellow
$maxAttempts = 30
$attempt = 0

do {
    $attempt++
    try {
        docker exec auto-grade-postgres pg_isready -U autograde -d gradegeniusdb 2>$null
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ PostgreSQL est prêt!" -ForegroundColor Green
            break
        }
    } catch {
        # Continue à attendre
    }
    
    if ($attempt -ge $maxAttempts) {
        Write-Host "❌ Timeout: PostgreSQL n'est pas prêt après $maxAttempts tentatives" -ForegroundColor Red
        exit 1
    }
    
    Write-Host "⏳ Tentative $attempt/$maxAttempts..." -ForegroundColor Yellow
    Start-Sleep -Seconds 2
} while ($true)

# Mettre à jour le fichier .env
Write-Host "⚙️ Mise à jour du fichier .env..." -ForegroundColor Yellow
$envContent = @"
# Configuration PostgreSQL pour Auto-Grade Scribe
ENVIRONMENT=development
DEBUG=true
DATABASE_URL=postgresql://autograde:autograde123@localhost:5432/gradegeniusdb
SECRET_KEY=your-secret-key-change-in-production
JWT_ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=60
API_HOST=0.0.0.0
API_PORT=8000
CORS_ORIGINS=http://localhost:3000,http://127.0.0.1:3000
MAX_FILE_SIZE=52428800
UPLOAD_DIRECTORY=uploads
RESULTS_DIRECTORY=results
TEMP_DIRECTORY=temp
TESSERACT_TIMEOUT=300
OCR_CONFIDENCE_THRESHOLD=0.6
GEMINI_MODEL=gemini-pro-vision
GEMINI_TIMEOUT=60
LOG_LEVEL=INFO
LOG_FILE=logs/app.log
DATABASE_POOL_SIZE=10
DATABASE_MAX_OVERFLOW=20
DATABASE_POOL_TIMEOUT=30
"@

$envContent | Out-File -FilePath ".env" -Encoding UTF8
Write-Host "✅ Fichier .env mis à jour" -ForegroundColor Green

# Tester la connexion
Write-Host "🧪 Test de connexion PostgreSQL..." -ForegroundColor Yellow
try {
    docker exec auto-grade-postgres psql -U autograde -d gradegeniusdb -c "SELECT version();" | Out-Null
    Write-Host "✅ Connexion PostgreSQL réussie!" -ForegroundColor Green
} catch {
    Write-Host "⚠️ Erreur de test de connexion" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "🎉 PostgreSQL configuré et prêt!" -ForegroundColor Green
Write-Host ""
Write-Host "📋 Informations de connexion:" -ForegroundColor Cyan
Write-Host "  Host: localhost" -ForegroundColor White
Write-Host "  Port: 5432" -ForegroundColor White
Write-Host "  Database: gradegeniusdb" -ForegroundColor White
Write-Host "  User: autograde" -ForegroundColor White
Write-Host "  Password: autograde123" -ForegroundColor White
Write-Host ""
Write-Host "🚀 Pour démarrer l'application:" -ForegroundColor Cyan
Write-Host "  cd backend" -ForegroundColor White
Write-Host "  python -m uvicorn app:app --host 0.0.0.0 --port 8000 --reload" -ForegroundColor White
Write-Host ""
Write-Host "🛑 Pour arrêter PostgreSQL:" -ForegroundColor Cyan
Write-Host "  docker-compose -f docker-compose-db-only.yml down" -ForegroundColor White
Write-Host ""
Write-Host "🗑️ Pour supprimer les données:" -ForegroundColor Cyan
Write-Host "  docker-compose -f docker-compose-db-only.yml down -v" -ForegroundColor White
