#pragma once

// @generated by torchgen/gen.py from NativeFunction.h

#include <c10/core/Scalar.h>
#include <c10/core/Storage.h>
#include <c10/core/TensorOptions.h>
#include <c10/util/Deprecated.h>
#include <c10/util/Optional.h>
#include <c10/core/QScheme.h>
#include <ATen/core/Reduction.h>
#include <ATen/core/Tensor.h>
#include <tuple>
#include <vector>


namespace at {
namespace native {
TORCH_API ::std::vector<at::Tensor> _foreach_copy(at::TensorList self, at::TensorList src, bool non_blocking=false);
TORCH_API void _foreach_copy_out(at::TensorList self, at::TensorList src, bool non_blocking, at::TensorList out);
TORCH_API void foreach_tensor_copy_list_kernel_slow_(at::TensorList self, at::TensorList src, bool non_blocking=false);
TORCH_API void foreach_tensor_copy_list_kernel_cuda_(at::TensorList self, at::TensorList src, bool non_blocking=false);
} // namespace native
} // namespace at
