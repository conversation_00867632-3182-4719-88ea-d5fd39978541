+---------------------+        +----------------------+
|                     |        |                      |
|  Frontend (Next.js) |<------>|  Backend (FastAPI)   |
|                     |  API   |                      |
+----------+----------+        +-----------+----------+
           ^                               |
           |                               |
           |                               v
+----------+----------+        +-----------+----------+
|                     |        |                      |
|  Interface          |        |  Services            |
|  Utilisateur        |        |  - OCR (Tesseract)   |
|  - Upload           |        |  - IA (TinyLlama)    |
|  - Résultats        |        |  - Authentification  |
|                     |        |                      |
+---------------------+        +-----------+----------+
                                           |
                                           |
                                           v
                               +-----------+----------+
                               |                      |
                               |  Base de Données     |
                               |  (SQLite)            |
                               |  - Users             |
                               |  - Exams             |
                               |                      |
                               +----------------------+

