import os
import cv2
import numpy as np
import json
import torch
import logging
from typing import Dict, List, Any, Tuple, Optional
from pathlib import Path
from transformers import AutoModelForCausalLM, AutoTokenizer, pipeline

# Get logger
logger = logging.getLogger("auto-grade-scribe.qcm")

# Configuration pour utiliser un modèle Hugging Face directement
MODEL_NAME = "TinyLlama/TinyLlama-1.1B-Chat-v1.0"  # Modèle léger et open source

MAX_LENGTH = 2048
DEVICE = "cuda" if torch.cuda.is_available() else "cpu"

class EnhancedQCMGrader:
    def __init__(self):
        self.model_name = MODEL_NAME
        self.device = DEVICE
        self.tokenizer = None
        self.model = None
        self.generator = None
        self.answer_symbols = ["A", "B", "C", "D", "E"]
        self._initialize_model()

    def _initialize_model(self):
        """Initialise le modèle LLM depuis Hugging Face"""
        logger.info(f"Loading model {self.model_name} on {self.device}...")

        # Utilisation du pipeline de generation qui optimise les ressources
        try:
            model_kwargs = {
                "device_map": "auto",
                "load_in_8bit": True if self.device == "cuda" else False,
                "torch_dtype": torch.float16 if self.device == "cuda" else torch.float32
            }

            self.generator = pipeline(
                "text-generation",
                model=self.model_name,
                tokenizer=self.model_name,
                model_kwargs=model_kwargs,
                max_length=MAX_LENGTH
            )
            logger.info("Model loaded successfully!")
        except Exception as e:
            logger.error(f"Error loading model: {e}")
            # Fallback sur un modèle plus petit si nécessaire
            try:
                logger.info("Attempting to load a lighter model...")
                self.model_name = "TinyLlama/TinyLlama-1.1B-Chat-v1.0"
                self.generator = pipeline(
                    "text-generation",
                    model=self.model_name,
                    tokenizer=self.model_name
                )
                logger.info("Fallback model loaded successfully!")
            except Exception as e2:
                logger.error(f"Fallback model also failed: {e2}")
                logger.warning("Service will operate in degraded mode")

    def _preprocess_image(self, image_path: str) -> np.ndarray:
        """Prétraite l'image pour améliorer la détection des marques"""
        image = cv2.imread(image_path)
        if image is None:
            raise ValueError("Impossible de lire l'image")

        # Convertir en niveaux de gris
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)

        # Appliquer un filtre pour réduire le bruit
        gray = cv2.GaussianBlur(gray, (5, 5), 0)

        # Améliorer le contraste
        clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8, 8))
        enhanced = clahe.apply(gray)

        # Binarisation adaptative
        binary = cv2.adaptiveThreshold(
            enhanced, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C,
            cv2.THRESH_BINARY_INV, 11, 2
        )

        return binary

    def _detect_grid(self, binary_image: np.ndarray) -> List[Dict[str, Any]]:
        """Détecte la grille de réponses QCM"""
        # Détecter les contours
        contours, _ = cv2.findContours(binary_image, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

        # Filtrer les contours pour trouver les cases
        boxes = []
        for contour in contours:
            # Calculer l'aire et le périmètre
            area = cv2.contourArea(contour)
            perimeter = cv2.arcLength(contour, True)

            # Filtrer par taille
            if area > 100 and area < 2000:
                # Approximer le contour en polygone
                approx = cv2.approxPolyDP(contour, 0.02 * perimeter, True)

                # Si le polygone a 4 côtés (rectangulaire), c'est probablement une case
                if len(approx) == 4:
                    x, y, w, h = cv2.boundingRect(contour)
                    boxes.append({
                        "x": x,
                        "y": y,
                        "width": w,
                        "height": h,
                        "area": area,
                        "contour": contour
                    })

        # Trier les cases par position (d'abord par ligne, puis par colonne)
        boxes.sort(key=lambda box: (box["y"], box["x"]))

        return boxes

    def _analyze_marked_boxes(self, binary_image: np.ndarray, boxes: List[Dict[str, Any]]) -> Dict[int, str]:
        """Analyse les cases marquées et retourne les réponses"""
        responses = {}

        # Regrouper les cases par ligne (questions)
        current_question = 1
        current_y = boxes[0]["y"] if boxes else 0
        question_boxes = []

        for box in boxes:
            # Si la différence de position Y est significative, c'est une nouvelle ligne/question
            if abs(box["y"] - current_y) > 20:
                # Traiter la question précédente
                if question_boxes:
                    # Trier les cases par position X
                    question_boxes.sort(key=lambda b: b["x"])

                    # Vérifier quelle case est marquée
                    marked_index = self._find_marked_box(binary_image, question_boxes)
                    if marked_index is not None and marked_index < len(self.answer_symbols):
                        responses[current_question] = self.answer_symbols[marked_index]

                    # Passer à la question suivante
                    current_question += 1
                    question_boxes = []

                current_y = box["y"]

            question_boxes.append(box)

        # Traiter la dernière question
        if question_boxes:
            question_boxes.sort(key=lambda b: b["x"])
            marked_index = self._find_marked_box(binary_image, question_boxes)
            if marked_index is not None and marked_index < len(self.answer_symbols):
                responses[current_question] = self.answer_symbols[marked_index]

        return responses

    def _find_marked_box(self, binary_image: np.ndarray, boxes: List[Dict[str, Any]]) -> Optional[int]:
        """Détermine quelle case est marquée dans une ligne de cases"""
        max_fill_ratio = 0
        marked_index = None

        for i, box in enumerate(boxes):
            # Extraire la région d'intérêt (ROI)
            x, y, w, h = box["x"], box["y"], box["width"], box["height"]
            roi = binary_image[y:y+h, x:x+w]

            # Calculer le ratio de remplissage (nombre de pixels blancs / surface totale)
            white_pixels = cv2.countNonZero(roi)
            total_pixels = w * h
            fill_ratio = white_pixels / total_pixels if total_pixels > 0 else 0

            # Si le ratio de remplissage est suffisamment élevé et plus grand que le maximum actuel
            if fill_ratio > 0.3 and fill_ratio > max_fill_ratio:
                max_fill_ratio = fill_ratio
                marked_index = i

        return marked_index

    def _check_answers(self, student_answers: Dict[int, str], correct_answers: Dict[int, str]) -> Dict[str, Any]:
        """Compare les réponses de l'étudiant avec les réponses correctes"""
        logger.info("Comparing student answers with correct answers...")
        total_questions = len(correct_answers)
        correct_count = 0
        results = {"questions": {}}

        logger.info("Answer comparison details:")
        for question_num, correct_answer in correct_answers.items():
            student_answer = student_answers.get(question_num, "")
            is_correct = student_answer == correct_answer

            if is_correct:
                correct_count += 1
                logger.info(f"Question {question_num}: CORRECT - Student: {student_answer}, Correct: {correct_answer}")
            else:
                logger.info(f"Question {question_num}: INCORRECT - Student: {student_answer or 'No answer'}, Correct: {correct_answer}")

            results["questions"][str(question_num)] = {
                "student_answer": student_answer,
                "correct_answer": correct_answer,
                "is_correct": is_correct
            }

        # Calculer le score
        score = (correct_count / total_questions) * 100 if total_questions > 0 else 0

        results["score"] = score
        results["correct_count"] = correct_count
        results["total_questions"] = total_questions

        logger.info(f"Final score: {score:.1f}% ({correct_count}/{total_questions} correct)")
        return results

    def _process_with_hf_model(self, prompt: str) -> str:
        """Traite le texte avec le modèle Hugging Face"""
        if not self.generator:
            # Si le modèle n'a pas pu être chargé, retourner une réponse générique
            return json.dumps({
                "récapitulatif": "Analyse non disponible",
                "conclusion": "Le modèle de traitement n'est pas disponible."
            })

        try:
            # Formatage du prompt pour modèle Mistral Instruct
            formatted_prompt = f"""<s>[INST] {prompt} [/INST]"""

            # Générer la réponse
            response = self.generator(
                formatted_prompt,
                max_new_tokens=1024,
                temperature=0.1,
                top_p=0.9,
                repetition_penalty=1.1,
                do_sample=True,
                num_return_sequences=1
            )

            generated_text = response[0]['generated_text']

            # Extraire la réponse après le prompt
            answer = generated_text.split("[/INST]")[-1].strip()

            return answer
        except Exception as e:
            print(f"Erreur lors du traitement avec le modèle Hugging Face: {e}")
            return json.dumps({
                "récapitulatif": f"Score: erreur lors de l'analyse",
                "conclusion": "Une erreur est survenue pendant le traitement."
            })

    def _generate_feedback(self, results: Dict[str, Any]) -> str:
        """Génère un feedback personnalisé à l'aide du LLM"""
        prompt = f"""
        En tant qu'enseignant, donne un feedback constructif et encourageant sur cette évaluation QCM.

        Résultats de l'évaluation:
        - Score: {results["score"]:.1f}%
        - Nombre de réponses correctes: {results["correct_count"]}/{results["total_questions"]}

        Détail des réponses:
        {json.dumps(results["questions"], indent=2, ensure_ascii=False)}

        Fournit un feedback qui:
        1. Encourage l'étudiant à s'améliorer
        2. Identifie les forces et les faiblesses
        3. Donne des conseils concrets pour progresser
        4. Reste positif et motivant

        Format ton feedback comme un JSON selon ce schéma:
        {{
          "récapitulatif": "résumé concis de la performance",
          "points_forts": ["liste des points forts"],
          "axes_amélioration": ["liste des points à améliorer"],
          "conseils": ["liste de conseils pratiques"],
          "conclusion": "message d'encouragement"
        }}
        """

        # Traiter avec le LLM
        feedback_text = self._process_with_hf_model(prompt)

        try:
            # Essayer d'extraire le JSON de la réponse
            json_start = feedback_text.find('{')
            json_end = feedback_text.rfind('}') + 1

            if json_start >= 0 and json_end > json_start:
                json_str = feedback_text[json_start:json_end]
                feedback = json.loads(json_str)
            else:
                # Fallback si le format JSON n'est pas respecté
                feedback = {
                    "récapitulatif": f"Score: {results['score']:.1f}%",
                    "conclusion": "Continue tes efforts!"
                }

            return feedback

        except json.JSONDecodeError:
            # En cas d'erreur, retourner un feedback basique
            return {
                "récapitulatif": f"Score: {results['score']:.1f}%",
                "conclusion": "Continue tes efforts!"
            }

    def grade_qcm(self, image_path: str, correct_answers: Dict[int, str]) -> Dict[str, Any]:
        """
        Analyse et note un examen QCM

        Args:
            image_path: Chemin vers l'image du QCM
            correct_answers: Dictionnaire {numéro_question: réponse_correcte}

        Returns:
            Dict: Résultat de l'évaluation avec score, feedback et détails
        """
        logger.info(f"Starting QCM grading for image: {image_path}")
        logger.info(f"Correct answers provided: {correct_answers}")

        try:
            # Prétraiter l'image
            logger.info("Preprocessing image...")
            processed_image = self._preprocess_image(image_path)

            # Détecter la grille et les cases
            logger.info("Detecting answer grid...")
            boxes = self._detect_grid(processed_image)

            if not boxes:
                logger.warning("No answer boxes detected in the image")
                return {
                    "success": False,
                    "error": "Aucune case détectée dans l'image"
                }

            logger.info(f"Detected {len(boxes)} potential answer boxes")

            # Analyser les cases marquées
            logger.info("Analyzing marked boxes to extract student answers...")
            student_answers = self._analyze_marked_boxes(processed_image, boxes)

            if not student_answers:
                logger.warning("No student answers detected")
                return {
                    "success": False,
                    "error": "Aucune réponse détectée"
                }

            logger.info(f"Extracted student answers: {student_answers}")

            # Vérifier les réponses
            logger.info("Checking answers against correct solutions...")
            results = self._check_answers(student_answers, correct_answers)

            logger.info(f"Grading results: Score={results['score']:.1f}%, Correct={results['correct_count']}/{results['total_questions']}")

            # Générer un feedback personnalisé
            logger.info("Generating personalized feedback...")
            feedback = self._generate_feedback(results)

            logger.info("QCM grading completed successfully")
            return {
                "success": True,
                "score": results["score"],
                "correct_count": results["correct_count"],
                "total_questions": results["total_questions"],
                "details": results["questions"],
                "feedback": feedback
            }

        except Exception as e:
            logger.error(f"Error during QCM grading: {str(e)}", exc_info=True)
            return {
                "success": False,
                "error": str(e)
            }

# Créer une instance du service
enhanced_qcm_grader = EnhancedQCMGrader()