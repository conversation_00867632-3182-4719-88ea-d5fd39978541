FROM python:3.11-slim

WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    libgl1-mesa-glx \
    libglib2.0-0 \
    tesseract-ocr \
    tesseract-ocr-eng \
    tesseract-ocr-fra \
    postgresql-client \
    && rm -rf /var/lib/apt/lists/*

COPY requirements.txt .

RUN pip install --no-cache-dir -r requirements.txt

# Create directories for data
RUN mkdir -p /app/data/digits /app/data/models /app/data/ground-truth-correct-answers /app/uploads /app/results /app/static

# Set environment variables
ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONUNBUFFERED=1
ENV DATABASE_URL=*************************************************/gradegeniusdb

COPY . .

EXPOSE 8000

# Make the wait script executable
COPY wait-for-postgres.sh /wait-for-postgres.sh
RUN chmod +x /wait-for-postgres.sh

# Create a startup script
RUN echo '#!/bin/bash\n\
echo "Initializing database..."\n\
python init_postgres_db.py\n\
echo "Starting API server..."\n\
python -m uvicorn app:app --host 0.0.0.0 --port 8000 --reload\n\
' > /start.sh && chmod +x /start.sh

# Wait for PostgreSQL to be ready before starting the application
CMD ["/wait-for-postgres.sh", "postgres", "/start.sh"]