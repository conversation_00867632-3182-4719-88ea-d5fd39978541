# Try a different base image that might be more accessible
FROM python:3.11-alpine

WORKDIR /app

# Install system dependencies for Alpine
RUN apk add --no-cache \
    gcc \
    musl-dev \
    libffi-dev \
    openssl-dev \
    tesseract-ocr \
    tesseract-ocr-data-eng \
    tesseract-ocr-data-fra

# Copier et installer les dépendances Python
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copier le reste des fichiers
COPY . .

# Script d'initialisation
COPY wait-for-postgres.sh /wait-for-postgres.sh
RUN chmod +x /wait-for-postgres.sh

# Exposer le port
EXPOSE 8000

# Commande de démarrage
CMD ["./wait-for-postgres.sh"]