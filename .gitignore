# Dependencies
/node_modules
/.pnp
.pnp.js
/backend-env/
/venv/
/env/
/.env/
/ENV/
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Testing
/coverage

# Next.js
/.next/
/out/

# Production
/build

# Misc
.DS_Store
*.pem
Thumbs.db
ehthumbs.db
Desktop.ini

# Debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pip-log.txt
pip-delete-this-directory.txt

# Local env files
.env*.local
.env
.env.development
.env.test
.env.production

# Vercel
.vercel

# TypeScript
*.tsbuildinfo
next-env.d.ts

# Logs
logs
*.log
.pytest_cache/

# Distribution / packaging
dist
dist-ssr
*.local
*.egg-info/
.installed.cfg
*.egg

# Database
*.db
*.sqlite
*.sqlite3

# Temporary files
/data/temp/
/temp/
/tmp/
.cache/
.pytest_cache/
.coverage
htmlcov/

# Uploads
/public/uploads/*
!/public/uploads/.gitkeep

# Editor directories and files
.vscode/*
!.vscode/extensions.json
.idea
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# Docker
docker-compose.override.yml
