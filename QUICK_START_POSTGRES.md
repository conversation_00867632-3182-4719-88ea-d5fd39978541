# 🚀 Démarrage Rapide PostgreSQL - Auto-Grade Scribe

## Une seule commande pour tout démarrer !

### 📋 Prérequis
- Docker Desktop installé et démarré
- Git (pour cloner le projet)

### 🐘 Option 1: PostgreSQL uniquement (Recommandé pour le développement)

```bash
# Démarrer PostgreSQL
docker-compose -f docker-compose-db-only.yml up -d

# Vérifier que PostgreSQL est prêt
docker exec auto-grade-postgres pg_isready -U autograde -d gradegeniusdb
```

### 🚀 Option 2: Application complète avec PostgreSQL

```bash
# Démarrer PostgreSQL + Backend
docker-compose -f docker-compose-postgres.yml up -d
```

### 🎯 Option 3: Script automatisé (Windows)

```powershell
# Exécuter le script de démarrage
.\start-postgres.ps1
```

## 📊 Vérification

Une fois PostgreSQL démarré, vous pouvez vérifier la connexion :

```bash
# Test de connexion
docker exec auto-grade-postgres psql -U autograde -d gradegeniusdb -c "SELECT version();"

# Lister les tables (après création par l'application)
docker exec auto-grade-postgres psql -U autograde -d gradegeniusdb -c "\dt"
```

## 🌐 Accès

- **PostgreSQL**: `localhost:5432`
- **Base de données**: `gradegeniusdb`
- **Utilisateur**: `autograde`
- **Mot de passe**: `autograde123`
- **API Backend**: `http://localhost:8000` (si démarré)
- **Documentation API**: `http://localhost:8000/docs`

## 🔧 Configuration automatique

Le système configure automatiquement :
- ✅ Base de données `gradegeniusdb`
- ✅ Utilisateur `autograde` avec tous les privilèges
- ✅ Extensions PostgreSQL (uuid-ossp, pg_trgm)
- ✅ Paramètres de performance optimisés
- ✅ Utilisateur lecture seule `readonly_user`
- ✅ Fichier `.env` avec la bonne configuration

## 🛑 Arrêt et nettoyage

```bash
# Arrêter PostgreSQL (garde les données)
docker-compose -f docker-compose-db-only.yml down

# Arrêter et supprimer les données
docker-compose -f docker-compose-db-only.yml down -v

# Supprimer complètement
docker-compose -f docker-compose-db-only.yml down -v --rmi all
```

## 🔍 Dépannage

### PostgreSQL ne démarre pas
```bash
# Vérifier les logs
docker-compose -f docker-compose-db-only.yml logs postgres

# Redémarrer
docker-compose -f docker-compose-db-only.yml restart postgres
```

### Problème de connexion
```bash
# Vérifier le statut
docker-compose -f docker-compose-db-only.yml ps

# Tester la connexion
docker exec auto-grade-postgres pg_isready -U autograde
```

### Réinitialiser complètement
```bash
# Tout supprimer et recommencer
docker-compose -f docker-compose-db-only.yml down -v
docker volume prune -f
docker-compose -f docker-compose-db-only.yml up -d
```

## 📝 Variables d'environnement

Le fichier `.env` est automatiquement créé avec :

```env
DATABASE_URL=postgresql://autograde:autograde123@localhost:5432/gradegeniusdb
ENVIRONMENT=development
DEBUG=true
# ... autres variables
```

## 🎉 C'est tout !

Votre PostgreSQL est maintenant prêt pour Auto-Grade Scribe. 

Pour démarrer l'application backend :

```bash
cd backend
python -m uvicorn app:app --host 0.0.0.0 --port 8000 --reload
```

L'application se connectera automatiquement à PostgreSQL et créera les tables nécessaires.
