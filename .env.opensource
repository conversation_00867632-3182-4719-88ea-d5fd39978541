# Configuration Auto-Grade Scribe Open-Source
# Version utilisant uniquement des solutions gratuites

# Environnement
ENVIRONMENT=development
DEBUG=true

# Base de données PostgreSQL
DATABASE_URL=postgresql://autograde:autograde123@localhost:5432/gradegeniusdb

# Sécurité
SECRET_KEY=your-secret-key-change-in-production
JWT_ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=60

# Configuration API
API_HOST=0.0.0.0
API_PORT=8001
CORS_ORIGINS=http://localhost:3000,http://127.0.0.1:3000

# Upload de fichiers
MAX_FILE_SIZE=52428800
UPLOAD_DIRECTORY=uploads
RESULTS_DIRECTORY=results
TEMP_DIRECTORY=temp

# Configuration OCR Open-Source
TESSERACT_TIMEOUT=300
OCR_CONFIDENCE_THRESHOLD=0.7
EASYOCR_GPU=false
PADDLEOCR_GPU=false

# Configuration IA Open-Source
# Sentence Transformers (gratuit)
SENTENCE_TRANSFORMERS_CACHE_FOLDER=./models/sentence_transformers
TRANSFORMERS_CACHE=./models/transformers

# TrOCR Models (gratuit via Hugging Face)
TROCR_HANDWRITTEN_MODEL=microsoft/trocr-base-handwritten
TROCR_PRINTED_MODEL=microsoft/trocr-base-printed

# Modèles de similarité sémantique
SIMILARITY_MODEL=all-MiniLM-L6-v2
MULTILINGUAL_MODEL=paraphrase-multilingual-MiniLM-L12-v2
SEMANTIC_MODEL=all-mpnet-base-v2

# Configuration de correction
GRADING_CONFIDENCE_THRESHOLD=0.7
MANUAL_REVIEW_THRESHOLD=0.6
FUZZY_MATCHING_THRESHOLD=0.8

# Google AI (optionnel - gratuit avec limites)
# GOOGLE_API_KEY=your-google-api-key-here
GEMINI_MODEL=gemini-pro
GEMINI_TIMEOUT=60

# Logging
LOG_LEVEL=INFO
LOG_FILE=logs/app.log

# Pool de connexions base de données
DATABASE_POOL_SIZE=10
DATABASE_MAX_OVERFLOW=20
DATABASE_POOL_TIMEOUT=30

# Cache et performance
ENABLE_MODEL_CACHING=true
MODEL_CACHE_SIZE=1000
RESULT_CACHE_TTL=3600

# Limites de traitement
MAX_CONCURRENT_OCR=3
MAX_CONCURRENT_GRADING=5
PROCESSING_TIMEOUT=300

# Fonctionnalités activées
ENABLE_SEMANTIC_ANALYSIS=true
ENABLE_ADVANCED_FEEDBACK=true
ENABLE_BATCH_PROCESSING=true
