import os
from typing import Dict, Any, List, Optional
import json
import logging
from datetime import datetime

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler()]
)
logger = logging.getLogger("qcm-grading-service")

class QCMGradingService:
    def __init__(self):
        """Initialize the QCM grading service"""
        self.results_dir = "results"
        os.makedirs(self.results_dir, exist_ok=True)
    
    def grade_qcm(self, 
                 file_id: str, 
                 file_path: str, 
                 ocr_result: Dict[str, Any], 
                 correct_answers: Dict[str, str]) -> Dict[str, Any]:
        """
        Grade a QCM based on OCR results and correct answers
        
        Args:
            file_id: The ID of the file
            file_path: The path to the file
            ocr_result: The OCR results containing extracted answers
            correct_answers: The correct answers for each question
            
        Returns:
            A dictionary with grading results
        """
        try:
            # Extract student information
            student_name = ocr_result.get("student_name", "Non détecté")
            student_id = ocr_result.get("student_id", "")
            extracted_answers = ocr_result.get("extracted_answers", {})
            
            # Convert keys to strings for comparison
            correct_answers_str = {str(k): v for k, v in correct_answers.items()}
            extracted_answers_str = {str(k): v for k, v in extracted_answers.items()}
            
            # Calculate score
            score = 0
            max_score = len(correct_answers_str)
            
            # Compare answers
            comparison = {}
            for q_num, correct_answer in correct_answers_str.items():
                student_answer = extracted_answers_str.get(q_num, "")
                is_correct = student_answer.upper() == correct_answer.upper()
                
                if is_correct:
                    score += 1
                
                comparison[q_num] = {
                    "question": int(q_num),
                    "correct_answer": correct_answer,
                    "student_answer": student_answer,
                    "is_correct": is_correct
                }
            
            # Calculate percentage
            percentage = (score / max_score * 100) if max_score > 0 else 0
            
            # Determine status (pass/fail)
            status = "passed" if percentage >= 50 else "failed"
            
            # Create result
            result = {
                "success": True,
                "file_id": file_id,
                "file_path": file_path,
                "original_filename": os.path.basename(file_path),
                "status": "graded",
                "ocr_result": ocr_result,
                "student": {
                    "name": student_name,
                    "id": student_id,
                    "answers": extracted_answers
                },
                "correct_answers": correct_answers,
                "comparison": comparison,
                "grade": {
                    "score": score,
                    "max_score": max_score,
                    "percentage": percentage,
                    "status": status
                },
                "summary": f"L'étudiant {student_name} a obtenu un score de {score}/{max_score} ({percentage:.1f}%).",
                "details": {
                    "process_status": {
                        "preprocessing": {"status": "completed", "timestamp": datetime.now().isoformat()},
                        "ocr_extraction": {"status": "completed", "timestamp": datetime.now().isoformat()},
                        "ai_analysis": {"status": "completed", "timestamp": datetime.now().isoformat()},
                        "final_grading": {"status": "completed", "timestamp": datetime.now().isoformat()}
                    }
                }
            }
            
            # Save result to file
            result_path = os.path.join(self.results_dir, f"{file_id}_result.json")
            with open(result_path, "w", encoding="utf-8") as f:
                json.dump(result, f, ensure_ascii=False, indent=2)
            
            return result
            
        except Exception as e:
            logger.error(f"Error grading QCM: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }

# Create an instance of the service
qcm_grading_service = QCMGradingService()
