absl-py==2.2.2
aiofiles==23.2.1
aiohappyeyeballs==2.6.1
aiohttp==3.11.18
aiosignal==1.3.2
ale-py==0.11.0
alembic==1.15.2
altair==5.5.0
annotated-types==0.7.0
anyio==4.9.0
appdirs==1.4.4
arrow==1.3.0
asgiref==3.8.1
asttokens==3.0.0
astunparse==1.6.3
async-timeout==4.0.3
attrs==25.3.0
auth0-python==4.9.0
backoff==2.2.1
bcrypt==4.3.0
beautifulsoup4==4.13.4
binaryornot==0.4.4
blinker==1.9.0
branca==0.8.1
build==1.2.2.post1
cachetools==5.5.2
certifi==2025.1.31
cffi==1.17.1
chardet==5.2.0
charset-normalizer==3.4.1
chroma-hnswlib==0.7.6
chromadb==0.5.23
click==8.1.8
cloudpickle==3.1.1
cohere==5.15.0
colorama==0.4.6
coloredlogs==15.0.1
comm==0.2.2
contourpy==1.3.2
cookiecutter==2.6.0
# Editable install with no version control (crew_automation_pipeline_for_product_data_insights==0.1.0)
-e c:\users\<USER>\downloads\cagents
crewai==0.119.0
crewai-tools==0.44.0
# Editable install with no version control (crewai_product_data_analysis_automation==0.1.0)
-e c:\users\<USER>\downloads\crewai_product_data_analysis_automation
cryptography==44.0.2
cycler==0.12.1
dataclasses-json==0.6.7
debugpy==1.8.13
decorator==5.2.1
Deprecated==1.2.18
deprecation==2.1.0
distro==1.9.0
dm-tree==0.1.9
docker==7.1.0
docstring_parser==0.16
durationpy==0.9
easyocr==1.7.2
ecdsa==0.19.1
embedchain==0.1.128
environment==1.0.0
et_xmlfile==2.0.0
exceptiongroup==1.2.2
executing==2.2.0
faiss-cpu==1.10.0
Farama-Notifications==0.0.4
fastapi==0.115.9
fastavro==1.10.0
fastjsonschema==2.21.1
ffmpy==0.5.0
filelock==3.18.0
filetype==1.2.0
Flask==2.3.3
Flask-Cors==4.0.0
flatbuffers==25.2.10
folium==0.14.0
fonttools==4.57.0
frozenlist==1.6.0
fsspec==2025.3.2
gast==0.6.0
geopandas==1.0.1
gin-config==0.5.0
gitdb==4.0.12
GitPython==3.1.44
google-ai-generativelanguage==0.6.15
google-api-core==2.24.2
google-api-python-client==2.167.0
google-auth==2.38.0
google-auth-httplib2==0.2.0
google-auth-oauthlib==1.2.1
google-generativeai==0.8.5
google-pasta==0.2.0
googleapis-common-protos==1.70.0
gptcache==0.1.44
gradio==3.50.2
gradio_client==0.6.1
greenlet==3.2.1
groq==0.24.0
grpcio==1.71.0
grpcio-status==1.62.3
gunicorn==21.2.0
gym==0.26.2
gym-notices==0.0.8
gym-super-mario-bros==7.4.0
gymnasium==1.1.1
h11==0.14.0
h2==4.2.0
h5py==3.13.0
hpack==4.1.0
httpcore==1.0.8
httplib2==0.22.0
httptools==0.6.4
httpx==0.28.1
httpx-sse==0.4.0
huggingface-hub==0.30.2
humanfriendly==10.0
humanize==4.12.2
hyperframe==6.1.0
idna==3.10
imageio==2.37.0
importlib_metadata==8.6.1
importlib_resources==6.5.2
iniconfig==2.1.0
instructor==1.8.0
ipykernel==6.29.5
ipython==8.35.0
ipyvue==1.11.2
ipyvuetify==1.11.1
ipywidgets==8.1.6
itsdangerous==2.2.0
jedi==0.19.2
Jinja2==3.1.6
jiter==0.8.2
joblib==1.4.2
json5==0.12.0
json_repair==0.44.1
jsonpatch==1.33
jsonpickle==4.0.5
jsonpointer==3.0.0
jsonref==1.1.0
jsonschema==4.23.0
jsonschema-specifications==2024.10.1
jupyter_client==8.6.3
jupyter_core==5.7.2
jupyterlab_widgets==3.0.14
keras==2.15.0
kiwisolver==1.4.8
kubernetes==32.0.1
lancedb==0.22.0
langchain==0.3.24
langchain-cohere==0.3.5
langchain-community==0.3.22
langchain-core==0.3.55
langchain-experimental==0.3.4
langchain-google-genai==2.0.10
langchain-openai==0.2.14
langchain-text-splitters==0.3.8
langsmith==0.3.33
lazy_loader==0.4
libclang==18.1.1
litellm==1.68.0
lxml==5.4.0
Mako==1.3.10
Markdown==3.7
markdown-it-py==3.0.0
MarkupSafe==2.1.5
marshmallow==3.26.1
matplotlib==3.7.2
matplotlib-inline==0.1.7
mdurl==0.1.2
mem0ai==0.1.98
Mesa==2.1.1
ml-dtypes==0.2.0
mmh3==5.1.0
monotonic==1.6
mpmath==1.3.0
multidict==6.4.3
mypy_extensions==1.1.0
narwhals==1.35.0
nbformat==5.10.4
nes-py==8.2.1
nest-asyncio==1.6.0
network==0.1
networkx==3.1
ninja==1.11.1.4
nodeenv==1.9.1
numpy==1.26.2
oauthlib==3.2.2
onnxruntime==1.21.1
openai==1.75.0
opencv-python==4.8.1.78
opencv-python-headless==4.11.0.86
openpyxl==3.1.5
opentelemetry-api==1.32.1
opentelemetry-exporter-otlp-proto-common==1.32.1
opentelemetry-exporter-otlp-proto-grpc==1.32.1
opentelemetry-exporter-otlp-proto-http==1.32.1
opentelemetry-instrumentation==0.53b1
opentelemetry-instrumentation-asgi==0.53b1
opentelemetry-instrumentation-fastapi==0.53b1
opentelemetry-proto==1.32.1
opentelemetry-sdk==1.32.1
opentelemetry-semantic-conventions==0.53b1
opentelemetry-util-http==0.53b1
opt_einsum==3.4.0
orjson==3.10.16
osmnx==2.0.3
overrides==7.7.0
packaging==24.2
pandas==2.0.2
parso==0.8.4
passlib==1.7.4
pdf2image==1.17.0
pdfminer.six==20250327
pdfplumber==0.11.6
pettingzoo==1.25.0
pillow==10.2.0
platformdirs==4.3.7
plotly==6.0.1
pluggy==1.5.0
portalocker==2.10.1
posthog==3.25.0
# Editable install with no version control (product_analytics_crew_automation==0.1.0)
-e c:\users\<USER>\downloads\product_analytics_crew_automation
prompt_toolkit==3.0.50
propcache==0.3.1
proto-plus==1.26.1
protobuf==5.29.4
psutil==7.0.0
psycopg2-binary==2.9.9
pure_eval==0.2.3
pyarrow==19.0.1
pyasn1==0.6.1
pyasn1_modules==0.4.2
pyclipper==1.3.0.post6
pycparser==2.22
pydantic==2.11.4
pydantic-settings==2.9.1
pydantic_core==2.33.2
pydeck==0.9.1
pydub==0.25.1
pygame==2.1.3
pyglet==1.5.21
Pygments==2.19.1
PyJWT==2.10.1
pymdown-extensions==10.14.3
pyogrio==0.10.0
pyparsing==3.0.9
pypdf==5.4.0
PyPDF2==3.0.1
pypdfium2==4.30.1
PyPika==0.48.9
pyproj==3.7.1
pyproject_hooks==1.2.0
pyreadline3==3.5.4
pyright==1.1.400
pysbd==0.3.4
pytesseract==0.3.10
pytest==8.3.5
python-bidi==0.6.6
python-dateutil==2.9.0.post0
python-dotenv==1.0.0
python-jose==3.3.0
python-multipart==0.0.6
python-pptx==1.0.2
python-slugify==8.0.4
pytube==15.0.0
pytz==2024.2
pyvis==0.3.2
pywin32==310
PyYAML==6.0.2
pyzmq==26.4.0
qdrant-client==1.14.2
reacton==1.9.1
referencing==0.36.2
regex==2024.11.6
requests==2.32.3
requests-oauthlib==2.0.0
requests-toolbelt==1.0.0
rich==13.9.4
rich-click==1.8.8
rpds-py==0.24.0
rsa==4.9
ruff==0.11.5
safetensors==0.5.3
schema==0.7.7
scikit-image==0.25.2
scikit-learn==1.6.1
scipy==1.15.2
seaborn==0.13.2
semantic-version==2.10.0
sentence-transformers==4.1.0
shapely==2.1.0
shellingham==1.5.4
six==1.17.0
smmap==5.0.2
sniffio==1.3.1
solara==1.45.0
solara-server==1.45.0
solara-ui==1.45.0
soupsieve==2.7
SQLAlchemy==2.0.40
stable_baselines3==2.6.0
stack-data==0.6.3
starlette==0.45.3
streamlit==1.44.1
# Editable install with no version control (sumo-rl==1.4.5)
-e c:\users\<USER>\downloads\ml9\ml9
sumolib==1.23.1
sympy==1.13.3
tabulate==0.9.0
tenacity==9.1.2
tensorboard==2.15.2
tensorboard-data-server==0.7.2
tensorflow==2.15.0
tensorflow-estimator==2.15.0
tensorflow-intel==2.15.0
tensorflow-io-gcs-filesystem==0.31.0
tensorflow-probability==0.22.1
termcolor==3.0.1
text-unidecode==1.3
tf-agents==0.18.0
threadpoolctl==3.6.0
tifffile==2025.5.10
tiktoken==0.9.0
tokenizers==0.21.1
toml==0.10.2
tomli==2.2.1
tomli_w==1.2.0
tomlkit==0.12.0
torch==2.7.0
torchvision==0.22.0
tornado==6.4.2
tqdm==4.67.1
traci==1.23.1
traitlets==5.14.3
transformers==4.51.3
typer==0.15.2
types-python-dateutil==2.9.0.20241206
types-requests==2.32.0.20250328
typing-inspect==0.9.0
typing-inspection==0.4.0
typing_extensions==4.13.1
tzdata==2025.2
uritemplate==4.1.1
urllib3==2.3.0
uv==0.7.3
uvicorn==0.27.0
watchdog==6.0.0
watchfiles==1.0.5
wcwidth==0.2.13
websocket-client==1.8.0
websockets==11.0.3
Werkzeug==2.3.7
widgetsnbextension==4.0.14
wrapt==1.14.1
XlsxWriter==3.2.3
yarl==1.20.0
zipp==3.21.0
zstandard==0.23.0
