import os
from typing import Dict, Any

# Import the Tesseract OCR service
from ai.tesseract_ocr_service import tesseract_ocr_service

class OCRService:
    def __init__(self):
        # Use Tesseract OCR service
        self.tesseract_service = tesseract_ocr_service

    def extract_text(self, file_path: str, mode: str = "standard") -> Dict[str, Any]:
        """
        Extract text from an image using OCR
        """
        try:
            if not os.path.exists(file_path):
                return {"error": "Le fichier n'existe pas", "success": False}

            # Use Tesseract OCR service to extract text
            result = self.tesseract_service.extract_text(file_path, mode)
            return result

        except Exception as e:
            return {
                "error": str(e),
                "success": False
            }

ocr_service = OCRService()