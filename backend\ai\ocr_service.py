import os
import logging
from typing import Dict, Any

# Import the OCR services
from ai.tesseract_ocr_service import tesseract_ocr_service
from ai.gemini_service import gemini_service

# Configure logging
logger = logging.getLogger("auto-grade-scribe.ocr")

class OCRService:
    def __init__(self):
        # Use Tesseract OCR service as default
        self.tesseract_service = tesseract_ocr_service
        # Use Gemini service for enhanced capabilities
        self.gemini_service = gemini_service

        # Check if Gemini service is available
        self.gemini_available = self.gemini_service.is_available
        if self.gemini_available:
            logger.info("Gemini AI service is available and will be used for enhanced OCR")
        else:
            logger.warning("Gemini AI service is not available. Using Tesseract OCR only.")

    def extract_text(self, file_path: str, mode: str = "standard") -> Dict[str, Any]:
        """
        Extract text from an image using OCR

        Args:
            file_path: Path to the image file
            mode: OCR mode (standard, qcm, handwritten)

        Returns:
            Dictionary with extracted text and analysis
        """
        try:
            if not os.path.exists(file_path):
                return {"error": "Le fichier n'existe pas", "success": False}

            # For handwritten mode, use Gemini if available
            if mode == "handwritten" and self.gemini_available:
                logger.info(f"Using Gemini AI for handwritten text extraction: {file_path}")
                result = self.gemini_service.analyze_handwritten_text(file_path)
                return result

            # For QCM mode with enhanced analysis, use Gemini if available
            if mode == "qcm-enhanced" and self.gemini_available:
                logger.info(f"Using Gemini AI for enhanced QCM analysis: {file_path}")
                result = self.gemini_service.analyze_handwritten_text(file_path)
                # Set the mode to qcm for compatibility
                result["mode"] = "qcm"
                return result

            # Default to Tesseract OCR service
            logger.info(f"Using Tesseract OCR for text extraction: {file_path}, mode: {mode}")
            result = self.tesseract_service.extract_text(file_path, mode)
            return result

        except Exception as e:
            logger.error(f"Error in OCR service: {str(e)}")
            return {
                "error": str(e),
                "success": False
            }

ocr_service = OCRService()