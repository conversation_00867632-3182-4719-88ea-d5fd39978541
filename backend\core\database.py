"""
Configuration et modèles de base de données pour Auto-Grade Scribe
"""

import uuid
from datetime import datetime
from typing import Optional, Dict, Any
from sqlalchemy import create_engine, Column, String, DateTime, Text, Float, <PERSON><PERSON>an, Integer, JSON
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.dialects.postgresql import UUID
from .config import settings

# Configuration de la base de données
engine = create_engine(
    settings.database_url,
    pool_size=settings.database_pool_size,
    max_overflow=settings.database_max_overflow,
    pool_timeout=settings.database_pool_timeout,
    echo=settings.debug
)

SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
Base = declarative_base()

# Modèles de base de données
class User(Base):
    """Modèle utilisateur"""
    __tablename__ = "users"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    username = Column(String(50), unique=True, nullable=False, index=True)
    email = Column(String(100), unique=True, nullable=False, index=True)
    hashed_password = Column(String(255), nullable=False)
    full_name = Column(String(100))
    role = Column(String(20), default="teacher")  # teacher, admin
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

class Exam(Base):
    """Modèle examen"""
    __tablename__ = "exams"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    title = Column(String(200), nullable=False)
    description = Column(Text)
    exam_type = Column(String(20), nullable=False)  # qcm, open_ended, mixed
    correct_answers = Column(JSON)
    grading_config = Column(JSON)
    created_by = Column(UUID(as_uuid=True), nullable=False)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

class UploadedFile(Base):
    """Modèle fichier uploadé"""
    __tablename__ = "uploaded_files"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    filename = Column(String(255), nullable=False)
    original_filename = Column(String(255), nullable=False)
    file_path = Column(String(500), nullable=False)
    file_size = Column(Integer)
    content_type = Column(String(100))
    uploaded_by = Column(UUID(as_uuid=True), nullable=False)
    uploaded_at = Column(DateTime, default=datetime.utcnow)

class OCRResult(Base):
    """Modèle résultat OCR"""
    __tablename__ = "ocr_results"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    file_id = Column(UUID(as_uuid=True), nullable=False)
    provider_used = Column(String(50), nullable=False)
    extracted_text = Column(Text)
    confidence = Column(Float)
    processing_time = Column(Float)
    metadata = Column(JSON)
    created_at = Column(DateTime, default=datetime.utcnow)

class GradingResult(Base):
    """Modèle résultat de correction"""
    __tablename__ = "grading_results"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    file_id = Column(UUID(as_uuid=True), nullable=False)
    exam_id = Column(UUID(as_uuid=True), nullable=False)
    student_answers = Column(JSON)
    grading_details = Column(JSON)
    final_score = Column(JSON)
    feedback = Column(JSON)
    requires_manual_review = Column(Boolean, default=False)
    processing_time = Column(Float)
    created_at = Column(DateTime, default=datetime.utcnow)

class ManualReview(Base):
    """Modèle révision manuelle"""
    __tablename__ = "manual_reviews"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    grading_result_id = Column(UUID(as_uuid=True), nullable=False)
    reviewer_id = Column(UUID(as_uuid=True), nullable=False)
    original_score = Column(Float)
    reviewed_score = Column(Float)
    review_comments = Column(Text)
    changes_made = Column(JSON)
    status = Column(String(20), default="pending")  # pending, completed, rejected
    reviewed_at = Column(DateTime, default=datetime.utcnow)

class AuditLog(Base):
    """Modèle journal d'audit"""
    __tablename__ = "audit_logs"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    user_id = Column(UUID(as_uuid=True))
    action = Column(String(100), nullable=False)
    resource_type = Column(String(50))
    resource_id = Column(UUID(as_uuid=True))
    details = Column(JSON)
    ip_address = Column(String(45))
    user_agent = Column(String(500))
    timestamp = Column(DateTime, default=datetime.utcnow)

# Fonctions utilitaires
def get_db() -> Session:
    """Obtenir une session de base de données"""
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

def create_tables():
    """Créer toutes les tables"""
    Base.metadata.create_all(bind=engine)

def drop_tables():
    """Supprimer toutes les tables"""
    Base.metadata.drop_all(bind=engine)

def init_database():
    """Initialiser la base de données"""
    create_tables()
    print("✅ Base de données initialisée avec succès")

if __name__ == "__main__":
    init_database()
