'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import QCMForm from '@/components/QCMForm';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { ArrowLeft } from 'lucide-react';
import { Button } from '@/components/ui/button';

export default function QCMPage() {
  const router = useRouter();
  
  return (
    <div className="flex min-h-screen flex-col">
      <Header />
      <main className="flex-1 p-4 md:p-6">
        <div className="container mx-auto py-6 max-w-7xl">
          <div className="mb-6">
            <Button 
              variant="ghost" 
              className="mb-4 flex items-center gap-2" 
              onClick={() => router.push('/dashboard')}
            >
              <ArrowLeft className="h-4 w-4" />
              <span>Retour au tableau de bord</span>
            </Button>
            <h1 className="text-3xl font-bold gradient-heading">Correction de QCM</h1>
            <p className="text-muted-foreground mt-2">
              Téléchargez une image de QCM et entrez les réponses correctes pour obtenir une correction automatique
            </p>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>Formulaire de correction QCM</CardTitle>
              <CardDescription>
                Téléchargez une image de la feuille de réponses et indiquez les réponses correctes
              </CardDescription>
            </CardHeader>
            <CardContent>
              <QCMForm />
            </CardContent>
          </Card>
        </div>
      </main>
      <Footer />
    </div>
  );
}
