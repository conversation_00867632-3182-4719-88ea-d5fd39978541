from sympy.testing.pytest import warns_deprecated_sympy

from sympy.core.symbol import Symbol
from sympy.polys.polytools import Poly
from sympy.matrices import Matrix, randMatrix
from sympy.matrices.normalforms import (
    invariant_factors,
    smith_normal_form,
    smith_normal_decomp,
    hermite_normal_form,
    is_smith_normal_form,
)
from sympy.polys.domains import ZZ, QQ
from sympy.core.numbers import Integer

import random


def test_smith_normal():
    m = Matrix([[12,6,4,8],[3,9,6,12],[2,16,14,28],[20,10,10,20]])
    smf = Matrix([[1, 0, 0, 0], [0, 10, 0, 0], [0, 0, 30, 0], [0, 0, 0, 0]])
    assert smith_normal_form(m) == smf

    a, s, t = smith_normal_decomp(m)
    assert a == s * m * t

    x = Symbol('x')
    with warns_deprecated_sympy():
        m = Matrix([[<PERSON>y(x-1), <PERSON><PERSON>(1, x),<PERSON><PERSON>(-1,x)],
                    [0, <PERSON><PERSON>(x), <PERSON><PERSON>(-1,x)],
                    [<PERSON><PERSON>(0,x),<PERSON><PERSON>(-1,x),<PERSON><PERSON>(x)]])
    invs = 1, x - 1, x**2 - 1
    assert invariant_factors(m, domain=QQ[x]) == invs

    m = Matrix([[2, 4]])
    smf = Matrix([[2, 0]])
    assert smith_normal_form(m) == smf

    prng = random.Random(0)
    for i in range(6):
        for j in range(6):
            for _ in range(10 if i*j else 1):
                m = randMatrix(i, j, max=5, percent=50, prng=prng)
                a, s, t = smith_normal_decomp(m)
                assert a == s * m * t
                assert is_smith_normal_form(a)
                s.inv().to_DM(ZZ)
                t.inv().to_DM(ZZ)

                a, s, t = smith_normal_decomp(m, QQ)
                assert a == s * m * t
                assert is_smith_normal_form(a)
                s.inv()
                t.inv()


def test_smith_normal_deprecated():
    from sympy.polys.solvers import RawMatrix as Matrix

    with warns_deprecated_sympy():
        m = Matrix([[12, 6, 4,8],[3,9,6,12],[2,16,14,28],[20,10,10,20]])
    setattr(m, 'ring', ZZ)
    with warns_deprecated_sympy():
        smf = Matrix([[1, 0, 0, 0], [0, 10, 0, 0], [0, 0, 30, 0], [0, 0, 0, 0]])
    assert smith_normal_form(m) == smf

    x = Symbol('x')
    with warns_deprecated_sympy():
        m = Matrix([[Poly(x-1), Poly(1, x),Poly(-1,x)],
                    [0, Poly(x), Poly(-1,x)],
                    [Poly(0,x),Poly(-1,x),Poly(x)]])
    setattr(m, 'ring', QQ[x])
    invs = (Poly(1, x, domain='QQ'), Poly(x - 1, domain='QQ'), Poly(x**2 - 1, domain='QQ'))
    assert invariant_factors(m) == invs

    with warns_deprecated_sympy():
        m = Matrix([[2, 4]])
    setattr(m, 'ring', ZZ)
    with warns_deprecated_sympy():
        smf = Matrix([[2, 0]])
    assert smith_normal_form(m) == smf


def test_hermite_normal():
    m = Matrix([[2, 7, 17, 29, 41], [3, 11, 19, 31, 43], [5, 13, 23, 37, 47]])
    hnf = Matrix([[1, 0, 0], [0, 2, 1], [0, 0, 1]])
    assert hermite_normal_form(m) == hnf

    tr_hnf = Matrix([[37, 0, 19], [222, -6, 113], [48, 0, 25], [0, 2, 1], [0, 0, 1]])
    assert hermite_normal_form(m.transpose()) == tr_hnf

    m = Matrix([[8, 28, 68, 116, 164], [3, 11, 19, 31, 43], [5, 13, 23, 37, 47]])
    hnf = Matrix([[4, 0, 0], [0, 2, 1], [0, 0, 1]])
    assert hermite_normal_form(m) == hnf
    assert hermite_normal_form(m, D=8) == hnf
    assert hermite_normal_form(m, D=ZZ(8)) == hnf
    assert hermite_normal_form(m, D=Integer(8)) == hnf

    m = Matrix([[10, 8, 6, 30, 2], [45, 36, 27, 18, 9], [5, 4, 3, 2, 1]])
    hnf = Matrix([[26, 2], [0, 9], [0, 1]])
    assert hermite_normal_form(m) == hnf

    m = Matrix([[2, 7], [0, 0], [0, 0]])
    hnf = Matrix([[1], [0], [0]])
    assert hermite_normal_form(m) == hnf


def test_issue_23410():
    A = Matrix([[1, 12], [0, 8], [0, 5]])
    H = Matrix([[1, 0], [0, 8], [0, 5]])
    assert hermite_normal_form(A) == H
