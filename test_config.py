#!/usr/bin/env python3
"""
Test de configuration pour Auto-Grade Scribe
"""

import sys
import os

def test_pydantic_settings():
    """Tester la configuration Pydantic"""
    try:
        # Ajouter le chemin backend
        sys.path.insert(0, 'backend')
        
        print("🔧 Test de la configuration Pydantic...")
        
        # Importer la configuration
        from core.config import settings
        
        print("✅ Configuration chargée avec succès!")
        print(f"📊 Environnement: {settings.environment}")
        print(f"🗄️ Base de données: {settings.database_url}")
        print(f"🌐 Port API: {settings.api_port}")
        print(f"📁 Répertoire uploads: {settings.upload_directory}")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur configuration: {e}")
        return False

def test_imports():
    """Tester les imports principaux"""
    try:
        print("\n📦 Test des imports...")
        
        # Test pydantic-settings
        try:
            import pydantic_settings
            print("✅ pydantic-settings disponible")
        except ImportError:
            print("❌ pydantic-settings manquant")
            return False
        
        # Test FastAPI
        try:
            import fastapi
            print("✅ FastAPI disponible")
        except ImportError:
            print("❌ FastAPI manquant")
            return False
        
        # Test SQLAlchemy
        try:
            import sqlalchemy
            print("✅ SQLAlchemy disponible")
        except ImportError:
            print("❌ SQLAlchemy manquant")
            return False
        
        # Test psycopg2
        try:
            import psycopg2
            print("✅ psycopg2 disponible")
        except ImportError:
            print("❌ psycopg2 manquant")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur imports: {e}")
        return False

def test_directories():
    """Tester la création des répertoires"""
    try:
        print("\n📁 Test des répertoires...")
        
        directories = ["uploads", "results", "temp", "logs", "models"]
        
        for directory in directories:
            os.makedirs(directory, exist_ok=True)
            if os.path.exists(directory):
                print(f"✅ {directory}")
            else:
                print(f"❌ {directory}")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur répertoires: {e}")
        return False

def main():
    """Fonction principale"""
    print("🧪 Test de Configuration Auto-Grade Scribe")
    print("=" * 50)
    
    tests = [
        ("Imports", test_imports),
        ("Répertoires", test_directories),
        ("Configuration", test_pydantic_settings)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n🔍 Test: {test_name}")
        result = test_func()
        results.append((test_name, result))
        
        if result:
            print(f"✅ {test_name}: RÉUSSI")
        else:
            print(f"❌ {test_name}: ÉCHOUÉ")
    
    # Résumé
    print("\n" + "=" * 50)
    print("📊 RÉSUMÉ DES TESTS")
    print("=" * 50)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ RÉUSSI" if result else "❌ ÉCHOUÉ"
        print(f"{test_name:15} : {status}")
    
    print(f"\nRésultat: {passed}/{total} tests réussis")
    
    if passed == total:
        print("\n🎉 Tous les tests sont réussis!")
        print("✅ L'application peut maintenant démarrer")
        print("🚀 Commande: python run.py")
    else:
        print("\n⚠️ Certains tests ont échoué")
        print("💡 Installez les dépendances manquantes:")
        print("   pip install -r backend/requirements_opensource.txt")

if __name__ == "__main__":
    main()
