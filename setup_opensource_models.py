#!/usr/bin/env python3
"""
Script d'installation des modèles open-source pour Auto-Grade Scribe
Télécharge et configure tous les modèles nécessaires
"""

import os
import sys
import logging
from pathlib import Path

# Configuration du logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def create_directories():
    """Créer les répertoires nécessaires"""
    directories = [
        'models/sentence_transformers',
        'models/transformers',
        'models/trocr',
        'uploads',
        'results',
        'temp',
        'logs'
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
        logger.info(f"Répertoire créé: {directory}")

def install_sentence_transformers():
    """Installer et télécharger les modèles Sentence Transformers"""
    try:
        from sentence_transformers import SentenceTransformer
        
        models = [
            'all-MiniLM-L6-v2',  # Modèle rapide et efficace
            'paraphrase-multilingual-MiniLM-L12-v2',  # Support multilingue
            'all-mpnet-base-v2'  # Haute qualité
        ]
        
        for model_name in models:
            logger.info(f"Téléchargement du modèle Sentence Transformers: {model_name}")
            try:
                model = SentenceTransformer(model_name)
                logger.info(f"✅ Modèle {model_name} téléchargé avec succès")
            except Exception as e:
                logger.error(f"❌ Erreur téléchargement {model_name}: {str(e)}")
                
    except ImportError:
        logger.error("❌ Sentence Transformers non installé. Exécutez: pip install sentence-transformers")

def install_trocr_models():
    """Installer et télécharger les modèles TrOCR"""
    try:
        from transformers import TrOCRProcessor, VisionEncoderDecoderModel
        
        models = [
            'microsoft/trocr-base-handwritten',  # Écriture manuscrite
            'microsoft/trocr-base-printed'       # Texte imprimé
        ]
        
        for model_name in models:
            logger.info(f"Téléchargement du modèle TrOCR: {model_name}")
            try:
                processor = TrOCRProcessor.from_pretrained(model_name)
                model = VisionEncoderDecoderModel.from_pretrained(model_name)
                logger.info(f"✅ Modèle TrOCR {model_name} téléchargé avec succès")
            except Exception as e:
                logger.error(f"❌ Erreur téléchargement TrOCR {model_name}: {str(e)}")
                
    except ImportError:
        logger.error("❌ Transformers non installé. Exécutez: pip install transformers")

def install_easyocr():
    """Installer et configurer EasyOCR"""
    try:
        import easyocr
        
        logger.info("Configuration EasyOCR...")
        # Initialiser EasyOCR pour télécharger les modèles
        reader = easyocr.Reader(['en', 'fr'], gpu=False)
        logger.info("✅ EasyOCR configuré avec succès (Anglais + Français)")
        
    except ImportError:
        logger.error("❌ EasyOCR non installé. Exécutez: pip install easyocr")
    except Exception as e:
        logger.error(f"❌ Erreur configuration EasyOCR: {str(e)}")

def install_paddleocr():
    """Installer et configurer PaddleOCR"""
    try:
        from paddleocr import PaddleOCR
        
        logger.info("Configuration PaddleOCR...")
        # Initialiser PaddleOCR pour télécharger les modèles
        ocr = PaddleOCR(use_angle_cls=True, lang='en', use_gpu=False, show_log=False)
        logger.info("✅ PaddleOCR configuré avec succès")
        
    except ImportError:
        logger.error("❌ PaddleOCR non installé. Exécutez: pip install paddleocr")
    except Exception as e:
        logger.error(f"❌ Erreur configuration PaddleOCR: {str(e)}")

def install_nltk_data():
    """Télécharger les données NLTK nécessaires"""
    try:
        import nltk
        
        logger.info("Téléchargement des données NLTK...")
        nltk_downloads = [
            'punkt',
            'stopwords',
            'wordnet',
            'averaged_perceptron_tagger'
        ]
        
        for item in nltk_downloads:
            try:
                nltk.download(item, quiet=True)
                logger.info(f"✅ NLTK {item} téléchargé")
            except Exception as e:
                logger.error(f"❌ Erreur téléchargement NLTK {item}: {str(e)}")
                
    except ImportError:
        logger.error("❌ NLTK non installé. Exécutez: pip install nltk")

def install_spacy_models():
    """Installer les modèles spaCy"""
    try:
        import spacy
        import subprocess
        
        models = [
            'en_core_web_sm',  # Anglais
            'fr_core_news_sm'  # Français
        ]
        
        for model in models:
            logger.info(f"Installation du modèle spaCy: {model}")
            try:
                subprocess.run([sys.executable, '-m', 'spacy', 'download', model], 
                             check=True, capture_output=True)
                logger.info(f"✅ Modèle spaCy {model} installé")
            except subprocess.CalledProcessError as e:
                logger.error(f"❌ Erreur installation spaCy {model}: {str(e)}")
                
    except ImportError:
        logger.error("❌ spaCy non installé. Exécutez: pip install spacy")

def test_installations():
    """Tester les installations"""
    logger.info("🧪 Test des installations...")
    
    tests = []
    
    # Test Sentence Transformers
    try:
        from sentence_transformers import SentenceTransformer
        model = SentenceTransformer('all-MiniLM-L6-v2')
        test_embedding = model.encode(["Test sentence"])
        tests.append(("Sentence Transformers", True, ""))
    except Exception as e:
        tests.append(("Sentence Transformers", False, str(e)))
    
    # Test TrOCR
    try:
        from transformers import TrOCRProcessor, VisionEncoderDecoderModel
        processor = TrOCRProcessor.from_pretrained('microsoft/trocr-base-printed')
        tests.append(("TrOCR", True, ""))
    except Exception as e:
        tests.append(("TrOCR", False, str(e)))
    
    # Test EasyOCR
    try:
        import easyocr
        reader = easyocr.Reader(['en'], gpu=False)
        tests.append(("EasyOCR", True, ""))
    except Exception as e:
        tests.append(("EasyOCR", False, str(e)))
    
    # Test PaddleOCR
    try:
        from paddleocr import PaddleOCR
        ocr = PaddleOCR(use_angle_cls=True, lang='en', use_gpu=False, show_log=False)
        tests.append(("PaddleOCR", True, ""))
    except Exception as e:
        tests.append(("PaddleOCR", False, str(e)))
    
    # Afficher les résultats
    logger.info("📊 Résultats des tests:")
    for name, success, error in tests:
        if success:
            logger.info(f"  ✅ {name}: OK")
        else:
            logger.error(f"  ❌ {name}: {error}")
    
    successful_tests = sum(1 for _, success, _ in tests if success)
    logger.info(f"📈 {successful_tests}/{len(tests)} tests réussis")
    
    return successful_tests == len(tests)

def create_config_file():
    """Créer un fichier de configuration pour les modèles"""
    config = {
        "models": {
            "sentence_transformers": {
                "similarity": "all-MiniLM-L6-v2",
                "multilingual": "paraphrase-multilingual-MiniLM-L12-v2",
                "semantic": "all-mpnet-base-v2"
            },
            "trocr": {
                "handwritten": "microsoft/trocr-base-handwritten",
                "printed": "microsoft/trocr-base-printed"
            },
            "ocr": {
                "easyocr_languages": ["en", "fr"],
                "paddleocr_language": "en"
            }
        },
        "cache_directories": {
            "sentence_transformers": "./models/sentence_transformers",
            "transformers": "./models/transformers"
        }
    }
    
    import json
    with open('models_config.json', 'w') as f:
        json.dump(config, f, indent=2)
    
    logger.info("✅ Fichier de configuration créé: models_config.json")

def main():
    """Fonction principale"""
    logger.info("🚀 Installation des modèles open-source pour Auto-Grade Scribe")
    logger.info("=" * 60)
    
    # Créer les répertoires
    create_directories()
    
    # Installer les modèles
    logger.info("📦 Installation des modèles...")
    install_sentence_transformers()
    install_trocr_models()
    install_easyocr()
    install_paddleocr()
    install_nltk_data()
    install_spacy_models()
    
    # Créer la configuration
    create_config_file()
    
    # Tester les installations
    if test_installations():
        logger.info("🎉 Toutes les installations sont réussies!")
        logger.info("✅ Auto-Grade Scribe est prêt avec les solutions open-source")
    else:
        logger.warning("⚠️ Certaines installations ont échoué")
        logger.info("💡 Vérifiez les erreurs ci-dessus et réinstallez si nécessaire")
    
    logger.info("\n📋 Prochaines étapes:")
    logger.info("1. Copiez .env.opensource vers .env")
    logger.info("2. Démarrez PostgreSQL: docker-compose -f docker-compose-simple.yml up -d")
    logger.info("3. Lancez l'application: cd backend && python app_working.py")

if __name__ == "__main__":
    main()
