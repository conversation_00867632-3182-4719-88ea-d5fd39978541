#!/usr/bin/env python3
"""
Script pour initialiser la base de données PostgreSQL pour Auto-Grade Scribe.
Ce script vérifie que PostgreSQL est accessible, crée les tables nécessaires
et ajoute un utilisateur administrateur par défaut.
"""

import os
import sys
import time
import subprocess
from sqlalchemy import create_engine, text
from sqlalchemy.exc import OperationalError
from dotenv import load_dotenv

# Charger les variables d'environnement
load_dotenv()

# Configuration de la base de données
DB_USER = os.getenv("POSTGRES_USER", "autograde")
DB_PASSWORD = os.getenv("POSTGRES_PASSWORD", "autograde123")
DB_HOST = os.getenv("POSTGRES_HOST", "localhost")
DB_PORT = os.getenv("POSTGRES_PORT", "5432")
DB_NAME = os.getenv("POSTGRES_DB", "autogradedb")

# Construire l'URL de connexion
DATABASE_URL = f"postgresql://{DB_USER}:{DB_PASSWORD}@{DB_HOST}:{DB_PORT}/{DB_NAME}"

def check_docker_running():
    """Vérifier si Docker est en cours d'exécution"""
    try:
        result = subprocess.run(["docker", "ps"], capture_output=True, text=True)
        return result.returncode == 0
    except Exception:
        return False

def start_docker_containers():
    """Démarrer les conteneurs Docker pour PostgreSQL"""
    try:
        print("Démarrage des conteneurs Docker pour PostgreSQL...")
        result = subprocess.run(["docker-compose", "up", "-d"], capture_output=True, text=True)
        if result.returncode == 0:
            print("Conteneurs Docker démarrés avec succès!")
            # Attendre que les conteneurs soient prêts
            print("Attente de 5 secondes pour que les conteneurs soient prêts...")
            time.sleep(5)
            return True
        else:
            print("Erreur lors du démarrage des conteneurs Docker:")
            print(result.stderr)
            return False
    except Exception as e:
        print(f"Erreur lors du démarrage des conteneurs Docker: {e}")
        return False

def wait_for_postgres(max_retries=10, retry_interval=5):
    """Attendre que PostgreSQL soit disponible"""
    print(f"Tentative de connexion à PostgreSQL à {DB_HOST}:{DB_PORT}...")
    
    engine = create_engine(
        f"postgresql://{DB_USER}:{DB_PASSWORD}@{DB_HOST}:{DB_PORT}/postgres",
        pool_pre_ping=True
    )
    
    for attempt in range(max_retries):
        try:
            with engine.connect() as conn:
                result = conn.execute(text("SELECT 1"))
                if result:
                    print("Connexion à PostgreSQL réussie!")
                    return True
        except OperationalError as e:
            print(f"Tentative {attempt+1}/{max_retries} échouée: {e}")
            if attempt < max_retries - 1:
                print(f"Nouvelle tentative dans {retry_interval} secondes...")
                time.sleep(retry_interval)
    
    print("Impossible de se connecter à PostgreSQL après plusieurs tentatives")
    return False

def create_database():
    """Créer la base de données si elle n'existe pas"""
    engine = create_engine(
        f"postgresql://{DB_USER}:{DB_PASSWORD}@{DB_HOST}:{DB_PORT}/postgres",
        isolation_level="AUTOCOMMIT"
    )
    
    try:
        with engine.connect() as conn:
            # Vérifier si la base de données existe
            result = conn.execute(text(
                f"SELECT 1 FROM pg_database WHERE datname = '{DB_NAME}'"
            ))
            if not result.fetchone():
                print(f"Création de la base de données '{DB_NAME}'...")
                conn.execute(text(f"CREATE DATABASE {DB_NAME}"))
                print(f"Base de données '{DB_NAME}' créée avec succès!")
            else:
                print(f"La base de données '{DB_NAME}' existe déjà.")
    except Exception as e:
        print(f"Erreur lors de la création de la base de données: {e}")
        return False
    
    return True

def create_tables():
    """Créer les tables dans la base de données"""
    try:
        # Importer les modèles et la base
        sys.path.append('backend')
        from database import Base, engine
        
        print("Création des tables dans la base de données...")
        Base.metadata.create_all(bind=engine)
        print("Tables créées avec succès!")
        return True
    except Exception as e:
        print(f"Erreur lors de la création des tables: {e}")
        return False

def main():
    """Fonction principale"""
    print("=== Initialisation de PostgreSQL pour Auto-Grade Scribe ===")
    
    # Vérifier si Docker est en cours d'exécution
    if not check_docker_running():
        print("Docker n'est pas en cours d'exécution. Veuillez démarrer Docker et réessayer.")
        sys.exit(1)
    
    # Démarrer les conteneurs Docker
    if not start_docker_containers():
        print("Impossible de démarrer les conteneurs Docker. Veuillez vérifier votre configuration.")
        sys.exit(1)
    
    # Attendre que PostgreSQL soit disponible
    if not wait_for_postgres():
        print("Impossible de se connecter à PostgreSQL. Veuillez vérifier que les conteneurs sont en cours d'exécution.")
        sys.exit(1)
    
    # Créer la base de données
    if not create_database():
        print("Impossible de créer la base de données. Veuillez vérifier vos droits d'accès.")
        sys.exit(1)
    
    # Créer les tables
    if not create_tables():
        print("Impossible de créer les tables. Veuillez vérifier votre modèle de données.")
        sys.exit(1)
    
    print("\nInitialisation terminée avec succès!")
    print(f"URL de connexion: {DATABASE_URL}")
    
    print("\nPour démarrer l'application:")
    print("1. Démarrer le backend: cd backend && uvicorn app:app --reload --host 0.0.0.0 --port 8000")
    print("2. Démarrer le frontend: npm run dev")
    
    print("\nPour accéder à pgAdmin:")
    print("URL: http://localhost:5050")
    print("Email: <EMAIL>")
    print("Mot de passe: admin123")

if __name__ == "__main__":
    main()
