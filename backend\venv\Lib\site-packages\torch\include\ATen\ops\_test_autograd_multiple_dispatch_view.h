#pragma once

// @generated by torchgen/gen.py from Function.h

#include <ATen/Context.h>
#include <ATen/DeviceGuard.h>
#include <ATen/TensorUtils.h>
#include <ATen/TracerMode.h>
#include <ATen/core/Generator.h>
#include <ATen/core/Reduction.h>
#include <ATen/core/Tensor.h>
#include <c10/core/Scalar.h>
#include <c10/core/Storage.h>
#include <c10/core/TensorOptions.h>
#include <c10/util/Deprecated.h>
#include <c10/util/Optional.h>



#include <ATen/ops/_test_autograd_multiple_dispatch_view_ops.h>

namespace at {


// aten::_test_autograd_multiple_dispatch_view(Tensor(a) self) -> Tensor(a)
inline at::Tensor _test_autograd_multiple_dispatch_view(const at::Tensor & self) {
    return at::_ops::_test_autograd_multiple_dispatch_view::call(self);
}

}
