#!/usr/bin/env python3
"""
🤖 Setup des Modèles IA Open-Source pour Auto-Grade Scribe v4.0.0
Télécharge et configure tous les modèles nécessaires
"""

import os
import sys
import subprocess
import logging

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def main():
    """Setup simplifié des modèles IA"""
    print("🤖 Setup des Modèles IA Open-Source")
    print("=" * 50)
    print("🆓 Auto-Grade Scribe v4.0.0")
    print("✨ Configuration des modèles 100% open-source")
    print("=" * 50)
    
    # Créer les répertoires
    os.makedirs("models", exist_ok=True)
    os.makedirs("models/cache", exist_ok=True)
    
    # Installer les dépendances essentielles
    dependencies = [
        "transformers>=4.21.0",
        "sentence-transformers>=2.2.0",
        "easyocr>=1.7.0",
        "fuzzywuzzy>=0.18.0",
        "python-Levenshtein>=0.20.0"
    ]
    
    logger.info("📦 Installation des dépendances IA...")
    for dep in dependencies:
        try:
            logger.info(f"Installing {dep}...")
            subprocess.run([sys.executable, "-m", "pip", "install", dep], 
                         check=True, capture_output=True)
            logger.info(f"✅ {dep}")
        except subprocess.CalledProcessError as e:
            logger.warning(f"⚠️ {dep}: {e}")
    
    # Configuration des modèles
    config = {
        "models": {
            "trocr_handwritten": "microsoft/trocr-base-handwritten",
            "trocr_printed": "microsoft/trocr-base-printed",
            "similarity": "all-MiniLM-L6-v2",
            "semantic": "all-mpnet-base-v2"
        },
        "cache_dir": "./models/cache",
        "ocr_providers": ["tesseract", "easyocr"],
        "grading_threshold": 0.7
    }
    
    import json
    with open("models_config.json", "w") as f:
        json.dump(config, f, indent=2)
    
    print("\n🎉 SETUP TERMINÉ!")
    print("✅ Configuration des modèles sauvegardée")
    print("🚀 Les modèles seront téléchargés automatiquement au premier usage")
    print("\n💡 Démarrage:")
    print("   python start_windows.py")

if __name__ == "__main__":
    main()
