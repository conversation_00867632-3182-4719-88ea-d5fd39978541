'use client';

import React from 'react';
import NextLink from 'next/link';
import { useRouter } from 'next/navigation';
import { cn } from '@/lib/utils';

interface LinkProps extends React.AnchorHTMLAttributes<HTMLAnchorElement> {
  href: string;
  prefetch?: boolean;
  replace?: boolean;
  scroll?: boolean;
  shallow?: boolean;
  locale?: string | false;
  children: React.ReactNode;
  className?: string;
  activeClassName?: string;
  exact?: boolean;
}

/**
 * Composant Link personnalisé qui utilise le routage de Next.js
 * 
 * @param href - URL de destination
 * @param prefetch - Précharger la page (true par défaut)
 * @param replace - Remplacer l'entrée d'historique actuelle au lieu d'en ajouter une nouvelle
 * @param scroll - Faire défiler jusqu'au haut de la page après la navigation
 * @param shallow - Mettre à jour le chemin sans exécuter getStaticProps, getServerSideProps ou getInitialProps
 * @param locale - Locale à utiliser pour la navigation
 * @param children - Contenu du lien
 * @param className - Classes CSS à appliquer au lien
 * @param activeClassName - Classes CSS à appliquer au lien lorsqu'il est actif
 * @param exact - Correspondance exacte du chemin pour la classe active
 */
export function Link({
  href,
  prefetch,
  replace,
  scroll,
  shallow,
  locale,
  children,
  className,
  activeClassName,
  exact,
  ...props
}: LinkProps) {
  const router = useRouter();
  const pathname = typeof window !== 'undefined' ? window.location.pathname : '';
  
  // Déterminer si le lien est actif
  const isActive = exact 
    ? pathname === href 
    : pathname.startsWith(href.toString()) && href !== '/';

  return (
    <NextLink
      href={href}
      prefetch={prefetch}
      replace={replace}
      scroll={scroll}
      shallow={shallow}
      locale={locale}
      className={cn(
        className,
        isActive && activeClassName
      )}
      {...props}
    >
      {children}
    </NextLink>
  );
}

export default Link;
