"""
Service de Base de Données Avancé pour Auto-Grade Scribe
Gestion complète des résultats avec audit trail et historique
"""

import os
import logging
import asyncio
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timezone
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, desc, func
import json

logger = logging.getLogger("auto-grade-scribe.database")

class DatabaseService:
    """Service de base de données avancé avec audit trail"""
    
    def __init__(self, db_session_factory):
        self.SessionLocal = db_session_factory
        logger.info("Database Service initialized")
    
    async def save_exam_results_complete(
        self,
        exam_id: str,
        ocr_result: Dict[str, Any],
        grading_result: Dict[str, Any],
        user_id: Optional[int] = None
    ) -> Dict[str, Any]:
        """
        Sauvegarder les résultats complets d'un examen avec audit trail
        """
        try:
            db = self.SessionLocal()
            
            try:
                from models_simple import (
                    Exam, ExamResult, GradeHistory, AuditLog,
                    ExamStatus, GradeStatus, ActionType
                )
                
                # 1. Mettre à jour l'examen
                exam = db.query(Exam).filter(Exam.id == exam_id).first()
                if not exam:
                    return {
                        'success': False,
                        'error': f'Exam {exam_id} not found'
                    }
                
                # Mettre à jour le statut de l'examen
                old_status = exam.status
                exam.status = ExamStatus.PROCESSED
                exam.updated_at = datetime.now(timezone.utc)
                
                # 2. Créer ou mettre à jour ExamResult
                exam_result = db.query(ExamResult).filter(
                    ExamResult.exam_id == exam_id
                ).first()
                
                if not exam_result:
                    exam_result = ExamResult(
                        exam_id=exam_id,
                        student_id=exam.user_id,  # Utiliser l'utilisateur de l'examen
                        total_score=grading_result.get('final_score', {}).get('score', 0),
                        max_score=grading_result.get('final_score', {}).get('max_score', 0),
                        percentage=grading_result.get('final_score', {}).get('percentage', 0),
                        letter_grade=grading_result.get('final_score', {}).get('grade', 'F'),
                        grade_status=GradeStatus.COMPLETED
                    )
                    db.add(exam_result)
                else:
                    # Mettre à jour les résultats existants
                    exam_result.total_score = grading_result.get('final_score', {}).get('score', 0)
                    exam_result.max_score = grading_result.get('final_score', {}).get('max_score', 0)
                    exam_result.percentage = grading_result.get('final_score', {}).get('percentage', 0)
                    exam_result.letter_grade = grading_result.get('final_score', {}).get('grade', 'F')
                    exam_result.updated_at = datetime.now(timezone.utc)
                
                # Ajouter les détails OCR et de correction
                exam_result.extracted_answers = grading_result.get('student_answers', {})
                exam_result.correct_answers = grading_result.get('correct_answers', {})
                exam_result.question_results = grading_result.get('grading_result', {}).get('results', {})
                
                # Informations sur l'IA utilisée
                exam_result.ai_confidence = grading_result.get('final_score', {}).get('confidence', 0.5)
                exam_result.ai_model_used = ocr_result.get('provider_used', 'unknown')
                exam_result.ai_analysis = {
                    'ocr_details': {
                        'confidence': ocr_result.get('confidence', 0),
                        'provider': ocr_result.get('provider_used', 'unknown'),
                        'processing_time': ocr_result.get('processing_time', 0)
                    },
                    'grading_details': {
                        'exam_type': grading_result.get('exam_type', 'unknown'),
                        'processing_time': grading_result.get('processing_time', 0),
                        'requires_review': grading_result.get('requires_manual_review', False)
                    }
                }
                
                # Marquer pour révision manuelle si nécessaire
                exam_result.requires_manual_review = grading_result.get('requires_manual_review', False)
                
                # Feedback détaillé
                exam_result.feedback = json.dumps(grading_result.get('feedback', {}))
                
                # 3. Créer une entrée dans l'historique des notes
                grade_history = GradeHistory(
                    exam_result_id=exam_result.id if exam_result.id else None,
                    old_score=0,  # Première note
                    new_score=exam_result.total_score,
                    old_grade=None,
                    new_grade=exam_result.letter_grade,
                    changed_by_id=user_id,
                    change_reason="Correction automatique",
                    change_timestamp=datetime.now(timezone.utc)
                )
                
                # 4. Créer des entrées d'audit
                audit_entries = [
                    AuditLog(
                        user_id=user_id,
                        action=ActionType.UPDATE,
                        table_name="exams",
                        record_id=exam_id,
                        old_values={'status': old_status.value if old_status else None},
                        new_values={'status': exam.status.value},
                        timestamp=datetime.now(timezone.utc),
                        additional_data={
                            'action': 'exam_processed',
                            'ocr_provider': ocr_result.get('provider_used'),
                            'processing_time': grading_result.get('processing_time')
                        }
                    ),
                    AuditLog(
                        user_id=user_id,
                        action=ActionType.CREATE,
                        table_name="exam_results",
                        record_id=exam_id,
                        new_values={
                            'score': exam_result.total_score,
                            'percentage': exam_result.percentage,
                            'grade': exam_result.letter_grade
                        },
                        timestamp=datetime.now(timezone.utc),
                        additional_data={
                            'action': 'results_created',
                            'ai_confidence': exam_result.ai_confidence,
                            'requires_review': exam_result.requires_manual_review
                        }
                    )
                ]
                
                # Sauvegarder tout
                db.commit()
                
                # Ajouter l'historique après commit pour avoir l'ID
                if exam_result.id:
                    grade_history.exam_result_id = exam_result.id
                    db.add(grade_history)
                
                # Ajouter les audits
                for audit in audit_entries:
                    db.add(audit)
                
                db.commit()
                
                logger.info(f"Exam results saved successfully for {exam_id}")
                
                return {
                    'success': True,
                    'exam_id': exam_id,
                    'exam_result_id': exam_result.id,
                    'grade_history_id': grade_history.id,
                    'audit_entries': len(audit_entries),
                    'requires_manual_review': exam_result.requires_manual_review
                }
                
            except Exception as e:
                db.rollback()
                logger.error(f"Database error saving exam results: {str(e)}")
                return {
                    'success': False,
                    'error': f'Database error: {str(e)}'
                }
            finally:
                db.close()
                
        except Exception as e:
            logger.error(f"Error in save_exam_results_complete: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }
    
    async def get_exam_results_detailed(
        self, 
        exam_id: str, 
        include_history: bool = True
    ) -> Dict[str, Any]:
        """
        Récupérer les résultats détaillés d'un examen
        """
        try:
            db = self.SessionLocal()
            
            try:
                from models_simple import Exam, ExamResult, GradeHistory, User
                
                # Récupérer l'examen
                exam = db.query(Exam).filter(Exam.id == exam_id).first()
                if not exam:
                    return {
                        'success': False,
                        'error': f'Exam {exam_id} not found'
                    }
                
                # Récupérer les résultats
                exam_result = db.query(ExamResult).filter(
                    ExamResult.exam_id == exam_id
                ).first()
                
                result_data = {
                    'success': True,
                    'exam': {
                        'id': exam.id,
                        'original_filename': exam.original_filename,
                        'file_path': exam.file_path,
                        'exam_type': exam.exam_type.value if exam.exam_type else None,
                        'status': exam.status.value if exam.status else None,
                        'upload_time': exam.upload_time.isoformat() if exam.upload_time else None,
                        'updated_at': exam.updated_at.isoformat() if exam.updated_at else None
                    }
                }
                
                if exam_result:
                    result_data['results'] = {
                        'id': exam_result.id,
                        'total_score': exam_result.total_score,
                        'max_score': exam_result.max_score,
                        'percentage': exam_result.percentage,
                        'letter_grade': exam_result.letter_grade,
                        'grade_status': exam_result.grade_status.value if exam_result.grade_status else None,
                        'extracted_answers': exam_result.extracted_answers,
                        'correct_answers': exam_result.correct_answers,
                        'question_results': exam_result.question_results,
                        'ai_confidence': exam_result.ai_confidence,
                        'ai_model_used': exam_result.ai_model_used,
                        'ai_analysis': exam_result.ai_analysis,
                        'requires_manual_review': exam_result.requires_manual_review,
                        'feedback': json.loads(exam_result.feedback) if exam_result.feedback else {},
                        'graded_at': exam_result.graded_at.isoformat() if exam_result.graded_at else None
                    }
                    
                    # Inclure l'historique si demandé
                    if include_history:
                        history = db.query(GradeHistory).filter(
                            GradeHistory.exam_result_id == exam_result.id
                        ).order_by(desc(GradeHistory.change_timestamp)).all()
                        
                        result_data['history'] = [
                            {
                                'id': h.id,
                                'old_score': h.old_score,
                                'new_score': h.new_score,
                                'old_grade': h.old_grade,
                                'new_grade': h.new_grade,
                                'change_reason': h.change_reason,
                                'change_timestamp': h.change_timestamp.isoformat() if h.change_timestamp else None,
                                'changed_by_id': h.changed_by_id
                            }
                            for h in history
                        ]
                else:
                    result_data['results'] = None
                    result_data['history'] = []
                
                return result_data
                
            finally:
                db.close()
                
        except Exception as e:
            logger.error(f"Error getting exam results: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }
    
    async def update_manual_review(
        self,
        exam_id: str,
        reviewer_id: int,
        manual_score: float,
        manual_grade: str,
        review_comments: str,
        question_adjustments: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Mettre à jour les résultats après révision manuelle
        """
        try:
            db = self.SessionLocal()
            
            try:
                from models_simple import ExamResult, GradeHistory, AuditLog, ActionType
                
                # Récupérer les résultats existants
                exam_result = db.query(ExamResult).filter(
                    ExamResult.exam_id == exam_id
                ).first()
                
                if not exam_result:
                    return {
                        'success': False,
                        'error': f'Exam results for {exam_id} not found'
                    }
                
                # Sauvegarder les anciennes valeurs
                old_score = exam_result.total_score
                old_grade = exam_result.letter_grade
                
                # Mettre à jour avec les nouvelles valeurs
                exam_result.total_score = manual_score
                exam_result.letter_grade = manual_grade
                exam_result.requires_manual_review = False
                exam_result.reviewed_by_id = reviewer_id
                exam_result.updated_at = datetime.now(timezone.utc)
                
                # Ajouter les commentaires de révision
                current_feedback = json.loads(exam_result.feedback) if exam_result.feedback else {}
                current_feedback['manual_review'] = {
                    'reviewer_id': reviewer_id,
                    'review_date': datetime.now(timezone.utc).isoformat(),
                    'comments': review_comments,
                    'adjustments': question_adjustments or {}
                }
                exam_result.feedback = json.dumps(current_feedback)
                
                # Créer une entrée dans l'historique
                grade_history = GradeHistory(
                    exam_result_id=exam_result.id,
                    old_score=old_score,
                    new_score=manual_score,
                    old_grade=old_grade,
                    new_grade=manual_grade,
                    changed_by_id=reviewer_id,
                    change_reason=f"Révision manuelle: {review_comments}",
                    change_timestamp=datetime.now(timezone.utc)
                )
                db.add(grade_history)
                
                # Créer une entrée d'audit
                audit_log = AuditLog(
                    user_id=reviewer_id,
                    action=ActionType.UPDATE,
                    table_name="exam_results",
                    record_id=exam_id,
                    old_values={
                        'score': old_score,
                        'grade': old_grade,
                        'requires_review': True
                    },
                    new_values={
                        'score': manual_score,
                        'grade': manual_grade,
                        'requires_review': False
                    },
                    timestamp=datetime.now(timezone.utc),
                    additional_data={
                        'action': 'manual_review_completed',
                        'review_comments': review_comments,
                        'question_adjustments': question_adjustments
                    }
                )
                db.add(audit_log)
                
                db.commit()
                
                logger.info(f"Manual review completed for exam {exam_id} by user {reviewer_id}")
                
                return {
                    'success': True,
                    'exam_id': exam_id,
                    'old_score': old_score,
                    'new_score': manual_score,
                    'old_grade': old_grade,
                    'new_grade': manual_grade,
                    'reviewer_id': reviewer_id,
                    'grade_history_id': grade_history.id
                }
                
            except Exception as e:
                db.rollback()
                logger.error(f"Database error in manual review: {str(e)}")
                return {
                    'success': False,
                    'error': f'Database error: {str(e)}'
                }
            finally:
                db.close()
                
        except Exception as e:
            logger.error(f"Error in update_manual_review: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }
    
    async def get_exams_requiring_review(
        self, 
        limit: int = 50, 
        offset: int = 0
    ) -> Dict[str, Any]:
        """
        Récupérer les examens nécessitant une révision manuelle
        """
        try:
            db = self.SessionLocal()
            
            try:
                from models_simple import Exam, ExamResult, User
                
                # Requête pour les examens nécessitant une révision
                query = db.query(Exam, ExamResult).join(
                    ExamResult, Exam.id == ExamResult.exam_id
                ).filter(
                    ExamResult.requires_manual_review == True
                ).order_by(desc(Exam.upload_time))
                
                total_count = query.count()
                exams = query.offset(offset).limit(limit).all()
                
                exam_list = []
                for exam, result in exams:
                    exam_list.append({
                        'exam_id': exam.id,
                        'filename': exam.original_filename,
                        'upload_time': exam.upload_time.isoformat() if exam.upload_time else None,
                        'exam_type': exam.exam_type.value if exam.exam_type else None,
                        'auto_score': result.total_score,
                        'auto_grade': result.letter_grade,
                        'ai_confidence': result.ai_confidence,
                        'ai_model': result.ai_model_used,
                        'graded_at': result.graded_at.isoformat() if result.graded_at else None
                    })
                
                return {
                    'success': True,
                    'exams': exam_list,
                    'total_count': total_count,
                    'offset': offset,
                    'limit': limit,
                    'has_more': (offset + limit) < total_count
                }
                
            finally:
                db.close()
                
        except Exception as e:
            logger.error(f"Error getting exams requiring review: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'exams': [],
                'total_count': 0
            }

# Cette classe sera initialisée avec la session de base de données
database_service = None

def init_database_service(session_factory):
    """Initialiser le service de base de données"""
    global database_service
    database_service = DatabaseService(session_factory)
    return database_service
