#pragma once

// @generated by torchgen/gen.py from NativeFunction.h

#include <c10/core/Scalar.h>
#include <c10/core/Storage.h>
#include <c10/core/TensorOptions.h>
#include <c10/util/Deprecated.h>
#include <c10/util/Optional.h>
#include <c10/core/QScheme.h>
#include <ATen/core/Reduction.h>
#include <ATen/core/Tensor.h>
#include <tuple>
#include <vector>


namespace at {
namespace native {
TORCH_API at::Tensor & repeat_interleave_Tensor_out(const at::Tensor & repeats, c10::optional<int64_t> output_size, at::Tensor & out);
TORCH_API at::Tensor repeat_interleave_cpu(const at::Tensor & repeats, c10::optional<int64_t> output_size=c10::nullopt);
TORCH_API at::Tensor repeat_interleave_cuda(const at::Tensor & repeats, c10::optional<int64_t> output_size=c10::nullopt);
TORCH_API at::Tensor repeat_interleave(const at::Tensor & self, const at::Tensor & repeats, c10::optional<int64_t> dim=c10::nullopt, c10::optional<int64_t> output_size=c10::nullopt);
TORCH_API at::Tensor repeat_interleave_symint(const at::Tensor & self, c10::SymInt repeats, c10::optional<int64_t> dim=c10::nullopt, c10::optional<int64_t> output_size=c10::nullopt);
} // namespace native
} // namespace at
