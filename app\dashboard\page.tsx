'use client';

import { useEffect, useState } from 'react';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import Dashboard from '@/components/Dashboard';
import { useAuth } from '@/providers/auth-provider';
import { Loader2 } from 'lucide-react';

export default function DashboardPage() {
  const { isAuthenticated, isLoading } = useAuth();
  const [pageLoading, setPageLoading] = useState(true);

  useEffect(() => {
    // This is just to simulate a loading state for demo purposes
    const timer = setTimeout(() => {
      setPageLoading(false);
    }, 1000);

    // Log authentication status for debugging
    console.log('Dashboard: Authentication status:', { isAuthenticated, isLoading });

    return () => clearTimeout(timer);
  }, [isAuthenticated, isLoading]);

  if (isLoading || pageLoading) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <div className="flex flex-col items-center">
          <Loader2 className="h-8 w-8 animate-spin mb-4 text-primary" />
          <div className="text-xl">Chargement...</div>
        </div>
      </div>
    );
  }

  return (
    <div className="flex min-h-screen flex-col">
      <Header />
      <main className="flex-1 p-4 md:p-6">
        <Dashboard />
      </main>
      <Footer />
    </div>
  );
}
