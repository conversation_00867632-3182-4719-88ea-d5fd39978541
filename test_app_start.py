#!/usr/bin/env python3
"""
Test simple de démarrage de l'application
"""

import sys
import os

def test_app_import():
    """Tester l'import de l'application"""
    try:
        print("🔍 Test d'import de l'application...")
        
        # Ajouter le chemin backend
        sys.path.insert(0, 'backend')
        
        # Importer l'application
        from main import app
        
        print("✅ Application importée avec succès!")
        print(f"📱 Type d'app: {type(app)}")
        
        # Tester la configuration
        from core.config import settings
        print(f"🌐 Port configuré: {settings.api_port}")
        print(f"🗄️ Base de données: {settings.database_url}")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur import application: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_services_import():
    """Tester l'import des services"""
    try:
        print("\n🔧 Test d'import des services...")
        
        sys.path.insert(0, 'backend')
        
        # Tester les imports des services
        services = [
            "services.enhanced_ocr_service",
            "services.intelligent_grading_service", 
            "services.manual_review_service",
            "services.audit_service"
        ]
        
        for service in services:
            try:
                __import__(service)
                print(f"✅ {service}")
            except Exception as e:
                print(f"❌ {service}: {e}")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur import services: {e}")
        return False

def main():
    """Fonction principale"""
    print("🧪 Test de Démarrage Auto-Grade Scribe")
    print("=" * 45)
    
    # Vérifier la structure
    if not os.path.exists("backend"):
        print("❌ Répertoire 'backend' non trouvé")
        return False
    
    if not os.path.exists("backend/main.py"):
        print("❌ Fichier 'backend/main.py' non trouvé")
        return False
    
    print("✅ Structure de fichiers OK")
    
    # Tests
    tests = [
        ("Import Application", test_app_import),
        ("Import Services", test_services_import)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        result = test_func()
        results.append((test_name, result))
    
    # Résumé
    print("\n" + "=" * 45)
    print("📊 RÉSUMÉ")
    print("=" * 45)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ RÉUSSI" if result else "❌ ÉCHOUÉ"
        print(f"{test_name:20} : {status}")
    
    print(f"\nRésultat: {passed}/{total} tests réussis")
    
    if passed == total:
        print("\n🎉 L'application peut démarrer!")
        print("🚀 Commandes de démarrage:")
        print("   Windows: python start_windows.py")
        print("   Manuel:  cd backend && python -m uvicorn main:app --reload")
    else:
        print("\n⚠️ Problèmes détectés")
        print("💡 Vérifiez les erreurs ci-dessus")

if __name__ == "__main__":
    main()
