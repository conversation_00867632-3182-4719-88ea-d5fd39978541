'use client';

import React, { ReactNode } from 'react';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import Navigation from '@/components/Navigation';
import { usePathname } from 'next/navigation';

interface MainLayoutProps {
  children: ReactNode;
  showNavigation?: boolean;
}

export default function MainLayout({
  children,
  showNavigation = true
}: MainLayoutProps) {
  const pathname = usePathname();

  // Déterminer si la page actuelle est une page d'authentification
  const isAuthPage = pathname === '/login' || pathname === '/register';

  return (
    <div className="min-h-screen bg-background flex flex-col">
      <Header />

      {/* Navigation horizontale pour les écrans plus grands */}
      {showNavigation && !isAuthPage && (
        <div className="border-b bg-card">
          <div className="container mx-auto px-4">
            <Navigation className="py-2" />
          </div>
        </div>
      )}

      <div className="flex-1 flex">
        {/* Navigation verticale pour les pages du tableau de bord */}
        {showNavigation && !isAuthPage && pathname.startsWith('/dashboard') && (
          <div className="hidden md:block w-64 border-r bg-card p-4">
            <Navigation vertical />
          </div>
        )}

        <main className="flex-1 container mx-auto px-4 py-8">
          {children}
        </main>
      </div>

      <Footer />
    </div>
  );
}