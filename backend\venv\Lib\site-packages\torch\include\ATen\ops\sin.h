#pragma once

// @generated by torchgen/gen.py from Function.h

#include <ATen/Context.h>
#include <ATen/DeviceGuard.h>
#include <ATen/TensorUtils.h>
#include <ATen/TracerMode.h>
#include <ATen/core/Generator.h>
#include <ATen/core/Reduction.h>
#include <ATen/core/Tensor.h>
#include <c10/core/Scalar.h>
#include <c10/core/Storage.h>
#include <c10/core/TensorOptions.h>
#include <c10/util/Deprecated.h>
#include <c10/util/Optional.h>



#include <ATen/ops/sin_ops.h>

namespace at {


// aten::sin(Tensor self) -> Tensor
inline at::Tensor sin(const at::Tensor & self) {
    return at::_ops::sin::call(self);
}

// aten::sin_(Tensor(a!) self) -> Tensor(a!)
inline at::Tensor & sin_(at::Tensor & self) {
    return at::_ops::sin_::call(self);
}

// aten::sin.out(Tensor self, *, Tensor(a!) out) -> Tensor(a!)
inline at::Tensor & sin_out(at::Tensor & out, const at::Tensor & self) {
    return at::_ops::sin_out::call(self, out);
}
// aten::sin.out(Tensor self, *, Tensor(a!) out) -> Tensor(a!)
inline at::Tensor & sin_outf(const at::Tensor & self, at::Tensor & out) {
    return at::_ops::sin_out::call(self, out);
}

}
