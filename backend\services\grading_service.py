"""
Enhanced Grading Service for Auto-Grade Scribe
Provides comprehensive grading functionality with AI assistance and detailed analysis
"""

import logging
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime
from sqlalchemy.orm import Session

from models import (
    Exam, ExamResult, Student, GradeHistory, ExamType, GradeStatus, User
)
from ai.gemini_service import gemini_service
from services.audit_service import AuditService

logger = logging.getLogger("auto-grade-scribe.grading-service")

class EnhancedGradingService:
    """Enhanced service for exam grading with AI assistance"""
    
    def __init__(self):
        self.gemini_available = gemini_service.is_available
        self.audit_service = AuditService()
        
        # Grading configurations
        self.grade_scales = {
            "standard": {
                90: "A", 80: "B", 70: "C", 60: "D", 0: "F"
            },
            "strict": {
                95: "A", 85: "B", 75: "C", 65: "D", 0: "F"
            },
            "lenient": {
                85: "A", 75: "B", 65: "C", 55: "D", 0: "F"
            }
        }
        
        logger.info(f"Enhanced Grading Service initialized. Gemini available: {self.gemini_available}")
    
    async def grade_exam(
        self,
        db: Session,
        exam_id: str,
        correct_answers: Dict[str, str],
        user_id: int,
        grading_config: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Grade an exam with comprehensive analysis
        
        Args:
            db: Database session
            exam_id: ID of the exam to grade
            correct_answers: Dictionary of correct answers
            user_id: ID of the user performing grading
            grading_config: Configuration for grading behavior
            
        Returns:
            Grading results with detailed analysis
        """
        try:
            # Get exam
            exam = db.query(Exam).filter(Exam.id == exam_id).first()
            if not exam:
                return {"success": False, "error": "Exam not found"}
            
            # Check if exam is processed
            if not exam.extracted_text:
                return {"success": False, "error": "Exam not processed yet"}
            
            start_time = datetime.utcnow()
            
            # Determine grading strategy
            if exam.exam_type == ExamType.QCM:
                result = await self._grade_qcm_enhanced(exam, correct_answers, grading_config)
            elif exam.exam_type == ExamType.HANDWRITTEN and self.gemini_available:
                result = await self._grade_handwritten_with_ai(exam, correct_answers, grading_config)
            else:
                result = await self._grade_standard(exam, correct_answers, grading_config)
            
            if not result["success"]:
                return result
            
            # Create or update exam result
            exam_result = await self._create_exam_result(
                db, exam, result, user_id, correct_answers
            )
            
            # Calculate processing time
            processing_time = (datetime.utcnow() - start_time).total_seconds()
            
            # Audit log
            await self.audit_service.log_action(
                db, user_id, "grade", "exam", exam_id,
                new_values={
                    "score": result["grade_summary"]["score"],
                    "percentage": result["grade_summary"]["percentage"],
                    "status": result["grade_summary"]["status"]
                }
            )
            
            # Prepare response
            response = {
                "success": True,
                "exam_id": exam_id,
                "exam_result_id": exam_result.id,
                "grade_summary": result["grade_summary"],
                "details": result["details"],
                "student_info": {
                    "name": result.get("student_name", "Non détecté"),
                    "id": result.get("student_id", ""),
                    "answers": result.get("extracted_answers", {})
                },
                "processing_metadata": {
                    "processing_time_seconds": processing_time,
                    "grading_method": result.get("grading_method", "standard"),
                    "ai_model_used": result.get("ai_model", None),
                    "timestamp": datetime.utcnow().isoformat()
                }
            }
            
            logger.info(f"Successfully graded exam {exam_id} in {processing_time:.2f}s")
            return response
            
        except Exception as e:
            logger.error(f"Error grading exam {exam_id}: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "exam_id": exam_id
            }
    
    async def _grade_qcm_enhanced(
        self,
        exam: Exam,
        correct_answers: Dict[str, str],
        grading_config: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Enhanced QCM grading with detailed analysis"""
        try:
            # Extract student answers from OCR results
            extracted_answers = {}
            student_name = "Non détecté"
            student_id = ""
            
            # Try to get answers from exam metadata or re-extract
            if hasattr(exam, 'metadata') and exam.metadata:
                ocr_data = exam.metadata.get('ocr_result', {})
                extracted_answers = ocr_data.get('extracted_answers', {})
                student_name = ocr_data.get('student_name', 'Non détecté')
                student_id = ocr_data.get('student_id', '')
            
            # If no answers in metadata, try to extract from text
            if not extracted_answers:
                extracted_answers = self._extract_answers_from_text(exam.extracted_text)
            
            # Use Gemini for enhanced analysis if available
            if self.gemini_available and exam.extracted_text:
                gemini_result = gemini_service.grade_exam(exam.extracted_text, correct_answers)
                if gemini_result.get("success", False):
                    return self._process_gemini_grading_result(
                        gemini_result, extracted_answers, student_name, student_id
                    )
            
            # Fallback to traditional grading
            return await self._grade_qcm_traditional(
                extracted_answers, correct_answers, student_name, student_id, grading_config
            )
            
        except Exception as e:
            logger.error(f"Error in enhanced QCM grading: {str(e)}")
            return {"success": False, "error": str(e)}
    
    async def _grade_qcm_traditional(
        self,
        extracted_answers: Dict[str, str],
        correct_answers: Dict[str, str],
        student_name: str,
        student_id: str,
        grading_config: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Traditional QCM grading with detailed breakdown"""
        try:
            # Convert keys to strings for comparison
            correct_answers_str = {str(k): v.upper() for k, v in correct_answers.items()}
            extracted_answers_str = {str(k): v.upper() for k, v in extracted_answers.items()}
            
            # Grade each question
            question_results = {}
            correct_count = 0
            total_questions = len(correct_answers_str)
            
            for q_num, correct_answer in correct_answers_str.items():
                student_answer = extracted_answers_str.get(q_num, "")
                is_correct = student_answer == correct_answer
                
                if is_correct:
                    correct_count += 1
                
                question_results[q_num] = {
                    "question_number": int(q_num),
                    "correct_answer": correct_answer,
                    "student_answer": student_answer,
                    "is_correct": is_correct,
                    "points_earned": 1 if is_correct else 0,
                    "points_possible": 1
                }
            
            # Calculate scores
            percentage = (correct_count / total_questions * 100) if total_questions > 0 else 0
            letter_grade = self._calculate_letter_grade(percentage, grading_config)
            status = GradeStatus.PASSED if percentage >= 60 else GradeStatus.FAILED
            
            # Detailed analysis
            analysis = self._analyze_qcm_performance(question_results, percentage)
            
            return {
                "success": True,
                "student_name": student_name,
                "student_id": student_id,
                "extracted_answers": extracted_answers,
                "grade_summary": {
                    "score": correct_count,
                    "max_score": total_questions,
                    "percentage": round(percentage, 2),
                    "letter_grade": letter_grade,
                    "status": status.value
                },
                "details": {
                    "question_results": question_results,
                    "analysis": analysis,
                    "grading_method": "traditional_qcm"
                },
                "grading_method": "traditional_qcm"
            }
            
        except Exception as e:
            logger.error(f"Error in traditional QCM grading: {str(e)}")
            return {"success": False, "error": str(e)}
    
    async def _grade_handwritten_with_ai(
        self,
        exam: Exam,
        correct_answers: Dict[str, str],
        grading_config: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Grade handwritten exam using AI assistance"""
        try:
            logger.info("Using AI-assisted grading for handwritten exam")
            
            # Use Gemini for grading
            gemini_result = gemini_service.grade_exam(exam.extracted_text, correct_answers)
            
            if gemini_result.get("success", False):
                # Process Gemini result
                result = self._process_gemini_grading_result(
                    gemini_result, {}, "Non détecté", ""
                )
                result["grading_method"] = "ai_assisted_handwritten"
                result["ai_model"] = "gemini"
                return result
            else:
                # Fallback to standard grading
                return await self._grade_standard(exam, correct_answers, grading_config)
                
        except Exception as e:
            logger.error(f"Error in AI-assisted handwritten grading: {str(e)}")
            return {"success": False, "error": str(e)}
    
    async def _grade_standard(
        self,
        exam: Exam,
        correct_answers: Dict[str, str],
        grading_config: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Standard grading fallback method"""
        try:
            # Extract basic information
            extracted_answers = self._extract_answers_from_text(exam.extracted_text)
            student_info = self._extract_student_info(exam.extracted_text)
            
            # Use traditional QCM grading as fallback
            return await self._grade_qcm_traditional(
                extracted_answers,
                correct_answers,
                student_info.get("name", "Non détecté"),
                student_info.get("id", ""),
                grading_config
            )
            
        except Exception as e:
            logger.error(f"Error in standard grading: {str(e)}")
            return {"success": False, "error": str(e)}
    
    def _process_gemini_grading_result(
        self,
        gemini_result: Dict[str, Any],
        extracted_answers: Dict[str, str],
        student_name: str,
        student_id: str
    ) -> Dict[str, Any]:
        """Process and format Gemini grading results"""
        try:
            grade_summary = gemini_result.get("grade_summary", {})
            details = gemini_result.get("details", {})
            
            # Ensure proper format
            formatted_summary = {
                "score": grade_summary.get("score", 0),
                "max_score": grade_summary.get("total", grade_summary.get("max_score", 100)),
                "percentage": grade_summary.get("percentage", 0),
                "letter_grade": self._calculate_letter_grade(grade_summary.get("percentage", 0)),
                "status": grade_summary.get("status", "failed")
            }
            
            return {
                "success": True,
                "student_name": student_name,
                "student_id": student_id,
                "extracted_answers": extracted_answers,
                "grade_summary": formatted_summary,
                "details": {
                    "ai_analysis": details,
                    "grading_method": "gemini_ai"
                },
                "grading_method": "gemini_ai",
                "ai_model": "gemini"
            }
            
        except Exception as e:
            logger.error(f"Error processing Gemini result: {str(e)}")
            return {"success": False, "error": str(e)}
    
    async def _create_exam_result(
        self,
        db: Session,
        exam: Exam,
        grading_result: Dict[str, Any],
        user_id: int,
        correct_answers: Dict[str, str]
    ) -> ExamResult:
        """Create or update exam result in database"""
        try:
            # Check if result already exists
            existing_result = db.query(ExamResult).filter(ExamResult.exam_id == exam.id).first()
            
            grade_summary = grading_result["grade_summary"]
            
            if existing_result:
                # Create history entry
                history = GradeHistory(
                    exam_result_id=existing_result.id,
                    previous_score=existing_result.total_score,
                    previous_grade=existing_result.letter_grade,
                    previous_status=existing_result.grade_status.value if existing_result.grade_status else None,
                    new_score=grade_summary["score"],
                    new_grade=grade_summary["letter_grade"],
                    new_status=grade_summary["status"],
                    changed_by_id=user_id,
                    change_reason="Re-grading"
                )
                db.add(history)
                
                # Update existing result
                existing_result.total_score = grade_summary["score"]
                existing_result.max_score = grade_summary["max_score"]
                existing_result.percentage = grade_summary["percentage"]
                existing_result.letter_grade = grade_summary["letter_grade"]
                existing_result.grade_status = GradeStatus(grade_summary["status"])
                existing_result.question_results = grading_result["details"].get("question_results", {})
                existing_result.extracted_answers = grading_result.get("extracted_answers", {})
                existing_result.correct_answers = correct_answers
                existing_result.ai_model_used = grading_result.get("ai_model")
                existing_result.ai_analysis = grading_result["details"]
                existing_result.updated_at = datetime.utcnow()
                
                exam_result = existing_result
            else:
                # Create new result
                exam_result = ExamResult(
                    exam_id=exam.id,
                    student_name=grading_result.get("student_name", "Non détecté"),
                    student_identifier=grading_result.get("student_id", ""),
                    total_score=grade_summary["score"],
                    max_score=grade_summary["max_score"],
                    percentage=grade_summary["percentage"],
                    letter_grade=grade_summary["letter_grade"],
                    grade_status=GradeStatus(grade_summary["status"]),
                    question_results=grading_result["details"].get("question_results", {}),
                    extracted_answers=grading_result.get("extracted_answers", {}),
                    correct_answers=correct_answers,
                    ai_model_used=grading_result.get("ai_model"),
                    ai_analysis=grading_result["details"],
                    grading_rubric={"grading_method": grading_result.get("grading_method", "standard")}
                )
                db.add(exam_result)
            
            # Update exam status
            exam.status = exam.status if exam.status.value in ["graded", "completed"] else "graded"
            
            db.commit()
            db.refresh(exam_result)
            
            return exam_result
            
        except Exception as e:
            logger.error(f"Error creating exam result: {str(e)}")
            db.rollback()
            raise
    
    def _calculate_letter_grade(
        self, 
        percentage: float, 
        grading_config: Optional[Dict[str, Any]] = None
    ) -> str:
        """Calculate letter grade based on percentage"""
        try:
            scale_name = "standard"
            if grading_config and "grade_scale" in grading_config:
                scale_name = grading_config["grade_scale"]
            
            scale = self.grade_scales.get(scale_name, self.grade_scales["standard"])
            
            for threshold, grade in sorted(scale.items(), reverse=True):
                if percentage >= threshold:
                    return grade
            
            return "F"
            
        except Exception:
            return "F"
    
    def _analyze_qcm_performance(
        self, 
        question_results: Dict[str, Dict[str, Any]], 
        percentage: float
    ) -> Dict[str, Any]:
        """Analyze QCM performance and provide insights"""
        try:
            total_questions = len(question_results)
            correct_answers = sum(1 for q in question_results.values() if q["is_correct"])
            
            # Performance categories
            if percentage >= 90:
                performance_level = "Excellent"
            elif percentage >= 80:
                performance_level = "Good"
            elif percentage >= 70:
                performance_level = "Satisfactory"
            elif percentage >= 60:
                performance_level = "Needs Improvement"
            else:
                performance_level = "Poor"
            
            # Question analysis
            missed_questions = [
                q["question_number"] for q in question_results.values() 
                if not q["is_correct"]
            ]
            
            return {
                "performance_level": performance_level,
                "total_questions": total_questions,
                "correct_answers": correct_answers,
                "incorrect_answers": total_questions - correct_answers,
                "missed_questions": missed_questions,
                "accuracy_rate": percentage / 100,
                "recommendations": self._generate_recommendations(percentage, missed_questions)
            }
            
        except Exception as e:
            logger.error(f"Error in performance analysis: {str(e)}")
            return {"error": "Analysis failed"}
    
    def _generate_recommendations(
        self, 
        percentage: float, 
        missed_questions: List[int]
    ) -> List[str]:
        """Generate recommendations based on performance"""
        recommendations = []
        
        if percentage < 60:
            recommendations.append("Consider reviewing the fundamental concepts covered in this exam.")
            recommendations.append("Practice more questions in the areas where mistakes were made.")
        elif percentage < 80:
            recommendations.append("Good performance overall. Focus on the specific questions missed.")
            recommendations.append("Review the topics related to the incorrect answers.")
        else:
            recommendations.append("Excellent performance! Continue with the current study approach.")
        
        if len(missed_questions) > 0:
            recommendations.append(f"Pay special attention to questions: {', '.join(map(str, missed_questions))}")
        
        return recommendations
    
    def _extract_answers_from_text(self, text: str) -> Dict[str, str]:
        """Extract answers from text using pattern matching"""
        import re
        
        answers = {}
        
        # Pattern for question-answer pairs
        patterns = [
            r"(?:Question|Q)?\s*(\d+)[\.:\)]\s*([A-Za-z])",
            r"(\d+)\s*[\.:\)]\s*([A-Za-z])",
            r"(\d+)\s*([A-Za-z])"
        ]
        
        for pattern in patterns:
            matches = re.findall(pattern, text)
            for match in matches:
                try:
                    q_num = int(match[0])
                    answer = match[1].upper()
                    if answer in ['A', 'B', 'C', 'D', 'E']:
                        answers[str(q_num)] = answer
                except (ValueError, IndexError):
                    continue
        
        return answers
    
    def _extract_student_info(self, text: str) -> Dict[str, str]:
        """Extract student information from text"""
        import re
        
        student_info = {"name": "Non détecté", "id": ""}
        
        # Extract name patterns
        name_patterns = [
            r"[Nn]om\s*:?\s*([A-Za-zÀ-ÖØ-öø-ÿ\s\-\'\.]+)",
            r"[Nn]ame\s*:?\s*([A-Za-zÀ-ÖØ-öø-ÿ\s\-\'\.]+)",
            r"[Éé]tudiant\s*:?\s*([A-Za-zÀ-ÖØ-öø-ÿ\s\-\'\.]+)"
        ]
        
        for pattern in name_patterns:
            match = re.search(pattern, text)
            if match:
                student_info["name"] = match.group(1).strip()
                break
        
        # Extract ID patterns
        id_patterns = [
            r"[Ii][Dd]\s*:?\s*(\d+)",
            r"[Nn]uméro\s*:?\s*(\d+)",
            r"[Mm]atricule\s*:?\s*(\d+)"
        ]
        
        for pattern in id_patterns:
            match = re.search(pattern, text)
            if match:
                student_info["id"] = match.group(1).strip()
                break
        
        return student_info

# Create singleton instance
enhanced_grading_service = EnhancedGradingService()
