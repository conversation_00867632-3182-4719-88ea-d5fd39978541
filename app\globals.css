@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 30 100% 50%;
    --primary-foreground: 210 40% 98%;

    --secondary: 30 30% 96.1%;
    --secondary-foreground: 30 47.4% 11.2%;

    --muted: 30 30% 96.1%;
    --muted-foreground: 30 16.3% 46.9%;

    --accent: 30 30% 96.1%;
    --accent-foreground: 30 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 30 31.8% 91.4%;
    --input: 30 31.8% 91.4%;
    --ring: 30 83.2% 53.3%;

    --radius: 0.5rem;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 30 91.2% 59.8%;
    --primary-foreground: 30 47.4% 11.2%;

    --secondary: 30 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 30 32.6% 17.5%;
    --muted-foreground: 30 20.2% 65.1%;

    --accent: 30 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 30 32.6% 17.5%;
    --input: 30 32.6% 17.5%;
    --ring: 30 76.3% 48%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
}

/* Styles spécifiques pour l'application Auto-Grade Scribe */
.login-gradient {
  background: linear-gradient(to right, rgba(255, 153, 0, 0.9), rgba(255, 128, 0, 0.9));
}

.dashboard-card-hover {
  transition: transform 0.2s, box-shadow 0.2s;
}

.dashboard-card-hover:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

/* Animations pour les éléments de l'interface */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.animate-fadeIn {
  animation: fadeIn 0.5s ease-in-out;
}

/* Styles pour les pages de résultats d'analyse */
.result-highlight {
  @apply bg-yellow-100 dark:bg-yellow-900/30 p-1 rounded;
}

.correct-answer {
  @apply text-green-600 dark:text-green-400 font-medium;
}

.incorrect-answer {
  @apply text-red-600 dark:text-red-400 font-medium;
}