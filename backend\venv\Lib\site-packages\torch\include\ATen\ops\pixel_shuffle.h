#pragma once

// @generated by torchgen/gen.py from Function.h

#include <ATen/Context.h>
#include <ATen/DeviceGuard.h>
#include <ATen/TensorUtils.h>
#include <ATen/TracerMode.h>
#include <ATen/core/Generator.h>
#include <ATen/core/Reduction.h>
#include <ATen/core/Tensor.h>
#include <c10/core/Scalar.h>
#include <c10/core/Storage.h>
#include <c10/core/TensorOptions.h>
#include <c10/util/Deprecated.h>
#include <c10/util/Optional.h>



#include <ATen/ops/pixel_shuffle_ops.h>

namespace at {


// aten::pixel_shuffle(Tensor self, int upscale_factor) -> Tensor
inline at::Tensor pixel_shuffle(const at::Tensor & self, int64_t upscale_factor) {
    return at::_ops::pixel_shuffle::call(self, upscale_factor);
}

// aten::pixel_shuffle.out(Tensor self, int upscale_factor, *, Tensor(a!) out) -> Tensor(a!)
inline at::Tensor & pixel_shuffle_out(at::Tensor & out, const at::Tensor & self, int64_t upscale_factor) {
    return at::_ops::pixel_shuffle_out::call(self, upscale_factor, out);
}
// aten::pixel_shuffle.out(Tensor self, int upscale_factor, *, Tensor(a!) out) -> Tensor(a!)
inline at::Tensor & pixel_shuffle_outf(const at::Tensor & self, int64_t upscale_factor, at::Tensor & out) {
    return at::_ops::pixel_shuffle_out::call(self, upscale_factor, out);
}

}
