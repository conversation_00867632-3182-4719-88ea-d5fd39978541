// automatically generated by the FlatBuffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_MOBILEBYTECODE_TORCH_JIT_MOBILE_SERIALIZATION_H_
#define FLATBUFFERS_GENERATED_MOBILEBYTECODE_TORCH_JIT_MOBILE_SERIALIZATION_H_

#include "flatbuffers/flatbuffers.h"

// Ensure the included flatbuffers.h is the same version as when this file was
// generated, otherwise it may not be compatible.
static_assert(FLATBUFFERS_VERSION_MAJOR == 23 &&
              FLATBUFFERS_VERSION_MINOR == 3 &&
              FLATBUFFERS_VERSION_REVISION == 3,
             "Non-compatible flatbuffers version included");

namespace torch {
namespace jit {
namespace mobile {
namespace serialization {

struct Int;

struct Bool;

struct Double;

struct PerTensorAffineSchema;

struct QuantizedSchema;
struct QuantizedSchemaBuilder;

struct TensorMetadata;
struct TensorMetadataBuilder;

struct String;
struct StringBuilder;

struct Device;
struct DeviceBuilder;

struct List;
struct ListBuilder;

struct IntList;
struct IntListBuilder;

struct DoubleList;
struct DoubleListBuilder;

struct BoolList;
struct BoolListBuilder;

struct Tuple;
struct TupleBuilder;

struct Dict;
struct DictBuilder;

struct ObjectType;
struct ObjectTypeBuilder;

struct Object;
struct ObjectBuilder;

struct ComplexDouble;

struct EnumValue;
struct EnumValueBuilder;

struct Instruction;

struct Operator;
struct OperatorBuilder;

struct Arg;
struct ArgBuilder;

struct Schema;
struct SchemaBuilder;

struct DebugInfo;
struct DebugInfoBuilder;

struct Function;
struct FunctionBuilder;

struct StorageData;
struct StorageDataBuilder;

struct IValue;
struct IValueBuilder;

struct ExtraFile;
struct ExtraFileBuilder;

struct Module;
struct ModuleBuilder;

enum class TypeType : uint8_t {
  UNSET = 0,
  CLASS_WITH_FIELD = 1,
  CUSTOM_CLASS = 2,
  CLASS_WITH_SETSTATE = 3,
  NON_OBJ = 4,
  MIN = UNSET,
  MAX = NON_OBJ
};

inline const TypeType (&EnumValuesTypeType())[5] {
  static const TypeType values[] = {
    TypeType::UNSET,
    TypeType::CLASS_WITH_FIELD,
    TypeType::CUSTOM_CLASS,
    TypeType::CLASS_WITH_SETSTATE,
    TypeType::NON_OBJ
  };
  return values;
}

inline const char * const *EnumNamesTypeType() {
  static const char * const names[6] = {
    "UNSET",
    "CLASS_WITH_FIELD",
    "CUSTOM_CLASS",
    "CLASS_WITH_SETSTATE",
    "NON_OBJ",
    nullptr
  };
  return names;
}

inline const char *EnumNameTypeType(TypeType e) {
  if (::flatbuffers::IsOutRange(e, TypeType::UNSET, TypeType::NON_OBJ)) return "";
  const size_t index = static_cast<size_t>(e);
  return EnumNamesTypeType()[index];
}

enum class IValueUnion : uint8_t {
  NONE = 0,
  Int = 1,
  Bool = 2,
  Double = 3,
  ComplexDouble = 4,
  TensorMetadata = 5,
  String = 6,
  List = 7,
  Tuple = 8,
  Dict = 9,
  Object = 10,
  IntList = 11,
  DoubleList = 12,
  BoolList = 13,
  Device = 14,
  EnumValue = 15,
  Function = 16,
  MIN = NONE,
  MAX = Function
};

inline const IValueUnion (&EnumValuesIValueUnion())[17] {
  static const IValueUnion values[] = {
    IValueUnion::NONE,
    IValueUnion::Int,
    IValueUnion::Bool,
    IValueUnion::Double,
    IValueUnion::ComplexDouble,
    IValueUnion::TensorMetadata,
    IValueUnion::String,
    IValueUnion::List,
    IValueUnion::Tuple,
    IValueUnion::Dict,
    IValueUnion::Object,
    IValueUnion::IntList,
    IValueUnion::DoubleList,
    IValueUnion::BoolList,
    IValueUnion::Device,
    IValueUnion::EnumValue,
    IValueUnion::Function
  };
  return values;
}

inline const char * const *EnumNamesIValueUnion() {
  static const char * const names[18] = {
    "NONE",
    "Int",
    "Bool",
    "Double",
    "ComplexDouble",
    "TensorMetadata",
    "String",
    "List",
    "Tuple",
    "Dict",
    "Object",
    "IntList",
    "DoubleList",
    "BoolList",
    "Device",
    "EnumValue",
    "Function",
    nullptr
  };
  return names;
}

inline const char *EnumNameIValueUnion(IValueUnion e) {
  if (::flatbuffers::IsOutRange(e, IValueUnion::NONE, IValueUnion::Function)) return "";
  const size_t index = static_cast<size_t>(e);
  return EnumNamesIValueUnion()[index];
}

template<typename T> struct IValueUnionTraits {
  static const IValueUnion enum_value = IValueUnion::NONE;
};

template<> struct IValueUnionTraits<torch::jit::mobile::serialization::Int> {
  static const IValueUnion enum_value = IValueUnion::Int;
};

template<> struct IValueUnionTraits<torch::jit::mobile::serialization::Bool> {
  static const IValueUnion enum_value = IValueUnion::Bool;
};

template<> struct IValueUnionTraits<torch::jit::mobile::serialization::Double> {
  static const IValueUnion enum_value = IValueUnion::Double;
};

template<> struct IValueUnionTraits<torch::jit::mobile::serialization::ComplexDouble> {
  static const IValueUnion enum_value = IValueUnion::ComplexDouble;
};

template<> struct IValueUnionTraits<torch::jit::mobile::serialization::TensorMetadata> {
  static const IValueUnion enum_value = IValueUnion::TensorMetadata;
};

template<> struct IValueUnionTraits<torch::jit::mobile::serialization::String> {
  static const IValueUnion enum_value = IValueUnion::String;
};

template<> struct IValueUnionTraits<torch::jit::mobile::serialization::List> {
  static const IValueUnion enum_value = IValueUnion::List;
};

template<> struct IValueUnionTraits<torch::jit::mobile::serialization::Tuple> {
  static const IValueUnion enum_value = IValueUnion::Tuple;
};

template<> struct IValueUnionTraits<torch::jit::mobile::serialization::Dict> {
  static const IValueUnion enum_value = IValueUnion::Dict;
};

template<> struct IValueUnionTraits<torch::jit::mobile::serialization::Object> {
  static const IValueUnion enum_value = IValueUnion::Object;
};

template<> struct IValueUnionTraits<torch::jit::mobile::serialization::IntList> {
  static const IValueUnion enum_value = IValueUnion::IntList;
};

template<> struct IValueUnionTraits<torch::jit::mobile::serialization::DoubleList> {
  static const IValueUnion enum_value = IValueUnion::DoubleList;
};

template<> struct IValueUnionTraits<torch::jit::mobile::serialization::BoolList> {
  static const IValueUnion enum_value = IValueUnion::BoolList;
};

template<> struct IValueUnionTraits<torch::jit::mobile::serialization::Device> {
  static const IValueUnion enum_value = IValueUnion::Device;
};

template<> struct IValueUnionTraits<torch::jit::mobile::serialization::EnumValue> {
  static const IValueUnion enum_value = IValueUnion::EnumValue;
};

template<> struct IValueUnionTraits<torch::jit::mobile::serialization::Function> {
  static const IValueUnion enum_value = IValueUnion::Function;
};

bool VerifyIValueUnion(::flatbuffers::Verifier &verifier, const void *obj, IValueUnion type);
bool VerifyIValueUnionVector(::flatbuffers::Verifier &verifier, const ::flatbuffers::Vector<::flatbuffers::Offset<void>> *values, const ::flatbuffers::Vector<IValueUnion> *types);

FLATBUFFERS_MANUALLY_ALIGNED_STRUCT(8) Int FLATBUFFERS_FINAL_CLASS {
 private:
  int64_t int_val_;

 public:
  Int()
      : int_val_(0) {
  }
  Int(int64_t _int_val)
      : int_val_(::flatbuffers::EndianScalar(_int_val)) {
  }
  int64_t int_val() const {
    return ::flatbuffers::EndianScalar(int_val_);
  }
  void mutate_int_val(int64_t _int_val) {
    ::flatbuffers::WriteScalar(&int_val_, _int_val);
  }
};
FLATBUFFERS_STRUCT_END(Int, 8);

FLATBUFFERS_MANUALLY_ALIGNED_STRUCT(1) Bool FLATBUFFERS_FINAL_CLASS {
 private:
  uint8_t bool_val_;

 public:
  Bool()
      : bool_val_(0) {
  }
  Bool(bool _bool_val)
      : bool_val_(::flatbuffers::EndianScalar(static_cast<uint8_t>(_bool_val))) {
  }
  bool bool_val() const {
    return ::flatbuffers::EndianScalar(bool_val_) != 0;
  }
  void mutate_bool_val(bool _bool_val) {
    ::flatbuffers::WriteScalar(&bool_val_, static_cast<uint8_t>(_bool_val));
  }
};
FLATBUFFERS_STRUCT_END(Bool, 1);

FLATBUFFERS_MANUALLY_ALIGNED_STRUCT(8) Double FLATBUFFERS_FINAL_CLASS {
 private:
  double double_val_;

 public:
  Double()
      : double_val_(0) {
  }
  Double(double _double_val)
      : double_val_(::flatbuffers::EndianScalar(_double_val)) {
  }
  double double_val() const {
    return ::flatbuffers::EndianScalar(double_val_);
  }
  void mutate_double_val(double _double_val) {
    ::flatbuffers::WriteScalar(&double_val_, _double_val);
  }
};
FLATBUFFERS_STRUCT_END(Double, 8);

FLATBUFFERS_MANUALLY_ALIGNED_STRUCT(8) PerTensorAffineSchema FLATBUFFERS_FINAL_CLASS {
 private:
  double q_scale_;
  int32_t q_zero_point_;
  int32_t padding0__;

 public:
  PerTensorAffineSchema()
      : q_scale_(0),
        q_zero_point_(0),
        padding0__(0) {
    (void)padding0__;
  }
  PerTensorAffineSchema(double _q_scale, int32_t _q_zero_point)
      : q_scale_(::flatbuffers::EndianScalar(_q_scale)),
        q_zero_point_(::flatbuffers::EndianScalar(_q_zero_point)),
        padding0__(0) {
    (void)padding0__;
  }
  double q_scale() const {
    return ::flatbuffers::EndianScalar(q_scale_);
  }
  void mutate_q_scale(double _q_scale) {
    ::flatbuffers::WriteScalar(&q_scale_, _q_scale);
  }
  int32_t q_zero_point() const {
    return ::flatbuffers::EndianScalar(q_zero_point_);
  }
  void mutate_q_zero_point(int32_t _q_zero_point) {
    ::flatbuffers::WriteScalar(&q_zero_point_, _q_zero_point);
  }
};
FLATBUFFERS_STRUCT_END(PerTensorAffineSchema, 16);

FLATBUFFERS_MANUALLY_ALIGNED_STRUCT(8) ComplexDouble FLATBUFFERS_FINAL_CLASS {
 private:
  double real_;
  double imag_;

 public:
  ComplexDouble()
      : real_(0),
        imag_(0) {
  }
  ComplexDouble(double _real, double _imag)
      : real_(::flatbuffers::EndianScalar(_real)),
        imag_(::flatbuffers::EndianScalar(_imag)) {
  }
  double real() const {
    return ::flatbuffers::EndianScalar(real_);
  }
  void mutate_real(double _real) {
    ::flatbuffers::WriteScalar(&real_, _real);
  }
  double imag() const {
    return ::flatbuffers::EndianScalar(imag_);
  }
  void mutate_imag(double _imag) {
    ::flatbuffers::WriteScalar(&imag_, _imag);
  }
};
FLATBUFFERS_STRUCT_END(ComplexDouble, 16);

FLATBUFFERS_MANUALLY_ALIGNED_STRUCT(4) Instruction FLATBUFFERS_FINAL_CLASS {
 private:
  int8_t op_;
  int8_t padding0__;
  uint16_t n_;
  int32_t x_;

 public:
  Instruction()
      : op_(0),
        padding0__(0),
        n_(0),
        x_(0) {
    (void)padding0__;
  }
  Instruction(int8_t _op, uint16_t _n, int32_t _x)
      : op_(::flatbuffers::EndianScalar(_op)),
        padding0__(0),
        n_(::flatbuffers::EndianScalar(_n)),
        x_(::flatbuffers::EndianScalar(_x)) {
    (void)padding0__;
  }
  int8_t op() const {
    return ::flatbuffers::EndianScalar(op_);
  }
  void mutate_op(int8_t _op) {
    ::flatbuffers::WriteScalar(&op_, _op);
  }
  uint16_t n() const {
    return ::flatbuffers::EndianScalar(n_);
  }
  void mutate_n(uint16_t _n) {
    ::flatbuffers::WriteScalar(&n_, _n);
  }
  int32_t x() const {
    return ::flatbuffers::EndianScalar(x_);
  }
  void mutate_x(int32_t _x) {
    ::flatbuffers::WriteScalar(&x_, _x);
  }
};
FLATBUFFERS_STRUCT_END(Instruction, 8);

struct QuantizedSchema FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef QuantizedSchemaBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_QSCHEME = 4,
    VT_SCALE = 6,
    VT_ZERO_POINT = 8,
    VT_SCALES = 10,
    VT_ZERO_POINTS = 12,
    VT_AXIS = 14
  };
  int8_t qscheme() const {
    return GetField<int8_t>(VT_QSCHEME, 0);
  }
  bool mutate_qscheme(int8_t _qscheme = 0) {
    return SetField<int8_t>(VT_QSCHEME, _qscheme, 0);
  }
  double scale() const {
    return GetField<double>(VT_SCALE, 0.0);
  }
  bool mutate_scale(double _scale = 0.0) {
    return SetField<double>(VT_SCALE, _scale, 0.0);
  }
  int32_t zero_point() const {
    return GetField<int32_t>(VT_ZERO_POINT, 0);
  }
  bool mutate_zero_point(int32_t _zero_point = 0) {
    return SetField<int32_t>(VT_ZERO_POINT, _zero_point, 0);
  }
  const torch::jit::mobile::serialization::TensorMetadata *scales() const {
    return GetPointer<const torch::jit::mobile::serialization::TensorMetadata *>(VT_SCALES);
  }
  torch::jit::mobile::serialization::TensorMetadata *mutable_scales() {
    return GetPointer<torch::jit::mobile::serialization::TensorMetadata *>(VT_SCALES);
  }
  const torch::jit::mobile::serialization::TensorMetadata *zero_points() const {
    return GetPointer<const torch::jit::mobile::serialization::TensorMetadata *>(VT_ZERO_POINTS);
  }
  torch::jit::mobile::serialization::TensorMetadata *mutable_zero_points() {
    return GetPointer<torch::jit::mobile::serialization::TensorMetadata *>(VT_ZERO_POINTS);
  }
  int32_t axis() const {
    return GetField<int32_t>(VT_AXIS, 0);
  }
  bool mutate_axis(int32_t _axis = 0) {
    return SetField<int32_t>(VT_AXIS, _axis, 0);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<int8_t>(verifier, VT_QSCHEME, 1) &&
           VerifyField<double>(verifier, VT_SCALE, 8) &&
           VerifyField<int32_t>(verifier, VT_ZERO_POINT, 4) &&
           VerifyOffset(verifier, VT_SCALES) &&
           verifier.VerifyTable(scales()) &&
           VerifyOffset(verifier, VT_ZERO_POINTS) &&
           verifier.VerifyTable(zero_points()) &&
           VerifyField<int32_t>(verifier, VT_AXIS, 4) &&
           verifier.EndTable();
  }
};

struct QuantizedSchemaBuilder {
  typedef QuantizedSchema Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_qscheme(int8_t qscheme) {
    fbb_.AddElement<int8_t>(QuantizedSchema::VT_QSCHEME, qscheme, 0);
  }
  void add_scale(double scale) {
    fbb_.AddElement<double>(QuantizedSchema::VT_SCALE, scale, 0.0);
  }
  void add_zero_point(int32_t zero_point) {
    fbb_.AddElement<int32_t>(QuantizedSchema::VT_ZERO_POINT, zero_point, 0);
  }
  void add_scales(::flatbuffers::Offset<torch::jit::mobile::serialization::TensorMetadata> scales) {
    fbb_.AddOffset(QuantizedSchema::VT_SCALES, scales);
  }
  void add_zero_points(::flatbuffers::Offset<torch::jit::mobile::serialization::TensorMetadata> zero_points) {
    fbb_.AddOffset(QuantizedSchema::VT_ZERO_POINTS, zero_points);
  }
  void add_axis(int32_t axis) {
    fbb_.AddElement<int32_t>(QuantizedSchema::VT_AXIS, axis, 0);
  }
  explicit QuantizedSchemaBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<QuantizedSchema> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<QuantizedSchema>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<QuantizedSchema> CreateQuantizedSchema(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    int8_t qscheme = 0,
    double scale = 0.0,
    int32_t zero_point = 0,
    ::flatbuffers::Offset<torch::jit::mobile::serialization::TensorMetadata> scales = 0,
    ::flatbuffers::Offset<torch::jit::mobile::serialization::TensorMetadata> zero_points = 0,
    int32_t axis = 0) {
  QuantizedSchemaBuilder builder_(_fbb);
  builder_.add_scale(scale);
  builder_.add_axis(axis);
  builder_.add_zero_points(zero_points);
  builder_.add_scales(scales);
  builder_.add_zero_point(zero_point);
  builder_.add_qscheme(qscheme);
  return builder_.Finish();
}

struct TensorMetadata FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef TensorMetadataBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_STORAGE_LOCATION_INDEX = 4,
    VT_SCALAR_TYPE = 6,
    VT_STORAGE_OFFSET = 8,
    VT_SIZES = 10,
    VT_STRIDES = 12,
    VT_REQUIRES_GRAD = 14,
    VT_QUANTIZED_SCHEMA = 16
  };
  uint32_t storage_location_index() const {
    return GetField<uint32_t>(VT_STORAGE_LOCATION_INDEX, 0);
  }
  bool mutate_storage_location_index(uint32_t _storage_location_index = 0) {
    return SetField<uint32_t>(VT_STORAGE_LOCATION_INDEX, _storage_location_index, 0);
  }
  int8_t scalar_type() const {
    return GetField<int8_t>(VT_SCALAR_TYPE, 0);
  }
  bool mutate_scalar_type(int8_t _scalar_type = 0) {
    return SetField<int8_t>(VT_SCALAR_TYPE, _scalar_type, 0);
  }
  int32_t storage_offset() const {
    return GetField<int32_t>(VT_STORAGE_OFFSET, 0);
  }
  bool mutate_storage_offset(int32_t _storage_offset = 0) {
    return SetField<int32_t>(VT_STORAGE_OFFSET, _storage_offset, 0);
  }
  const ::flatbuffers::Vector<int32_t> *sizes() const {
    return GetPointer<const ::flatbuffers::Vector<int32_t> *>(VT_SIZES);
  }
  ::flatbuffers::Vector<int32_t> *mutable_sizes() {
    return GetPointer<::flatbuffers::Vector<int32_t> *>(VT_SIZES);
  }
  const ::flatbuffers::Vector<int32_t> *strides() const {
    return GetPointer<const ::flatbuffers::Vector<int32_t> *>(VT_STRIDES);
  }
  ::flatbuffers::Vector<int32_t> *mutable_strides() {
    return GetPointer<::flatbuffers::Vector<int32_t> *>(VT_STRIDES);
  }
  bool requires_grad() const {
    return GetField<uint8_t>(VT_REQUIRES_GRAD, 0) != 0;
  }
  bool mutate_requires_grad(bool _requires_grad = 0) {
    return SetField<uint8_t>(VT_REQUIRES_GRAD, static_cast<uint8_t>(_requires_grad), 0);
  }
  const torch::jit::mobile::serialization::QuantizedSchema *quantized_schema() const {
    return GetPointer<const torch::jit::mobile::serialization::QuantizedSchema *>(VT_QUANTIZED_SCHEMA);
  }
  torch::jit::mobile::serialization::QuantizedSchema *mutable_quantized_schema() {
    return GetPointer<torch::jit::mobile::serialization::QuantizedSchema *>(VT_QUANTIZED_SCHEMA);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<uint32_t>(verifier, VT_STORAGE_LOCATION_INDEX, 4) &&
           VerifyField<int8_t>(verifier, VT_SCALAR_TYPE, 1) &&
           VerifyField<int32_t>(verifier, VT_STORAGE_OFFSET, 4) &&
           VerifyOffset(verifier, VT_SIZES) &&
           verifier.VerifyVector(sizes()) &&
           VerifyOffset(verifier, VT_STRIDES) &&
           verifier.VerifyVector(strides()) &&
           VerifyField<uint8_t>(verifier, VT_REQUIRES_GRAD, 1) &&
           VerifyOffset(verifier, VT_QUANTIZED_SCHEMA) &&
           verifier.VerifyTable(quantized_schema()) &&
           verifier.EndTable();
  }
};

struct TensorMetadataBuilder {
  typedef TensorMetadata Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_storage_location_index(uint32_t storage_location_index) {
    fbb_.AddElement<uint32_t>(TensorMetadata::VT_STORAGE_LOCATION_INDEX, storage_location_index, 0);
  }
  void add_scalar_type(int8_t scalar_type) {
    fbb_.AddElement<int8_t>(TensorMetadata::VT_SCALAR_TYPE, scalar_type, 0);
  }
  void add_storage_offset(int32_t storage_offset) {
    fbb_.AddElement<int32_t>(TensorMetadata::VT_STORAGE_OFFSET, storage_offset, 0);
  }
  void add_sizes(::flatbuffers::Offset<::flatbuffers::Vector<int32_t>> sizes) {
    fbb_.AddOffset(TensorMetadata::VT_SIZES, sizes);
  }
  void add_strides(::flatbuffers::Offset<::flatbuffers::Vector<int32_t>> strides) {
    fbb_.AddOffset(TensorMetadata::VT_STRIDES, strides);
  }
  void add_requires_grad(bool requires_grad) {
    fbb_.AddElement<uint8_t>(TensorMetadata::VT_REQUIRES_GRAD, static_cast<uint8_t>(requires_grad), 0);
  }
  void add_quantized_schema(::flatbuffers::Offset<torch::jit::mobile::serialization::QuantizedSchema> quantized_schema) {
    fbb_.AddOffset(TensorMetadata::VT_QUANTIZED_SCHEMA, quantized_schema);
  }
  explicit TensorMetadataBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<TensorMetadata> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<TensorMetadata>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<TensorMetadata> CreateTensorMetadata(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    uint32_t storage_location_index = 0,
    int8_t scalar_type = 0,
    int32_t storage_offset = 0,
    ::flatbuffers::Offset<::flatbuffers::Vector<int32_t>> sizes = 0,
    ::flatbuffers::Offset<::flatbuffers::Vector<int32_t>> strides = 0,
    bool requires_grad = false,
    ::flatbuffers::Offset<torch::jit::mobile::serialization::QuantizedSchema> quantized_schema = 0) {
  TensorMetadataBuilder builder_(_fbb);
  builder_.add_quantized_schema(quantized_schema);
  builder_.add_strides(strides);
  builder_.add_sizes(sizes);
  builder_.add_storage_offset(storage_offset);
  builder_.add_storage_location_index(storage_location_index);
  builder_.add_requires_grad(requires_grad);
  builder_.add_scalar_type(scalar_type);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<TensorMetadata> CreateTensorMetadataDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    uint32_t storage_location_index = 0,
    int8_t scalar_type = 0,
    int32_t storage_offset = 0,
    const std::vector<int32_t> *sizes = nullptr,
    const std::vector<int32_t> *strides = nullptr,
    bool requires_grad = false,
    ::flatbuffers::Offset<torch::jit::mobile::serialization::QuantizedSchema> quantized_schema = 0) {
  auto sizes__ = sizes ? _fbb.CreateVector<int32_t>(*sizes) : 0;
  auto strides__ = strides ? _fbb.CreateVector<int32_t>(*strides) : 0;
  return torch::jit::mobile::serialization::CreateTensorMetadata(
      _fbb,
      storage_location_index,
      scalar_type,
      storage_offset,
      sizes__,
      strides__,
      requires_grad,
      quantized_schema);
}

struct String FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef StringBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_DATA = 4
  };
  const ::flatbuffers::String *data() const {
    return GetPointer<const ::flatbuffers::String *>(VT_DATA);
  }
  ::flatbuffers::String *mutable_data() {
    return GetPointer<::flatbuffers::String *>(VT_DATA);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_DATA) &&
           verifier.VerifyString(data()) &&
           verifier.EndTable();
  }
};

struct StringBuilder {
  typedef String Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_data(::flatbuffers::Offset<::flatbuffers::String> data) {
    fbb_.AddOffset(String::VT_DATA, data);
  }
  explicit StringBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<String> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<String>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<String> CreateString(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<::flatbuffers::String> data = 0) {
  StringBuilder builder_(_fbb);
  builder_.add_data(data);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<String> CreateStringDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    const char *data = nullptr) {
  auto data__ = data ? _fbb.CreateString(data) : 0;
  return torch::jit::mobile::serialization::CreateString(
      _fbb,
      data__);
}

struct Device FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DeviceBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_STR = 4
  };
  const ::flatbuffers::String *str() const {
    return GetPointer<const ::flatbuffers::String *>(VT_STR);
  }
  ::flatbuffers::String *mutable_str() {
    return GetPointer<::flatbuffers::String *>(VT_STR);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_STR) &&
           verifier.VerifyString(str()) &&
           verifier.EndTable();
  }
};

struct DeviceBuilder {
  typedef Device Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_str(::flatbuffers::Offset<::flatbuffers::String> str) {
    fbb_.AddOffset(Device::VT_STR, str);
  }
  explicit DeviceBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<Device> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<Device>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<Device> CreateDevice(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<::flatbuffers::String> str = 0) {
  DeviceBuilder builder_(_fbb);
  builder_.add_str(str);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<Device> CreateDeviceDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    const char *str = nullptr) {
  auto str__ = str ? _fbb.CreateString(str) : 0;
  return torch::jit::mobile::serialization::CreateDevice(
      _fbb,
      str__);
}

struct List FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef ListBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_ITEMS = 4,
    VT_ANNOTATION_STR = 6
  };
  const ::flatbuffers::Vector<uint32_t> *items() const {
    return GetPointer<const ::flatbuffers::Vector<uint32_t> *>(VT_ITEMS);
  }
  ::flatbuffers::Vector<uint32_t> *mutable_items() {
    return GetPointer<::flatbuffers::Vector<uint32_t> *>(VT_ITEMS);
  }
  const ::flatbuffers::String *annotation_str() const {
    return GetPointer<const ::flatbuffers::String *>(VT_ANNOTATION_STR);
  }
  ::flatbuffers::String *mutable_annotation_str() {
    return GetPointer<::flatbuffers::String *>(VT_ANNOTATION_STR);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_ITEMS) &&
           verifier.VerifyVector(items()) &&
           VerifyOffset(verifier, VT_ANNOTATION_STR) &&
           verifier.VerifyString(annotation_str()) &&
           verifier.EndTable();
  }
};

struct ListBuilder {
  typedef List Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_items(::flatbuffers::Offset<::flatbuffers::Vector<uint32_t>> items) {
    fbb_.AddOffset(List::VT_ITEMS, items);
  }
  void add_annotation_str(::flatbuffers::Offset<::flatbuffers::String> annotation_str) {
    fbb_.AddOffset(List::VT_ANNOTATION_STR, annotation_str);
  }
  explicit ListBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<List> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<List>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<List> CreateList(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<::flatbuffers::Vector<uint32_t>> items = 0,
    ::flatbuffers::Offset<::flatbuffers::String> annotation_str = 0) {
  ListBuilder builder_(_fbb);
  builder_.add_annotation_str(annotation_str);
  builder_.add_items(items);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<List> CreateListDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    const std::vector<uint32_t> *items = nullptr,
    const char *annotation_str = nullptr) {
  auto items__ = items ? _fbb.CreateVector<uint32_t>(*items) : 0;
  auto annotation_str__ = annotation_str ? _fbb.CreateString(annotation_str) : 0;
  return torch::jit::mobile::serialization::CreateList(
      _fbb,
      items__,
      annotation_str__);
}

struct IntList FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef IntListBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_ITEMS = 4
  };
  const ::flatbuffers::Vector<int64_t> *items() const {
    return GetPointer<const ::flatbuffers::Vector<int64_t> *>(VT_ITEMS);
  }
  ::flatbuffers::Vector<int64_t> *mutable_items() {
    return GetPointer<::flatbuffers::Vector<int64_t> *>(VT_ITEMS);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_ITEMS) &&
           verifier.VerifyVector(items()) &&
           verifier.EndTable();
  }
};

struct IntListBuilder {
  typedef IntList Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_items(::flatbuffers::Offset<::flatbuffers::Vector<int64_t>> items) {
    fbb_.AddOffset(IntList::VT_ITEMS, items);
  }
  explicit IntListBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<IntList> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<IntList>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<IntList> CreateIntList(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<::flatbuffers::Vector<int64_t>> items = 0) {
  IntListBuilder builder_(_fbb);
  builder_.add_items(items);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<IntList> CreateIntListDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    const std::vector<int64_t> *items = nullptr) {
  auto items__ = items ? _fbb.CreateVector<int64_t>(*items) : 0;
  return torch::jit::mobile::serialization::CreateIntList(
      _fbb,
      items__);
}

struct DoubleList FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DoubleListBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_ITEMS = 4
  };
  const ::flatbuffers::Vector<double> *items() const {
    return GetPointer<const ::flatbuffers::Vector<double> *>(VT_ITEMS);
  }
  ::flatbuffers::Vector<double> *mutable_items() {
    return GetPointer<::flatbuffers::Vector<double> *>(VT_ITEMS);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_ITEMS) &&
           verifier.VerifyVector(items()) &&
           verifier.EndTable();
  }
};

struct DoubleListBuilder {
  typedef DoubleList Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_items(::flatbuffers::Offset<::flatbuffers::Vector<double>> items) {
    fbb_.AddOffset(DoubleList::VT_ITEMS, items);
  }
  explicit DoubleListBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DoubleList> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DoubleList>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DoubleList> CreateDoubleList(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<::flatbuffers::Vector<double>> items = 0) {
  DoubleListBuilder builder_(_fbb);
  builder_.add_items(items);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<DoubleList> CreateDoubleListDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    const std::vector<double> *items = nullptr) {
  auto items__ = items ? _fbb.CreateVector<double>(*items) : 0;
  return torch::jit::mobile::serialization::CreateDoubleList(
      _fbb,
      items__);
}

struct BoolList FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef BoolListBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_ITEMS = 4
  };
  const ::flatbuffers::Vector<uint8_t> *items() const {
    return GetPointer<const ::flatbuffers::Vector<uint8_t> *>(VT_ITEMS);
  }
  ::flatbuffers::Vector<uint8_t> *mutable_items() {
    return GetPointer<::flatbuffers::Vector<uint8_t> *>(VT_ITEMS);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_ITEMS) &&
           verifier.VerifyVector(items()) &&
           verifier.EndTable();
  }
};

struct BoolListBuilder {
  typedef BoolList Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_items(::flatbuffers::Offset<::flatbuffers::Vector<uint8_t>> items) {
    fbb_.AddOffset(BoolList::VT_ITEMS, items);
  }
  explicit BoolListBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<BoolList> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<BoolList>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<BoolList> CreateBoolList(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<::flatbuffers::Vector<uint8_t>> items = 0) {
  BoolListBuilder builder_(_fbb);
  builder_.add_items(items);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<BoolList> CreateBoolListDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    const std::vector<uint8_t> *items = nullptr) {
  auto items__ = items ? _fbb.CreateVector<uint8_t>(*items) : 0;
  return torch::jit::mobile::serialization::CreateBoolList(
      _fbb,
      items__);
}

struct Tuple FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef TupleBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_ITEMS = 4
  };
  const ::flatbuffers::Vector<uint32_t> *items() const {
    return GetPointer<const ::flatbuffers::Vector<uint32_t> *>(VT_ITEMS);
  }
  ::flatbuffers::Vector<uint32_t> *mutable_items() {
    return GetPointer<::flatbuffers::Vector<uint32_t> *>(VT_ITEMS);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_ITEMS) &&
           verifier.VerifyVector(items()) &&
           verifier.EndTable();
  }
};

struct TupleBuilder {
  typedef Tuple Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_items(::flatbuffers::Offset<::flatbuffers::Vector<uint32_t>> items) {
    fbb_.AddOffset(Tuple::VT_ITEMS, items);
  }
  explicit TupleBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<Tuple> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<Tuple>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<Tuple> CreateTuple(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<::flatbuffers::Vector<uint32_t>> items = 0) {
  TupleBuilder builder_(_fbb);
  builder_.add_items(items);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<Tuple> CreateTupleDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    const std::vector<uint32_t> *items = nullptr) {
  auto items__ = items ? _fbb.CreateVector<uint32_t>(*items) : 0;
  return torch::jit::mobile::serialization::CreateTuple(
      _fbb,
      items__);
}

struct Dict FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DictBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_KEYS = 4,
    VT_VALUES = 6,
    VT_ANNOTATION_STR = 8
  };
  const ::flatbuffers::Vector<uint32_t> *keys() const {
    return GetPointer<const ::flatbuffers::Vector<uint32_t> *>(VT_KEYS);
  }
  ::flatbuffers::Vector<uint32_t> *mutable_keys() {
    return GetPointer<::flatbuffers::Vector<uint32_t> *>(VT_KEYS);
  }
  const ::flatbuffers::Vector<uint32_t> *values() const {
    return GetPointer<const ::flatbuffers::Vector<uint32_t> *>(VT_VALUES);
  }
  ::flatbuffers::Vector<uint32_t> *mutable_values() {
    return GetPointer<::flatbuffers::Vector<uint32_t> *>(VT_VALUES);
  }
  const ::flatbuffers::String *annotation_str() const {
    return GetPointer<const ::flatbuffers::String *>(VT_ANNOTATION_STR);
  }
  ::flatbuffers::String *mutable_annotation_str() {
    return GetPointer<::flatbuffers::String *>(VT_ANNOTATION_STR);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_KEYS) &&
           verifier.VerifyVector(keys()) &&
           VerifyOffset(verifier, VT_VALUES) &&
           verifier.VerifyVector(values()) &&
           VerifyOffset(verifier, VT_ANNOTATION_STR) &&
           verifier.VerifyString(annotation_str()) &&
           verifier.EndTable();
  }
};

struct DictBuilder {
  typedef Dict Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_keys(::flatbuffers::Offset<::flatbuffers::Vector<uint32_t>> keys) {
    fbb_.AddOffset(Dict::VT_KEYS, keys);
  }
  void add_values(::flatbuffers::Offset<::flatbuffers::Vector<uint32_t>> values) {
    fbb_.AddOffset(Dict::VT_VALUES, values);
  }
  void add_annotation_str(::flatbuffers::Offset<::flatbuffers::String> annotation_str) {
    fbb_.AddOffset(Dict::VT_ANNOTATION_STR, annotation_str);
  }
  explicit DictBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<Dict> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<Dict>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<Dict> CreateDict(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<::flatbuffers::Vector<uint32_t>> keys = 0,
    ::flatbuffers::Offset<::flatbuffers::Vector<uint32_t>> values = 0,
    ::flatbuffers::Offset<::flatbuffers::String> annotation_str = 0) {
  DictBuilder builder_(_fbb);
  builder_.add_annotation_str(annotation_str);
  builder_.add_values(values);
  builder_.add_keys(keys);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<Dict> CreateDictDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    const std::vector<uint32_t> *keys = nullptr,
    const std::vector<uint32_t> *values = nullptr,
    const char *annotation_str = nullptr) {
  auto keys__ = keys ? _fbb.CreateVector<uint32_t>(*keys) : 0;
  auto values__ = values ? _fbb.CreateVector<uint32_t>(*values) : 0;
  auto annotation_str__ = annotation_str ? _fbb.CreateString(annotation_str) : 0;
  return torch::jit::mobile::serialization::CreateDict(
      _fbb,
      keys__,
      values__,
      annotation_str__);
}

struct ObjectType FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef ObjectTypeBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_TYPE_NAME = 4,
    VT_TYPE = 6,
    VT_ATTR_NAMES = 8
  };
  const ::flatbuffers::String *type_name() const {
    return GetPointer<const ::flatbuffers::String *>(VT_TYPE_NAME);
  }
  ::flatbuffers::String *mutable_type_name() {
    return GetPointer<::flatbuffers::String *>(VT_TYPE_NAME);
  }
  torch::jit::mobile::serialization::TypeType type() const {
    return static_cast<torch::jit::mobile::serialization::TypeType>(GetField<uint8_t>(VT_TYPE, 0));
  }
  bool mutate_type(torch::jit::mobile::serialization::TypeType _type = static_cast<torch::jit::mobile::serialization::TypeType>(0)) {
    return SetField<uint8_t>(VT_TYPE, static_cast<uint8_t>(_type), 0);
  }
  const ::flatbuffers::Vector<::flatbuffers::Offset<::flatbuffers::String>> *attr_names() const {
    return GetPointer<const ::flatbuffers::Vector<::flatbuffers::Offset<::flatbuffers::String>> *>(VT_ATTR_NAMES);
  }
  ::flatbuffers::Vector<::flatbuffers::Offset<::flatbuffers::String>> *mutable_attr_names() {
    return GetPointer<::flatbuffers::Vector<::flatbuffers::Offset<::flatbuffers::String>> *>(VT_ATTR_NAMES);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_TYPE_NAME) &&
           verifier.VerifyString(type_name()) &&
           VerifyField<uint8_t>(verifier, VT_TYPE, 1) &&
           VerifyOffset(verifier, VT_ATTR_NAMES) &&
           verifier.VerifyVector(attr_names()) &&
           verifier.VerifyVectorOfStrings(attr_names()) &&
           verifier.EndTable();
  }
};

struct ObjectTypeBuilder {
  typedef ObjectType Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_type_name(::flatbuffers::Offset<::flatbuffers::String> type_name) {
    fbb_.AddOffset(ObjectType::VT_TYPE_NAME, type_name);
  }
  void add_type(torch::jit::mobile::serialization::TypeType type) {
    fbb_.AddElement<uint8_t>(ObjectType::VT_TYPE, static_cast<uint8_t>(type), 0);
  }
  void add_attr_names(::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<::flatbuffers::String>>> attr_names) {
    fbb_.AddOffset(ObjectType::VT_ATTR_NAMES, attr_names);
  }
  explicit ObjectTypeBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<ObjectType> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<ObjectType>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<ObjectType> CreateObjectType(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<::flatbuffers::String> type_name = 0,
    torch::jit::mobile::serialization::TypeType type = torch::jit::mobile::serialization::TypeType::UNSET,
    ::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<::flatbuffers::String>>> attr_names = 0) {
  ObjectTypeBuilder builder_(_fbb);
  builder_.add_attr_names(attr_names);
  builder_.add_type_name(type_name);
  builder_.add_type(type);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<ObjectType> CreateObjectTypeDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    const char *type_name = nullptr,
    torch::jit::mobile::serialization::TypeType type = torch::jit::mobile::serialization::TypeType::UNSET,
    const std::vector<::flatbuffers::Offset<::flatbuffers::String>> *attr_names = nullptr) {
  auto type_name__ = type_name ? _fbb.CreateString(type_name) : 0;
  auto attr_names__ = attr_names ? _fbb.CreateVector<::flatbuffers::Offset<::flatbuffers::String>>(*attr_names) : 0;
  return torch::jit::mobile::serialization::CreateObjectType(
      _fbb,
      type_name__,
      type,
      attr_names__);
}

struct Object FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef ObjectBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_TYPE_INDEX = 4,
    VT_STATE = 6,
    VT_ATTRS = 8,
    VT_SETSTATE_FUNC = 10
  };
  uint32_t type_index() const {
    return GetField<uint32_t>(VT_TYPE_INDEX, 0);
  }
  bool mutate_type_index(uint32_t _type_index = 0) {
    return SetField<uint32_t>(VT_TYPE_INDEX, _type_index, 0);
  }
  uint32_t state() const {
    return GetField<uint32_t>(VT_STATE, 0);
  }
  bool mutate_state(uint32_t _state = 0) {
    return SetField<uint32_t>(VT_STATE, _state, 0);
  }
  const ::flatbuffers::Vector<uint32_t> *attrs() const {
    return GetPointer<const ::flatbuffers::Vector<uint32_t> *>(VT_ATTRS);
  }
  ::flatbuffers::Vector<uint32_t> *mutable_attrs() {
    return GetPointer<::flatbuffers::Vector<uint32_t> *>(VT_ATTRS);
  }
  uint32_t setstate_func() const {
    return GetField<uint32_t>(VT_SETSTATE_FUNC, 0);
  }
  bool mutate_setstate_func(uint32_t _setstate_func = 0) {
    return SetField<uint32_t>(VT_SETSTATE_FUNC, _setstate_func, 0);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<uint32_t>(verifier, VT_TYPE_INDEX, 4) &&
           VerifyField<uint32_t>(verifier, VT_STATE, 4) &&
           VerifyOffset(verifier, VT_ATTRS) &&
           verifier.VerifyVector(attrs()) &&
           VerifyField<uint32_t>(verifier, VT_SETSTATE_FUNC, 4) &&
           verifier.EndTable();
  }
};

struct ObjectBuilder {
  typedef Object Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_type_index(uint32_t type_index) {
    fbb_.AddElement<uint32_t>(Object::VT_TYPE_INDEX, type_index, 0);
  }
  void add_state(uint32_t state) {
    fbb_.AddElement<uint32_t>(Object::VT_STATE, state, 0);
  }
  void add_attrs(::flatbuffers::Offset<::flatbuffers::Vector<uint32_t>> attrs) {
    fbb_.AddOffset(Object::VT_ATTRS, attrs);
  }
  void add_setstate_func(uint32_t setstate_func) {
    fbb_.AddElement<uint32_t>(Object::VT_SETSTATE_FUNC, setstate_func, 0);
  }
  explicit ObjectBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<Object> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<Object>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<Object> CreateObject(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    uint32_t type_index = 0,
    uint32_t state = 0,
    ::flatbuffers::Offset<::flatbuffers::Vector<uint32_t>> attrs = 0,
    uint32_t setstate_func = 0) {
  ObjectBuilder builder_(_fbb);
  builder_.add_setstate_func(setstate_func);
  builder_.add_attrs(attrs);
  builder_.add_state(state);
  builder_.add_type_index(type_index);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<Object> CreateObjectDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    uint32_t type_index = 0,
    uint32_t state = 0,
    const std::vector<uint32_t> *attrs = nullptr,
    uint32_t setstate_func = 0) {
  auto attrs__ = attrs ? _fbb.CreateVector<uint32_t>(*attrs) : 0;
  return torch::jit::mobile::serialization::CreateObject(
      _fbb,
      type_index,
      state,
      attrs__,
      setstate_func);
}

struct EnumValue FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef EnumValueBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_TYPE_NAME = 4,
    VT_VALUE = 6
  };
  const ::flatbuffers::String *type_name() const {
    return GetPointer<const ::flatbuffers::String *>(VT_TYPE_NAME);
  }
  ::flatbuffers::String *mutable_type_name() {
    return GetPointer<::flatbuffers::String *>(VT_TYPE_NAME);
  }
  uint32_t value() const {
    return GetField<uint32_t>(VT_VALUE, 0);
  }
  bool mutate_value(uint32_t _value = 0) {
    return SetField<uint32_t>(VT_VALUE, _value, 0);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_TYPE_NAME) &&
           verifier.VerifyString(type_name()) &&
           VerifyField<uint32_t>(verifier, VT_VALUE, 4) &&
           verifier.EndTable();
  }
};

struct EnumValueBuilder {
  typedef EnumValue Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_type_name(::flatbuffers::Offset<::flatbuffers::String> type_name) {
    fbb_.AddOffset(EnumValue::VT_TYPE_NAME, type_name);
  }
  void add_value(uint32_t value) {
    fbb_.AddElement<uint32_t>(EnumValue::VT_VALUE, value, 0);
  }
  explicit EnumValueBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<EnumValue> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<EnumValue>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<EnumValue> CreateEnumValue(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<::flatbuffers::String> type_name = 0,
    uint32_t value = 0) {
  EnumValueBuilder builder_(_fbb);
  builder_.add_value(value);
  builder_.add_type_name(type_name);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<EnumValue> CreateEnumValueDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    const char *type_name = nullptr,
    uint32_t value = 0) {
  auto type_name__ = type_name ? _fbb.CreateString(type_name) : 0;
  return torch::jit::mobile::serialization::CreateEnumValue(
      _fbb,
      type_name__,
      value);
}

struct Operator FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef OperatorBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_NAME = 4,
    VT_OVERLOAD_NAME = 6,
    VT_NUM_ARGS_SERIALIZED = 8
  };
  const ::flatbuffers::String *name() const {
    return GetPointer<const ::flatbuffers::String *>(VT_NAME);
  }
  ::flatbuffers::String *mutable_name() {
    return GetPointer<::flatbuffers::String *>(VT_NAME);
  }
  const ::flatbuffers::String *overload_name() const {
    return GetPointer<const ::flatbuffers::String *>(VT_OVERLOAD_NAME);
  }
  ::flatbuffers::String *mutable_overload_name() {
    return GetPointer<::flatbuffers::String *>(VT_OVERLOAD_NAME);
  }
  int32_t num_args_serialized() const {
    return GetField<int32_t>(VT_NUM_ARGS_SERIALIZED, -1);
  }
  bool mutate_num_args_serialized(int32_t _num_args_serialized = -1) {
    return SetField<int32_t>(VT_NUM_ARGS_SERIALIZED, _num_args_serialized, -1);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_NAME) &&
           verifier.VerifyString(name()) &&
           VerifyOffset(verifier, VT_OVERLOAD_NAME) &&
           verifier.VerifyString(overload_name()) &&
           VerifyField<int32_t>(verifier, VT_NUM_ARGS_SERIALIZED, 4) &&
           verifier.EndTable();
  }
};

struct OperatorBuilder {
  typedef Operator Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_name(::flatbuffers::Offset<::flatbuffers::String> name) {
    fbb_.AddOffset(Operator::VT_NAME, name);
  }
  void add_overload_name(::flatbuffers::Offset<::flatbuffers::String> overload_name) {
    fbb_.AddOffset(Operator::VT_OVERLOAD_NAME, overload_name);
  }
  void add_num_args_serialized(int32_t num_args_serialized) {
    fbb_.AddElement<int32_t>(Operator::VT_NUM_ARGS_SERIALIZED, num_args_serialized, -1);
  }
  explicit OperatorBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<Operator> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<Operator>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<Operator> CreateOperator(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<::flatbuffers::String> name = 0,
    ::flatbuffers::Offset<::flatbuffers::String> overload_name = 0,
    int32_t num_args_serialized = -1) {
  OperatorBuilder builder_(_fbb);
  builder_.add_num_args_serialized(num_args_serialized);
  builder_.add_overload_name(overload_name);
  builder_.add_name(name);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<Operator> CreateOperatorDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    const char *name = nullptr,
    const char *overload_name = nullptr,
    int32_t num_args_serialized = -1) {
  auto name__ = name ? _fbb.CreateString(name) : 0;
  auto overload_name__ = overload_name ? _fbb.CreateString(overload_name) : 0;
  return torch::jit::mobile::serialization::CreateOperator(
      _fbb,
      name__,
      overload_name__,
      num_args_serialized);
}

struct Arg FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef ArgBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_NAME = 4,
    VT_TYPE = 6,
    VT_DEFAULT_VALUE = 8
  };
  const ::flatbuffers::String *name() const {
    return GetPointer<const ::flatbuffers::String *>(VT_NAME);
  }
  ::flatbuffers::String *mutable_name() {
    return GetPointer<::flatbuffers::String *>(VT_NAME);
  }
  const ::flatbuffers::String *type() const {
    return GetPointer<const ::flatbuffers::String *>(VT_TYPE);
  }
  ::flatbuffers::String *mutable_type() {
    return GetPointer<::flatbuffers::String *>(VT_TYPE);
  }
  uint32_t default_value() const {
    return GetField<uint32_t>(VT_DEFAULT_VALUE, 0);
  }
  bool mutate_default_value(uint32_t _default_value = 0) {
    return SetField<uint32_t>(VT_DEFAULT_VALUE, _default_value, 0);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_NAME) &&
           verifier.VerifyString(name()) &&
           VerifyOffset(verifier, VT_TYPE) &&
           verifier.VerifyString(type()) &&
           VerifyField<uint32_t>(verifier, VT_DEFAULT_VALUE, 4) &&
           verifier.EndTable();
  }
};

struct ArgBuilder {
  typedef Arg Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_name(::flatbuffers::Offset<::flatbuffers::String> name) {
    fbb_.AddOffset(Arg::VT_NAME, name);
  }
  void add_type(::flatbuffers::Offset<::flatbuffers::String> type) {
    fbb_.AddOffset(Arg::VT_TYPE, type);
  }
  void add_default_value(uint32_t default_value) {
    fbb_.AddElement<uint32_t>(Arg::VT_DEFAULT_VALUE, default_value, 0);
  }
  explicit ArgBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<Arg> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<Arg>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<Arg> CreateArg(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<::flatbuffers::String> name = 0,
    ::flatbuffers::Offset<::flatbuffers::String> type = 0,
    uint32_t default_value = 0) {
  ArgBuilder builder_(_fbb);
  builder_.add_default_value(default_value);
  builder_.add_type(type);
  builder_.add_name(name);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<Arg> CreateArgDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    const char *name = nullptr,
    const char *type = nullptr,
    uint32_t default_value = 0) {
  auto name__ = name ? _fbb.CreateString(name) : 0;
  auto type__ = type ? _fbb.CreateString(type) : 0;
  return torch::jit::mobile::serialization::CreateArg(
      _fbb,
      name__,
      type__,
      default_value);
}

struct Schema FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef SchemaBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_ARGUMENTS = 4,
    VT_RETURNS = 6
  };
  const ::flatbuffers::Vector<::flatbuffers::Offset<torch::jit::mobile::serialization::Arg>> *arguments() const {
    return GetPointer<const ::flatbuffers::Vector<::flatbuffers::Offset<torch::jit::mobile::serialization::Arg>> *>(VT_ARGUMENTS);
  }
  ::flatbuffers::Vector<::flatbuffers::Offset<torch::jit::mobile::serialization::Arg>> *mutable_arguments() {
    return GetPointer<::flatbuffers::Vector<::flatbuffers::Offset<torch::jit::mobile::serialization::Arg>> *>(VT_ARGUMENTS);
  }
  const ::flatbuffers::Vector<::flatbuffers::Offset<torch::jit::mobile::serialization::Arg>> *returns() const {
    return GetPointer<const ::flatbuffers::Vector<::flatbuffers::Offset<torch::jit::mobile::serialization::Arg>> *>(VT_RETURNS);
  }
  ::flatbuffers::Vector<::flatbuffers::Offset<torch::jit::mobile::serialization::Arg>> *mutable_returns() {
    return GetPointer<::flatbuffers::Vector<::flatbuffers::Offset<torch::jit::mobile::serialization::Arg>> *>(VT_RETURNS);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_ARGUMENTS) &&
           verifier.VerifyVector(arguments()) &&
           verifier.VerifyVectorOfTables(arguments()) &&
           VerifyOffset(verifier, VT_RETURNS) &&
           verifier.VerifyVector(returns()) &&
           verifier.VerifyVectorOfTables(returns()) &&
           verifier.EndTable();
  }
};

struct SchemaBuilder {
  typedef Schema Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_arguments(::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<torch::jit::mobile::serialization::Arg>>> arguments) {
    fbb_.AddOffset(Schema::VT_ARGUMENTS, arguments);
  }
  void add_returns(::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<torch::jit::mobile::serialization::Arg>>> returns) {
    fbb_.AddOffset(Schema::VT_RETURNS, returns);
  }
  explicit SchemaBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<Schema> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<Schema>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<Schema> CreateSchema(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<torch::jit::mobile::serialization::Arg>>> arguments = 0,
    ::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<torch::jit::mobile::serialization::Arg>>> returns = 0) {
  SchemaBuilder builder_(_fbb);
  builder_.add_returns(returns);
  builder_.add_arguments(arguments);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<Schema> CreateSchemaDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    const std::vector<::flatbuffers::Offset<torch::jit::mobile::serialization::Arg>> *arguments = nullptr,
    const std::vector<::flatbuffers::Offset<torch::jit::mobile::serialization::Arg>> *returns = nullptr) {
  auto arguments__ = arguments ? _fbb.CreateVector<::flatbuffers::Offset<torch::jit::mobile::serialization::Arg>>(*arguments) : 0;
  auto returns__ = returns ? _fbb.CreateVector<::flatbuffers::Offset<torch::jit::mobile::serialization::Arg>>(*returns) : 0;
  return torch::jit::mobile::serialization::CreateSchema(
      _fbb,
      arguments__,
      returns__);
}

struct DebugInfo FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DebugInfoBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_DEBUG_HANDLE = 4
  };
  const ::flatbuffers::Vector<int64_t> *debug_handle() const {
    return GetPointer<const ::flatbuffers::Vector<int64_t> *>(VT_DEBUG_HANDLE);
  }
  ::flatbuffers::Vector<int64_t> *mutable_debug_handle() {
    return GetPointer<::flatbuffers::Vector<int64_t> *>(VT_DEBUG_HANDLE);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_DEBUG_HANDLE) &&
           verifier.VerifyVector(debug_handle()) &&
           verifier.EndTable();
  }
};

struct DebugInfoBuilder {
  typedef DebugInfo Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_debug_handle(::flatbuffers::Offset<::flatbuffers::Vector<int64_t>> debug_handle) {
    fbb_.AddOffset(DebugInfo::VT_DEBUG_HANDLE, debug_handle);
  }
  explicit DebugInfoBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DebugInfo> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DebugInfo>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DebugInfo> CreateDebugInfo(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<::flatbuffers::Vector<int64_t>> debug_handle = 0) {
  DebugInfoBuilder builder_(_fbb);
  builder_.add_debug_handle(debug_handle);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<DebugInfo> CreateDebugInfoDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    const std::vector<int64_t> *debug_handle = nullptr) {
  auto debug_handle__ = debug_handle ? _fbb.CreateVector<int64_t>(*debug_handle) : 0;
  return torch::jit::mobile::serialization::CreateDebugInfo(
      _fbb,
      debug_handle__);
}

struct Function FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef FunctionBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_QN = 4,
    VT_INSTRUCTIONS = 6,
    VT_OPERATORS = 8,
    VT_CONSTANTS = 10,
    VT_TYPE_ANNOTATIONS = 12,
    VT_REGISTER_SIZE = 14,
    VT_SCHEMA = 16,
    VT_DEBUG_INFO = 18,
    VT_CLASS_TYPE = 20
  };
  const ::flatbuffers::String *qn() const {
    return GetPointer<const ::flatbuffers::String *>(VT_QN);
  }
  ::flatbuffers::String *mutable_qn() {
    return GetPointer<::flatbuffers::String *>(VT_QN);
  }
  const ::flatbuffers::Vector<const torch::jit::mobile::serialization::Instruction *> *instructions() const {
    return GetPointer<const ::flatbuffers::Vector<const torch::jit::mobile::serialization::Instruction *> *>(VT_INSTRUCTIONS);
  }
  ::flatbuffers::Vector<const torch::jit::mobile::serialization::Instruction *> *mutable_instructions() {
    return GetPointer<::flatbuffers::Vector<const torch::jit::mobile::serialization::Instruction *> *>(VT_INSTRUCTIONS);
  }
  const ::flatbuffers::Vector<::flatbuffers::Offset<torch::jit::mobile::serialization::Operator>> *operators() const {
    return GetPointer<const ::flatbuffers::Vector<::flatbuffers::Offset<torch::jit::mobile::serialization::Operator>> *>(VT_OPERATORS);
  }
  ::flatbuffers::Vector<::flatbuffers::Offset<torch::jit::mobile::serialization::Operator>> *mutable_operators() {
    return GetPointer<::flatbuffers::Vector<::flatbuffers::Offset<torch::jit::mobile::serialization::Operator>> *>(VT_OPERATORS);
  }
  const ::flatbuffers::Vector<uint32_t> *constants() const {
    return GetPointer<const ::flatbuffers::Vector<uint32_t> *>(VT_CONSTANTS);
  }
  ::flatbuffers::Vector<uint32_t> *mutable_constants() {
    return GetPointer<::flatbuffers::Vector<uint32_t> *>(VT_CONSTANTS);
  }
  const ::flatbuffers::Vector<::flatbuffers::Offset<::flatbuffers::String>> *type_annotations() const {
    return GetPointer<const ::flatbuffers::Vector<::flatbuffers::Offset<::flatbuffers::String>> *>(VT_TYPE_ANNOTATIONS);
  }
  ::flatbuffers::Vector<::flatbuffers::Offset<::flatbuffers::String>> *mutable_type_annotations() {
    return GetPointer<::flatbuffers::Vector<::flatbuffers::Offset<::flatbuffers::String>> *>(VT_TYPE_ANNOTATIONS);
  }
  int32_t register_size() const {
    return GetField<int32_t>(VT_REGISTER_SIZE, 0);
  }
  bool mutate_register_size(int32_t _register_size = 0) {
    return SetField<int32_t>(VT_REGISTER_SIZE, _register_size, 0);
  }
  const torch::jit::mobile::serialization::Schema *schema() const {
    return GetPointer<const torch::jit::mobile::serialization::Schema *>(VT_SCHEMA);
  }
  torch::jit::mobile::serialization::Schema *mutable_schema() {
    return GetPointer<torch::jit::mobile::serialization::Schema *>(VT_SCHEMA);
  }
  const torch::jit::mobile::serialization::DebugInfo *debug_info() const {
    return GetPointer<const torch::jit::mobile::serialization::DebugInfo *>(VT_DEBUG_INFO);
  }
  torch::jit::mobile::serialization::DebugInfo *mutable_debug_info() {
    return GetPointer<torch::jit::mobile::serialization::DebugInfo *>(VT_DEBUG_INFO);
  }
  uint32_t class_type() const {
    return GetField<uint32_t>(VT_CLASS_TYPE, 0);
  }
  bool mutate_class_type(uint32_t _class_type = 0) {
    return SetField<uint32_t>(VT_CLASS_TYPE, _class_type, 0);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_QN) &&
           verifier.VerifyString(qn()) &&
           VerifyOffset(verifier, VT_INSTRUCTIONS) &&
           verifier.VerifyVector(instructions()) &&
           VerifyOffset(verifier, VT_OPERATORS) &&
           verifier.VerifyVector(operators()) &&
           verifier.VerifyVectorOfTables(operators()) &&
           VerifyOffset(verifier, VT_CONSTANTS) &&
           verifier.VerifyVector(constants()) &&
           VerifyOffset(verifier, VT_TYPE_ANNOTATIONS) &&
           verifier.VerifyVector(type_annotations()) &&
           verifier.VerifyVectorOfStrings(type_annotations()) &&
           VerifyField<int32_t>(verifier, VT_REGISTER_SIZE, 4) &&
           VerifyOffset(verifier, VT_SCHEMA) &&
           verifier.VerifyTable(schema()) &&
           VerifyOffset(verifier, VT_DEBUG_INFO) &&
           verifier.VerifyTable(debug_info()) &&
           VerifyField<uint32_t>(verifier, VT_CLASS_TYPE, 4) &&
           verifier.EndTable();
  }
};

struct FunctionBuilder {
  typedef Function Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_qn(::flatbuffers::Offset<::flatbuffers::String> qn) {
    fbb_.AddOffset(Function::VT_QN, qn);
  }
  void add_instructions(::flatbuffers::Offset<::flatbuffers::Vector<const torch::jit::mobile::serialization::Instruction *>> instructions) {
    fbb_.AddOffset(Function::VT_INSTRUCTIONS, instructions);
  }
  void add_operators(::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<torch::jit::mobile::serialization::Operator>>> operators) {
    fbb_.AddOffset(Function::VT_OPERATORS, operators);
  }
  void add_constants(::flatbuffers::Offset<::flatbuffers::Vector<uint32_t>> constants) {
    fbb_.AddOffset(Function::VT_CONSTANTS, constants);
  }
  void add_type_annotations(::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<::flatbuffers::String>>> type_annotations) {
    fbb_.AddOffset(Function::VT_TYPE_ANNOTATIONS, type_annotations);
  }
  void add_register_size(int32_t register_size) {
    fbb_.AddElement<int32_t>(Function::VT_REGISTER_SIZE, register_size, 0);
  }
  void add_schema(::flatbuffers::Offset<torch::jit::mobile::serialization::Schema> schema) {
    fbb_.AddOffset(Function::VT_SCHEMA, schema);
  }
  void add_debug_info(::flatbuffers::Offset<torch::jit::mobile::serialization::DebugInfo> debug_info) {
    fbb_.AddOffset(Function::VT_DEBUG_INFO, debug_info);
  }
  void add_class_type(uint32_t class_type) {
    fbb_.AddElement<uint32_t>(Function::VT_CLASS_TYPE, class_type, 0);
  }
  explicit FunctionBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<Function> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<Function>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<Function> CreateFunction(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<::flatbuffers::String> qn = 0,
    ::flatbuffers::Offset<::flatbuffers::Vector<const torch::jit::mobile::serialization::Instruction *>> instructions = 0,
    ::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<torch::jit::mobile::serialization::Operator>>> operators = 0,
    ::flatbuffers::Offset<::flatbuffers::Vector<uint32_t>> constants = 0,
    ::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<::flatbuffers::String>>> type_annotations = 0,
    int32_t register_size = 0,
    ::flatbuffers::Offset<torch::jit::mobile::serialization::Schema> schema = 0,
    ::flatbuffers::Offset<torch::jit::mobile::serialization::DebugInfo> debug_info = 0,
    uint32_t class_type = 0) {
  FunctionBuilder builder_(_fbb);
  builder_.add_class_type(class_type);
  builder_.add_debug_info(debug_info);
  builder_.add_schema(schema);
  builder_.add_register_size(register_size);
  builder_.add_type_annotations(type_annotations);
  builder_.add_constants(constants);
  builder_.add_operators(operators);
  builder_.add_instructions(instructions);
  builder_.add_qn(qn);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<Function> CreateFunctionDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    const char *qn = nullptr,
    const std::vector<torch::jit::mobile::serialization::Instruction> *instructions = nullptr,
    const std::vector<::flatbuffers::Offset<torch::jit::mobile::serialization::Operator>> *operators = nullptr,
    const std::vector<uint32_t> *constants = nullptr,
    const std::vector<::flatbuffers::Offset<::flatbuffers::String>> *type_annotations = nullptr,
    int32_t register_size = 0,
    ::flatbuffers::Offset<torch::jit::mobile::serialization::Schema> schema = 0,
    ::flatbuffers::Offset<torch::jit::mobile::serialization::DebugInfo> debug_info = 0,
    uint32_t class_type = 0) {
  auto qn__ = qn ? _fbb.CreateString(qn) : 0;
  auto instructions__ = instructions ? _fbb.CreateVectorOfStructs<torch::jit::mobile::serialization::Instruction>(*instructions) : 0;
  auto operators__ = operators ? _fbb.CreateVector<::flatbuffers::Offset<torch::jit::mobile::serialization::Operator>>(*operators) : 0;
  auto constants__ = constants ? _fbb.CreateVector<uint32_t>(*constants) : 0;
  auto type_annotations__ = type_annotations ? _fbb.CreateVector<::flatbuffers::Offset<::flatbuffers::String>>(*type_annotations) : 0;
  return torch::jit::mobile::serialization::CreateFunction(
      _fbb,
      qn__,
      instructions__,
      operators__,
      constants__,
      type_annotations__,
      register_size,
      schema,
      debug_info,
      class_type);
}

struct StorageData FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef StorageDataBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_DATA = 4
  };
  const ::flatbuffers::Vector<uint8_t> *data() const {
    return GetPointer<const ::flatbuffers::Vector<uint8_t> *>(VT_DATA);
  }
  ::flatbuffers::Vector<uint8_t> *mutable_data() {
    return GetPointer<::flatbuffers::Vector<uint8_t> *>(VT_DATA);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_DATA) &&
           verifier.VerifyVector(data()) &&
           verifier.EndTable();
  }
};

struct StorageDataBuilder {
  typedef StorageData Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_data(::flatbuffers::Offset<::flatbuffers::Vector<uint8_t>> data) {
    fbb_.AddOffset(StorageData::VT_DATA, data);
  }
  explicit StorageDataBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<StorageData> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<StorageData>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<StorageData> CreateStorageData(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<::flatbuffers::Vector<uint8_t>> data = 0) {
  StorageDataBuilder builder_(_fbb);
  builder_.add_data(data);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<StorageData> CreateStorageDataDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    const std::vector<uint8_t> *data = nullptr) {
  if (data) { _fbb.ForceVectorAlignment(data->size(), sizeof(uint8_t), 16); }
  auto data__ = data ? _fbb.CreateVector<uint8_t>(*data) : 0;
  return torch::jit::mobile::serialization::CreateStorageData(
      _fbb,
      data__);
}

struct IValue FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef IValueBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_VAL_TYPE = 4,
    VT_VAL = 6
  };
  torch::jit::mobile::serialization::IValueUnion val_type() const {
    return static_cast<torch::jit::mobile::serialization::IValueUnion>(GetField<uint8_t>(VT_VAL_TYPE, 0));
  }
  const void *val() const {
    return GetPointer<const void *>(VT_VAL);
  }
  template<typename T> const T *val_as() const;
  const torch::jit::mobile::serialization::Int *val_as_Int() const {
    return val_type() == torch::jit::mobile::serialization::IValueUnion::Int ? static_cast<const torch::jit::mobile::serialization::Int *>(val()) : nullptr;
  }
  const torch::jit::mobile::serialization::Bool *val_as_Bool() const {
    return val_type() == torch::jit::mobile::serialization::IValueUnion::Bool ? static_cast<const torch::jit::mobile::serialization::Bool *>(val()) : nullptr;
  }
  const torch::jit::mobile::serialization::Double *val_as_Double() const {
    return val_type() == torch::jit::mobile::serialization::IValueUnion::Double ? static_cast<const torch::jit::mobile::serialization::Double *>(val()) : nullptr;
  }
  const torch::jit::mobile::serialization::ComplexDouble *val_as_ComplexDouble() const {
    return val_type() == torch::jit::mobile::serialization::IValueUnion::ComplexDouble ? static_cast<const torch::jit::mobile::serialization::ComplexDouble *>(val()) : nullptr;
  }
  const torch::jit::mobile::serialization::TensorMetadata *val_as_TensorMetadata() const {
    return val_type() == torch::jit::mobile::serialization::IValueUnion::TensorMetadata ? static_cast<const torch::jit::mobile::serialization::TensorMetadata *>(val()) : nullptr;
  }
  const torch::jit::mobile::serialization::String *val_as_String() const {
    return val_type() == torch::jit::mobile::serialization::IValueUnion::String ? static_cast<const torch::jit::mobile::serialization::String *>(val()) : nullptr;
  }
  const torch::jit::mobile::serialization::List *val_as_List() const {
    return val_type() == torch::jit::mobile::serialization::IValueUnion::List ? static_cast<const torch::jit::mobile::serialization::List *>(val()) : nullptr;
  }
  const torch::jit::mobile::serialization::Tuple *val_as_Tuple() const {
    return val_type() == torch::jit::mobile::serialization::IValueUnion::Tuple ? static_cast<const torch::jit::mobile::serialization::Tuple *>(val()) : nullptr;
  }
  const torch::jit::mobile::serialization::Dict *val_as_Dict() const {
    return val_type() == torch::jit::mobile::serialization::IValueUnion::Dict ? static_cast<const torch::jit::mobile::serialization::Dict *>(val()) : nullptr;
  }
  const torch::jit::mobile::serialization::Object *val_as_Object() const {
    return val_type() == torch::jit::mobile::serialization::IValueUnion::Object ? static_cast<const torch::jit::mobile::serialization::Object *>(val()) : nullptr;
  }
  const torch::jit::mobile::serialization::IntList *val_as_IntList() const {
    return val_type() == torch::jit::mobile::serialization::IValueUnion::IntList ? static_cast<const torch::jit::mobile::serialization::IntList *>(val()) : nullptr;
  }
  const torch::jit::mobile::serialization::DoubleList *val_as_DoubleList() const {
    return val_type() == torch::jit::mobile::serialization::IValueUnion::DoubleList ? static_cast<const torch::jit::mobile::serialization::DoubleList *>(val()) : nullptr;
  }
  const torch::jit::mobile::serialization::BoolList *val_as_BoolList() const {
    return val_type() == torch::jit::mobile::serialization::IValueUnion::BoolList ? static_cast<const torch::jit::mobile::serialization::BoolList *>(val()) : nullptr;
  }
  const torch::jit::mobile::serialization::Device *val_as_Device() const {
    return val_type() == torch::jit::mobile::serialization::IValueUnion::Device ? static_cast<const torch::jit::mobile::serialization::Device *>(val()) : nullptr;
  }
  const torch::jit::mobile::serialization::EnumValue *val_as_EnumValue() const {
    return val_type() == torch::jit::mobile::serialization::IValueUnion::EnumValue ? static_cast<const torch::jit::mobile::serialization::EnumValue *>(val()) : nullptr;
  }
  const torch::jit::mobile::serialization::Function *val_as_Function() const {
    return val_type() == torch::jit::mobile::serialization::IValueUnion::Function ? static_cast<const torch::jit::mobile::serialization::Function *>(val()) : nullptr;
  }
  void *mutable_val() {
    return GetPointer<void *>(VT_VAL);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<uint8_t>(verifier, VT_VAL_TYPE, 1) &&
           VerifyOffset(verifier, VT_VAL) &&
           VerifyIValueUnion(verifier, val(), val_type()) &&
           verifier.EndTable();
  }
};

template<> inline const torch::jit::mobile::serialization::Int *IValue::val_as<torch::jit::mobile::serialization::Int>() const {
  return val_as_Int();
}

template<> inline const torch::jit::mobile::serialization::Bool *IValue::val_as<torch::jit::mobile::serialization::Bool>() const {
  return val_as_Bool();
}

template<> inline const torch::jit::mobile::serialization::Double *IValue::val_as<torch::jit::mobile::serialization::Double>() const {
  return val_as_Double();
}

template<> inline const torch::jit::mobile::serialization::ComplexDouble *IValue::val_as<torch::jit::mobile::serialization::ComplexDouble>() const {
  return val_as_ComplexDouble();
}

template<> inline const torch::jit::mobile::serialization::TensorMetadata *IValue::val_as<torch::jit::mobile::serialization::TensorMetadata>() const {
  return val_as_TensorMetadata();
}

template<> inline const torch::jit::mobile::serialization::String *IValue::val_as<torch::jit::mobile::serialization::String>() const {
  return val_as_String();
}

template<> inline const torch::jit::mobile::serialization::List *IValue::val_as<torch::jit::mobile::serialization::List>() const {
  return val_as_List();
}

template<> inline const torch::jit::mobile::serialization::Tuple *IValue::val_as<torch::jit::mobile::serialization::Tuple>() const {
  return val_as_Tuple();
}

template<> inline const torch::jit::mobile::serialization::Dict *IValue::val_as<torch::jit::mobile::serialization::Dict>() const {
  return val_as_Dict();
}

template<> inline const torch::jit::mobile::serialization::Object *IValue::val_as<torch::jit::mobile::serialization::Object>() const {
  return val_as_Object();
}

template<> inline const torch::jit::mobile::serialization::IntList *IValue::val_as<torch::jit::mobile::serialization::IntList>() const {
  return val_as_IntList();
}

template<> inline const torch::jit::mobile::serialization::DoubleList *IValue::val_as<torch::jit::mobile::serialization::DoubleList>() const {
  return val_as_DoubleList();
}

template<> inline const torch::jit::mobile::serialization::BoolList *IValue::val_as<torch::jit::mobile::serialization::BoolList>() const {
  return val_as_BoolList();
}

template<> inline const torch::jit::mobile::serialization::Device *IValue::val_as<torch::jit::mobile::serialization::Device>() const {
  return val_as_Device();
}

template<> inline const torch::jit::mobile::serialization::EnumValue *IValue::val_as<torch::jit::mobile::serialization::EnumValue>() const {
  return val_as_EnumValue();
}

template<> inline const torch::jit::mobile::serialization::Function *IValue::val_as<torch::jit::mobile::serialization::Function>() const {
  return val_as_Function();
}

struct IValueBuilder {
  typedef IValue Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_val_type(torch::jit::mobile::serialization::IValueUnion val_type) {
    fbb_.AddElement<uint8_t>(IValue::VT_VAL_TYPE, static_cast<uint8_t>(val_type), 0);
  }
  void add_val(::flatbuffers::Offset<void> val) {
    fbb_.AddOffset(IValue::VT_VAL, val);
  }
  explicit IValueBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<IValue> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<IValue>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<IValue> CreateIValue(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    torch::jit::mobile::serialization::IValueUnion val_type = torch::jit::mobile::serialization::IValueUnion::NONE,
    ::flatbuffers::Offset<void> val = 0) {
  IValueBuilder builder_(_fbb);
  builder_.add_val(val);
  builder_.add_val_type(val_type);
  return builder_.Finish();
}

struct ExtraFile FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef ExtraFileBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_NAME = 4,
    VT_CONTENT = 6
  };
  const ::flatbuffers::String *name() const {
    return GetPointer<const ::flatbuffers::String *>(VT_NAME);
  }
  ::flatbuffers::String *mutable_name() {
    return GetPointer<::flatbuffers::String *>(VT_NAME);
  }
  const ::flatbuffers::String *content() const {
    return GetPointer<const ::flatbuffers::String *>(VT_CONTENT);
  }
  ::flatbuffers::String *mutable_content() {
    return GetPointer<::flatbuffers::String *>(VT_CONTENT);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_NAME) &&
           verifier.VerifyString(name()) &&
           VerifyOffset(verifier, VT_CONTENT) &&
           verifier.VerifyString(content()) &&
           verifier.EndTable();
  }
};

struct ExtraFileBuilder {
  typedef ExtraFile Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_name(::flatbuffers::Offset<::flatbuffers::String> name) {
    fbb_.AddOffset(ExtraFile::VT_NAME, name);
  }
  void add_content(::flatbuffers::Offset<::flatbuffers::String> content) {
    fbb_.AddOffset(ExtraFile::VT_CONTENT, content);
  }
  explicit ExtraFileBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<ExtraFile> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<ExtraFile>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<ExtraFile> CreateExtraFile(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<::flatbuffers::String> name = 0,
    ::flatbuffers::Offset<::flatbuffers::String> content = 0) {
  ExtraFileBuilder builder_(_fbb);
  builder_.add_content(content);
  builder_.add_name(name);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<ExtraFile> CreateExtraFileDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    const char *name = nullptr,
    const char *content = nullptr) {
  auto name__ = name ? _fbb.CreateString(name) : 0;
  auto content__ = content ? _fbb.CreateString(content) : 0;
  return torch::jit::mobile::serialization::CreateExtraFile(
      _fbb,
      name__,
      content__);
}

struct Module FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef ModuleBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_BYTECODE_VERSION = 4,
    VT_EXTRA_FILES = 6,
    VT_METHODS = 8,
    VT_STATE_OBJ = 10,
    VT_IVALUES = 12,
    VT_STORAGE_DATA_SIZE = 14,
    VT_STORAGE_DATA = 16,
    VT_OBJECT_TYPES = 18,
    VT_JIT_SOURCES = 20,
    VT_JIT_CONSTANTS = 22,
    VT_OPERATOR_VERSION = 24,
    VT_MOBILE_IVALUE_SIZE = 26
  };
  uint32_t bytecode_version() const {
    return GetField<uint32_t>(VT_BYTECODE_VERSION, 0);
  }
  bool mutate_bytecode_version(uint32_t _bytecode_version = 0) {
    return SetField<uint32_t>(VT_BYTECODE_VERSION, _bytecode_version, 0);
  }
  const ::flatbuffers::Vector<::flatbuffers::Offset<torch::jit::mobile::serialization::ExtraFile>> *extra_files() const {
    return GetPointer<const ::flatbuffers::Vector<::flatbuffers::Offset<torch::jit::mobile::serialization::ExtraFile>> *>(VT_EXTRA_FILES);
  }
  ::flatbuffers::Vector<::flatbuffers::Offset<torch::jit::mobile::serialization::ExtraFile>> *mutable_extra_files() {
    return GetPointer<::flatbuffers::Vector<::flatbuffers::Offset<torch::jit::mobile::serialization::ExtraFile>> *>(VT_EXTRA_FILES);
  }
  const ::flatbuffers::Vector<uint32_t> *methods() const {
    return GetPointer<const ::flatbuffers::Vector<uint32_t> *>(VT_METHODS);
  }
  ::flatbuffers::Vector<uint32_t> *mutable_methods() {
    return GetPointer<::flatbuffers::Vector<uint32_t> *>(VT_METHODS);
  }
  uint32_t state_obj() const {
    return GetField<uint32_t>(VT_STATE_OBJ, 0);
  }
  bool mutate_state_obj(uint32_t _state_obj = 0) {
    return SetField<uint32_t>(VT_STATE_OBJ, _state_obj, 0);
  }
  const ::flatbuffers::Vector<::flatbuffers::Offset<torch::jit::mobile::serialization::IValue>> *ivalues() const {
    return GetPointer<const ::flatbuffers::Vector<::flatbuffers::Offset<torch::jit::mobile::serialization::IValue>> *>(VT_IVALUES);
  }
  ::flatbuffers::Vector<::flatbuffers::Offset<torch::jit::mobile::serialization::IValue>> *mutable_ivalues() {
    return GetPointer<::flatbuffers::Vector<::flatbuffers::Offset<torch::jit::mobile::serialization::IValue>> *>(VT_IVALUES);
  }
  int32_t storage_data_size() const {
    return GetField<int32_t>(VT_STORAGE_DATA_SIZE, 0);
  }
  bool mutate_storage_data_size(int32_t _storage_data_size = 0) {
    return SetField<int32_t>(VT_STORAGE_DATA_SIZE, _storage_data_size, 0);
  }
  const ::flatbuffers::Vector<::flatbuffers::Offset<torch::jit::mobile::serialization::StorageData>> *storage_data() const {
    return GetPointer<const ::flatbuffers::Vector<::flatbuffers::Offset<torch::jit::mobile::serialization::StorageData>> *>(VT_STORAGE_DATA);
  }
  ::flatbuffers::Vector<::flatbuffers::Offset<torch::jit::mobile::serialization::StorageData>> *mutable_storage_data() {
    return GetPointer<::flatbuffers::Vector<::flatbuffers::Offset<torch::jit::mobile::serialization::StorageData>> *>(VT_STORAGE_DATA);
  }
  const ::flatbuffers::Vector<::flatbuffers::Offset<torch::jit::mobile::serialization::ObjectType>> *object_types() const {
    return GetPointer<const ::flatbuffers::Vector<::flatbuffers::Offset<torch::jit::mobile::serialization::ObjectType>> *>(VT_OBJECT_TYPES);
  }
  ::flatbuffers::Vector<::flatbuffers::Offset<torch::jit::mobile::serialization::ObjectType>> *mutable_object_types() {
    return GetPointer<::flatbuffers::Vector<::flatbuffers::Offset<torch::jit::mobile::serialization::ObjectType>> *>(VT_OBJECT_TYPES);
  }
  const ::flatbuffers::Vector<::flatbuffers::Offset<torch::jit::mobile::serialization::ExtraFile>> *jit_sources() const {
    return GetPointer<const ::flatbuffers::Vector<::flatbuffers::Offset<torch::jit::mobile::serialization::ExtraFile>> *>(VT_JIT_SOURCES);
  }
  ::flatbuffers::Vector<::flatbuffers::Offset<torch::jit::mobile::serialization::ExtraFile>> *mutable_jit_sources() {
    return GetPointer<::flatbuffers::Vector<::flatbuffers::Offset<torch::jit::mobile::serialization::ExtraFile>> *>(VT_JIT_SOURCES);
  }
  const ::flatbuffers::Vector<uint32_t> *jit_constants() const {
    return GetPointer<const ::flatbuffers::Vector<uint32_t> *>(VT_JIT_CONSTANTS);
  }
  ::flatbuffers::Vector<uint32_t> *mutable_jit_constants() {
    return GetPointer<::flatbuffers::Vector<uint32_t> *>(VT_JIT_CONSTANTS);
  }
  uint32_t operator_version() const {
    return GetField<uint32_t>(VT_OPERATOR_VERSION, 0);
  }
  bool mutate_operator_version(uint32_t _operator_version = 0) {
    return SetField<uint32_t>(VT_OPERATOR_VERSION, _operator_version, 0);
  }
  uint32_t mobile_ivalue_size() const {
    return GetField<uint32_t>(VT_MOBILE_IVALUE_SIZE, 0);
  }
  bool mutate_mobile_ivalue_size(uint32_t _mobile_ivalue_size = 0) {
    return SetField<uint32_t>(VT_MOBILE_IVALUE_SIZE, _mobile_ivalue_size, 0);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<uint32_t>(verifier, VT_BYTECODE_VERSION, 4) &&
           VerifyOffset(verifier, VT_EXTRA_FILES) &&
           verifier.VerifyVector(extra_files()) &&
           verifier.VerifyVectorOfTables(extra_files()) &&
           VerifyOffset(verifier, VT_METHODS) &&
           verifier.VerifyVector(methods()) &&
           VerifyField<uint32_t>(verifier, VT_STATE_OBJ, 4) &&
           VerifyOffset(verifier, VT_IVALUES) &&
           verifier.VerifyVector(ivalues()) &&
           verifier.VerifyVectorOfTables(ivalues()) &&
           VerifyField<int32_t>(verifier, VT_STORAGE_DATA_SIZE, 4) &&
           VerifyOffset(verifier, VT_STORAGE_DATA) &&
           verifier.VerifyVector(storage_data()) &&
           verifier.VerifyVectorOfTables(storage_data()) &&
           VerifyOffset(verifier, VT_OBJECT_TYPES) &&
           verifier.VerifyVector(object_types()) &&
           verifier.VerifyVectorOfTables(object_types()) &&
           VerifyOffset(verifier, VT_JIT_SOURCES) &&
           verifier.VerifyVector(jit_sources()) &&
           verifier.VerifyVectorOfTables(jit_sources()) &&
           VerifyOffset(verifier, VT_JIT_CONSTANTS) &&
           verifier.VerifyVector(jit_constants()) &&
           VerifyField<uint32_t>(verifier, VT_OPERATOR_VERSION, 4) &&
           VerifyField<uint32_t>(verifier, VT_MOBILE_IVALUE_SIZE, 4) &&
           verifier.EndTable();
  }
};

struct ModuleBuilder {
  typedef Module Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_bytecode_version(uint32_t bytecode_version) {
    fbb_.AddElement<uint32_t>(Module::VT_BYTECODE_VERSION, bytecode_version, 0);
  }
  void add_extra_files(::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<torch::jit::mobile::serialization::ExtraFile>>> extra_files) {
    fbb_.AddOffset(Module::VT_EXTRA_FILES, extra_files);
  }
  void add_methods(::flatbuffers::Offset<::flatbuffers::Vector<uint32_t>> methods) {
    fbb_.AddOffset(Module::VT_METHODS, methods);
  }
  void add_state_obj(uint32_t state_obj) {
    fbb_.AddElement<uint32_t>(Module::VT_STATE_OBJ, state_obj, 0);
  }
  void add_ivalues(::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<torch::jit::mobile::serialization::IValue>>> ivalues) {
    fbb_.AddOffset(Module::VT_IVALUES, ivalues);
  }
  void add_storage_data_size(int32_t storage_data_size) {
    fbb_.AddElement<int32_t>(Module::VT_STORAGE_DATA_SIZE, storage_data_size, 0);
  }
  void add_storage_data(::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<torch::jit::mobile::serialization::StorageData>>> storage_data) {
    fbb_.AddOffset(Module::VT_STORAGE_DATA, storage_data);
  }
  void add_object_types(::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<torch::jit::mobile::serialization::ObjectType>>> object_types) {
    fbb_.AddOffset(Module::VT_OBJECT_TYPES, object_types);
  }
  void add_jit_sources(::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<torch::jit::mobile::serialization::ExtraFile>>> jit_sources) {
    fbb_.AddOffset(Module::VT_JIT_SOURCES, jit_sources);
  }
  void add_jit_constants(::flatbuffers::Offset<::flatbuffers::Vector<uint32_t>> jit_constants) {
    fbb_.AddOffset(Module::VT_JIT_CONSTANTS, jit_constants);
  }
  void add_operator_version(uint32_t operator_version) {
    fbb_.AddElement<uint32_t>(Module::VT_OPERATOR_VERSION, operator_version, 0);
  }
  void add_mobile_ivalue_size(uint32_t mobile_ivalue_size) {
    fbb_.AddElement<uint32_t>(Module::VT_MOBILE_IVALUE_SIZE, mobile_ivalue_size, 0);
  }
  explicit ModuleBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<Module> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<Module>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<Module> CreateModule(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    uint32_t bytecode_version = 0,
    ::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<torch::jit::mobile::serialization::ExtraFile>>> extra_files = 0,
    ::flatbuffers::Offset<::flatbuffers::Vector<uint32_t>> methods = 0,
    uint32_t state_obj = 0,
    ::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<torch::jit::mobile::serialization::IValue>>> ivalues = 0,
    int32_t storage_data_size = 0,
    ::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<torch::jit::mobile::serialization::StorageData>>> storage_data = 0,
    ::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<torch::jit::mobile::serialization::ObjectType>>> object_types = 0,
    ::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<torch::jit::mobile::serialization::ExtraFile>>> jit_sources = 0,
    ::flatbuffers::Offset<::flatbuffers::Vector<uint32_t>> jit_constants = 0,
    uint32_t operator_version = 0,
    uint32_t mobile_ivalue_size = 0) {
  ModuleBuilder builder_(_fbb);
  builder_.add_mobile_ivalue_size(mobile_ivalue_size);
  builder_.add_operator_version(operator_version);
  builder_.add_jit_constants(jit_constants);
  builder_.add_jit_sources(jit_sources);
  builder_.add_object_types(object_types);
  builder_.add_storage_data(storage_data);
  builder_.add_storage_data_size(storage_data_size);
  builder_.add_ivalues(ivalues);
  builder_.add_state_obj(state_obj);
  builder_.add_methods(methods);
  builder_.add_extra_files(extra_files);
  builder_.add_bytecode_version(bytecode_version);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<Module> CreateModuleDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    uint32_t bytecode_version = 0,
    const std::vector<::flatbuffers::Offset<torch::jit::mobile::serialization::ExtraFile>> *extra_files = nullptr,
    const std::vector<uint32_t> *methods = nullptr,
    uint32_t state_obj = 0,
    const std::vector<::flatbuffers::Offset<torch::jit::mobile::serialization::IValue>> *ivalues = nullptr,
    int32_t storage_data_size = 0,
    const std::vector<::flatbuffers::Offset<torch::jit::mobile::serialization::StorageData>> *storage_data = nullptr,
    const std::vector<::flatbuffers::Offset<torch::jit::mobile::serialization::ObjectType>> *object_types = nullptr,
    const std::vector<::flatbuffers::Offset<torch::jit::mobile::serialization::ExtraFile>> *jit_sources = nullptr,
    const std::vector<uint32_t> *jit_constants = nullptr,
    uint32_t operator_version = 0,
    uint32_t mobile_ivalue_size = 0) {
  auto extra_files__ = extra_files ? _fbb.CreateVector<::flatbuffers::Offset<torch::jit::mobile::serialization::ExtraFile>>(*extra_files) : 0;
  auto methods__ = methods ? _fbb.CreateVector<uint32_t>(*methods) : 0;
  auto ivalues__ = ivalues ? _fbb.CreateVector<::flatbuffers::Offset<torch::jit::mobile::serialization::IValue>>(*ivalues) : 0;
  auto storage_data__ = storage_data ? _fbb.CreateVector<::flatbuffers::Offset<torch::jit::mobile::serialization::StorageData>>(*storage_data) : 0;
  auto object_types__ = object_types ? _fbb.CreateVector<::flatbuffers::Offset<torch::jit::mobile::serialization::ObjectType>>(*object_types) : 0;
  auto jit_sources__ = jit_sources ? _fbb.CreateVector<::flatbuffers::Offset<torch::jit::mobile::serialization::ExtraFile>>(*jit_sources) : 0;
  auto jit_constants__ = jit_constants ? _fbb.CreateVector<uint32_t>(*jit_constants) : 0;
  return torch::jit::mobile::serialization::CreateModule(
      _fbb,
      bytecode_version,
      extra_files__,
      methods__,
      state_obj,
      ivalues__,
      storage_data_size,
      storage_data__,
      object_types__,
      jit_sources__,
      jit_constants__,
      operator_version,
      mobile_ivalue_size);
}

inline bool VerifyIValueUnion(::flatbuffers::Verifier &verifier, const void *obj, IValueUnion type) {
  switch (type) {
    case IValueUnion::NONE: {
      return true;
    }
    case IValueUnion::Int: {
      return verifier.VerifyField<torch::jit::mobile::serialization::Int>(static_cast<const uint8_t *>(obj), 0, 8);
    }
    case IValueUnion::Bool: {
      return verifier.VerifyField<torch::jit::mobile::serialization::Bool>(static_cast<const uint8_t *>(obj), 0, 1);
    }
    case IValueUnion::Double: {
      return verifier.VerifyField<torch::jit::mobile::serialization::Double>(static_cast<const uint8_t *>(obj), 0, 8);
    }
    case IValueUnion::ComplexDouble: {
      return verifier.VerifyField<torch::jit::mobile::serialization::ComplexDouble>(static_cast<const uint8_t *>(obj), 0, 8);
    }
    case IValueUnion::TensorMetadata: {
      auto ptr = reinterpret_cast<const torch::jit::mobile::serialization::TensorMetadata *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case IValueUnion::String: {
      auto ptr = reinterpret_cast<const torch::jit::mobile::serialization::String *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case IValueUnion::List: {
      auto ptr = reinterpret_cast<const torch::jit::mobile::serialization::List *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case IValueUnion::Tuple: {
      auto ptr = reinterpret_cast<const torch::jit::mobile::serialization::Tuple *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case IValueUnion::Dict: {
      auto ptr = reinterpret_cast<const torch::jit::mobile::serialization::Dict *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case IValueUnion::Object: {
      auto ptr = reinterpret_cast<const torch::jit::mobile::serialization::Object *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case IValueUnion::IntList: {
      auto ptr = reinterpret_cast<const torch::jit::mobile::serialization::IntList *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case IValueUnion::DoubleList: {
      auto ptr = reinterpret_cast<const torch::jit::mobile::serialization::DoubleList *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case IValueUnion::BoolList: {
      auto ptr = reinterpret_cast<const torch::jit::mobile::serialization::BoolList *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case IValueUnion::Device: {
      auto ptr = reinterpret_cast<const torch::jit::mobile::serialization::Device *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case IValueUnion::EnumValue: {
      auto ptr = reinterpret_cast<const torch::jit::mobile::serialization::EnumValue *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case IValueUnion::Function: {
      auto ptr = reinterpret_cast<const torch::jit::mobile::serialization::Function *>(obj);
      return verifier.VerifyTable(ptr);
    }
    default: return true;
  }
}

inline bool VerifyIValueUnionVector(::flatbuffers::Verifier &verifier, const ::flatbuffers::Vector<::flatbuffers::Offset<void>> *values, const ::flatbuffers::Vector<IValueUnion> *types) {
  if (!values || !types) return !values && !types;
  if (values->size() != types->size()) return false;
  for (::flatbuffers::uoffset_t i = 0; i < values->size(); ++i) {
    if (!VerifyIValueUnion(
        verifier,  values->Get(i), types->GetEnum<IValueUnion>(i))) {
      return false;
    }
  }
  return true;
}

inline const torch::jit::mobile::serialization::Module *GetModule(const void *buf) {
  return ::flatbuffers::GetRoot<torch::jit::mobile::serialization::Module>(buf);
}

inline const torch::jit::mobile::serialization::Module *GetSizePrefixedModule(const void *buf) {
  return ::flatbuffers::GetSizePrefixedRoot<torch::jit::mobile::serialization::Module>(buf);
}

inline Module *GetMutableModule(void *buf) {
  return ::flatbuffers::GetMutableRoot<Module>(buf);
}

inline torch::jit::mobile::serialization::Module *GetMutableSizePrefixedModule(void *buf) {
  return ::flatbuffers::GetMutableSizePrefixedRoot<torch::jit::mobile::serialization::Module>(buf);
}

inline const char *ModuleIdentifier() {
  return "PTMF";
}

inline bool ModuleBufferHasIdentifier(const void *buf) {
  return ::flatbuffers::BufferHasIdentifier(
      buf, ModuleIdentifier());
}

inline bool SizePrefixedModuleBufferHasIdentifier(const void *buf) {
  return ::flatbuffers::BufferHasIdentifier(
      buf, ModuleIdentifier(), true);
}

inline bool VerifyModuleBuffer(
    ::flatbuffers::Verifier &verifier) {
  return verifier.VerifyBuffer<torch::jit::mobile::serialization::Module>(ModuleIdentifier());
}

inline bool VerifySizePrefixedModuleBuffer(
    ::flatbuffers::Verifier &verifier) {
  return verifier.VerifySizePrefixedBuffer<torch::jit::mobile::serialization::Module>(ModuleIdentifier());
}

inline void FinishModuleBuffer(
    ::flatbuffers::FlatBufferBuilder &fbb,
    ::flatbuffers::Offset<torch::jit::mobile::serialization::Module> root) {
  fbb.Finish(root, ModuleIdentifier());
}

inline void FinishSizePrefixedModuleBuffer(
    ::flatbuffers::FlatBufferBuilder &fbb,
    ::flatbuffers::Offset<torch::jit::mobile::serialization::Module> root) {
  fbb.FinishSizePrefixed(root, ModuleIdentifier());
}

}  // namespace serialization
}  // namespace mobile
}  // namespace jit
}  // namespace torch

#endif  // FLATBUFFERS_GENERATED_MOBILEBYTECODE_TORCH_JIT_MOBILE_SERIALIZATION_H_
