'use client';

import { useEffect, useState, Suspense } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import { useAuth } from '@/providers/auth-provider';
import { Loader2 } from 'lucide-react';

interface RouteGuardProps {
  children: React.ReactNode;
}

// Liste des chemins publics qui ne nécessitent pas d'authentification
const publicPaths = ['/', '/login', '/about'];

// Composant de chargement pour l'authentification
function AuthLoader() {
  return (
    <div className="flex min-h-screen flex-col items-center justify-center">
      <Loader2 className="h-8 w-8 animate-spin text-primary mb-4" />
      <p className="text-muted-foreground">Vérification de l'authentification...</p>
    </div>
  );
}

// Composant de redirection
function RedirectLoader() {
  return (
    <div className="flex min-h-screen flex-col items-center justify-center">
      <Loader2 className="h-8 w-8 animate-spin text-primary mb-4" />
      <p className="text-muted-foreground">Redirection vers la page de connexion...</p>
    </div>
  );
}

// Composant principal de garde de route
const RouteGuard: React.FC<RouteGuardProps> = ({ children }) => {
  const { isAuthenticated, isLoading } = useAuth();
  const router = useRouter();
  const pathname = usePathname();
  const [authState, setAuthState] = useState({
    authorized: false,
    checking: true
  });

  useEffect(() => {
    const checkAuth = () => {
      const isPublicPath = publicPaths.some(path => 
        pathname === path || pathname.startsWith(`${path}/`)
      );

      if (isPublicPath) {
        setAuthState({ authorized: true, checking: false });
      } else if (!isAuthenticated && !isLoading) {
        setAuthState({ authorized: false, checking: false });
        router.push('/login');
      } else {
        setAuthState({ authorized: true, checking: false });
      }
    };

    checkAuth();
  }, [isAuthenticated, isLoading, pathname, router]);

  const renderContent = () => {
    if (authState.checking || isLoading) {
      return <AuthLoader />;
    }
    
    if (!authState.authorized) {
      return <RedirectLoader />;
    }
    
    return children;
  };

  return <>{renderContent()}</>;
};

export default RouteGuard;
