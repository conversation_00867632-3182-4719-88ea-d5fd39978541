"""
Validation Utilities for Auto-Grade Scribe
Provides comprehensive input validation and sanitization
"""

import re
import logging
from typing import Dict, Any, List, Optional, Union
from email_validator import validate_email, EmailNotValidError

logger = logging.getLogger("auto-grade-scribe.validation")

class ValidationUtils:
    """Utility class for input validation and sanitization"""
    
    def __init__(self):
        # Validation patterns
        self.patterns = {
            "username": re.compile(r"^[a-zA-Z0-9_]{3,30}$"),
            "student_id": re.compile(r"^[a-zA-Z0-9\-_]{1,50}$"),
            "phone": re.compile(r"^[\+]?[1-9][\d]{0,15}$"),
            "academic_year": re.compile(r"^\d{4}-\d{4}$"),
            "grade": re.compile(r"^[A-F][+-]?$"),
            "percentage": re.compile(r"^(100|[0-9]{1,2})(\.\d{1,2})?$")
        }
        
        # Allowed HTML tags for rich text (if needed)
        self.allowed_html_tags = {
            'b', 'i', 'u', 'strong', 'em', 'p', 'br', 'ul', 'ol', 'li'
        }
        
        logger.info("Validation Utils initialized")
    
    def validate_exam_file(self, file_data: bytes, filename: str) -> Dict[str, Any]:
        """
        Validate exam file upload
        
        Args:
            file_data: Binary file data
            filename: Original filename
            
        Returns:
            Validation result
        """
        try:
            from utils.file_utils import file_utils
            
            # Use file utils for validation
            result = file_utils.validate_file(file_data, filename)
            
            # Additional exam-specific validation
            if result["valid"]:
                file_info = result["file_info"]
                
                # Check if image is suitable for OCR
                if file_info.get("mime_type", "").startswith("image/"):
                    width = file_info.get("width", 0)
                    height = file_info.get("height", 0)
                    
                    # Minimum resolution for good OCR
                    if width < 300 or height < 300:
                        result["warnings"] = result.get("warnings", [])
                        result["warnings"].append("Low resolution image may result in poor OCR quality")
                    
                    # Check if image is too large (processing time)
                    if width * height > 25000000:  # 25 megapixels
                        result["warnings"] = result.get("warnings", [])
                        result["warnings"].append("Very large image may take longer to process")
            
            return result
            
        except Exception as e:
            logger.error(f"Error validating exam file: {str(e)}")
            return {
                "valid": False,
                "error": f"File validation error: {str(e)}"
            }
    
    def validate_user_data(self, user_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Validate user registration/update data
        
        Args:
            user_data: User data dictionary
            
        Returns:
            Validation result with errors
        """
        errors = {}
        warnings = []
        
        try:
            # Validate username
            username = user_data.get("username", "").strip()
            if not username:
                errors["username"] = "Username is required"
            elif not self.patterns["username"].match(username):
                errors["username"] = "Username must be 3-30 characters, alphanumeric and underscore only"
            
            # Validate email
            email = user_data.get("email", "").strip()
            if not email:
                errors["email"] = "Email is required"
            else:
                try:
                    validated_email = validate_email(email)
                    user_data["email"] = validated_email.email  # Normalized email
                except EmailNotValidError as e:
                    errors["email"] = str(e)
            
            # Validate full name
            full_name = user_data.get("full_name", "").strip()
            if full_name and len(full_name) > 255:
                errors["full_name"] = "Full name too long (max 255 characters)"
            elif full_name and not re.match(r"^[a-zA-ZÀ-ÖØ-öø-ÿ\s\-\'\.]+$", full_name):
                errors["full_name"] = "Full name contains invalid characters"
            
            # Validate phone
            phone = user_data.get("phone", "").strip()
            if phone and not self.patterns["phone"].match(phone):
                errors["phone"] = "Invalid phone number format"
            
            # Validate department
            department = user_data.get("department", "").strip()
            if department and len(department) > 100:
                errors["department"] = "Department name too long (max 100 characters)"
            
            # Validate institution
            institution = user_data.get("institution", "").strip()
            if institution and len(institution) > 255:
                errors["institution"] = "Institution name too long (max 255 characters)"
            
            # Validate role
            role = user_data.get("role", "").strip().lower()
            valid_roles = ["admin", "teacher", "student", "user"]
            if role and role not in valid_roles:
                errors["role"] = f"Invalid role. Must be one of: {', '.join(valid_roles)}"
            
            return {
                "valid": len(errors) == 0,
                "errors": errors,
                "warnings": warnings,
                "sanitized_data": user_data
            }
            
        except Exception as e:
            logger.error(f"Error validating user data: {str(e)}")
            return {
                "valid": False,
                "errors": {"general": f"Validation error: {str(e)}"},
                "warnings": []
            }
    
    def validate_student_data(self, student_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Validate student data
        
        Args:
            student_data: Student data dictionary
            
        Returns:
            Validation result
        """
        errors = {}
        warnings = []
        
        try:
            # Validate student ID
            student_id = student_data.get("student_id", "").strip()
            if not student_id:
                errors["student_id"] = "Student ID is required"
            elif not self.patterns["student_id"].match(student_id):
                errors["student_id"] = "Invalid student ID format"
            
            # Validate first name
            first_name = student_data.get("first_name", "").strip()
            if not first_name:
                errors["first_name"] = "First name is required"
            elif len(first_name) > 100:
                errors["first_name"] = "First name too long (max 100 characters)"
            elif not re.match(r"^[a-zA-ZÀ-ÖØ-öø-ÿ\s\-\'\.]+$", first_name):
                errors["first_name"] = "First name contains invalid characters"
            
            # Validate last name
            last_name = student_data.get("last_name", "").strip()
            if not last_name:
                errors["last_name"] = "Last name is required"
            elif len(last_name) > 100:
                errors["last_name"] = "Last name too long (max 100 characters)"
            elif not re.match(r"^[a-zA-ZÀ-ÖØ-öø-ÿ\s\-\'\.]+$", last_name):
                errors["last_name"] = "Last name contains invalid characters"
            
            # Validate email (optional)
            email = student_data.get("email", "").strip()
            if email:
                try:
                    validated_email = validate_email(email)
                    student_data["email"] = validated_email.email
                except EmailNotValidError as e:
                    errors["email"] = str(e)
            
            # Validate phone (optional)
            phone = student_data.get("phone", "").strip()
            if phone and not self.patterns["phone"].match(phone):
                errors["phone"] = "Invalid phone number format"
            
            # Validate class name
            class_name = student_data.get("class_name", "").strip()
            if class_name and len(class_name) > 100:
                errors["class_name"] = "Class name too long (max 100 characters)"
            
            # Validate academic year
            academic_year = student_data.get("academic_year", "").strip()
            if academic_year and not self.patterns["academic_year"].match(academic_year):
                errors["academic_year"] = "Academic year must be in format YYYY-YYYY (e.g., 2023-2024)"
            
            # Validate department
            department = student_data.get("department", "").strip()
            if department and len(department) > 100:
                errors["department"] = "Department name too long (max 100 characters)"
            
            return {
                "valid": len(errors) == 0,
                "errors": errors,
                "warnings": warnings,
                "sanitized_data": student_data
            }
            
        except Exception as e:
            logger.error(f"Error validating student data: {str(e)}")
            return {
                "valid": False,
                "errors": {"general": f"Validation error: {str(e)}"},
                "warnings": []
            }
    
    def validate_exam_data(self, exam_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Validate exam metadata
        
        Args:
            exam_data: Exam data dictionary
            
        Returns:
            Validation result
        """
        errors = {}
        warnings = []
        
        try:
            # Validate exam name
            exam_name = exam_data.get("exam_name", "").strip()
            if exam_name and len(exam_name) > 255:
                errors["exam_name"] = "Exam name too long (max 255 characters)"
            
            # Validate subject
            subject = exam_data.get("subject", "").strip()
            if subject and len(subject) > 100:
                errors["subject"] = "Subject name too long (max 100 characters)"
            
            # Validate class name
            class_name = exam_data.get("class_name", "").strip()
            if class_name and len(class_name) > 100:
                errors["class_name"] = "Class name too long (max 100 characters)"
            
            # Validate academic year
            academic_year = exam_data.get("academic_year", "").strip()
            if academic_year and not self.patterns["academic_year"].match(academic_year):
                errors["academic_year"] = "Academic year must be in format YYYY-YYYY"
            
            # Validate exam type
            exam_type = exam_data.get("exam_type", "").strip().lower()
            valid_types = ["qcm", "handwritten", "mixed", "essay"]
            if exam_type and exam_type not in valid_types:
                errors["exam_type"] = f"Invalid exam type. Must be one of: {', '.join(valid_types)}"
            
            return {
                "valid": len(errors) == 0,
                "errors": errors,
                "warnings": warnings,
                "sanitized_data": exam_data
            }
            
        except Exception as e:
            logger.error(f"Error validating exam data: {str(e)}")
            return {
                "valid": False,
                "errors": {"general": f"Validation error: {str(e)}"},
                "warnings": []
            }
    
    def validate_grading_data(self, grading_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Validate grading configuration and answers
        
        Args:
            grading_data: Grading data dictionary
            
        Returns:
            Validation result
        """
        errors = {}
        warnings = []
        
        try:
            # Validate correct answers
            correct_answers = grading_data.get("correct_answers", {})
            if not correct_answers:
                errors["correct_answers"] = "Correct answers are required"
            else:
                for q_num, answer in correct_answers.items():
                    # Validate question number
                    try:
                        int(q_num)
                    except ValueError:
                        errors[f"question_{q_num}"] = "Question number must be numeric"
                    
                    # Validate answer format
                    if not isinstance(answer, str) or not answer.strip():
                        errors[f"answer_{q_num}"] = "Answer cannot be empty"
                    elif len(answer.strip()) > 10:
                        errors[f"answer_{q_num}"] = "Answer too long (max 10 characters)"
            
            # Validate grading scale
            grade_scale = grading_data.get("grade_scale", "standard")
            valid_scales = ["standard", "strict", "lenient"]
            if grade_scale not in valid_scales:
                errors["grade_scale"] = f"Invalid grade scale. Must be one of: {', '.join(valid_scales)}"
            
            # Validate passing threshold
            passing_threshold = grading_data.get("passing_threshold")
            if passing_threshold is not None:
                try:
                    threshold = float(passing_threshold)
                    if threshold < 0 or threshold > 100:
                        errors["passing_threshold"] = "Passing threshold must be between 0 and 100"
                except (ValueError, TypeError):
                    errors["passing_threshold"] = "Passing threshold must be a number"
            
            return {
                "valid": len(errors) == 0,
                "errors": errors,
                "warnings": warnings,
                "sanitized_data": grading_data
            }
            
        except Exception as e:
            logger.error(f"Error validating grading data: {str(e)}")
            return {
                "valid": False,
                "errors": {"general": f"Validation error: {str(e)}"},
                "warnings": []
            }
    
    def sanitize_text(self, text: str, max_length: Optional[int] = None) -> str:
        """
        Sanitize text input
        
        Args:
            text: Input text
            max_length: Maximum allowed length
            
        Returns:
            Sanitized text
        """
        try:
            if not isinstance(text, str):
                text = str(text)
            
            # Strip whitespace
            text = text.strip()
            
            # Remove null bytes
            text = text.replace('\x00', '')
            
            # Limit length
            if max_length and len(text) > max_length:
                text = text[:max_length]
            
            return text
            
        except Exception as e:
            logger.error(f"Error sanitizing text: {str(e)}")
            return ""
    
    def validate_pagination(self, skip: int, limit: int) -> Dict[str, Any]:
        """
        Validate pagination parameters
        
        Args:
            skip: Number of records to skip
            limit: Maximum number of records to return
            
        Returns:
            Validation result with sanitized values
        """
        errors = {}
        
        # Validate skip
        if skip < 0:
            errors["skip"] = "Skip must be non-negative"
            skip = 0
        elif skip > 10000:
            errors["skip"] = "Skip too large (max 10000)"
            skip = 10000
        
        # Validate limit
        if limit <= 0:
            errors["limit"] = "Limit must be positive"
            limit = 10
        elif limit > 1000:
            errors["limit"] = "Limit too large (max 1000)"
            limit = 1000
        
        return {
            "valid": len(errors) == 0,
            "errors": errors,
            "skip": skip,
            "limit": limit
        }

# Create singleton instance
validation_utils = ValidationUtils()
