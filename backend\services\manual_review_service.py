"""
Service de Révision Manuelle pour Auto-Grade Scribe
Permet aux enseignants de réviser et ajuster les corrections automatiques
"""

import os
import logging
import asyncio
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timezone
import json

logger = logging.getLogger("auto-grade-scribe.manual-review")

class ManualReviewService:
    """Service de révision manuelle pour les enseignants"""
    
    def __init__(self, database_service):
        self.db_service = database_service
        self.confidence_threshold = 0.7  # Seuil pour révision automatique
        logger.info("Manual Review Service initialized")
    
    async def get_review_dashboard(
        self, 
        teacher_id: int,
        filters: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Tableau de bord pour la révision manuelle
        """
        try:
            # Récupérer les examens nécessitant une révision
            pending_reviews = await self.db_service.get_exams_requiring_review(
                limit=50, offset=0
            )
            
            # Statistiques de révision
            stats = await self._get_review_statistics(teacher_id)
            
            # Examens récemment révisés
            recent_reviews = await self._get_recent_reviews(teacher_id, limit=10)
            
            return {
                'success': True,
                'dashboard': {
                    'pending_reviews': pending_reviews,
                    'statistics': stats,
                    'recent_reviews': recent_reviews,
                    'teacher_id': teacher_id,
                    'timestamp': datetime.now().isoformat()
                }
            }
            
        except Exception as e:
            logger.error(f"Error getting review dashboard: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }
    
    async def get_exam_for_review(
        self, 
        exam_id: str, 
        teacher_id: int
    ) -> Dict[str, Any]:
        """
        Récupérer un examen pour révision détaillée
        """
        try:
            # Récupérer les résultats détaillés
            exam_details = await self.db_service.get_exam_results_detailed(
                exam_id, include_history=True
            )
            
            if not exam_details['success']:
                return exam_details
            
            # Ajouter des informations spécifiques à la révision
            review_data = await self._prepare_review_data(exam_details)
            
            # Suggestions d'amélioration basées sur l'IA
            suggestions = await self._generate_review_suggestions(exam_details)
            
            return {
                'success': True,
                'exam_id': exam_id,
                'exam_details': exam_details,
                'review_data': review_data,
                'suggestions': suggestions,
                'teacher_id': teacher_id
            }
            
        except Exception as e:
            logger.error(f"Error getting exam for review: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }
    
    async def submit_manual_review(
        self,
        exam_id: str,
        teacher_id: int,
        review_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Soumettre une révision manuelle
        """
        try:
            # Valider les données de révision
            validation = self._validate_review_data(review_data)
            if not validation['valid']:
                return {
                    'success': False,
                    'error': 'Invalid review data',
                    'details': validation['errors']
                }
            
            # Calculer le nouveau score
            new_score_data = await self._calculate_adjusted_score(review_data)
            
            # Sauvegarder la révision
            save_result = await self.db_service.update_manual_review(
                exam_id=exam_id,
                reviewer_id=teacher_id,
                manual_score=new_score_data['total_score'],
                manual_grade=new_score_data['letter_grade'],
                review_comments=review_data.get('overall_comments', ''),
                question_adjustments=review_data.get('question_adjustments', {})
            )
            
            if not save_result['success']:
                return save_result
            
            # Générer un rapport de révision
            review_report = await self._generate_review_report(
                exam_id, review_data, new_score_data, save_result
            )
            
            return {
                'success': True,
                'exam_id': exam_id,
                'teacher_id': teacher_id,
                'review_result': save_result,
                'new_score': new_score_data,
                'review_report': review_report
            }
            
        except Exception as e:
            logger.error(f"Error submitting manual review: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }
    
    async def _get_review_statistics(self, teacher_id: int) -> Dict[str, Any]:
        """Calculer les statistiques de révision"""
        try:
            # Ici on ferait des requêtes à la base de données
            # Pour l'instant, simulation
            return {
                'total_reviews_pending': 15,
                'reviews_completed_today': 3,
                'reviews_completed_this_week': 12,
                'average_confidence_improvement': 0.15,
                'most_common_adjustments': [
                    {'type': 'partial_credit', 'count': 8},
                    {'type': 'ocr_correction', 'count': 5},
                    {'type': 'interpretation_adjustment', 'count': 3}
                ]
            }
            
        except Exception as e:
            logger.error(f"Error getting review statistics: {str(e)}")
            return {}
    
    async def _get_recent_reviews(self, teacher_id: int, limit: int = 10) -> List[Dict[str, Any]]:
        """Récupérer les révisions récentes"""
        try:
            # Simulation de données récentes
            return [
                {
                    'exam_id': f'exam_{i}',
                    'filename': f'exam_{i}.jpg',
                    'review_date': datetime.now().isoformat(),
                    'old_score': 75 + i,
                    'new_score': 80 + i,
                    'adjustment_reason': 'Partial credit for methodology'
                }
                for i in range(min(limit, 5))
            ]
            
        except Exception as e:
            logger.error(f"Error getting recent reviews: {str(e)}")
            return []
    
    async def _prepare_review_data(self, exam_details: Dict[str, Any]) -> Dict[str, Any]:
        """Préparer les données pour la révision"""
        try:
            results = exam_details.get('results', {})
            question_results = results.get('question_results', {})
            
            review_data = {
                'questions': [],
                'overall_confidence': results.get('ai_confidence', 0.5),
                'requires_attention': [],
                'auto_suggestions': []
            }
            
            # Analyser chaque question
            for question_id, question_result in question_results.items():
                confidence = question_result.get('confidence', 0.5)
                
                question_review = {
                    'question_id': question_id,
                    'student_answer': question_result.get('student_answer', ''),
                    'correct_answer': question_result.get('correct_answer', ''),
                    'auto_score': question_result.get('score', 0),
                    'confidence': confidence,
                    'feedback': question_result.get('feedback', ''),
                    'needs_attention': confidence < self.confidence_threshold,
                    'suggested_adjustments': []
                }
                
                # Ajouter des suggestions si la confiance est faible
                if confidence < self.confidence_threshold:
                    question_review['suggested_adjustments'] = self._generate_question_suggestions(
                        question_result
                    )
                    review_data['requires_attention'].append(question_id)
                
                review_data['questions'].append(question_review)
            
            return review_data
            
        except Exception as e:
            logger.error(f"Error preparing review data: {str(e)}")
            return {}
    
    def _generate_question_suggestions(self, question_result: Dict[str, Any]) -> List[str]:
        """Générer des suggestions pour une question spécifique"""
        suggestions = []
        
        student_answer = question_result.get('student_answer', '').strip()
        correct_answer = question_result.get('correct_answer', '').strip()
        score = question_result.get('score', 0)
        
        # Suggestions basées sur l'analyse
        if not student_answer:
            suggestions.append("Vérifier si une réponse a été manquée par l'OCR")
        elif score == 0 and len(student_answer) > 0:
            suggestions.append("Considérer un crédit partiel pour l'effort")
            suggestions.append("Vérifier si la réponse contient des éléments corrects")
        elif 0 < score < 1:
            suggestions.append("Réviser le score partiel attribué")
        
        # Suggestions pour les erreurs OCR potentielles
        if len(student_answer) < 3:
            suggestions.append("Possible erreur de reconnaissance OCR")
        
        return suggestions
    
    async def _generate_review_suggestions(self, exam_details: Dict[str, Any]) -> Dict[str, Any]:
        """Générer des suggestions globales pour la révision"""
        try:
            results = exam_details.get('results', {})
            ai_confidence = results.get('ai_confidence', 0.5)
            
            suggestions = {
                'priority_level': 'high' if ai_confidence < 0.5 else 'medium' if ai_confidence < 0.7 else 'low',
                'recommended_actions': [],
                'focus_areas': [],
                'estimated_review_time': 0
            }
            
            # Suggestions basées sur la confiance
            if ai_confidence < 0.5:
                suggestions['recommended_actions'].extend([
                    "Révision complète recommandée",
                    "Vérifier toutes les réponses détectées",
                    "Considérer une nouvelle numérisation si nécessaire"
                ])
                suggestions['estimated_review_time'] = 15
            elif ai_confidence < 0.7:
                suggestions['recommended_actions'].extend([
                    "Révision ciblée des questions à faible confiance",
                    "Vérifier les réponses partiellement correctes"
                ])
                suggestions['estimated_review_time'] = 8
            else:
                suggestions['recommended_actions'].append("Révision rapide recommandée")
                suggestions['estimated_review_time'] = 3
            
            # Analyser les zones problématiques
            question_results = results.get('question_results', {})
            low_confidence_questions = [
                q_id for q_id, q_result in question_results.items()
                if q_result.get('confidence', 1.0) < 0.6
            ]
            
            if low_confidence_questions:
                suggestions['focus_areas'].append(
                    f"Questions à faible confiance: {', '.join(low_confidence_questions)}"
                )
            
            return suggestions
            
        except Exception as e:
            logger.error(f"Error generating review suggestions: {str(e)}")
            return {}
    
    def _validate_review_data(self, review_data: Dict[str, Any]) -> Dict[str, Any]:
        """Valider les données de révision soumises"""
        errors = []
        
        # Vérifier la structure de base
        if 'question_adjustments' not in review_data:
            errors.append("Missing question_adjustments")
        
        # Valider les ajustements de questions
        question_adjustments = review_data.get('question_adjustments', {})
        for question_id, adjustment in question_adjustments.items():
            if 'new_score' in adjustment:
                try:
                    score = float(adjustment['new_score'])
                    if not (0 <= score <= 1):
                        errors.append(f"Invalid score for {question_id}: must be between 0 and 1")
                except (ValueError, TypeError):
                    errors.append(f"Invalid score format for {question_id}")
        
        return {
            'valid': len(errors) == 0,
            'errors': errors
        }
    
    async def _calculate_adjusted_score(self, review_data: Dict[str, Any]) -> Dict[str, Any]:
        """Calculer le nouveau score après ajustements"""
        try:
            question_adjustments = review_data.get('question_adjustments', {})
            
            total_score = 0
            total_questions = len(question_adjustments) if question_adjustments else 1
            
            # Calculer le score total ajusté
            for question_id, adjustment in question_adjustments.items():
                if 'new_score' in adjustment:
                    total_score += float(adjustment['new_score'])
                else:
                    total_score += float(adjustment.get('original_score', 0))
            
            percentage = (total_score / total_questions * 100) if total_questions > 0 else 0
            letter_grade = self._percentage_to_letter_grade(percentage)
            
            return {
                'total_score': total_score,
                'max_score': total_questions,
                'percentage': percentage,
                'letter_grade': letter_grade
            }
            
        except Exception as e:
            logger.error(f"Error calculating adjusted score: {str(e)}")
            return {
                'total_score': 0,
                'max_score': 1,
                'percentage': 0,
                'letter_grade': 'F'
            }
    
    def _percentage_to_letter_grade(self, percentage: float) -> str:
        """Convertir un pourcentage en note littérale"""
        if percentage >= 90:
            return 'A'
        elif percentage >= 80:
            return 'B'
        elif percentage >= 70:
            return 'C'
        elif percentage >= 60:
            return 'D'
        else:
            return 'F'
    
    async def _generate_review_report(
        self,
        exam_id: str,
        review_data: Dict[str, Any],
        new_score_data: Dict[str, Any],
        save_result: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Générer un rapport de révision"""
        try:
            adjustments_made = []
            question_adjustments = review_data.get('question_adjustments', {})
            
            for question_id, adjustment in question_adjustments.items():
                if 'new_score' in adjustment:
                    adjustments_made.append({
                        'question_id': question_id,
                        'old_score': adjustment.get('original_score', 0),
                        'new_score': adjustment['new_score'],
                        'reason': adjustment.get('reason', 'Manual adjustment')
                    })
            
            return {
                'exam_id': exam_id,
                'review_date': datetime.now().isoformat(),
                'adjustments_count': len(adjustments_made),
                'adjustments_made': adjustments_made,
                'score_change': {
                    'old_score': save_result.get('old_score', 0),
                    'new_score': new_score_data['total_score'],
                    'old_grade': save_result.get('old_grade', 'F'),
                    'new_grade': new_score_data['letter_grade']
                },
                'overall_comments': review_data.get('overall_comments', ''),
                'review_time_estimate': review_data.get('review_time_minutes', 0)
            }
            
        except Exception as e:
            logger.error(f"Error generating review report: {str(e)}")
            return {}

# Instance globale
manual_review_service = None

def init_manual_review_service(database_service):
    """Initialiser le service de révision manuelle"""
    global manual_review_service
    manual_review_service = ManualReviewService(database_service)
    return manual_review_service
