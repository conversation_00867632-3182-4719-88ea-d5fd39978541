'use client';

import React, { useEffect, useState } from 'react';
import { usePathname, useRouter } from 'next/navigation';
import { AuthProvider } from '@/providers/auth-provider';
import { Toaster } from '@/components/ui/toaster';
import RouteGuard from '@/components/RouteGuard';

interface ProvidersProps {
  children: React.ReactNode;
}

export default function Providers({ children }: ProvidersProps) {
  const pathname = usePathname();
  const router = useRouter();
  const [mounted, setMounted] = useState(false);

  // Effet pour gérer l'initialisation côté client
  useEffect(() => {
    setMounted(true);
  }, []);

  // Utiliser le système de routage de Next.js
  useEffect(() => {
    if (mounted) {
      // Vous pouvez ajouter ici une logique supplémentaire pour le routage
      console.log('Route changed to:', pathname);
    }
  }, [pathname, mounted]);

  return (
    <AuthProvider>
      {/* Le RouteGuard gère l'authentification et les redirections */}
      <RouteGuard>
        {children}
      </RouteGuard>
      <Toaster />
    </AuthProvider>
  );
}