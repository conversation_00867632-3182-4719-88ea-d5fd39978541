import os
import sys
from sqlalchemy.orm import Session
from database import engine, Base, SessionLocal
from models import User, Exam
from auth import get_password_hash
import json
from datetime import datetime

# C<PERSON>er les tables
def init_db():
    Base.metadata.create_all(bind=engine)
    print("Base de données initialisée avec succès!")

# Créer un utilisateur administrateur par défaut
def create_default_admin():
    db = SessionLocal()
    try:
        # Vérifier si l'utilisateur admin existe déjà
        admin = db.query(User).filter(User.username == "admin").first()
        if admin:
            print("L'utilisateur admin existe déjà.")
            return

        # Créer l'utilisateur admin
        admin = User(
            username="admin",
            email="<EMAIL>",
            full_name="Administrateur",
            hashed_password=get_password_hash("admin123"),
            is_active=True,
            role="admin"
        )
        db.add(admin)
        db.commit()
        db.refresh(admin)
        print(f"Utilisateur admin créé avec succès! ID: {admin.id}")

        # Créer un utilisateur enseignant
        teacher = User(
            username="teacher",
            email="<EMAIL>",
            full_name="Enseignant Test",
            hashed_password=get_password_hash("teacher123"),
            is_active=True,
            role="teacher"
        )
        db.add(teacher)
        db.commit()
        db.refresh(teacher)
        print(f"Utilisateur enseignant créé avec succès! ID: {teacher.id}")

        # Créer un utilisateur étudiant
        student = User(
            username="student",
            email="<EMAIL>",
            full_name="Étudiant Test",
            hashed_password=get_password_hash("student123"),
            is_active=True,
            role="student"
        )
        db.add(student)
        db.commit()
        db.refresh(student)
        print(f"Utilisateur étudiant créé avec succès! ID: {student.id}")

    except Exception as e:
        print(f"Erreur lors de la création des utilisateurs: {e}")
    finally:
        db.close()

# Créer des exemples d'examens
def create_sample_exams():
    db = SessionLocal()
    try:
        # Vérifier si des examens existent déjà
        exams_count = db.query(Exam).count()
        if exams_count > 0:
            print(f"{exams_count} examens existent déjà.")
            return

        # Créer un exemple d'examen manuscrit
        handwritten_exam = Exam(
            id="sample-handwritten-1",
            user_id=2,  # ID de l'enseignant
            original_filename="exemple_manuscrit.jpg",
            file_path="uploads/exemple_manuscrit.jpg",
            upload_time=datetime.now(),
            status="completed",
            exam_type="Handwritten",
            extracted_text="Ceci est un exemple de texte extrait d'un examen manuscrit.\n\nQuestion 1: Expliquez le concept de la gravité.\nRéponse: La gravité est une force fondamentale qui attire les objets les uns vers les autres. Elle est proportionnelle à la masse des objets et inversement proportionnelle au carré de la distance qui les sépare.\n\nQuestion 2: Décrivez le cycle de l'eau.\nRéponse: Le cycle de l'eau comprend l'évaporation de l'eau des océans, la condensation en nuages, les précipitations sous forme de pluie ou de neige, et le ruissellement qui ramène l'eau aux océans.",
            score=85.5,
            grade="B+",
            summary="Bonne compréhension des concepts fondamentaux. Quelques imprécisions dans les explications.",
            details={
                "process_status": {
                    "preprocessing": {"status": "completed", "started_at": "2023-05-01T10:00:00", "completed_at": "2023-05-01T10:01:00"},
                    "ocr_extraction": {"status": "completed", "started_at": "2023-05-01T10:01:00", "completed_at": "2023-05-01T10:03:00"},
                    "ai_analysis": {"status": "completed", "started_at": "2023-05-01T10:03:00", "completed_at": "2023-05-01T10:05:00"},
                    "final_grading": {"status": "completed", "started_at": "2023-05-01T10:05:00", "completed_at": "2023-05-01T10:06:00"}
                },
                "extracted_text_sample": "Ceci est un exemple de texte extrait d'un examen manuscrit...",
                "grading_details": {
                    "score_total": 85.5,
                    "note": "B+",
                    "évaluation_par_question": [
                        {
                            "question": "Question 1: Expliquez le concept de la gravité.",
                            "points_obtenus": 8,
                            "points_possibles": 10,
                            "commentaire": "Bonne explication, mais manque de précision sur la constante gravitationnelle."
                        },
                        {
                            "question": "Question 2: Décrivez le cycle de l'eau.",
                            "points_obtenus": 9,
                            "points_possibles": 10,
                            "commentaire": "Excellente description du cycle de l'eau."
                        }
                    ],
                    "commentaire_général": "Bonne compréhension des concepts fondamentaux. Quelques imprécisions dans les explications."
                },
                "process_completed": True
            }
        )
        db.add(handwritten_exam)

        # Créer un exemple d'examen QCM
        qcm_exam = Exam(
            id="sample-qcm-1",
            user_id=2,  # ID de l'enseignant
            original_filename="exemple_qcm.jpg",
            file_path="uploads/exemple_qcm.jpg",
            upload_time=datetime.now(),
            status="completed",
            exam_type="QCM",
            extracted_text="QCM de sciences\n\n1. Quelle est la planète la plus proche du soleil?\nA) Vénus\nB) Mercure\nC) Mars\nD) Jupiter\n\n2. Quel est le symbole chimique de l'or?\nA) Au\nB) Ag\nC) Fe\nD) O",
            score=75.0,
            grade="C",
            summary="Résultats moyens. Plusieurs erreurs sur les questions de chimie.",
            details={
                "process_status": {
                    "preprocessing": {"status": "completed", "started_at": "2023-05-02T14:00:00", "completed_at": "2023-05-02T14:01:00"},
                    "ocr_extraction": {"status": "completed", "started_at": "2023-05-02T14:01:00", "completed_at": "2023-05-02T14:02:00"},
                    "ai_analysis": {"status": "completed", "started_at": "2023-05-02T14:02:00", "completed_at": "2023-05-02T14:03:00"},
                    "final_grading": {"status": "completed", "started_at": "2023-05-02T14:03:00", "completed_at": "2023-05-02T14:04:00"}
                },
                "extracted_text_sample": "QCM de sciences\n\n1. Quelle est la planète la plus proche du soleil?...",
                "grading_details": {
                    "score": 75.0,
                    "correct_count": 3,
                    "total_questions": 4,
                    "questions": {
                        "1": {"student_answer": "B", "correct_answer": "B", "is_correct": True},
                        "2": {"student_answer": "A", "correct_answer": "A", "is_correct": True},
                        "3": {"student_answer": "C", "correct_answer": "B", "is_correct": False},
                        "4": {"student_answer": "D", "correct_answer": "A", "is_correct": False}
                    }
                },
                "feedback": {
                    "récapitulatif": "Score: 75.0%",
                    "points_forts": ["Bonnes réponses en astronomie"],
                    "axes_amélioration": ["Revoir les notions de chimie"],
                    "conseils": ["Réviser les symboles chimiques des éléments"],
                    "conclusion": "Continuez vos efforts, vous êtes sur la bonne voie!"
                },
                "process_completed": True
            }
        )
        db.add(qcm_exam)

        db.commit()
        print("Exemples d'examens créés avec succès!")

    except Exception as e:
        print(f"Erreur lors de la création des exemples d'examens: {e}")
    finally:
        db.close()

if __name__ == "__main__":
    print("Initialisation de la base de données...")
    init_db()
    create_default_admin()
    create_sample_exams()
    print("Initialisation terminée!")