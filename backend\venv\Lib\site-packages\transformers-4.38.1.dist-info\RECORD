../../Scripts/transformers-cli.exe,sha256=s03LBn2fQtdDl3DvRkMgyzHtGi-y32C9JBVxqmdinss,108451
transformers-4.38.1.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
transformers-4.38.1.dist-info/LICENSE,sha256=d_1HEN757DwPYiWADgI18VpCWr1KiwNVkSf814JhIEk,11418
transformers-4.38.1.dist-info/METADATA,sha256=BkdcMQqpvtRkKZkL9CQxdMoW-dvnqBL6lUXpS6cqHGY,131071
transformers-4.38.1.dist-info/RECORD,,
transformers-4.38.1.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
transformers-4.38.1.dist-info/WHEEL,sha256=2wepM1nk4DS4eFpYrW1TTqPcoGNfHhhO_i5m4cOimbo,92
transformers-4.38.1.dist-info/entry_points.txt,sha256=kgdW_0F_tXNrWKSZXKWKeUD_LqVgcji9j7atGXve8z4,81
transformers-4.38.1.dist-info/top_level.txt,sha256=GLBaeTo_CSdhnHvbxQ0kzpEHdlLuA_33foIogaWxntI,13
transformers/__init__.py,sha256=aYd4ex5KfkqQ6pInD2fwStSos3UFOvZFFkAdp7YX7Ww,320231
transformers/__pycache__/__init__.cpython-311.pyc,,
transformers/__pycache__/activations.cpython-311.pyc,,
transformers/__pycache__/activations_tf.cpython-311.pyc,,
transformers/__pycache__/audio_utils.cpython-311.pyc,,
transformers/__pycache__/cache_utils.cpython-311.pyc,,
transformers/__pycache__/configuration_utils.cpython-311.pyc,,
transformers/__pycache__/convert_graph_to_onnx.cpython-311.pyc,,
transformers/__pycache__/convert_pytorch_checkpoint_to_tf2.cpython-311.pyc,,
transformers/__pycache__/convert_slow_tokenizer.cpython-311.pyc,,
transformers/__pycache__/convert_slow_tokenizers_checkpoints_to_fast.cpython-311.pyc,,
transformers/__pycache__/convert_tf_hub_seq_to_seq_bert_to_pytorch.cpython-311.pyc,,
transformers/__pycache__/debug_utils.cpython-311.pyc,,
transformers/__pycache__/deepspeed.cpython-311.pyc,,
transformers/__pycache__/dependency_versions_check.cpython-311.pyc,,
transformers/__pycache__/dependency_versions_table.cpython-311.pyc,,
transformers/__pycache__/dynamic_module_utils.cpython-311.pyc,,
transformers/__pycache__/feature_extraction_sequence_utils.cpython-311.pyc,,
transformers/__pycache__/feature_extraction_utils.cpython-311.pyc,,
transformers/__pycache__/file_utils.cpython-311.pyc,,
transformers/__pycache__/generation_flax_utils.cpython-311.pyc,,
transformers/__pycache__/generation_tf_utils.cpython-311.pyc,,
transformers/__pycache__/generation_utils.cpython-311.pyc,,
transformers/__pycache__/hf_argparser.cpython-311.pyc,,
transformers/__pycache__/hyperparameter_search.cpython-311.pyc,,
transformers/__pycache__/image_processing_utils.cpython-311.pyc,,
transformers/__pycache__/image_transforms.cpython-311.pyc,,
transformers/__pycache__/image_utils.cpython-311.pyc,,
transformers/__pycache__/keras_callbacks.cpython-311.pyc,,
transformers/__pycache__/modelcard.cpython-311.pyc,,
transformers/__pycache__/modeling_attn_mask_utils.cpython-311.pyc,,
transformers/__pycache__/modeling_flax_outputs.cpython-311.pyc,,
transformers/__pycache__/modeling_flax_pytorch_utils.cpython-311.pyc,,
transformers/__pycache__/modeling_flax_utils.cpython-311.pyc,,
transformers/__pycache__/modeling_outputs.cpython-311.pyc,,
transformers/__pycache__/modeling_tf_outputs.cpython-311.pyc,,
transformers/__pycache__/modeling_tf_pytorch_utils.cpython-311.pyc,,
transformers/__pycache__/modeling_tf_utils.cpython-311.pyc,,
transformers/__pycache__/modeling_utils.cpython-311.pyc,,
transformers/__pycache__/optimization.cpython-311.pyc,,
transformers/__pycache__/optimization_tf.cpython-311.pyc,,
transformers/__pycache__/processing_utils.cpython-311.pyc,,
transformers/__pycache__/pytorch_utils.cpython-311.pyc,,
transformers/__pycache__/safetensors_conversion.cpython-311.pyc,,
transformers/__pycache__/testing_utils.cpython-311.pyc,,
transformers/__pycache__/tf_utils.cpython-311.pyc,,
transformers/__pycache__/time_series_utils.cpython-311.pyc,,
transformers/__pycache__/tokenization_utils.cpython-311.pyc,,
transformers/__pycache__/tokenization_utils_base.cpython-311.pyc,,
transformers/__pycache__/tokenization_utils_fast.cpython-311.pyc,,
transformers/__pycache__/trainer.cpython-311.pyc,,
transformers/__pycache__/trainer_callback.cpython-311.pyc,,
transformers/__pycache__/trainer_pt_utils.cpython-311.pyc,,
transformers/__pycache__/trainer_seq2seq.cpython-311.pyc,,
transformers/__pycache__/trainer_utils.cpython-311.pyc,,
transformers/__pycache__/training_args.cpython-311.pyc,,
transformers/__pycache__/training_args_seq2seq.cpython-311.pyc,,
transformers/__pycache__/training_args_tf.cpython-311.pyc,,
transformers/activations.py,sha256=FG97toxmWlLq9lTq9CFyf0Us-C5A1qyQ0vUo_OjcOyM,8460
transformers/activations_tf.py,sha256=u2Y9dgDRgW-YbN_J-xmd05EK4p24rV8ZkzrQzpz4lCI,4689
transformers/audio_utils.py,sha256=YqyGk1aKglfF1vpCI9t2QYTEtLXhYNAuo--asf1rJiE,32360
transformers/benchmark/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
transformers/benchmark/__pycache__/__init__.cpython-311.pyc,,
transformers/benchmark/__pycache__/benchmark.cpython-311.pyc,,
transformers/benchmark/__pycache__/benchmark_args.cpython-311.pyc,,
transformers/benchmark/__pycache__/benchmark_args_tf.cpython-311.pyc,,
transformers/benchmark/__pycache__/benchmark_args_utils.cpython-311.pyc,,
transformers/benchmark/__pycache__/benchmark_tf.cpython-311.pyc,,
transformers/benchmark/__pycache__/benchmark_utils.cpython-311.pyc,,
transformers/benchmark/benchmark.py,sha256=q2Jk1RyHOtzNe7vDSVjkL9Kf1jkMiGZsJPDmsACnxxY,10752
transformers/benchmark/benchmark_args.py,sha256=YcLN181cLE8KTNxPFIaZx1qQEi839I5bQkJFb6FSaN8,3890
transformers/benchmark/benchmark_args_tf.py,sha256=bAcsgf7bOUyoo8AGFSiQhciR8S5wMJqnL5iVlvbQzow,4735
transformers/benchmark/benchmark_args_utils.py,sha256=pkgvor3IuC5v9BubOCFVuwbgGHsoGkNp1CDdgJlyBi4,6499
transformers/benchmark/benchmark_tf.py,sha256=aEjclKepsQhn6vjxVJ5l2ho0ptUJuvaSYfuP4rJE6MQ,13251
transformers/benchmark/benchmark_utils.py,sha256=f9fv_EF1GwfK6A9wS6O-AYDrjI_cBflTbffL32iFTY0,37600
transformers/cache_utils.py,sha256=cJ0XKmZVm1n2Mo2A5s8V4zvUveiHV_o56y1FrVTBhzw,19550
transformers/commands/__init__.py,sha256=aFO3I7C6G9OLA9JZSc_yMaZl0glOQtjNPjqMFfu9wfQ,923
transformers/commands/__pycache__/__init__.cpython-311.pyc,,
transformers/commands/__pycache__/add_new_model.cpython-311.pyc,,
transformers/commands/__pycache__/add_new_model_like.cpython-311.pyc,,
transformers/commands/__pycache__/convert.cpython-311.pyc,,
transformers/commands/__pycache__/download.cpython-311.pyc,,
transformers/commands/__pycache__/env.cpython-311.pyc,,
transformers/commands/__pycache__/lfs.cpython-311.pyc,,
transformers/commands/__pycache__/pt_to_tf.cpython-311.pyc,,
transformers/commands/__pycache__/run.cpython-311.pyc,,
transformers/commands/__pycache__/serving.cpython-311.pyc,,
transformers/commands/__pycache__/train.cpython-311.pyc,,
transformers/commands/__pycache__/transformers_cli.cpython-311.pyc,,
transformers/commands/__pycache__/user.cpython-311.pyc,,
transformers/commands/add_new_model.py,sha256=H8_UkJ8TYyC8sEMqE4Iu-Izq3lJi93l813oU-LI2XyY,11062
transformers/commands/add_new_model_like.py,sha256=5vHmoNfwa36tSBhTwQ571jG6VP7H5IMwgKXoTlQcxCg,72940
transformers/commands/convert.py,sha256=lHz2sQti9HubMNwObLCc_sw9Y7L-IPcaYJMSJR_AVWM,7068
transformers/commands/download.py,sha256=GKPadx-YGBL7dHJSEcUp-QNOP3R2L71-gPGP0z6NNQI,2395
transformers/commands/env.py,sha256=q21O011lwdgGX862xAxH1Pjhd53uuxgB3g6C8cfNGV4,5316
transformers/commands/lfs.py,sha256=4QDGBbJxBcRpgmhHXvigZQUsXuTPwrRY60t1qGjzfWU,8001
transformers/commands/pt_to_tf.py,sha256=qVXHzdjjik3n_y8Ci8A6Wg6ag0eX0T6Dj36-sSv18Xg,20540
transformers/commands/run.py,sha256=nyEe2lOoj6e0EOxjKeF08hdW9WVWa101r9hWXl9v3Jo,4249
transformers/commands/serving.py,sha256=CnNHFVM_SK_-aNxEJnq7vJK5dBqDBw7bxxQiv5truEU,8027
transformers/commands/train.py,sha256=FKlH-IYr3mVc7_mS5ObCyJaHs9JincYLg3Zt6WQz1ag,6341
transformers/commands/transformers_cli.py,sha256=QimzKwJXAzZ9da0NDFrupqnATqP8MQ7upoj9TspwnKA,2047
transformers/commands/user.py,sha256=t35-l945UBen5uYR_KsbhtNOqdHXrfdpHrhTbR3-YXc,7124
transformers/configuration_utils.py,sha256=8SN1exa2iWIKl08c29unKKVbLIQIn776wCnTfNZNGU4,57091
transformers/convert_graph_to_onnx.py,sha256=rJmIK0Rs5WPsOiRGWmgN9q4A5W5gqUDB7OmcRkTqvJY,20151
transformers/convert_pytorch_checkpoint_to_tf2.py,sha256=kssO2h4wJBrUMEFXmIeZpCKYAzBbGwUQWUZq59OGLkE,16958
transformers/convert_slow_tokenizer.py,sha256=WWA0OyU8Xa2xNuutDfo_afZXGiEeZ6BIs1RHpFEKBGg,54938
transformers/convert_slow_tokenizers_checkpoints_to_fast.py,sha256=mIX3e0r7Dci5lahBf0iO4C2rvj0OzwkJbmw5lmgiG0Q,4982
transformers/convert_tf_hub_seq_to_seq_bert_to_pytorch.py,sha256=so9OnNT3TmdTbRMGbuepLY0zCMNfB6huaLg38aDVWOU,2911
transformers/data/__init__.py,sha256=JWIY7GLKedWilK2mpd_qtVeXLQK2ZXki6ISkRUua09Y,1423
transformers/data/__pycache__/__init__.cpython-311.pyc,,
transformers/data/__pycache__/data_collator.cpython-311.pyc,,
transformers/data/data_collator.py,sha256=EQgDVvrLxXzDZqoMAHwVd6wkFMf0pjdCYERwlEb_L-w,78254
transformers/data/datasets/__init__.py,sha256=PGzUJjdmTPOPMyjV4-Tj3sNrmmh-lspjyxrVbrfJoX8,909
transformers/data/datasets/__pycache__/__init__.cpython-311.pyc,,
transformers/data/datasets/__pycache__/glue.cpython-311.pyc,,
transformers/data/datasets/__pycache__/language_modeling.cpython-311.pyc,,
transformers/data/datasets/__pycache__/squad.cpython-311.pyc,,
transformers/data/datasets/glue.py,sha256=K3h2KxjIg0kWegPCw6ikbOL-lCFbKoQewb7R8wLZoIc,6163
transformers/data/datasets/language_modeling.py,sha256=E-VGwuyb09J4KmV8v37bNH5in90wDPuZHCYsqGdT7W0,23721
transformers/data/datasets/squad.py,sha256=OUTQDd687SQns7HRWDCgAjnuo_ZXihifLS6jF2bhUhc,9219
transformers/data/metrics/__init__.py,sha256=o9t_VTQtqU3lEhqvocDzFMm7OvAKD-uxrjPWy0r74BI,3632
transformers/data/metrics/__pycache__/__init__.cpython-311.pyc,,
transformers/data/metrics/__pycache__/squad_metrics.cpython-311.pyc,,
transformers/data/metrics/squad_metrics.py,sha256=pMwqcTg9KnCvmhLzAy1VJHRgJOEx6lLD105d-JcnWfg,29698
transformers/data/processors/__init__.py,sha256=lvN5mp9mdrr5v6QvZT6VcoZ78zZUvXiumTm6Gdvlgvo,1014
transformers/data/processors/__pycache__/__init__.cpython-311.pyc,,
transformers/data/processors/__pycache__/glue.cpython-311.pyc,,
transformers/data/processors/__pycache__/squad.cpython-311.pyc,,
transformers/data/processors/__pycache__/utils.cpython-311.pyc,,
transformers/data/processors/__pycache__/xnli.cpython-311.pyc,,
transformers/data/processors/glue.py,sha256=hhY12jdX1WnZ3_E3vSv-0rmF53F56c_2gQeW8dTwYb4,23219
transformers/data/processors/squad.py,sha256=_4WNLcZA6TAy7uNZO46948tmL5ngVF0LSB0y8nUn6rs,33153
transformers/data/processors/utils.py,sha256=GSaZbJ--XYq57vqyRVx_5LHSR4tklzFyR7ZKHGWsTAs,13829
transformers/data/processors/xnli.py,sha256=i03-c8vaQVYKpR7r4B8PsF6_CXXHxB7N-YHdzxs-APU,3489
transformers/debug_utils.py,sha256=6q8ArB104GdcIC2qfBQzKLxO7PfXmHEKdYtfL2FOK2w,12907
transformers/deepspeed.py,sha256=6C1uUQ84ImJPYu3WqZ-o6uOGPa7IHzD0MkP7DgnQxJY,1478
transformers/dependency_versions_check.py,sha256=6HbgtT2Wp-QZGOAdyUOklHvNA4rOVITGHrX34dtMOqg,2115
transformers/dependency_versions_table.py,sha256=jQto3gn0a4Cp1GmJffubfm5QhBkd1at4cXfTje0elho,3182
transformers/dynamic_module_utils.py,sha256=pG84aLRKv5OFFdzq8NtDqFe8qtPls5thYgmm_-qvr58,27135
transformers/feature_extraction_sequence_utils.py,sha256=dPKvTC29tNn8xK_dxZSeDbhNRK2s8VHu2EZIEKesEAs,18307
transformers/feature_extraction_utils.py,sha256=JiBZuuVHZNh2KxtE_FCH_78N_Ijp_qZZ1Q5LtBxPytk,29436
transformers/file_utils.py,sha256=OsQTsmio_TDSX7ojlK0o8JXwKXJ1TJOQsbcEG-4NA0Q,3744
transformers/generation/__init__.py,sha256=ZZDRXMV4uz32Mc924yXRmml8SrmiF-b2-hJFb3i-w2k,10889
transformers/generation/__pycache__/__init__.cpython-311.pyc,,
transformers/generation/__pycache__/beam_constraints.cpython-311.pyc,,
transformers/generation/__pycache__/beam_search.cpython-311.pyc,,
transformers/generation/__pycache__/candidate_generator.cpython-311.pyc,,
transformers/generation/__pycache__/configuration_utils.cpython-311.pyc,,
transformers/generation/__pycache__/flax_logits_process.cpython-311.pyc,,
transformers/generation/__pycache__/flax_utils.cpython-311.pyc,,
transformers/generation/__pycache__/logits_process.cpython-311.pyc,,
transformers/generation/__pycache__/stopping_criteria.cpython-311.pyc,,
transformers/generation/__pycache__/streamers.cpython-311.pyc,,
transformers/generation/__pycache__/tf_logits_process.cpython-311.pyc,,
transformers/generation/__pycache__/tf_utils.cpython-311.pyc,,
transformers/generation/__pycache__/utils.cpython-311.pyc,,
transformers/generation/beam_constraints.py,sha256=GefqriO2jWruyhdZI9pyGz4yZ-W9AYmzZueSWITgok4,19105
transformers/generation/beam_search.py,sha256=d6ZduwortYoRu6d0uCWfz1ivHqeQAxdA_lDrRA0kUOU,48812
transformers/generation/candidate_generator.py,sha256=29AYZRr8BgRnRSfrR6FX90NhPN91Q5INVfqfBZvIzCA,19760
transformers/generation/configuration_utils.py,sha256=0CRouCEPvsxcDeqyQ9l8FVovOsIM2E62rYyqqFpSHQU,54236
transformers/generation/flax_logits_process.py,sha256=fuuAEM_Bo7vRUY1OhfYJozuQBhE8QmM0X1ZxJvelJ-Y,19196
transformers/generation/flax_utils.py,sha256=PJvXaYYeEv8RteD0YfGuxGKfGVSudBUsSmeVqFMHTec,49820
transformers/generation/logits_process.py,sha256=kLKw-AD_ZERkeh7jTjsDnvhu0V6PTfSgxCmS62rcl9g,105174
transformers/generation/stopping_criteria.py,sha256=t8rbUpiFdWzmJLkQ3ZVkOUSIY4PfOeDOtx0Hn1Os-xU,6693
transformers/generation/streamers.py,sha256=ArJCKAVRKIKALqdGBAsQu038-BwZbo05tzOXZWP9yng,9213
transformers/generation/tf_logits_process.py,sha256=ZsIBDrFJ3egkk8aWYKtCvqH4M7INnlBa2zoCAIT5MR0,28114
transformers/generation/tf_utils.py,sha256=RRGrtYqmkqHHM_IEwNYGxxUfxgATUHHG513SW8PIHis,178522
transformers/generation/utils.py,sha256=h1z9UrKUEYmCRgw5FqkopEmpJE-pHo0LYekukM_-vVE,264395
transformers/generation_flax_utils.py,sha256=rIa4b7o_oiqd7x0Sa4YqUDJlF1jZH-Ifj8boMLtUhLg,1121
transformers/generation_tf_utils.py,sha256=lAc2_AsCgpkOMwdf4trZu-URUZpOCHs9AUGtli4RjvQ,1112
transformers/generation_utils.py,sha256=dsGGHEbZ0610SCN9wZ7ea1p8mXPa0AuT2xGJWyx38ys,1129
transformers/hf_argparser.py,sha256=Ah91LzoTwRPuWxONLFmPxDC8DGcUK_ebghoCQV2xNu8,19745
transformers/hyperparameter_search.py,sha256=wmfAWk_NTUQj3MezO_6CaDaJyUt9pbARcs-tbo_BdeM,4171
transformers/image_processing_utils.py,sha256=VqBYWt2gHnYV02fkRSmIjuwpzYQNqRO1hutcFPQUrvA,34719
transformers/image_transforms.py,sha256=Ag4YGN3gKHH10Z8acNpPxsjV43cyv9Ak5u3CwXTboZM,34154
transformers/image_utils.py,sha256=jpoULBipgJaZjh-NT6fZhFZSPjK_syF3LbAtUq47MfM,29639
transformers/integrations/__init__.py,sha256=-osJo_zPLNuZmm2V2aSmZZJItfQgXzlgvbkoOxfusd0,4607
transformers/integrations/__pycache__/__init__.cpython-311.pyc,,
transformers/integrations/__pycache__/aqlm.cpython-311.pyc,,
transformers/integrations/__pycache__/awq.cpython-311.pyc,,
transformers/integrations/__pycache__/bitsandbytes.cpython-311.pyc,,
transformers/integrations/__pycache__/deepspeed.cpython-311.pyc,,
transformers/integrations/__pycache__/integration_utils.cpython-311.pyc,,
transformers/integrations/__pycache__/peft.cpython-311.pyc,,
transformers/integrations/__pycache__/tpu.cpython-311.pyc,,
transformers/integrations/aqlm.py,sha256=wpVq2OAdGDMTywT-_rpH6vpRQEhUH4hLTi13jACFDCg,4462
transformers/integrations/awq.py,sha256=RaeEKBR_gZqOPdm9jJN4a4tmKxsYiuFlYUeZxrs4UfQ,15734
transformers/integrations/bitsandbytes.py,sha256=v8W4cXdy6Fh2doWBsYJRlMmiIv6lf2oHHgw2Wbg2S6c,14791
transformers/integrations/deepspeed.py,sha256=9m7igH4mdvM843jLRFKsBTMM6AAq-aHb5IMP50-J3TQ,18545
transformers/integrations/integration_utils.py,sha256=zWQTL9T-sdrJ9ieR6AM7ImgOjYBEXBha-9zB90LLuCI,84170
transformers/integrations/peft.py,sha256=_1zABToVWSH9U7XoPG5cJVmAT_5jbSbMDUADHvGiAXE,22620
transformers/integrations/tpu.py,sha256=4eQOxMwf2F33VSMlQ6bWBZuiV3HzBHcUXWnqQSKojVM,1392
transformers/keras_callbacks.py,sha256=i95nrEd_QsEo10x3T9RqZf3xGzfPiMOhmU1Ef_HvnGE,20675
transformers/kernels/deformable_detr/cpu/ms_deform_attn_cpu.cpp,sha256=VcCGm9IrvgVvmyZt0KyP16Q-ONmbeg6bKwccP6KadL0,1255
transformers/kernels/deformable_detr/cpu/ms_deform_attn_cpu.h,sha256=nvVsKj9nabQ7IaNY4di5xVx6u-0lIifQvLg2JCoxiik,1138
transformers/kernels/deformable_detr/cuda/ms_deform_attn_cuda.cu,sha256=NvqRUnThWxIFGh6FD18pKK-AZqHojOmb5pxfRFQLrxY,7378
transformers/kernels/deformable_detr/cuda/ms_deform_attn_cuda.cuh,sha256=RPWzVfYbAdepyLQxl82RSTWnOOwR1n9BKP3-p6RzDvg,61451
transformers/kernels/deformable_detr/cuda/ms_deform_attn_cuda.h,sha256=rPWOOMo3QyFdB5kMiexpApLFZ4dnRtx4CluEAGwsfO8,1139
transformers/kernels/deformable_detr/cuda/ms_deform_im2col_cuda.cuh,sha256=BRN8-yfSHY8ChLij8jFl2_z2LL0LEFKuVF6Byi-YLAY,54695
transformers/kernels/deformable_detr/ms_deform_attn.h,sha256=H2bBXGyl0R-v2DqGVz11asoRvxbjZ9iWB9djomZTpgY,1837
transformers/kernels/deformable_detr/vision.cpp,sha256=8RvZy7P_MMx5QEszo_MwNODddJLQ8mKcmmMfgLYC_HA,798
transformers/kernels/deta/cpu/ms_deform_attn_cpu.cpp,sha256=VcCGm9IrvgVvmyZt0KyP16Q-ONmbeg6bKwccP6KadL0,1255
transformers/kernels/deta/cpu/ms_deform_attn_cpu.h,sha256=nvVsKj9nabQ7IaNY4di5xVx6u-0lIifQvLg2JCoxiik,1138
transformers/kernels/deta/cuda/ms_deform_attn_cuda.cu,sha256=M5-bW9g5z-upTFMNPIfnyLAqKTxGMCjAPqBr0GmWHX8,7360
transformers/kernels/deta/cuda/ms_deform_attn_cuda.cuh,sha256=hygB20Vh3RttOSdCuTFz8V0d3CXNp-Q89x22rYmD258,61433
transformers/kernels/deta/cuda/ms_deform_attn_cuda.h,sha256=rPWOOMo3QyFdB5kMiexpApLFZ4dnRtx4CluEAGwsfO8,1139
transformers/kernels/deta/cuda/ms_deform_im2col_cuda.cuh,sha256=BRN8-yfSHY8ChLij8jFl2_z2LL0LEFKuVF6Byi-YLAY,54695
transformers/kernels/deta/ms_deform_attn.h,sha256=H2bBXGyl0R-v2DqGVz11asoRvxbjZ9iWB9djomZTpgY,1837
transformers/kernels/deta/vision.cpp,sha256=8RvZy7P_MMx5QEszo_MwNODddJLQ8mKcmmMfgLYC_HA,798
transformers/kernels/mra/cuda_kernel.cu,sha256=LxxRYTymSoBEQpWXHA0PMzwZwpolcwX7mFAjwU8-ZMc,11678
transformers/kernels/mra/cuda_kernel.h,sha256=UJvYq_MDzhcp07bZpYcOBn8ZGFcf_Ax1dynuiVTBvmA,1682
transformers/kernels/mra/cuda_launch.cu,sha256=Ox5MTACriC30CGyn-g1Kb5EgQSMAZSaN6fpit3xLFWc,4072
transformers/kernels/mra/cuda_launch.h,sha256=RVCkN_euasvgPK0zADNRvRYGWd4ah5l9X-7UG_AcdH8,707
transformers/kernels/mra/torch_extension.cpp,sha256=N0YdBLVX0lZabckJzV_RYTHS2atCNvn13E4Ivobt25g,1405
transformers/kernels/rwkv/wkv_cuda.cu,sha256=EvaUrEnw_qr2EjMKP-Pq7VPzFfGlMJnFhdHNLtn1fPU,6219
transformers/kernels/rwkv/wkv_cuda_bf16.cu,sha256=DG9hTtOAlrnpDFahjt-MmnOxjMuhGU55GPsmV21HtrQ,6633
transformers/kernels/rwkv/wkv_op.cpp,sha256=qSExhKdT6p3hyaTv5SypCnH_c7EmaX6HbhTcCntvZWg,4022
transformers/kernels/yoso/common.h,sha256=Tq2rOUtE8Y4DRAUrRISvwIwVI3u8JBf21WgWSAYiDlQ,273
transformers/kernels/yoso/common_cuda.h,sha256=Sji70AuVcuZSotLF7Gotmun9MJuOHo8wEkxizKXLRtc,258
transformers/kernels/yoso/common_cuda_device.h,sha256=y6WUgAiapnMKqthRMS5s-DMSWNVkar_i8g4KPFvqiuk,2063
transformers/kernels/yoso/fast_lsh_cumulation.cu,sha256=LA4LGNgyXT3osIyQtFBcRanSyNQWm8yqmpz7AeLP7cw,19061
transformers/kernels/yoso/fast_lsh_cumulation.h,sha256=1cTWZjOm751HGiEB5P-UPJ8SE1VO7XRyXmBgyxYDyjI,1575
transformers/kernels/yoso/fast_lsh_cumulation_cuda.cu,sha256=HKGLWl-WFz5BXjaAPHTNTbG6IUkJjhBdvFf2K7hrDVQ,32870
transformers/kernels/yoso/fast_lsh_cumulation_cuda.h,sha256=_KGI8HQbVFtCN5KAcSGpyiJ2foGi26RKen138CUc2fY,5490
transformers/kernels/yoso/fast_lsh_cumulation_torch.cpp,sha256=-Rh7o39Z3rtOPwNnEM-c51TCqywpVdK0WVaA7VRrXbQ,3154
transformers/modelcard.py,sha256=zeGRoH_h9x3BNmXiG_YhZ69pCxp8YSgzt2tMooaszGQ,35155
transformers/modeling_attn_mask_utils.py,sha256=wHqkGInITiY1_Lj6MO64gc-aX5bFf0hIrMB-cvA_s-U,22361
transformers/modeling_flax_outputs.py,sha256=wXse1g9VyQyVOZ9DrbPALeoZBdS45fsBA9fNrGnwaZc,41961
transformers/modeling_flax_pytorch_utils.py,sha256=UL5zridIWWbmo5vZ6uVoRcF6kIuEN4jthQ4q8uRKgRQ,21886
transformers/modeling_flax_utils.py,sha256=03qhVMFQed7a5wha8eYVe5w7Ml86VCadeTe0a9xGi8c,61343
transformers/modeling_outputs.py,sha256=CYpjijqZNOVUc-kixDLI-jMFru9MhpDQvnncSfp0wb4,112567
transformers/modeling_tf_outputs.py,sha256=nXCMOmFZ7IZFVuiQr7EU2ciV9QqwOYPYld_r2jBxVpE,56074
transformers/modeling_tf_pytorch_utils.py,sha256=GDG3dWIudHjaF12eIfsWd_TGv7fPKoHDoAkxsntLp6c,25664
transformers/modeling_tf_utils.py,sha256=QIYqnMiN0zY8e3UQSLve3075ToEYBnNTmI0lAmJGfrU,161631
transformers/modeling_utils.py,sha256=Rx9Q8JMS8EDedqCwYKgigLsFtc_KCnPxWS23sfxbNlM,227887
transformers/models/__init__.py,sha256=5CjT5d3Vj0wB81-zFsJCn_f-HR2jzgtpvgDUl8Bb-cA,3995
transformers/models/__pycache__/__init__.cpython-311.pyc,,
transformers/models/albert/__init__.py,sha256=eXW8msH9V8No-Tb5R28tdpXQbOnnSG77L_TVEwCRf9o,5482
transformers/models/albert/__pycache__/__init__.cpython-311.pyc,,
transformers/models/albert/__pycache__/configuration_albert.cpython-311.pyc,,
transformers/models/albert/__pycache__/convert_albert_original_tf_checkpoint_to_pytorch.cpython-311.pyc,,
transformers/models/albert/__pycache__/modeling_albert.cpython-311.pyc,,
transformers/models/albert/__pycache__/modeling_flax_albert.cpython-311.pyc,,
transformers/models/albert/__pycache__/modeling_tf_albert.cpython-311.pyc,,
transformers/models/albert/__pycache__/tokenization_albert.cpython-311.pyc,,
transformers/models/albert/__pycache__/tokenization_albert_fast.cpython-311.pyc,,
transformers/models/albert/configuration_albert.py,sha256=ktQvyJyCPHOxNnlZ0oO_rV2RyrDwS7xoN3AaoTXqO80,8973
transformers/models/albert/convert_albert_original_tf_checkpoint_to_pytorch.py,sha256=nTwtVg0AZgG4QnG9K361HM37gxGegQvD-ymZWuhic7s,2162
transformers/models/albert/modeling_albert.py,sha256=ePgQTKIGKGQ5fgKwrC2mvV7AMs6KOs2q0BqK-NfuONI,60785
transformers/models/albert/modeling_flax_albert.py,sha256=u2EEkckxVFt5WA8oQNbLJGcV5mhHGIJ6DMS867O150U,40739
transformers/models/albert/modeling_tf_albert.py,sha256=DDhPaLzweUUU4I2eTIPGUaIcEZXjcUJX_51HY5jW9mc,69215
transformers/models/albert/tokenization_albert.py,sha256=cBBBouyW1Key63yUycgAdFlXwrl7d2wnto3fjXNm1OM,15819
transformers/models/albert/tokenization_albert_fast.py,sha256=7g0hCMVXP-iA5D6QygWTaygilWhgIbv30HW5gy7YDLk,11156
transformers/models/align/__init__.py,sha256=DWtMJsXbmRuoSAwLLOy6aXKY65IT1TDV4ifwBmApkM0,2064
transformers/models/align/__pycache__/__init__.cpython-311.pyc,,
transformers/models/align/__pycache__/configuration_align.cpython-311.pyc,,
transformers/models/align/__pycache__/convert_align_tf_to_hf.cpython-311.pyc,,
transformers/models/align/__pycache__/modeling_align.cpython-311.pyc,,
transformers/models/align/__pycache__/processing_align.cpython-311.pyc,,
transformers/models/align/configuration_align.py,sha256=hv_Qla2NBHxk-zqK7B1inpcACzuzXN6_8oK_Gw9XB8w,18242
transformers/models/align/convert_align_tf_to_hf.py,sha256=tzPoEMyLV_ckVngYdvJ6uAFZ6RgsuX55JYjEkIMtPTg,15536
transformers/models/align/modeling_align.py,sha256=Ncsm3PefFCPcNvZJWnOtpJjTKcL8EP8pixcPLYe16f4,71836
transformers/models/align/processing_align.py,sha256=xuKyyR9ouKXWtDOr2VDspp_ciDVAhVUMgCwbBi_T0i0,6216
transformers/models/altclip/__init__.py,sha256=bvOH6rQhnWm4shjpJ51SPs0uxlDdPrViBxQqTt3gRik,2126
transformers/models/altclip/__pycache__/__init__.cpython-311.pyc,,
transformers/models/altclip/__pycache__/configuration_altclip.cpython-311.pyc,,
transformers/models/altclip/__pycache__/modeling_altclip.cpython-311.pyc,,
transformers/models/altclip/__pycache__/processing_altclip.cpython-311.pyc,,
transformers/models/altclip/configuration_altclip.py,sha256=CQ-i_4H8UGrU4JC6oUAUiYiCJfpfznW5D9RnLi_3lLc,19908
transformers/models/altclip/modeling_altclip.py,sha256=OT-GM_4zx1mgZ-tvGEPcBsvZqPv28CXC7lPaMxUvc9c,78293
transformers/models/altclip/processing_altclip.py,sha256=uaRYuV0f_EaU-_b6-zkjG1_EYYXwBCa7dgI9XMCvIho,6502
transformers/models/audio_spectrogram_transformer/__init__.py,sha256=-LyBP9am8Di97o7CZupQyqD1-2bYHKLcUqVWTZBHVs8,2159
transformers/models/audio_spectrogram_transformer/__pycache__/__init__.cpython-311.pyc,,
transformers/models/audio_spectrogram_transformer/__pycache__/configuration_audio_spectrogram_transformer.cpython-311.pyc,,
transformers/models/audio_spectrogram_transformer/__pycache__/convert_audio_spectrogram_transformer_original_to_pytorch.cpython-311.pyc,,
transformers/models/audio_spectrogram_transformer/__pycache__/feature_extraction_audio_spectrogram_transformer.cpython-311.pyc,,
transformers/models/audio_spectrogram_transformer/__pycache__/modeling_audio_spectrogram_transformer.cpython-311.pyc,,
transformers/models/audio_spectrogram_transformer/configuration_audio_spectrogram_transformer.py,sha256=JOl78E7J3sK4jPlLBi7AuzEdMu1pkTnbL31lNMHeNy4,5649
transformers/models/audio_spectrogram_transformer/convert_audio_spectrogram_transformer_original_to_pytorch.py,sha256=Csn0NnGlPMLUehRWvgU1cW49EzTNZ7p0COxWNIqQIp8,11052
transformers/models/audio_spectrogram_transformer/feature_extraction_audio_spectrogram_transformer.py,sha256=CLMcdUUk8ehA2PC9wEBwvWd68tIMFtZswNhVbVwXWc8,9908
transformers/models/audio_spectrogram_transformer/modeling_audio_spectrogram_transformer.py,sha256=SxXZUboxV5dpnCT_5zjgh1lJLj-LukgwCAydxy8cwYs,26013
transformers/models/auto/__init__.py,sha256=OU8_fzxvhi-Z3SDSsGMBZ6QZKGUjLAjTIIYOo7HmtHg,16586
transformers/models/auto/__pycache__/__init__.cpython-311.pyc,,
transformers/models/auto/__pycache__/auto_factory.cpython-311.pyc,,
transformers/models/auto/__pycache__/configuration_auto.cpython-311.pyc,,
transformers/models/auto/__pycache__/feature_extraction_auto.cpython-311.pyc,,
transformers/models/auto/__pycache__/image_processing_auto.cpython-311.pyc,,
transformers/models/auto/__pycache__/modeling_auto.cpython-311.pyc,,
transformers/models/auto/__pycache__/modeling_flax_auto.cpython-311.pyc,,
transformers/models/auto/__pycache__/modeling_tf_auto.cpython-311.pyc,,
transformers/models/auto/__pycache__/processing_auto.cpython-311.pyc,,
transformers/models/auto/__pycache__/tokenization_auto.cpython-311.pyc,,
transformers/models/auto/auto_factory.py,sha256=TWcupJMQqFE2cMQ1lJnqAJeS1qK3zId4zgWke-VCyWA,42627
transformers/models/auto/configuration_auto.py,sha256=TunGbMPobc3VVglIj4KjNplviv-DDkxNowMYe2whx5Q,50686
transformers/models/auto/feature_extraction_auto.py,sha256=YfgaeHgaDj8qtVEHSLdq9Xjit6c_O5c1wO_pRykwGrY,19509
transformers/models/auto/image_processing_auto.py,sha256=itRUCIZ1xKiUtQBJzcG0X_g4A3bzxAADPWaU7wtmDto,21579
transformers/models/auto/modeling_auto.py,sha256=n9mLTNq-YZIUpSfKNPLORNpqX6FhB7NNUXL7S_Mwhbc,64407
transformers/models/auto/modeling_flax_auto.py,sha256=WKcWOmDTq2kwtFYGHccSyV3o8yUtvHlCgVRlh_5K2OI,14475
transformers/models/auto/modeling_tf_auto.py,sha256=fB3ufe0eyB2DzDupxt_EBfDUybgUz3HdT6qhF7DAUu8,28077
transformers/models/auto/processing_auto.py,sha256=EZAH2DH5fuMQER05eVTjFXoj1fsvKBq53SrR6NWi-8w,16967
transformers/models/auto/tokenization_auto.py,sha256=HgLsPaRKlWwrtMt_1fyHwf5KJEo0bD5OkGlEC33uSz4,44955
transformers/models/autoformer/__init__.py,sha256=wNFDMEr-Yo9Bt33bP5qqiC5dWKXOnWQPFg4C_ewyfGU,1914
transformers/models/autoformer/__pycache__/__init__.cpython-311.pyc,,
transformers/models/autoformer/__pycache__/configuration_autoformer.cpython-311.pyc,,
transformers/models/autoformer/__pycache__/modeling_autoformer.cpython-311.pyc,,
transformers/models/autoformer/configuration_autoformer.py,sha256=6aYNqme_BQL0dvImtD0gt88OUnkmGPURBBrjI_GGWZk,12326
transformers/models/autoformer/modeling_autoformer.py,sha256=l89x3e1h8QuHFuLnbhqtfLyCsIElUG2Ndqoo2ghbfv4,107214
transformers/models/bark/__init__.py,sha256=o6hWj_LrFLp-JSNY04tbWewQyrA44B0mhLUDpyv4jVw,2212
transformers/models/bark/__pycache__/__init__.cpython-311.pyc,,
transformers/models/bark/__pycache__/configuration_bark.cpython-311.pyc,,
transformers/models/bark/__pycache__/convert_suno_to_hf.cpython-311.pyc,,
transformers/models/bark/__pycache__/generation_configuration_bark.cpython-311.pyc,,
transformers/models/bark/__pycache__/modeling_bark.cpython-311.pyc,,
transformers/models/bark/__pycache__/processing_bark.cpython-311.pyc,,
transformers/models/bark/configuration_bark.py,sha256=JQxdKx552_IozCxuGbWrT99B8F8-T86GDd68QNeV_Uc,13046
transformers/models/bark/convert_suno_to_hf.py,sha256=O1OYzKyTr-9snPYUAw09GmVwb76UmiQGi3C2WfEIwTw,9373
transformers/models/bark/generation_configuration_bark.py,sha256=80ZI8x5r8JH26siXfm_c8NkuaRTUUzcxiMrtfIKDoSg,14992
transformers/models/bark/modeling_bark.py,sha256=LYudd0AjWen8BFuasggXMbJi1jRxObwwaAPK5tWtMQc,86617
transformers/models/bark/processing_bark.py,sha256=PgoptE_6V_ESvgXhGrRfVa68pTjJHXv1j9YwV24W9HA,13312
transformers/models/bart/__init__.py,sha256=FH8iETt_U4YAIIjo-Oap-WtQsBZqsaxGr9028KnrDEQ,4397
transformers/models/bart/__pycache__/__init__.cpython-311.pyc,,
transformers/models/bart/__pycache__/configuration_bart.cpython-311.pyc,,
transformers/models/bart/__pycache__/convert_bart_original_pytorch_checkpoint_to_pytorch.cpython-311.pyc,,
transformers/models/bart/__pycache__/modeling_bart.cpython-311.pyc,,
transformers/models/bart/__pycache__/modeling_flax_bart.cpython-311.pyc,,
transformers/models/bart/__pycache__/modeling_tf_bart.cpython-311.pyc,,
transformers/models/bart/__pycache__/tokenization_bart.cpython-311.pyc,,
transformers/models/bart/__pycache__/tokenization_bart_fast.cpython-311.pyc,,
transformers/models/bart/configuration_bart.py,sha256=FC2xQItsO2mqpk9EHA3US6_2j3bjSPOFG9zlDaj5cSw,18994
transformers/models/bart/convert_bart_original_pytorch_checkpoint_to_pytorch.py,sha256=VIRm-jWP4PNWN0Japr8yCJAJAAPVkJpJmEzYnHexU88,6055
transformers/models/bart/modeling_bart.py,sha256=YMEJJwtBAzoUkGoT2g72opihEppv1oJ1NPYYlZ00FGE,109275
transformers/models/bart/modeling_flax_bart.py,sha256=JH4YXctmpkynng1wP-50Vn4t8vEuhEmFfsfQZu1-lFI,82707
transformers/models/bart/modeling_tf_bart.py,sha256=SCaGH910Egz8gtbPF8Kg38uTG5KwPilRQTG4CMLvTaU,80773
transformers/models/bart/tokenization_bart.py,sha256=eDtvUiWOfG4-3gXrfrZC7O0GGVq02DDT5goW_7GA91k,17981
transformers/models/bart/tokenization_bart_fast.py,sha256=trWcch-2mM_kwUpefMCurfYNhlHpCS2xPtyIdzxjTrg,14139
transformers/models/barthez/__init__.py,sha256=7IXg6okZoJ10NCYRWn0GvoWWUvGUN27eIw7CzJ5CVGA,1848
transformers/models/barthez/__pycache__/__init__.cpython-311.pyc,,
transformers/models/barthez/__pycache__/tokenization_barthez.cpython-311.pyc,,
transformers/models/barthez/__pycache__/tokenization_barthez_fast.cpython-311.pyc,,
transformers/models/barthez/tokenization_barthez.py,sha256=czcdpl3uIe_0in2og_ULk89rnhK3OkUJ-lFKRNd7CqA,12797
transformers/models/barthez/tokenization_barthez_fast.py,sha256=HuYoq_rMfA1TmOfUppFdG4CEowDeGbBUFc-4JGYxlkY,8961
transformers/models/bartpho/__init__.py,sha256=Q0mAOPJGQaHHigdajLg5-2TPOw9NWw5uIRQlmfhh8Ds,1362
transformers/models/bartpho/__pycache__/__init__.cpython-311.pyc,,
transformers/models/bartpho/__pycache__/tokenization_bartpho.cpython-311.pyc,,
transformers/models/bartpho/tokenization_bartpho.py,sha256=NWvZzH9O_aBN3QcK7nSu_VrYjP3LT-wZWSEbutQH40I,14052
transformers/models/beit/__init__.py,sha256=T88Lwe4Y0tQmdrOpVnewjuHJoW_DZEbRmbTZDU2oAR0,3339
transformers/models/beit/__pycache__/__init__.cpython-311.pyc,,
transformers/models/beit/__pycache__/configuration_beit.cpython-311.pyc,,
transformers/models/beit/__pycache__/convert_beit_unilm_to_pytorch.cpython-311.pyc,,
transformers/models/beit/__pycache__/feature_extraction_beit.cpython-311.pyc,,
transformers/models/beit/__pycache__/image_processing_beit.cpython-311.pyc,,
transformers/models/beit/__pycache__/modeling_beit.cpython-311.pyc,,
transformers/models/beit/__pycache__/modeling_flax_beit.cpython-311.pyc,,
transformers/models/beit/configuration_beit.py,sha256=1y2kQ8o9ASQeO2g1aT6IY0ND7uwBBRUJz5nKFPGEhQM,11865
transformers/models/beit/convert_beit_unilm_to_pytorch.py,sha256=CndMgSTJoOik5LPH3YVLnQ6IR7IqfCsEN0KPUR43jHA,16578
transformers/models/beit/feature_extraction_beit.py,sha256=C9wchKLt3K__wzqOkDWsbK0hMPzVn9HZtm5KPI5Oq2s,1172
transformers/models/beit/image_processing_beit.py,sha256=4R1YZHdKC_14eHTrQmhdKwYU4FNvj094Jyz__W94Lpg,24184
transformers/models/beit/modeling_beit.py,sha256=nWBzj3J5fx2dstCF-WyM3_Ts_Hj6iEp1z4b3n74ieUA,59847
transformers/models/beit/modeling_flax_beit.py,sha256=9_xkFN7xtiLrxbShhpX8EgpY8kuOKIui-OlRidmNUAI,36996
transformers/models/bert/__init__.py,sha256=Tj3tueT-1FoWBmNNZXGGnytzeoLeEcjviP32uyfU1rw,6057
transformers/models/bert/__pycache__/__init__.cpython-311.pyc,,
transformers/models/bert/__pycache__/configuration_bert.cpython-311.pyc,,
transformers/models/bert/__pycache__/convert_bert_original_tf2_checkpoint_to_pytorch.cpython-311.pyc,,
transformers/models/bert/__pycache__/convert_bert_original_tf_checkpoint_to_pytorch.cpython-311.pyc,,
transformers/models/bert/__pycache__/convert_bert_pytorch_checkpoint_to_original_tf.cpython-311.pyc,,
transformers/models/bert/__pycache__/convert_bert_token_dropping_original_tf2_checkpoint_to_pytorch.cpython-311.pyc,,
transformers/models/bert/__pycache__/modeling_bert.cpython-311.pyc,,
transformers/models/bert/__pycache__/modeling_flax_bert.cpython-311.pyc,,
transformers/models/bert/__pycache__/modeling_tf_bert.cpython-311.pyc,,
transformers/models/bert/__pycache__/tokenization_bert.cpython-311.pyc,,
transformers/models/bert/__pycache__/tokenization_bert_fast.cpython-311.pyc,,
transformers/models/bert/__pycache__/tokenization_bert_tf.cpython-311.pyc,,
transformers/models/bert/configuration_bert.py,sha256=oWZEGd5GmC8zoCIgDv_zj0NnbY5IkzE9Xdljxdm_Qb8,10559
transformers/models/bert/convert_bert_original_tf2_checkpoint_to_pytorch.py,sha256=niQmTMwlmUA0aII1Zzg2OiJSpFljzwLCeJYotJ4tKOY,10490
transformers/models/bert/convert_bert_original_tf_checkpoint_to_pytorch.py,sha256=Hq-TMOnQnfpZOh0m9GHoykkogg0-HgLAmSiFvK8E6K4,2159
transformers/models/bert/convert_bert_pytorch_checkpoint_to_original_tf.py,sha256=6nISsCdgO_sJFFiLpnkGGsmTqC9Yp-gzDPDM-EafVXA,4112
transformers/models/bert/convert_bert_token_dropping_original_tf2_checkpoint_to_pytorch.py,sha256=5kYqUUc-RGck4D0OUTlLDnyIPb_OIJ1NWboYRJ-7H0c,7606
transformers/models/bert/modeling_bert.py,sha256=_A7v7GDMOO3foDPELNgRED7DM8Ul7MWYQ-ENwBvDC6s,84303
transformers/models/bert/modeling_flax_bert.py,sha256=UMRUMxvvwu8oIzkLfVjXWP9Y47WolZPtZFELypsG-pg,63672
transformers/models/bert/modeling_tf_bert.py,sha256=2CxZqU-oaMwJRHMXYdWEgC5XS6jBloHWOSjqrSLQb0A,95305
transformers/models/bert/tokenization_bert.py,sha256=IGdnDLMh8BJlwIgrzUfqkKXgupiVmIEGgPu0R3I1Iic,25895
transformers/models/bert/tokenization_bert_fast.py,sha256=MFsErqYDu3eAFKgrwTrfsUeeTfNByO_oeK4vMhJtDPk,15963
transformers/models/bert/tokenization_bert_tf.py,sha256=1zWzz3FPrh5zWqRG7YVY_wIVCzzB8iNGR6MGx48ke3c,11895
transformers/models/bert_generation/__init__.py,sha256=2XUvSVePne5Hspjzn6l_PonKfZ9WXjRBub9bevOv8R4,2275
transformers/models/bert_generation/__pycache__/__init__.cpython-311.pyc,,
transformers/models/bert_generation/__pycache__/configuration_bert_generation.cpython-311.pyc,,
transformers/models/bert_generation/__pycache__/modeling_bert_generation.cpython-311.pyc,,
transformers/models/bert_generation/__pycache__/tokenization_bert_generation.cpython-311.pyc,,
transformers/models/bert_generation/configuration_bert_generation.py,sha256=DIEAcuNI_Ufp7hPLN-nDuvJLYDYgr9gNphiroKv-4qY,6342
transformers/models/bert_generation/modeling_bert_generation.py,sha256=XwCC1kp-Sr2QssLGXpH4wds3Y8J80Xz8MNHHj2_w9j4,48087
transformers/models/bert_generation/tokenization_bert_generation.py,sha256=NTwSl4_Ia_UYJNtV4k0X1RMaKoi8wk1iVXAbcKv9aUE,7499
transformers/models/bert_japanese/__init__.py,sha256=6prQNXS2J4cWXqAqkqDyxNmzx-vaFQtOjJQio-ZUc4g,1053
transformers/models/bert_japanese/__pycache__/__init__.cpython-311.pyc,,
transformers/models/bert_japanese/__pycache__/tokenization_bert_japanese.cpython-311.pyc,,
transformers/models/bert_japanese/tokenization_bert_japanese.py,sha256=LBo1ryhg834EgoRfHZOQNUdnCjwoUAw4rHspMVPCLt8,40920
transformers/models/bertweet/__init__.py,sha256=sXE2NweoWp8UIaJkuSaLSw4EaSEzpWwBe3pegec_Kj0,959
transformers/models/bertweet/__pycache__/__init__.cpython-311.pyc,,
transformers/models/bertweet/__pycache__/tokenization_bertweet.cpython-311.pyc,,
transformers/models/bertweet/tokenization_bertweet.py,sha256=RKgK5ohzBE_RnsgL04k4l2k5shQcM3392GsbAf3vpL8,27482
transformers/models/big_bird/__init__.py,sha256=XaBDMkK9Dhqc9pVSqqn2xFCNYInFMsBpPOP8GZ0F04Q,4574
transformers/models/big_bird/__pycache__/__init__.cpython-311.pyc,,
transformers/models/big_bird/__pycache__/configuration_big_bird.cpython-311.pyc,,
transformers/models/big_bird/__pycache__/convert_bigbird_original_tf_checkpoint_to_pytorch.cpython-311.pyc,,
transformers/models/big_bird/__pycache__/modeling_big_bird.cpython-311.pyc,,
transformers/models/big_bird/__pycache__/modeling_flax_big_bird.cpython-311.pyc,,
transformers/models/big_bird/__pycache__/tokenization_big_bird.cpython-311.pyc,,
transformers/models/big_bird/__pycache__/tokenization_big_bird_fast.cpython-311.pyc,,
transformers/models/big_bird/configuration_big_bird.py,sha256=Kdh0L4MCFWA-TomkSiN9mN6omlYs7CC1tRK334uwauc,8306
transformers/models/big_bird/convert_bigbird_original_tf_checkpoint_to_pytorch.py,sha256=Y75oSwtX-d2wwOSwLo6LlUlZ9uzSEVtWwzwiJYcrXyg,2493
transformers/models/big_bird/modeling_big_bird.py,sha256=qcREr6njbuYhP54tFL1IZ93Xcbb-CC4RxpnJt0NMAhk,142461
transformers/models/big_bird/modeling_flax_big_bird.py,sha256=ePVW-6VwD8sgJYIlX4eWv0EVNaInVosJW_CtqlyzpGs,109510
transformers/models/big_bird/tokenization_big_bird.py,sha256=HjZf4dXToVYpVO008lZFMcMFiYuNKhQjSG2zM6cQUW0,14991
transformers/models/big_bird/tokenization_big_bird_fast.py,sha256=Cy8VpqLv0nnW3jnfsRvALVh35CS9blyljQwurBvmzAw,11416
transformers/models/bigbird_pegasus/__init__.py,sha256=lTnaYtQ3nRjYYND5G3wilFyh6VOOWlKjNXbsmJTo-A4,2316
transformers/models/bigbird_pegasus/__pycache__/__init__.cpython-311.pyc,,
transformers/models/bigbird_pegasus/__pycache__/configuration_bigbird_pegasus.cpython-311.pyc,,
transformers/models/bigbird_pegasus/__pycache__/convert_bigbird_pegasus_tf_to_pytorch.cpython-311.pyc,,
transformers/models/bigbird_pegasus/__pycache__/modeling_bigbird_pegasus.cpython-311.pyc,,
transformers/models/bigbird_pegasus/configuration_bigbird_pegasus.py,sha256=wQe2DFenXFvHnhh8gITYxOnI1OqV1P-hcyv5Bj7-i_8,19803
transformers/models/bigbird_pegasus/convert_bigbird_pegasus_tf_to_pytorch.py,sha256=Wc7aoNvtzxt-DPi655Kl30CgDgq_hp08psISb8dWpLU,6288
transformers/models/bigbird_pegasus/modeling_bigbird_pegasus.py,sha256=FMVIOQf0Ku0P3RLDXtyfY1a90G43VzOrWVuJpWte6Js,146081
transformers/models/biogpt/__init__.py,sha256=dV4wh5lT3U-EYdvjCy6b9lI4Lr2zIN1RqSs6Rsuc6Sg,2058
transformers/models/biogpt/__pycache__/__init__.cpython-311.pyc,,
transformers/models/biogpt/__pycache__/configuration_biogpt.cpython-311.pyc,,
transformers/models/biogpt/__pycache__/convert_biogpt_original_pytorch_checkpoint_to_pytorch.cpython-311.pyc,,
transformers/models/biogpt/__pycache__/modeling_biogpt.cpython-311.pyc,,
transformers/models/biogpt/__pycache__/tokenization_biogpt.cpython-311.pyc,,
transformers/models/biogpt/configuration_biogpt.py,sha256=d1CAfNFTf8W3Q37emleIS_ATSt2RsUTmgWATPhaMavs,6390
transformers/models/biogpt/convert_biogpt_original_pytorch_checkpoint_to_pytorch.py,sha256=5zNYzaEy7QPc99LCHTcofXSCI3tr0pzlIpFpwT1ZgN0,10578
transformers/models/biogpt/modeling_biogpt.py,sha256=y9jKLzVDHQPTguYlLjaQqEQ3zDWigG8r-yKL9nTRSjg,41160
transformers/models/biogpt/tokenization_biogpt.py,sha256=keQm4qzy2m4SZVgg4E29wCn-iin25o1Z0TyfADTG04E,13723
transformers/models/bit/__init__.py,sha256=g9Upc1daCF75FealBk9SK9FMQ-wkJMQxtjoN5mDk4cI,2244
transformers/models/bit/__pycache__/__init__.cpython-311.pyc,,
transformers/models/bit/__pycache__/configuration_bit.cpython-311.pyc,,
transformers/models/bit/__pycache__/convert_bit_to_pytorch.cpython-311.pyc,,
transformers/models/bit/__pycache__/image_processing_bit.cpython-311.pyc,,
transformers/models/bit/__pycache__/modeling_bit.cpython-311.pyc,,
transformers/models/bit/configuration_bit.py,sha256=pHWXgjyvgVYOgs1K9cHrKhFLD8GaeBigfBcVqFW3WJY,6397
transformers/models/bit/convert_bit_to_pytorch.py,sha256=Z50gXtfe6Tj44cPdIvrFRqjHPdWHdeka5oAqsTuK_ig,5955
transformers/models/bit/image_processing_bit.py,sha256=A8E6YS8dXFTSwCca0oUK4z4z4nE7RAgezcqGFu6PsMs,15819
transformers/models/bit/modeling_bit.py,sha256=Pj41T4OQaSepqZgJwO32OVqKA-vmSNuG-k908qptUWw,31850
transformers/models/blenderbot/__init__.py,sha256=nB9V1KQEetB0dazUyJ_KWDJscltclpJ6fJ746wy6zuU,4031
transformers/models/blenderbot/__pycache__/__init__.cpython-311.pyc,,
transformers/models/blenderbot/__pycache__/configuration_blenderbot.cpython-311.pyc,,
transformers/models/blenderbot/__pycache__/convert_blenderbot_original_pytorch_checkpoint_to_pytorch.cpython-311.pyc,,
transformers/models/blenderbot/__pycache__/modeling_blenderbot.cpython-311.pyc,,
transformers/models/blenderbot/__pycache__/modeling_flax_blenderbot.cpython-311.pyc,,
transformers/models/blenderbot/__pycache__/modeling_tf_blenderbot.cpython-311.pyc,,
transformers/models/blenderbot/__pycache__/tokenization_blenderbot.cpython-311.pyc,,
transformers/models/blenderbot/__pycache__/tokenization_blenderbot_fast.cpython-311.pyc,,
transformers/models/blenderbot/configuration_blenderbot.py,sha256=m2YojN7IYReFcsxg-sjlI6SDKqZbzH6oo1BIRFYnorA,19017
transformers/models/blenderbot/convert_blenderbot_original_pytorch_checkpoint_to_pytorch.py,sha256=86QBWYTeyJvxMUOfxqmGHwpDneadfqbEGSujMYw3yuU,3702
transformers/models/blenderbot/modeling_blenderbot.py,sha256=iddV_TFpMJr_Ed_fSKJ0sAlwf9dg46r1QvpfZX9P9pc,75749
transformers/models/blenderbot/modeling_flax_blenderbot.py,sha256=-2C6LxBSnWTRtoaOHDJrt9pGPLqo-7nGwCYQkJdQ4Js,64985
transformers/models/blenderbot/modeling_tf_blenderbot.py,sha256=YROTUbcA-LZRlKB0Fuo1_glkTd-Vuu45h6YOrx9ti4U,72696
transformers/models/blenderbot/tokenization_blenderbot.py,sha256=aFPN6m3CGzp4fAL5GQDDaTxyi60fEa2O-2SGDZcaLh4,19704
transformers/models/blenderbot/tokenization_blenderbot_fast.py,sha256=JjC9TmCN6hmqsF1aAWmHrC1XETZtTo9CoVD0xJ5bzws,14506
transformers/models/blenderbot_small/__init__.py,sha256=O-iMMZ9xZdyvP2PV4QYvxFcCaY6jEpKt5iyDzI_mrfM,4263
transformers/models/blenderbot_small/__pycache__/__init__.cpython-311.pyc,,
transformers/models/blenderbot_small/__pycache__/configuration_blenderbot_small.cpython-311.pyc,,
transformers/models/blenderbot_small/__pycache__/modeling_blenderbot_small.cpython-311.pyc,,
transformers/models/blenderbot_small/__pycache__/modeling_flax_blenderbot_small.cpython-311.pyc,,
transformers/models/blenderbot_small/__pycache__/modeling_tf_blenderbot_small.cpython-311.pyc,,
transformers/models/blenderbot_small/__pycache__/tokenization_blenderbot_small.cpython-311.pyc,,
transformers/models/blenderbot_small/__pycache__/tokenization_blenderbot_small_fast.cpython-311.pyc,,
transformers/models/blenderbot_small/configuration_blenderbot_small.py,sha256=BVfaMDmCo0zlZ06hmmjGUm84_gQvFNPH9MMPXSqh-hc,18480
transformers/models/blenderbot_small/modeling_blenderbot_small.py,sha256=aDv9EgAiLmuk7cT5jkchAhdmgJhJ9aEOMhzc3amH2zI,74633
transformers/models/blenderbot_small/modeling_flax_blenderbot_small.py,sha256=7S4Aw5OKwRuUErJrna1O5LNERPCtclQ4p_bFbApnLOI,65946
transformers/models/blenderbot_small/modeling_tf_blenderbot_small.py,sha256=fJBdZGkwA1VejvUF8iPZj8gUFN6Bt3knuxU-C6NsGQI,71608
transformers/models/blenderbot_small/tokenization_blenderbot_small.py,sha256=gGoLNRXM8zCX3eZxjWFIk9rxmNxmT7roP003t7XpjQE,9641
transformers/models/blenderbot_small/tokenization_blenderbot_small_fast.py,sha256=4fakxK-TssZxZhOY5VlF8wyaT-rhOuM-GMEsJt4rVok,5046
transformers/models/blip/__init__.py,sha256=1OJOhjlrdGG1mkS-46qni8DdTosNMNVWZlR9QTe1K2I,3692
transformers/models/blip/__pycache__/__init__.cpython-311.pyc,,
transformers/models/blip/__pycache__/configuration_blip.cpython-311.pyc,,
transformers/models/blip/__pycache__/convert_blip_original_pytorch_to_hf.cpython-311.pyc,,
transformers/models/blip/__pycache__/image_processing_blip.cpython-311.pyc,,
transformers/models/blip/__pycache__/modeling_blip.cpython-311.pyc,,
transformers/models/blip/__pycache__/modeling_blip_text.cpython-311.pyc,,
transformers/models/blip/__pycache__/modeling_tf_blip.cpython-311.pyc,,
transformers/models/blip/__pycache__/modeling_tf_blip_text.cpython-311.pyc,,
transformers/models/blip/__pycache__/processing_blip.cpython-311.pyc,,
transformers/models/blip/configuration_blip.py,sha256=IT5IR9CeqKDAiN5KMM2nACMSeNjQkUpxWbCIlLl49Z4,17562
transformers/models/blip/convert_blip_original_pytorch_to_hf.py,sha256=olLA10DbRUnCUOY2uHxF70u3W9wY2EBwm7eyAGfm8nM,6992
transformers/models/blip/image_processing_blip.py,sha256=zgbQMlgTs-oZPkOQ8SrlDluuocrFtQ-Ku1sC0A3nR_s,15171
transformers/models/blip/modeling_blip.py,sha256=ld_G_OUghBnb47VsjWSAy5qfWgFp5oizo157muVhu6k,61801
transformers/models/blip/modeling_blip_text.py,sha256=MMmp7Is3B_dluI1QIqGI6_yQ8EQHY34_cJBB-aQN4kE,43781
transformers/models/blip/modeling_tf_blip.py,sha256=mnkc3l__sF1aIceu8UT4irIh6dVqL8XTYVjTfBjcKzU,71749
transformers/models/blip/modeling_tf_blip_text.py,sha256=iJiYcnZpqJhoNrfUcxPxtokT_qMJGgLyz1hAcAWZ-t4,49972
transformers/models/blip/processing_blip.py,sha256=oU2XUYUq7FZy_9TiJFzlsojF0P-hTd9o93f4TNtSxxo,6205
transformers/models/blip_2/__init__.py,sha256=uEo0Z9nF4AxtGnnMSZPEvbdImyy24KR_F1YtOJj_mvY,2153
transformers/models/blip_2/__pycache__/__init__.cpython-311.pyc,,
transformers/models/blip_2/__pycache__/configuration_blip_2.cpython-311.pyc,,
transformers/models/blip_2/__pycache__/convert_blip_2_original_to_pytorch.cpython-311.pyc,,
transformers/models/blip_2/__pycache__/modeling_blip_2.cpython-311.pyc,,
transformers/models/blip_2/__pycache__/processing_blip_2.cpython-311.pyc,,
transformers/models/blip_2/configuration_blip_2.py,sha256=LsMNegy1IW5wYUhuBuQSjgsFLIq0aCWJYwI6MIBISW4,16643
transformers/models/blip_2/convert_blip_2_original_to_pytorch.py,sha256=0343xouUoM4JqP29bgDyCbNIJfSl8BO-e278133ytSA,12276
transformers/models/blip_2/modeling_blip_2.py,sha256=Zpnf1Vb2H1mIlj6QGMvPfRTqn6wLuQn7XrzTCQ8Um-E,81576
transformers/models/blip_2/processing_blip_2.py,sha256=4HnjqBRHKwuEH6NKGv0s27Tx3-alA0DYoWXJtM2gZ2I,6699
transformers/models/bloom/__init__.py,sha256=21dUYJI8_NttCwbHTXqYSl6VcqLj_PoHPPr5NRRu49E,3098
transformers/models/bloom/__pycache__/__init__.cpython-311.pyc,,
transformers/models/bloom/__pycache__/configuration_bloom.cpython-311.pyc,,
transformers/models/bloom/__pycache__/convert_bloom_original_checkpoint_to_pytorch.cpython-311.pyc,,
transformers/models/bloom/__pycache__/modeling_bloom.cpython-311.pyc,,
transformers/models/bloom/__pycache__/modeling_flax_bloom.cpython-311.pyc,,
transformers/models/bloom/__pycache__/tokenization_bloom_fast.cpython-311.pyc,,
transformers/models/bloom/configuration_bloom.py,sha256=95E05m3wMv77Tyl7xfSrFfU4jWutXboiL83oa4PQpTQ,10758
transformers/models/bloom/convert_bloom_original_checkpoint_to_pytorch.py,sha256=WvxNS5YRu84Ek1ieKkyHRKcakRbZFJr5989nEjI6qQs,10302
transformers/models/bloom/modeling_bloom.py,sha256=We5wfJPeGg3HlxBe2Usm_lEp-kMTf1FAIxu6D6rap4M,55147
transformers/models/bloom/modeling_flax_bloom.py,sha256=zBWwHZI6OBs9S1h9JSSAaEnskPKpa8jHn5AROhbLXpw,30092
transformers/models/bloom/tokenization_bloom_fast.py,sha256=bPeMw1CeJ066JpaYDBP6RtAjRutt0MMU4rCV31vghPQ,7878
transformers/models/bridgetower/__init__.py,sha256=hqrBKe3gtOVATPn1QP5BEpqSVNhJZ2x_Cg11t0Bv-lc,2864
transformers/models/bridgetower/__pycache__/__init__.cpython-311.pyc,,
transformers/models/bridgetower/__pycache__/configuration_bridgetower.cpython-311.pyc,,
transformers/models/bridgetower/__pycache__/image_processing_bridgetower.cpython-311.pyc,,
transformers/models/bridgetower/__pycache__/modeling_bridgetower.cpython-311.pyc,,
transformers/models/bridgetower/__pycache__/processing_bridgetower.cpython-311.pyc,,
transformers/models/bridgetower/configuration_bridgetower.py,sha256=U4Ha4Gds3fgLzmi-TQQGkkXzgY99nHdVQl9YtjLExmQ,16517
transformers/models/bridgetower/image_processing_bridgetower.py,sha256=Flt8o3-l9gA6mg8FGUzF1Rwtxxt2fmpzLnQ9keR7r5o,26225
transformers/models/bridgetower/modeling_bridgetower.py,sha256=VQvdDi-r0nh3nJvV4nh9-VRnjsVpmRPx5oA4YcLTIV4,88294
transformers/models/bridgetower/processing_bridgetower.py,sha256=FriChYR6CPgyDBUwOJrDlCJBuHo9RBIWXwN_NxgSGN8,5057
transformers/models/bros/__init__.py,sha256=T1UKhF6X3-gs8q9-oIzspFbX-kmnMVirfNN1yZyCT2o,2445
transformers/models/bros/__pycache__/__init__.cpython-311.pyc,,
transformers/models/bros/__pycache__/configuration_bros.cpython-311.pyc,,
transformers/models/bros/__pycache__/convert_bros_to_pytorch.cpython-311.pyc,,
transformers/models/bros/__pycache__/modeling_bros.cpython-311.pyc,,
transformers/models/bros/__pycache__/processing_bros.cpython-311.pyc,,
transformers/models/bros/configuration_bros.py,sha256=V0OrFMvH3MHoFaKnQdG7pL1elgJ-PBuZghbGHI9E_0s,6658
transformers/models/bros/convert_bros_to_pytorch.py,sha256=kxZDGzvIYxz9hbIzzJOfOj5tixji5efb2884rqwoY6A,4871
transformers/models/bros/modeling_bros.py,sha256=0GhM7g6oz7qvMX2dnQl4Rbi9hGskHfLa73JxlhfGPso,58023
transformers/models/bros/processing_bros.py,sha256=FQUu5czHHvQzZ1P5N9GhfjZu4cmZw_mYKuX0VNjrB54,4193
transformers/models/byt5/__init__.py,sha256=06YhQd8TFNbc9lU5qzERZUdcSWIFxOeBOaqQh6S4WC4,942
transformers/models/byt5/__pycache__/__init__.cpython-311.pyc,,
transformers/models/byt5/__pycache__/convert_byt5_original_tf_checkpoint_to_pytorch.cpython-311.pyc,,
transformers/models/byt5/__pycache__/tokenization_byt5.cpython-311.pyc,,
transformers/models/byt5/convert_byt5_original_tf_checkpoint_to_pytorch.py,sha256=83tKCwYRSRW7zXtm9cmszqtPhpw44cH8Cj0SWUSBgN0,2120
transformers/models/byt5/tokenization_byt5.py,sha256=DF8GtvaS6EpR1UqaQEh6IRaT0lRQD3CKineT6ngRy_4,10031
transformers/models/camembert/__init__.py,sha256=UBlxBknmDgdOkelwnQSGkAejq1meoGd2CgmQtGayhII,4443
transformers/models/camembert/__pycache__/__init__.cpython-311.pyc,,
transformers/models/camembert/__pycache__/configuration_camembert.cpython-311.pyc,,
transformers/models/camembert/__pycache__/modeling_camembert.cpython-311.pyc,,
transformers/models/camembert/__pycache__/modeling_tf_camembert.cpython-311.pyc,,
transformers/models/camembert/__pycache__/tokenization_camembert.cpython-311.pyc,,
transformers/models/camembert/__pycache__/tokenization_camembert_fast.cpython-311.pyc,,
transformers/models/camembert/configuration_camembert.py,sha256=om5qK7TwsE5LCYW0JHiQH5z6OZ_iD259xnGlkGfcIr8,7789
transformers/models/camembert/modeling_camembert.py,sha256=nPLaxgEiYr5kq9w2kzEyqpBWXyV84RbdMF_mHI6HjjI,72660
transformers/models/camembert/modeling_tf_camembert.py,sha256=2bhQhWFE2sscA4i0iFW83CKE8ydOt5TwECNEOZruSdw,81663
transformers/models/camembert/tokenization_camembert.py,sha256=JhddSOrTGmjYVusRld-f3VyJ3zyQO1wWjgOtGCIEwVM,14368
transformers/models/camembert/tokenization_camembert_fast.py,sha256=vgTyUk5rABosuB12_JKtJ1GgiCLHWh1pdV36aYJSq9A,8809
transformers/models/canine/__init__.py,sha256=7AYQEAa5qVyCZ73fkPg0yXl5-YpLg55i3RpY1J3KulM,2272
transformers/models/canine/__pycache__/__init__.cpython-311.pyc,,
transformers/models/canine/__pycache__/configuration_canine.cpython-311.pyc,,
transformers/models/canine/__pycache__/convert_canine_original_tf_checkpoint_to_pytorch.cpython-311.pyc,,
transformers/models/canine/__pycache__/modeling_canine.cpython-311.pyc,,
transformers/models/canine/__pycache__/tokenization_canine.cpython-311.pyc,,
transformers/models/canine/configuration_canine.py,sha256=Fej_3BNyWvLMQQPOvhd7acq7gso8MWz0478OeBE78TA,6765
transformers/models/canine/convert_canine_original_tf_checkpoint_to_pytorch.py,sha256=vGfFFo49PfyXtZdgIQHRcqMPcbmF8aMEC9DiHMyEsn0,2117
transformers/models/canine/modeling_canine.py,sha256=H9pgMkzEChdPLhRTfNdUiHaTTHJD9r8Yk0oBt7tdfMY,73565
transformers/models/canine/tokenization_canine.py,sha256=jr6XQv3grxNmVHn_p4M_mlxE8jh0kz1gDji_OC1xEOY,9430
transformers/models/chinese_clip/__init__.py,sha256=SNfgqh2dGAcoNXXZx-8XFNO3UDriK_yV7vf-M23Qnfk,2919
transformers/models/chinese_clip/__pycache__/__init__.cpython-311.pyc,,
transformers/models/chinese_clip/__pycache__/configuration_chinese_clip.cpython-311.pyc,,
transformers/models/chinese_clip/__pycache__/convert_chinese_clip_original_pytorch_to_hf.cpython-311.pyc,,
transformers/models/chinese_clip/__pycache__/feature_extraction_chinese_clip.cpython-311.pyc,,
transformers/models/chinese_clip/__pycache__/image_processing_chinese_clip.cpython-311.pyc,,
transformers/models/chinese_clip/__pycache__/modeling_chinese_clip.cpython-311.pyc,,
transformers/models/chinese_clip/__pycache__/processing_chinese_clip.cpython-311.pyc,,
transformers/models/chinese_clip/configuration_chinese_clip.py,sha256=5Ts24wz8ULUefBXYg_umYQxyFcFWZ9ncOtSU4Y0jOcY,22527
transformers/models/chinese_clip/convert_chinese_clip_original_pytorch_to_hf.py,sha256=-0bnVcdXxStmygkyj6S1hIGCVbpEbe3cM7AoshHH5ZE,5069
transformers/models/chinese_clip/feature_extraction_chinese_clip.py,sha256=znduyOyJ-Qdx4MC5CPb6MFZ-Wrb5PLgHWRh0xfoULR0,1247
transformers/models/chinese_clip/image_processing_chinese_clip.py,sha256=XtlBloE45CudC2LyS9nQi9r7cywaZeLfA30HDXytu1E,15370
transformers/models/chinese_clip/modeling_chinese_clip.py,sha256=fjw2AhKhgYGyn7jfDL_t7rRJoYW5juHCMQwfbVr9rW0,73159
transformers/models/chinese_clip/processing_chinese_clip.py,sha256=qBYQRHQFIeCIzagJfn4KbH9qSodPmR4llukXWZx5oj8,6812
transformers/models/clap/__init__.py,sha256=MOoheQt_0P8KCRlN4QiWyzrskH9dUUfSSF_pZpJEchw,2322
transformers/models/clap/__pycache__/__init__.cpython-311.pyc,,
transformers/models/clap/__pycache__/configuration_clap.cpython-311.pyc,,
transformers/models/clap/__pycache__/convert_clap_original_pytorch_to_hf.cpython-311.pyc,,
transformers/models/clap/__pycache__/feature_extraction_clap.cpython-311.pyc,,
transformers/models/clap/__pycache__/modeling_clap.cpython-311.pyc,,
transformers/models/clap/__pycache__/processing_clap.cpython-311.pyc,,
transformers/models/clap/configuration_clap.py,sha256=Vxf84cxCKWBAVGMeFLF9BBxw9Zyswp_-AdMY45QaYHs,20636
transformers/models/clap/convert_clap_original_pytorch_to_hf.py,sha256=FqHoVAYXIzfUY9342azwlm9zfSP7QdS8p-u9Q6RE_K4,5149
transformers/models/clap/feature_extraction_clap.py,sha256=rN5ZDLkqtfddEsT6kcFW2OVe7nehoPUE4HM7T3ua5us,18692
transformers/models/clap/modeling_clap.py,sha256=Ya3yPH2ey2jU2k0w8_M-hguSisDPDzDU-0d2aALO8DY,104771
transformers/models/clap/processing_clap.py,sha256=QpXK1vA69fFLzQesu-qetj22YiV_BiO-0cpatq8ViKo,5705
transformers/models/clip/__init__.py,sha256=4_WowO4qRlP_COGzdscG6QH0pZU-Q5a38GsrtBTlSHs,5193
transformers/models/clip/__pycache__/__init__.cpython-311.pyc,,
transformers/models/clip/__pycache__/configuration_clip.cpython-311.pyc,,
transformers/models/clip/__pycache__/convert_clip_original_pytorch_to_hf.cpython-311.pyc,,
transformers/models/clip/__pycache__/feature_extraction_clip.cpython-311.pyc,,
transformers/models/clip/__pycache__/image_processing_clip.cpython-311.pyc,,
transformers/models/clip/__pycache__/modeling_clip.cpython-311.pyc,,
transformers/models/clip/__pycache__/modeling_flax_clip.cpython-311.pyc,,
transformers/models/clip/__pycache__/modeling_tf_clip.cpython-311.pyc,,
transformers/models/clip/__pycache__/processing_clip.cpython-311.pyc,,
transformers/models/clip/__pycache__/tokenization_clip.cpython-311.pyc,,
transformers/models/clip/__pycache__/tokenization_clip_fast.cpython-311.pyc,,
transformers/models/clip/configuration_clip.py,sha256=ehKGi1zRD5RYSgWWYIjiT17R8R6sL6Nr_FCgHsIOueM,21123
transformers/models/clip/convert_clip_original_pytorch_to_hf.py,sha256=3_eKm-gpqB5DNvL8b3OKSUrjG7YFxqrQl1DBdL_IboA,5306
transformers/models/clip/feature_extraction_clip.py,sha256=hgRfD-s9DoI7tzDLAJ0EW3rSbkY9dOiGqoGClOiRiBM,1172
transformers/models/clip/image_processing_clip.py,sha256=hjE4EJ4jydmtYp-DXagTRfZeO6Eco7SjKWN83HM1ZV0,15936
transformers/models/clip/modeling_clip.py,sha256=AAA0sndswqqAfRrG_1b8YK9HY6vH7PEXBkDIctsYArA,61243
transformers/models/clip/modeling_flax_clip.py,sha256=4uabm9t6i4bnqRR3DZrGk7X1NcaV78L6b6E6i0Gkl2U,50517
transformers/models/clip/modeling_tf_clip.py,sha256=stRgBojQLsuqrT8f1AQXv6Vuwa_N1D5-j70pyvXnswY,60514
transformers/models/clip/processing_clip.py,sha256=aZe5MxUDCc5GuxjeV7nJq22G6KRsjvhdbNjAr3nI0OU,6879
transformers/models/clip/tokenization_clip.py,sha256=EddC7lDEThyvOs3KueGoZF5cHBSJKBEttvRKgrYmRX8,21202
transformers/models/clip/tokenization_clip_fast.py,sha256=97dwKorumSFNRgCrs-EQLJJUEJPn8yNx3IgilD_4aT4,7273
transformers/models/clipseg/__init__.py,sha256=XmEjQiZo2l7fQvPX8Tm_rsd3wItyBrBg3gtvDAkOTZM,2179
transformers/models/clipseg/__pycache__/__init__.cpython-311.pyc,,
transformers/models/clipseg/__pycache__/configuration_clipseg.cpython-311.pyc,,
transformers/models/clipseg/__pycache__/convert_clipseg_original_pytorch_to_hf.cpython-311.pyc,,
transformers/models/clipseg/__pycache__/modeling_clipseg.cpython-311.pyc,,
transformers/models/clipseg/__pycache__/processing_clipseg.cpython-311.pyc,,
transformers/models/clipseg/configuration_clipseg.py,sha256=zYzu3Mlnc2n5ZRakpu8wrXD081-EDHMVzlsn6m38Oq0,21071
transformers/models/clipseg/convert_clipseg_original_pytorch_to_hf.py,sha256=kYyPxdpdtt6nSxD65tXUTMbN0xPyyzjfTOOMbQ8OL0Y,11114
transformers/models/clipseg/modeling_clipseg.py,sha256=VVZ3FM1Eq4IKSdtagzZo5QR07ckFC3uYUeOUaXSb_oQ,64570
transformers/models/clipseg/processing_clipseg.py,sha256=TjJdEr9E-Pfn9cum23gcgpMgHgCD5riXATrtPbSTpTk,7896
transformers/models/clvp/__init__.py,sha256=VUtmHMpw33TwZIXIYxV_ImQSKobm9ItMAZnw87Ke4Dg,2396
transformers/models/clvp/__pycache__/__init__.cpython-311.pyc,,
transformers/models/clvp/__pycache__/configuration_clvp.cpython-311.pyc,,
transformers/models/clvp/__pycache__/convert_clvp_to_hf.cpython-311.pyc,,
transformers/models/clvp/__pycache__/feature_extraction_clvp.cpython-311.pyc,,
transformers/models/clvp/__pycache__/modeling_clvp.cpython-311.pyc,,
transformers/models/clvp/__pycache__/number_normalizer.cpython-311.pyc,,
transformers/models/clvp/__pycache__/processing_clvp.cpython-311.pyc,,
transformers/models/clvp/__pycache__/tokenization_clvp.cpython-311.pyc,,
transformers/models/clvp/configuration_clvp.py,sha256=s5f6PchOKe-DJOXcnq7IU7PxNdshSKL8aoETxfovFZs,21067
transformers/models/clvp/convert_clvp_to_hf.py,sha256=1WYf_vwj1CeQ_VU9iMqu7Grr_MmlAsaKEK1Lojk6yM4,9326
transformers/models/clvp/feature_extraction_clvp.py,sha256=rq0Ygr1pCT1DK4mMzv6f4b06zgXeAwT29GYSzu1Fprw,10935
transformers/models/clvp/modeling_clvp.py,sha256=cgDvqux42ThczEX6D9D_zgXbkV3GkzZoqtB3Bd_uQbQ,91254
transformers/models/clvp/number_normalizer.py,sha256=gJb8KFEdsDWgzubs6cTn1i2q2R1fHCYs9C3k2hBoCyU,8857
transformers/models/clvp/processing_clvp.py,sha256=zn13cG8abp5_ZFhoL_QQxcoTRS57rLKXBh9H5KAUBxk,3605
transformers/models/clvp/tokenization_clvp.py,sha256=YXEc0bqH1AD54UNXFXvWQib_9l21cMhCIlDFDRbqeW8,15252
transformers/models/code_llama/__init__.py,sha256=S1xpVZ6cLZxN1ADmRNp7dCsoKQKnb3-Tw-HkHjHcnBY,1882
transformers/models/code_llama/__pycache__/__init__.cpython-311.pyc,,
transformers/models/code_llama/__pycache__/tokenization_code_llama.cpython-311.pyc,,
transformers/models/code_llama/__pycache__/tokenization_code_llama_fast.cpython-311.pyc,,
transformers/models/code_llama/tokenization_code_llama.py,sha256=JFhJvXNKhjlf6DXXnahNo6qWvN0K-umV4CdeFcZ0xkw,23568
transformers/models/code_llama/tokenization_code_llama_fast.py,sha256=REf6FgNg7WbBovoDFKJey0VekXWMFVJGMVfOwHUCZaU,19758
transformers/models/codegen/__init__.py,sha256=Zb96Hyd6W5WaIc7l-psLnEhYjANmwxzZlAR-g37xKkI,2443
transformers/models/codegen/__pycache__/__init__.cpython-311.pyc,,
transformers/models/codegen/__pycache__/configuration_codegen.cpython-311.pyc,,
transformers/models/codegen/__pycache__/modeling_codegen.cpython-311.pyc,,
transformers/models/codegen/__pycache__/tokenization_codegen.cpython-311.pyc,,
transformers/models/codegen/__pycache__/tokenization_codegen_fast.cpython-311.pyc,,
transformers/models/codegen/configuration_codegen.py,sha256=h38yq3btP-MUu7eP3PQmj16JbvV-ZR3YtFJcpTnMy1o,10892
transformers/models/codegen/modeling_codegen.py,sha256=8MXuzwPuSkn9KwebitqGry-EfVWWt24w2rrqip1utlI,31739
transformers/models/codegen/tokenization_codegen.py,sha256=9GiXRHwUL1d8K7CYY94--7aXYlDEHCOC5U0UrEowzps,15509
transformers/models/codegen/tokenization_codegen_fast.py,sha256=XycmVo4GJpMt04VXJ1SbvzTExQajuDvISyoshK9ElBw,10467
transformers/models/conditional_detr/__init__.py,sha256=aFyaZb6RKCOPPf_kPK83WhyaDO5NFiox70ZbMe5gxvw,2828
transformers/models/conditional_detr/__pycache__/__init__.cpython-311.pyc,,
transformers/models/conditional_detr/__pycache__/configuration_conditional_detr.cpython-311.pyc,,
transformers/models/conditional_detr/__pycache__/convert_conditional_detr_original_pytorch_checkpoint_to_pytorch.cpython-311.pyc,,
transformers/models/conditional_detr/__pycache__/feature_extraction_conditional_detr.cpython-311.pyc,,
transformers/models/conditional_detr/__pycache__/image_processing_conditional_detr.cpython-311.pyc,,
transformers/models/conditional_detr/__pycache__/modeling_conditional_detr.cpython-311.pyc,,
transformers/models/conditional_detr/configuration_conditional_detr.py,sha256=MAZ39E65QBSg1hk0phEZZA0Yp5FxYbirXzXMM63uG_I,13400
transformers/models/conditional_detr/convert_conditional_detr_original_pytorch_checkpoint_to_pytorch.py,sha256=O0da9fOwcPhpQSaa0Ci34txn-9YF9fAMGvRHK0dCk3Q,15930
transformers/models/conditional_detr/feature_extraction_conditional_detr.py,sha256=opHXZebd-6cMJnO6RbrAdmVYmnkNzK1up_fPlHTSLrk,1553
transformers/models/conditional_detr/image_processing_conditional_detr.py,sha256=pg7wsDjt1mvWlYxzvO5eJjPNMDdoGTXqZs9sUFyFisk,80615
transformers/models/conditional_detr/modeling_conditional_detr.py,sha256=asNTkqIdw3kI1-YQhR_Uz-M9Tr5GJdb0lrV-RU0Y0pA,132222
transformers/models/convbert/__init__.py,sha256=wkLfe2pjkQmfQ0sd28ixnL1__YYimYDtT5FP1bRD0YE,4069
transformers/models/convbert/__pycache__/__init__.cpython-311.pyc,,
transformers/models/convbert/__pycache__/configuration_convbert.cpython-311.pyc,,
transformers/models/convbert/__pycache__/convert_convbert_original_tf1_checkpoint_to_pytorch_and_tf2.cpython-311.pyc,,
transformers/models/convbert/__pycache__/modeling_convbert.cpython-311.pyc,,
transformers/models/convbert/__pycache__/modeling_tf_convbert.cpython-311.pyc,,
transformers/models/convbert/__pycache__/tokenization_convbert.cpython-311.pyc,,
transformers/models/convbert/__pycache__/tokenization_convbert_fast.cpython-311.pyc,,
transformers/models/convbert/configuration_convbert.py,sha256=eHY0rBE2RVGRXA9-hiPnqslXRwwXlFVCK243Tb3lJG0,7311
transformers/models/convbert/convert_convbert_original_tf1_checkpoint_to_pytorch_and_tf2.py,sha256=vTZyGhG9v7o4rDuP9-xM26gX1EzlCda7Sn_ELT9n3Gk,2108
transformers/models/convbert/modeling_convbert.py,sha256=GSUxIdPK42DKLY4BNT1Pri7AU-W9YmrJKTd9SrsTzfU,58507
transformers/models/convbert/modeling_tf_convbert.py,sha256=rIcf4kSG60pYaAyomiJnyajXhZ1aZv6M8vF6Mm25YEM,61595
transformers/models/convbert/tokenization_convbert.py,sha256=PZWQ5SRiN8OlpN8TFW9SwBGmgwzLdtcLj5FmepOKiWQ,21967
transformers/models/convbert/tokenization_convbert_fast.py,sha256=OyqpBEU-CsCMDUFFuoYjCGOaAX1AHmlxF5lIT58_C9M,8777
transformers/models/convnext/__init__.py,sha256=K8TKvIQuVogfZPifZjZeCwGJKA_vnASMr7LWx4CggqA,3150
transformers/models/convnext/__pycache__/__init__.cpython-311.pyc,,
transformers/models/convnext/__pycache__/configuration_convnext.cpython-311.pyc,,
transformers/models/convnext/__pycache__/convert_convnext_to_pytorch.cpython-311.pyc,,
transformers/models/convnext/__pycache__/feature_extraction_convnext.cpython-311.pyc,,
transformers/models/convnext/__pycache__/image_processing_convnext.cpython-311.pyc,,
transformers/models/convnext/__pycache__/modeling_convnext.cpython-311.pyc,,
transformers/models/convnext/__pycache__/modeling_tf_convnext.cpython-311.pyc,,
transformers/models/convnext/configuration_convnext.py,sha256=VvyMLpIFXwoyIGuVpO9FZ36K1zhePrbr6D4HW12yTZo,6364
transformers/models/convnext/convert_convnext_to_pytorch.py,sha256=6QenssUB5Op--7nvPTPjRUEozX-4kljweJvc-blSpnQ,10220
transformers/models/convnext/feature_extraction_convnext.py,sha256=TyFMochXYlN3vKH7Ud0nXagzxGhio2Bfma4ofceR_zA,1200
transformers/models/convnext/image_processing_convnext.py,sha256=DcJW53QZ70acoPgSFba9sBihlrnIuEO3N1-tE53F5Qw,15773
transformers/models/convnext/modeling_convnext.py,sha256=ASWQlDANTUrtDyYv3QszasjqF388eQjb3NPvCFrGbEA,21942
transformers/models/convnext/modeling_tf_convnext.py,sha256=E21qdpGpPYVH4xJcMyjw5tdTCpoVobcjEcqhhtSID90,27195
transformers/models/convnextv2/__init__.py,sha256=JmOrlR6-q7yFZqSG7obPonJSuSpLVhTOIax7X-3FDwY,2825
transformers/models/convnextv2/__pycache__/__init__.cpython-311.pyc,,
transformers/models/convnextv2/__pycache__/configuration_convnextv2.cpython-311.pyc,,
transformers/models/convnextv2/__pycache__/convert_convnextv2_to_pytorch.cpython-311.pyc,,
transformers/models/convnextv2/__pycache__/modeling_convnextv2.cpython-311.pyc,,
transformers/models/convnextv2/__pycache__/modeling_tf_convnextv2.cpython-311.pyc,,
transformers/models/convnextv2/configuration_convnextv2.py,sha256=_zweey1P5JcSKCwnuLqqJHlwqXuuORngXamxl2O7Uzo,5593
transformers/models/convnextv2/convert_convnextv2_to_pytorch.py,sha256=Yswl5UwLP0t0tC8O2b8wix2beNaMtPy7areKFCuEccg,12473
transformers/models/convnextv2/modeling_convnextv2.py,sha256=C5n4A_CE38KLzWgvdH_SDlH5elenPo4J0O3YQi0NoNo,23723
transformers/models/convnextv2/modeling_tf_convnextv2.py,sha256=ut2P18FJeu0fy3MdS6SSrH-4KQ2K7-KYQznCOn5ON4o,27765
transformers/models/cpm/__init__.py,sha256=9SmT0nL5DgGjXxmPaQFi9GGPXWuhFic2DX2GsF-BynQ,1816
transformers/models/cpm/__pycache__/__init__.cpython-311.pyc,,
transformers/models/cpm/__pycache__/tokenization_cpm.cpython-311.pyc,,
transformers/models/cpm/__pycache__/tokenization_cpm_fast.cpython-311.pyc,,
transformers/models/cpm/tokenization_cpm.py,sha256=fnIP4gBquryMnWxmi6waP6TFoOD-PQGnW0A8QRkYkhk,15257
transformers/models/cpm/tokenization_cpm_fast.py,sha256=K7za8eoExrXm8ZsH_TOY1IXnZ7PwzJeGrS_seXBY39c,10741
transformers/models/cpmant/__init__.py,sha256=5hTyJtQwoONrf9-BMvt_nT_bovkj9avoSk9UdLCvW4w,2117
transformers/models/cpmant/__pycache__/__init__.cpython-311.pyc,,
transformers/models/cpmant/__pycache__/configuration_cpmant.cpython-311.pyc,,
transformers/models/cpmant/__pycache__/modeling_cpmant.cpython-311.pyc,,
transformers/models/cpmant/__pycache__/tokenization_cpmant.cpython-311.pyc,,
transformers/models/cpmant/configuration_cpmant.py,sha256=zybAtR4-5OW9DXPljm-nrp-RHVTfM30F6g2o9JXLUrc,5330
transformers/models/cpmant/modeling_cpmant.py,sha256=g6RiojhjKMr6NE9z8SmehocOeV8FBC9wAA8hBtAsPkc,37560
transformers/models/cpmant/tokenization_cpmant.py,sha256=ESWl2D6v9STC9QArkFX-SvnFFj9qjyZKNK-5fBO_xxU,10075
transformers/models/ctrl/__init__.py,sha256=-Sa7nUQv3Cxj4KLXFaBtnkG_r3uIdpbU_Q_TmMl1lKM,2688
transformers/models/ctrl/__pycache__/__init__.cpython-311.pyc,,
transformers/models/ctrl/__pycache__/configuration_ctrl.cpython-311.pyc,,
transformers/models/ctrl/__pycache__/modeling_ctrl.cpython-311.pyc,,
transformers/models/ctrl/__pycache__/modeling_tf_ctrl.cpython-311.pyc,,
transformers/models/ctrl/__pycache__/tokenization_ctrl.cpython-311.pyc,,
transformers/models/ctrl/configuration_ctrl.py,sha256=nQu7QgY0XgPDa3yHewO9YnX1aSLzv-YS3Z8gpPIloRk,4789
transformers/models/ctrl/modeling_ctrl.py,sha256=0u2yQVeCqBdEql1YQkzkV0sP-2zYreXHGV5H1nhfres,35736
transformers/models/ctrl/modeling_tf_ctrl.py,sha256=dE3XdBFLVdHg9dyQQCDU1fktxhcdct8Z48qbzCH_kNI,39780
transformers/models/ctrl/tokenization_ctrl.py,sha256=saxZaLtEZRADve2UuADYYagNgcN3Pdn3hiC8hkM-z5c,8523
transformers/models/cvt/__init__.py,sha256=dk1C0zaBDT0dl7BYLe1mRb85Dp_a_IHomekjOjYPHJ8,2434
transformers/models/cvt/__pycache__/__init__.cpython-311.pyc,,
transformers/models/cvt/__pycache__/configuration_cvt.cpython-311.pyc,,
transformers/models/cvt/__pycache__/convert_cvt_original_pytorch_checkpoint_to_pytorch.cpython-311.pyc,,
transformers/models/cvt/__pycache__/modeling_cvt.cpython-311.pyc,,
transformers/models/cvt/__pycache__/modeling_tf_cvt.cpython-311.pyc,,
transformers/models/cvt/configuration_cvt.py,sha256=8KFh8F1jxe9EFX1Ke0jfjLEvJMG4_S5LgGKBfGkXXog,6861
transformers/models/cvt/convert_cvt_original_pytorch_checkpoint_to_pytorch.py,sha256=miqNzPWIAjwl5rtkWOmRUJl-18X-9cRXXWb9M3ScHI4,13570
transformers/models/cvt/modeling_cvt.py,sha256=oVBY0LhQ6lTYuICgBwsCF_Irm--5DlXKR0P7xcFeOPQ,28948
transformers/models/cvt/modeling_tf_cvt.py,sha256=seV777z93aHiY2xCOKE-GT7-qRRGG_9_l9HAONsMNzo,43746
transformers/models/data2vec/__init__.py,sha256=1Pq8n8wNccLQ76e8oNDwOemqh-E0eMKpr6tdt2ata8w,4933
transformers/models/data2vec/__pycache__/__init__.cpython-311.pyc,,
transformers/models/data2vec/__pycache__/configuration_data2vec_audio.cpython-311.pyc,,
transformers/models/data2vec/__pycache__/configuration_data2vec_text.cpython-311.pyc,,
transformers/models/data2vec/__pycache__/configuration_data2vec_vision.cpython-311.pyc,,
transformers/models/data2vec/__pycache__/convert_data2vec_audio_original_pytorch_checkpoint_to_pytorch.cpython-311.pyc,,
transformers/models/data2vec/__pycache__/convert_data2vec_text_original_pytorch_checkpoint_to_pytorch.cpython-311.pyc,,
transformers/models/data2vec/__pycache__/convert_data2vec_vision_original_pytorch_checkpoint_to_pytorch.cpython-311.pyc,,
transformers/models/data2vec/__pycache__/modeling_data2vec_audio.cpython-311.pyc,,
transformers/models/data2vec/__pycache__/modeling_data2vec_text.cpython-311.pyc,,
transformers/models/data2vec/__pycache__/modeling_data2vec_vision.cpython-311.pyc,,
transformers/models/data2vec/__pycache__/modeling_tf_data2vec_vision.cpython-311.pyc,,
transformers/models/data2vec/configuration_data2vec_audio.py,sha256=FdLRyMjwHwpQAdOi6BqWF_u-wBAT9lO1vpFf4XZi-1s,16584
transformers/models/data2vec/configuration_data2vec_text.py,sha256=cX0pr2HTyus3TYjJpu4dmdF1jibViHQqpve8d2pJP5M,7421
transformers/models/data2vec/configuration_data2vec_vision.py,sha256=sohmtbN8PqkgDgCLeCqsXfi8d8oImLLlhx0wcXrt7fI,9433
transformers/models/data2vec/convert_data2vec_audio_original_pytorch_checkpoint_to_pytorch.py,sha256=czYaA_tlF-uCDMFV1RFaL5g8QJRozBiVUCu9nuhLcZU,10858
transformers/models/data2vec/convert_data2vec_text_original_pytorch_checkpoint_to_pytorch.py,sha256=4scSS9J1m1xG6sy_BLvjbCeEL8Ke2RhNtNqsVt2zUCI,9580
transformers/models/data2vec/convert_data2vec_vision_original_pytorch_checkpoint_to_pytorch.py,sha256=qKjV-jqIgL-6i17m4yQLW_93SbPpGxQnvHjuy1xVxQU,15340
transformers/models/data2vec/modeling_data2vec_audio.py,sha256=qXSDAXIz49pPeBs_UTtup22u2CYfgdgHkHtEyqZh_tQ,65600
transformers/models/data2vec/modeling_data2vec_text.py,sha256=01ZGcQFC2kUgZlgZLQ4E7M3X3AYSDBhouPRoEHKbnFI,71344
transformers/models/data2vec/modeling_data2vec_vision.py,sha256=b77WwEGzhbZY0gt8CO3sYjCghi6HUV21sgzfDM4n4ak,53838
transformers/models/data2vec/modeling_tf_data2vec_vision.py,sha256=bNUXAlwJxNC3v9sr7czPwUqVbhSIATk3gwLLKCaZwm4,73546
transformers/models/deberta/__init__.py,sha256=azYcZaZso6o7T3SDyUrczkAZ4ZzgDh4hcPoT0bgPRSE,3677
transformers/models/deberta/__pycache__/__init__.cpython-311.pyc,,
transformers/models/deberta/__pycache__/configuration_deberta.cpython-311.pyc,,
transformers/models/deberta/__pycache__/modeling_deberta.cpython-311.pyc,,
transformers/models/deberta/__pycache__/modeling_tf_deberta.cpython-311.pyc,,
transformers/models/deberta/__pycache__/tokenization_deberta.cpython-311.pyc,,
transformers/models/deberta/__pycache__/tokenization_deberta_fast.cpython-311.pyc,,
transformers/models/deberta/configuration_deberta.py,sha256=FSPMuu2tGc5qSZm_bTxpDyo2A2mPQJeyFtvs9g6pPJw,9394
transformers/models/deberta/modeling_deberta.py,sha256=TIV-j31lWPgCHVhuucTs4GAiG1Yp-bL9TiBe2hZQvSg,58066
transformers/models/deberta/modeling_tf_deberta.py,sha256=Uo-sM88WyT63-10CMj4-Tq-ryJ3iGRKuivCD_nqDYkg,68988
transformers/models/deberta/tokenization_deberta.py,sha256=SHsmOHhItaBAHByQNQM3_r0E78uufAHbuXB8qvWCzps,19111
transformers/models/deberta/tokenization_deberta_fast.py,sha256=p9GyZ8E1-JRQ7Ap5UVZGGKF6B0yXtkOGTWkt8N1nMgo,12781
transformers/models/deberta_v2/__init__.py,sha256=afG1pzu0TIczwpL6vPJXnwkO5Sn9R5qrMvjaTzysH1U,3981
transformers/models/deberta_v2/__pycache__/__init__.cpython-311.pyc,,
transformers/models/deberta_v2/__pycache__/configuration_deberta_v2.cpython-311.pyc,,
transformers/models/deberta_v2/__pycache__/modeling_deberta_v2.cpython-311.pyc,,
transformers/models/deberta_v2/__pycache__/modeling_tf_deberta_v2.cpython-311.pyc,,
transformers/models/deberta_v2/__pycache__/tokenization_deberta_v2.cpython-311.pyc,,
transformers/models/deberta_v2/__pycache__/tokenization_deberta_v2_fast.cpython-311.pyc,,
transformers/models/deberta_v2/configuration_deberta_v2.py,sha256=7WyxCnBsJ1Kf3grwN19m6SHia_9onZl9WADAO0F7Xss,9180
transformers/models/deberta_v2/modeling_deberta_v2.py,sha256=Oglza986w7VrrR_SHXC7gxJILLJluZ3mz4gNOb34nMw,67591
transformers/models/deberta_v2/modeling_tf_deberta_v2.py,sha256=Nd9ZB68jT99faY6tTHVecDLzNxAwgPoWzSABCFXm-yA,81295
transformers/models/deberta_v2/tokenization_deberta_v2.py,sha256=u7NFCDR9H0fryg2ddJu7IXAEadufJYCn3TdmMh9dQ5w,22003
transformers/models/deberta_v2/tokenization_deberta_v2_fast.py,sha256=Jz-3J-FOx0kp0aMT5EhtHCnZCLI4_LPL5ABTMfIn0A0,11058
transformers/models/decision_transformer/__init__.py,sha256=geVmBybTFepK0keGuRrLYl6hwZhT5I2BK4dfeYFDqWw,2124
transformers/models/decision_transformer/__pycache__/__init__.cpython-311.pyc,,
transformers/models/decision_transformer/__pycache__/configuration_decision_transformer.cpython-311.pyc,,
transformers/models/decision_transformer/__pycache__/modeling_decision_transformer.cpython-311.pyc,,
transformers/models/decision_transformer/configuration_decision_transformer.py,sha256=lI5Yh4GNB4nG-96XfywcCwKVm-yzvPHme_EndgkfJCQ,7321
transformers/models/decision_transformer/modeling_decision_transformer.py,sha256=pXBWeIWXY25OiZsUmdQXDIKFYLhtZjgiEuARtTE0nhU,43157
transformers/models/deformable_detr/__init__.py,sha256=jwNDOMAnuD5Efvu3FYvA1H9JJB9QBb6NpoaoCCJU1Ns,2599
transformers/models/deformable_detr/__pycache__/__init__.cpython-311.pyc,,
transformers/models/deformable_detr/__pycache__/configuration_deformable_detr.cpython-311.pyc,,
transformers/models/deformable_detr/__pycache__/convert_deformable_detr_to_pytorch.cpython-311.pyc,,
transformers/models/deformable_detr/__pycache__/feature_extraction_deformable_detr.cpython-311.pyc,,
transformers/models/deformable_detr/__pycache__/image_processing_deformable_detr.cpython-311.pyc,,
transformers/models/deformable_detr/__pycache__/load_custom.cpython-311.pyc,,
transformers/models/deformable_detr/__pycache__/modeling_deformable_detr.cpython-311.pyc,,
transformers/models/deformable_detr/configuration_deformable_detr.py,sha256=mg-utuggN1DE847OetcOgdXb88tYVTTo_J2_pbMloCc,14671
transformers/models/deformable_detr/convert_deformable_detr_to_pytorch.py,sha256=264dW2XMu4QcgO6IaMa4eOjrIHErz-RLw_9FLD6C46Q,9477
transformers/models/deformable_detr/feature_extraction_deformable_detr.py,sha256=GwYaT6B6-Fu2Jbl8CALodb7Lz4gr9jSRfq01QfLQc7Y,1546
transformers/models/deformable_detr/image_processing_deformable_detr.py,sha256=HSqIvuiS7pWIXUQUaS3nImPYgYvpNFiwfuY5vgppXPM,68047
transformers/models/deformable_detr/load_custom.py,sha256=0jENX1Mkz0bYlyUYYgp1YYEpQ8r32degzoL4CmVGe3w,1559
transformers/models/deformable_detr/modeling_deformable_detr.py,sha256=Q173QXAy8z-GW-KKKGw2Rws7jzC9FtlU3pjjcMwbDac,121309
transformers/models/deit/__init__.py,sha256=ZVWuhflGzxt-AZ2wcCTX0JfXBY3puVD_O9WkNqfOH1A,3486
transformers/models/deit/__pycache__/__init__.cpython-311.pyc,,
transformers/models/deit/__pycache__/configuration_deit.cpython-311.pyc,,
transformers/models/deit/__pycache__/convert_deit_timm_to_pytorch.cpython-311.pyc,,
transformers/models/deit/__pycache__/feature_extraction_deit.cpython-311.pyc,,
transformers/models/deit/__pycache__/image_processing_deit.cpython-311.pyc,,
transformers/models/deit/__pycache__/modeling_deit.cpython-311.pyc,,
transformers/models/deit/__pycache__/modeling_tf_deit.cpython-311.pyc,,
transformers/models/deit/configuration_deit.py,sha256=omMgnN8FcGjOHdFqQYMbTHywoJQP8lZVV11r4fR7kXw,5955
transformers/models/deit/convert_deit_timm_to_pytorch.py,sha256=JMCXzccvcbz1euXpqx-pb86V2PVDLKl-OYbFDLvvSZU,9217
transformers/models/deit/feature_extraction_deit.py,sha256=1j_aV0oAZUofSYJGCEFRo0WNd_zVEXjj3SFlTQSuV1E,1172
transformers/models/deit/image_processing_deit.py,sha256=WBHvlCJYhoQe4HgqFB5J7CTnL7DCeuvOzYGf2r8e9D8,15174
transformers/models/deit/modeling_deit.py,sha256=1XGb_G7YPpVqtb_0lA4rupdLBK7h6zLCWATO1F6Kgrw,38218
transformers/models/deit/modeling_tf_deit.py,sha256=qF4nRe5X6ZmXI5qOp52-o1qORCXl3Z_5EYCAkNdJ6M8,49579
transformers/models/deprecated/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
transformers/models/deprecated/__pycache__/__init__.cpython-311.pyc,,
transformers/models/deprecated/bort/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
transformers/models/deprecated/bort/__pycache__/__init__.cpython-311.pyc,,
transformers/models/deprecated/bort/__pycache__/convert_bort_original_gluonnlp_checkpoint_to_pytorch.cpython-311.pyc,,
transformers/models/deprecated/bort/convert_bort_original_gluonnlp_checkpoint_to_pytorch.py,sha256=y0wlQneBswkzekq70fW2-mqsn9RuITThO1AKV_8Cn5I,14068
transformers/models/deprecated/mctct/__init__.py,sha256=Rbzjcs6HiXhpUeaKRE6Qtj9XsIRLkUrFAiQnbOerMrM,1892
transformers/models/deprecated/mctct/__pycache__/__init__.cpython-311.pyc,,
transformers/models/deprecated/mctct/__pycache__/configuration_mctct.cpython-311.pyc,,
transformers/models/deprecated/mctct/__pycache__/feature_extraction_mctct.cpython-311.pyc,,
transformers/models/deprecated/mctct/__pycache__/modeling_mctct.cpython-311.pyc,,
transformers/models/deprecated/mctct/__pycache__/processing_mctct.cpython-311.pyc,,
transformers/models/deprecated/mctct/configuration_mctct.py,sha256=DefzLKOvSAH4DIfxDn4PR7-v6Wb6hH0G87NOGoOVxzA,9301
transformers/models/deprecated/mctct/feature_extraction_mctct.py,sha256=JsaSE20NeqBX8Uw-07Y5HdUcQtbYZqCrTN18Wu2B4rI,13460
transformers/models/deprecated/mctct/modeling_mctct.py,sha256=VdH14mNSM0UI3siMNF-_CXGvnj-Z62exIcP1ogvWAFo,32947
transformers/models/deprecated/mctct/processing_mctct.py,sha256=0ejBpQWA6YVuU0A7hrFg797hFZnOO7GexVU5Da7xLP0,5930
transformers/models/deprecated/mmbt/__init__.py,sha256=0CCmesCwGIMNFlf2oDsL0gYaCSpsfAC1_bMOXRcAgF4,1480
transformers/models/deprecated/mmbt/__pycache__/__init__.cpython-311.pyc,,
transformers/models/deprecated/mmbt/__pycache__/configuration_mmbt.cpython-311.pyc,,
transformers/models/deprecated/mmbt/__pycache__/modeling_mmbt.cpython-311.pyc,,
transformers/models/deprecated/mmbt/configuration_mmbt.py,sha256=agMAOVRnUrMlA8C6adBRLTuLmt8qG4lm4ykjGwS-qs4,1606
transformers/models/deprecated/mmbt/modeling_mmbt.py,sha256=daov1Smf2qd_BhebAOQiyN53C-8oZZary9m7iZV-nuU,18914
transformers/models/deprecated/open_llama/__init__.py,sha256=Mlmat1Ln8JLYZcldnGrMfBdgOwM01CmsoQEFedbJ24g,2788
transformers/models/deprecated/open_llama/__pycache__/__init__.cpython-311.pyc,,
transformers/models/deprecated/open_llama/__pycache__/configuration_open_llama.cpython-311.pyc,,
transformers/models/deprecated/open_llama/__pycache__/modeling_open_llama.cpython-311.pyc,,
transformers/models/deprecated/open_llama/configuration_open_llama.py,sha256=6Hj56fiYPIXARvZCHpu-tjkCt1WRaeWsfzt_XcS0IKo,7857
transformers/models/deprecated/open_llama/modeling_open_llama.py,sha256=Ji6L1r-q9em-HCLYqmooznvja-3oku37CQbLC6ks6ZE,43852
transformers/models/deprecated/retribert/__init__.py,sha256=yMGneTgD7_VaMhXG00Liyvt4digAfyQ_j6Ou55p8iEU,2351
transformers/models/deprecated/retribert/__pycache__/__init__.cpython-311.pyc,,
transformers/models/deprecated/retribert/__pycache__/configuration_retribert.cpython-311.pyc,,
transformers/models/deprecated/retribert/__pycache__/modeling_retribert.cpython-311.pyc,,
transformers/models/deprecated/retribert/__pycache__/tokenization_retribert.cpython-311.pyc,,
transformers/models/deprecated/retribert/__pycache__/tokenization_retribert_fast.cpython-311.pyc,,
transformers/models/deprecated/retribert/configuration_retribert.py,sha256=APvX5UKmf3utR32F5oUr_gYw7vGyxsC-bx87ujRFCE8,5408
transformers/models/deprecated/retribert/modeling_retribert.py,sha256=pT9VHCn8k7_Zrj61lvybMYweOxAlauYO8mz5ErlRhI4,9465
transformers/models/deprecated/retribert/tokenization_retribert.py,sha256=6YMrjNWWQ2SQPE-bPhsvXrb-4JZOVYOXekTUcJqCwiM,22683
transformers/models/deprecated/retribert/tokenization_retribert_fast.py,sha256=_9-xP_fFDrHkeLKUySJCq1BL6hwFJHMCKoEIam6Yw8o,9029
transformers/models/deprecated/tapex/__init__.py,sha256=lQutKYtwbU8ztPva0tyRnnV-zOWw6rxkGyoOUSuvnUo,926
transformers/models/deprecated/tapex/__pycache__/__init__.cpython-311.pyc,,
transformers/models/deprecated/tapex/__pycache__/tokenization_tapex.cpython-311.pyc,,
transformers/models/deprecated/tapex/tokenization_tapex.py,sha256=H9iOn3soYK11srHRKhEIbWgYbZpnHxvVBHHClIQQpYE,65004
transformers/models/deprecated/trajectory_transformer/__init__.py,sha256=NZl7qNHOSc-VlOFIvhh4iSpn_fyGHZ8k7a9WXXG5HGg,2077
transformers/models/deprecated/trajectory_transformer/__pycache__/__init__.cpython-311.pyc,,
transformers/models/deprecated/trajectory_transformer/__pycache__/configuration_trajectory_transformer.cpython-311.pyc,,
transformers/models/deprecated/trajectory_transformer/__pycache__/convert_trajectory_transformer_original_pytorch_checkpoint_to_pytorch.cpython-311.pyc,,
transformers/models/deprecated/trajectory_transformer/__pycache__/modeling_trajectory_transformer.cpython-311.pyc,,
transformers/models/deprecated/trajectory_transformer/configuration_trajectory_transformer.py,sha256=odKjaQb-ZDN4oSF1R5NDTCKGea4ZX2TbL80nXY4ztx0,7414
transformers/models/deprecated/trajectory_transformer/convert_trajectory_transformer_original_pytorch_checkpoint_to_pytorch.py,sha256=9jmCO1yueIbzUUvOHCl62XDCG4ExTkvsgRVCe-aBG7U,3139
transformers/models/deprecated/trajectory_transformer/modeling_trajectory_transformer.py,sha256=HwPYTZkN3S1h6bYeo3cbiS45Q5IlbPrZk9WdG-7ezyE,25823
transformers/models/deprecated/transfo_xl/__init__.py,sha256=bO5xiMeUsfu9k2nqJ4N2qTGvSniyD9oA8rHEn46ne-0,3183
transformers/models/deprecated/transfo_xl/__pycache__/__init__.cpython-311.pyc,,
transformers/models/deprecated/transfo_xl/__pycache__/configuration_transfo_xl.cpython-311.pyc,,
transformers/models/deprecated/transfo_xl/__pycache__/convert_transfo_xl_original_tf_checkpoint_to_pytorch.cpython-311.pyc,,
transformers/models/deprecated/transfo_xl/__pycache__/modeling_tf_transfo_xl.cpython-311.pyc,,
transformers/models/deprecated/transfo_xl/__pycache__/modeling_tf_transfo_xl_utilities.cpython-311.pyc,,
transformers/models/deprecated/transfo_xl/__pycache__/modeling_transfo_xl.cpython-311.pyc,,
transformers/models/deprecated/transfo_xl/__pycache__/modeling_transfo_xl_utilities.cpython-311.pyc,,
transformers/models/deprecated/transfo_xl/__pycache__/tokenization_transfo_xl.cpython-311.pyc,,
transformers/models/deprecated/transfo_xl/configuration_transfo_xl.py,sha256=wSRlnk87cZjcq80ekxuAeqaNhODRyCrXREnIdy38_8k,8037
transformers/models/deprecated/transfo_xl/convert_transfo_xl_original_tf_checkpoint_to_pytorch.py,sha256=cUL10fYCG-kWYI3BHuKto2AIxb0V2pgPQ3Z8JU9G-Sg,4938
transformers/models/deprecated/transfo_xl/modeling_tf_transfo_xl.py,sha256=BOCcWIqfdmlEFWoDsjmRnbbUyd6OcD220lAmiLN4ijI,46074
transformers/models/deprecated/transfo_xl/modeling_tf_transfo_xl_utilities.py,sha256=Kd2QFblDU3C5U0uqrkCIg1U3vytu9a8VLccyomBUu2o,7635
transformers/models/deprecated/transfo_xl/modeling_transfo_xl.py,sha256=E9XIe__lnBf56RFl0KcTWwvchvNURg1NRhXf1hh4e-g,56058
transformers/models/deprecated/transfo_xl/modeling_transfo_xl_utilities.py,sha256=oZAsrKz41ek-kSV2rvFHyCHfkAM6e5NyqbGCZSxIML4,10861
transformers/models/deprecated/transfo_xl/tokenization_transfo_xl.py,sha256=3npal0v2cekTlBfmzlV9gfX-79SxXqXsy-F9q6No4ZA,32373
transformers/models/deprecated/van/__init__.py,sha256=LfVeE-QGxQJS0QZhWPmPD9s2yX5Pk9iA5NK90CkoyQQ,1728
transformers/models/deprecated/van/__pycache__/__init__.cpython-311.pyc,,
transformers/models/deprecated/van/__pycache__/configuration_van.cpython-311.pyc,,
transformers/models/deprecated/van/__pycache__/convert_van_to_pytorch.cpython-311.pyc,,
transformers/models/deprecated/van/__pycache__/modeling_van.cpython-311.pyc,,
transformers/models/deprecated/van/configuration_van.py,sha256=8h4d2uHBFAiiHAMqtqhAv_fsqdrqxXxFfIcPE3sabgs,4838
transformers/models/deprecated/van/convert_van_to_pytorch.py,sha256=KW-0r4GVcmH_EzxC-qsdUn5TJw4TEl0wmUKPnJPYZaw,10374
transformers/models/deprecated/van/modeling_van.py,sha256=h3u6GcsTPcQMCGe-hZ4B-_XJHId5C8mCEUyHTK31vh4,21450
transformers/models/depth_anything/__init__.py,sha256=nSTo0y3RhnvBAua09yiGxbsVy8YKNb6x7Hl-jaM3Sro,1858
transformers/models/depth_anything/__pycache__/__init__.cpython-311.pyc,,
transformers/models/depth_anything/__pycache__/configuration_depth_anything.cpython-311.pyc,,
transformers/models/depth_anything/__pycache__/convert_depth_anything_to_hf.cpython-311.pyc,,
transformers/models/depth_anything/__pycache__/modeling_depth_anything.cpython-311.pyc,,
transformers/models/depth_anything/configuration_depth_anything.py,sha256=OAzuPDQL1DGV5S0F9lg0SjcHtgeyy2oCN2Vrkge7Ulw,6681
transformers/models/depth_anything/convert_depth_anything_to_hf.py,sha256=N2RCeVAiH6pzGmUZnHq0FPoCHD-EkrMviOqof1Qd7Ww,13710
transformers/models/depth_anything/modeling_depth_anything.py,sha256=1dx7AM5aWIWkI8xhkO3qWWxLKd71PZoqWMZSH0AsmqY,18197
transformers/models/deta/__init__.py,sha256=eHgP2aY7a0Of2OkxgCPavzEYvqk2etS3aqXD23Zd3Rc,2205
transformers/models/deta/__pycache__/__init__.cpython-311.pyc,,
transformers/models/deta/__pycache__/configuration_deta.cpython-311.pyc,,
transformers/models/deta/__pycache__/convert_deta_resnet_to_pytorch.cpython-311.pyc,,
transformers/models/deta/__pycache__/convert_deta_swin_to_pytorch.cpython-311.pyc,,
transformers/models/deta/__pycache__/image_processing_deta.cpython-311.pyc,,
transformers/models/deta/__pycache__/modeling_deta.cpython-311.pyc,,
transformers/models/deta/configuration_deta.py,sha256=LPteF8dNnCpv18dCsVSRRnTg17sWYMLeDhS6YC6kMFQ,14063
transformers/models/deta/convert_deta_resnet_to_pytorch.py,sha256=r-beTAdmCNONvgIPQmIf890KgDQmdi8mRoDkSWoumJg,16833
transformers/models/deta/convert_deta_swin_to_pytorch.py,sha256=WL18erfLKYr7-pmcHC5i5t6it7EnSagPsuHs5VEgLEA,19031
transformers/models/deta/image_processing_deta.py,sha256=32jbJymBXq0aWrMx2bUV22d6GssnIufh_emIddCWBIw,52396
transformers/models/deta/modeling_deta.py,sha256=mMLCT7ouduq-KryMCscg0gIOLoVx9sXZOy-kTrJlAjA,139525
transformers/models/detr/__init__.py,sha256=dWemW6cL_QLOXK3i2uoP6ywKNrjVkpw8IXeQYbs0HfA,2438
transformers/models/detr/__pycache__/__init__.cpython-311.pyc,,
transformers/models/detr/__pycache__/configuration_detr.cpython-311.pyc,,
transformers/models/detr/__pycache__/convert_detr_original_pytorch_checkpoint_to_pytorch.cpython-311.pyc,,
transformers/models/detr/__pycache__/convert_detr_to_pytorch.cpython-311.pyc,,
transformers/models/detr/__pycache__/feature_extraction_detr.cpython-311.pyc,,
transformers/models/detr/__pycache__/image_processing_detr.cpython-311.pyc,,
transformers/models/detr/__pycache__/modeling_detr.cpython-311.pyc,,
transformers/models/detr/configuration_detr.py,sha256=qrQAdKg3FxvfQZwDxhzhyzOAAxRe-1cPPwmPMgq2L3o,13638
transformers/models/detr/convert_detr_original_pytorch_checkpoint_to_pytorch.py,sha256=_4fQ1N3Zat1x1r-Gr3FosWuV3pW3yFKQQgM9MKujmbY,13561
transformers/models/detr/convert_detr_to_pytorch.py,sha256=_E63l9rWZUfwSHCfJbz-HoIDT4hxAwoHRKXj1Ni03AA,18993
transformers/models/detr/feature_extraction_detr.py,sha256=gMyG16pNJKoimImXOyqi589hGj37OYGWb7ZoTx84d5I,1474
transformers/models/detr/image_processing_detr.py,sha256=SfB8Gli67gfeJXiFX0nDG8vp_j6FDT-e9YWadqy4Qrg,88543
transformers/models/detr/modeling_detr.py,sha256=k1zKBhkORF_d35_HkUfkYhbzxh0aZabcFK4TnU3kbB4,116494
transformers/models/dialogpt/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
transformers/models/dialogpt/__pycache__/__init__.cpython-311.pyc,,
transformers/models/dialogpt/__pycache__/convert_dialogpt_original_pytorch_checkpoint_to_pytorch.cpython-311.pyc,,
transformers/models/dialogpt/convert_dialogpt_original_pytorch_checkpoint_to_pytorch.py,sha256=Zp59TmLBKEs-x1-quZZeqARhpS3cTnnmgT4nCI0zsHY,1537
transformers/models/dinat/__init__.py,sha256=Jt3EAbCCZcBjJD_sEane9NU0btqsFkOTqz6JkUtmY_4,1812
transformers/models/dinat/__pycache__/__init__.cpython-311.pyc,,
transformers/models/dinat/__pycache__/configuration_dinat.cpython-311.pyc,,
transformers/models/dinat/__pycache__/modeling_dinat.cpython-311.pyc,,
transformers/models/dinat/configuration_dinat.py,sha256=XFoAE6V1O7x_hxgPwfccyymCPc3v0Wo7JHvEJ9PRe8U,7561
transformers/models/dinat/modeling_dinat.py,sha256=qfjnsxY2TvWoYMxYLB9OhN0hObSlI2p6lIUSdn9eW7w,41774
transformers/models/dinov2/__init__.py,sha256=vQdLyp1VnVfmx0Vdvwvgvk9bsWCUArt-hPzzoDsA20I,1890
transformers/models/dinov2/__pycache__/__init__.cpython-311.pyc,,
transformers/models/dinov2/__pycache__/configuration_dinov2.cpython-311.pyc,,
transformers/models/dinov2/__pycache__/convert_dinov2_to_hf.cpython-311.pyc,,
transformers/models/dinov2/__pycache__/modeling_dinov2.cpython-311.pyc,,
transformers/models/dinov2/configuration_dinov2.py,sha256=EBWHdsHLbt_ijIRumJOU-8lVjABWfhINEQHT5l5ROv0,8186
transformers/models/dinov2/convert_dinov2_to_hf.py,sha256=g4wmiqVdUlNbRoy_GbEws3DQaXfUA1I9Qh6bHhL6yZk,11964
transformers/models/dinov2/modeling_dinov2.py,sha256=araUay0dnTpbnoJfZIrx8ROAunb4RTOP7ivS4_yB5_A,36403
transformers/models/distilbert/__init__.py,sha256=64w_AOUP-vupRT6bGlQF7Ak24rJB5AX58n1V8V_aHM0,5167
transformers/models/distilbert/__pycache__/__init__.cpython-311.pyc,,
transformers/models/distilbert/__pycache__/configuration_distilbert.cpython-311.pyc,,
transformers/models/distilbert/__pycache__/modeling_distilbert.cpython-311.pyc,,
transformers/models/distilbert/__pycache__/modeling_flax_distilbert.cpython-311.pyc,,
transformers/models/distilbert/__pycache__/modeling_tf_distilbert.cpython-311.pyc,,
transformers/models/distilbert/__pycache__/tokenization_distilbert.cpython-311.pyc,,
transformers/models/distilbert/__pycache__/tokenization_distilbert_fast.cpython-311.pyc,,
transformers/models/distilbert/configuration_distilbert.py,sha256=IFyo18RrxX0C9i4aaBKq0_Kexsw-e17XGytZSYcRubo,6979
transformers/models/distilbert/modeling_distilbert.py,sha256=1ajlZmA3THwscecZWaQdV2npXCiPd6whMADEE7Xs6NI,61890
transformers/models/distilbert/modeling_flax_distilbert.py,sha256=cBRX7sUX2G9aSX6_I15sZ_H1yTXOMvwM7Gw3xbgOL6Q,32629
transformers/models/distilbert/modeling_tf_distilbert.py,sha256=3NOIUhn7bgOZ28JwOLVWNYyQcxtDMdCyC1I2nQ-TiAQ,49230
transformers/models/distilbert/tokenization_distilbert.py,sha256=iteMI8vbds3HhchcTDQ8bZ0aRUxMt2TSVODCh2CPIzo,23695
transformers/models/distilbert/tokenization_distilbert_fast.py,sha256=cTVfYemhriohlvB7Z1jFZodwfaKkdaznNj7BBPqpJiQ,10720
transformers/models/dit/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
transformers/models/dit/__pycache__/__init__.cpython-311.pyc,,
transformers/models/dit/__pycache__/convert_dit_unilm_to_pytorch.cpython-311.pyc,,
transformers/models/dit/convert_dit_unilm_to_pytorch.py,sha256=qoCC3Hm-enjzLj5LoxjbpP8EaIsyhi3U3PERYYeSt7c,9420
transformers/models/donut/__init__.py,sha256=VraCMZ5ZG0WtYvLmZv-B-gIH5joEM_QdAkiH2iDjLls,2455
transformers/models/donut/__pycache__/__init__.cpython-311.pyc,,
transformers/models/donut/__pycache__/configuration_donut_swin.cpython-311.pyc,,
transformers/models/donut/__pycache__/convert_donut_to_pytorch.cpython-311.pyc,,
transformers/models/donut/__pycache__/feature_extraction_donut.cpython-311.pyc,,
transformers/models/donut/__pycache__/image_processing_donut.cpython-311.pyc,,
transformers/models/donut/__pycache__/modeling_donut_swin.cpython-311.pyc,,
transformers/models/donut/__pycache__/processing_donut.cpython-311.pyc,,
transformers/models/donut/configuration_donut_swin.py,sha256=yMrc2WA182D1i1AcfagE2783D9sQiwvotsiqAOCgPLo,5990
transformers/models/donut/convert_donut_to_pytorch.py,sha256=0IgQ3V9hNWPOJ6KtOfowhVMfTh1m4WEVLOAQSMEGjJE,9316
transformers/models/donut/feature_extraction_donut.py,sha256=jBSpDfoiCg_IWr4gcphIcxs7DA760JnH6V6hAfaoYPM,1179
transformers/models/donut/image_processing_donut.py,sha256=q_f4VSLDnSClz1qA90yJWev4aPw29Pgsq_QZZzFNN7Y,21705
transformers/models/donut/modeling_donut_swin.py,sha256=a81ycUMJe-YGqM7tErtl0e5Jw9JjETC2lYR3yjq9RhY,43515
transformers/models/donut/processing_donut.py,sha256=FQ00liC2m5vvrZN1njJkTM8WQtrPw4Fv_Hv4DYmfoh0,8170
transformers/models/dpr/__init__.py,sha256=qc_Fe-hF94ZxS9cfEXCp9h7-tkmi9Tj4KV9h_wg6yhs,4535
transformers/models/dpr/__pycache__/__init__.cpython-311.pyc,,
transformers/models/dpr/__pycache__/configuration_dpr.cpython-311.pyc,,
transformers/models/dpr/__pycache__/convert_dpr_original_checkpoint_to_pytorch.cpython-311.pyc,,
transformers/models/dpr/__pycache__/modeling_dpr.cpython-311.pyc,,
transformers/models/dpr/__pycache__/modeling_tf_dpr.cpython-311.pyc,,
transformers/models/dpr/__pycache__/tokenization_dpr.cpython-311.pyc,,
transformers/models/dpr/__pycache__/tokenization_dpr_fast.cpython-311.pyc,,
transformers/models/dpr/configuration_dpr.py,sha256=XmTGhGd06PQdN1sde5TNJbxIFrMCJkK0wcEKKwmObWY,7350
transformers/models/dpr/convert_dpr_original_checkpoint_to_pytorch.py,sha256=XsxG5FBg46-EHlDsMq4w21C9W4wl8RZ6GZvx5coBmfk,6132
transformers/models/dpr/modeling_dpr.py,sha256=eIiA7OrGS8__e43olq5zTcJTIdxdjP3OA8tUoldHQNw,28749
transformers/models/dpr/modeling_tf_dpr.py,sha256=vQuLqqmfZAuf_nVSor6ynCX4kPwtu7-QfEFuoJUhPrw,34085
transformers/models/dpr/tokenization_dpr.py,sha256=MonNVmQeNBUp328ReY-hNux9hxgY0zghl_2utZLq1Gk,19789
transformers/models/dpr/tokenization_dpr_fast.py,sha256=x4f1UxOzkygehKv6RYr7Gh4_BbDYU7NEzn5s29mSubw,20175
transformers/models/dpt/__init__.py,sha256=WoC0ADjpTTkspHtgIX_TtHXXG-4t8S-NGgJaAUiG-q4,2444
transformers/models/dpt/__pycache__/__init__.cpython-311.pyc,,
transformers/models/dpt/__pycache__/configuration_dpt.cpython-311.pyc,,
transformers/models/dpt/__pycache__/convert_dinov2_depth_to_hf.cpython-311.pyc,,
transformers/models/dpt/__pycache__/convert_dpt_beit_to_hf.cpython-311.pyc,,
transformers/models/dpt/__pycache__/convert_dpt_hybrid_to_pytorch.cpython-311.pyc,,
transformers/models/dpt/__pycache__/convert_dpt_swinv2_to_hf.cpython-311.pyc,,
transformers/models/dpt/__pycache__/convert_dpt_to_pytorch.cpython-311.pyc,,
transformers/models/dpt/__pycache__/feature_extraction_dpt.cpython-311.pyc,,
transformers/models/dpt/__pycache__/image_processing_dpt.cpython-311.pyc,,
transformers/models/dpt/__pycache__/modeling_dpt.cpython-311.pyc,,
transformers/models/dpt/configuration_dpt.py,sha256=nOmiUNhaHHER1-i1cVz0meYSMnDqTAhAbUbvacblFUY,14609
transformers/models/dpt/convert_dinov2_depth_to_hf.py,sha256=azN2ivIGa-g5fe6kdkQ0kJbgKitt10k8C2R3x3ff6FI,16935
transformers/models/dpt/convert_dpt_beit_to_hf.py,sha256=VeC3Jpf_BVCkTdFJQHhrJPTgyRIibPzC32Isrd5iBPg,14347
transformers/models/dpt/convert_dpt_hybrid_to_pytorch.py,sha256=czo2aHnDSZZqv2qwpx48s1dRTg25v-R5giSg4seNebE,12994
transformers/models/dpt/convert_dpt_swinv2_to_hf.py,sha256=rFZSF_WFfMcVxXz815SX0THuTfg0juJBy6qCy8yT6QY,15176
transformers/models/dpt/convert_dpt_to_pytorch.py,sha256=-SpPQGZ5tD6g0g5fQpSbMmUDK9xc1OFIInk9yyjkahE,11894
transformers/models/dpt/feature_extraction_dpt.py,sha256=ZgBcSKNDX0_Fstv94sp1r9jpr9zvXCLPwvIek76Fkso,1165
transformers/models/dpt/image_processing_dpt.py,sha256=sRC09mSk47UGjUgFyxN5m7dcV7Jtb6DoPV_J41ljlhs,22412
transformers/models/dpt/modeling_dpt.py,sha256=eSzg5hzv2ed--RcbkTn5MeY14jBtCCZOI8-3CIzxmyQ,57411
transformers/models/efficientformer/__init__.py,sha256=hFVX-KUt3FRIjqb_MzHVif_h8r9FFezpRtRwFKLBKuY,3550
transformers/models/efficientformer/__pycache__/__init__.cpython-311.pyc,,
transformers/models/efficientformer/__pycache__/configuration_efficientformer.cpython-311.pyc,,
transformers/models/efficientformer/__pycache__/convert_efficientformer_original_pytorch_checkpoint_to_pytorch.cpython-311.pyc,,
transformers/models/efficientformer/__pycache__/image_processing_efficientformer.cpython-311.pyc,,
transformers/models/efficientformer/__pycache__/modeling_efficientformer.cpython-311.pyc,,
transformers/models/efficientformer/__pycache__/modeling_tf_efficientformer.cpython-311.pyc,,
transformers/models/efficientformer/configuration_efficientformer.py,sha256=SVqPRlF4ZdcExlycpBVwqsHY7lGEQqDZ9uW3Dk1v0aE,7919
transformers/models/efficientformer/convert_efficientformer_original_pytorch_checkpoint_to_pytorch.py,sha256=1ni0wyhRjTbF8U4BZ_FXU-_9Jzy43HMLKI3vGlyPjFc,9381
transformers/models/efficientformer/image_processing_efficientformer.py,sha256=6y9YUghFggOnk1s9eb3O9Qi5KxrgvuZdBmj_rPmQRC4,15148
transformers/models/efficientformer/modeling_efficientformer.py,sha256=k3Y_i8gqgYUGMqGkMpQzVfxsxsQid8yIKLOX20WWBKI,33878
transformers/models/efficientformer/modeling_tf_efficientformer.py,sha256=4kGS2vWF4DILvbEVSDFRbjPtAFcraTgDqYltav3ofW8,49384
transformers/models/efficientnet/__init__.py,sha256=mS43eilPqqiySKV0CZ34jg1SPUJa2zc6qyCwwRoJQFM,2670
transformers/models/efficientnet/__pycache__/__init__.cpython-311.pyc,,
transformers/models/efficientnet/__pycache__/configuration_efficientnet.cpython-311.pyc,,
transformers/models/efficientnet/__pycache__/convert_efficientnet_to_pytorch.cpython-311.pyc,,
transformers/models/efficientnet/__pycache__/image_processing_efficientnet.cpython-311.pyc,,
transformers/models/efficientnet/__pycache__/modeling_efficientnet.cpython-311.pyc,,
transformers/models/efficientnet/configuration_efficientnet.py,sha256=WmiYtIBwTTa-GSSURQBOh4EVuA6zJ9sCIzVhU-_nQtE,7751
transformers/models/efficientnet/convert_efficientnet_to_pytorch.py,sha256=e2Na1xvNc7z9XvvI7v6v1V2uFWr88MSTN3JPKR5GstM,12756
transformers/models/efficientnet/image_processing_efficientnet.py,sha256=G9IuChmOF-_SC18Hwb2mqL1z-0z_qWHuP4tjEeGBmFA,18245
transformers/models/efficientnet/modeling_efficientnet.py,sha256=yRM8EcrDPB8H_PSJ38nIdTyQdJzqio6oI_4Q0LnQ1ig,24088
transformers/models/electra/__init__.py,sha256=UVRK4T71rPHmZYRbrQ_-5eu98Gfrkp6I9SA3KVVCcYQ,5257
transformers/models/electra/__pycache__/__init__.cpython-311.pyc,,
transformers/models/electra/__pycache__/configuration_electra.cpython-311.pyc,,
transformers/models/electra/__pycache__/convert_electra_original_tf_checkpoint_to_pytorch.cpython-311.pyc,,
transformers/models/electra/__pycache__/modeling_electra.cpython-311.pyc,,
transformers/models/electra/__pycache__/modeling_flax_electra.cpython-311.pyc,,
transformers/models/electra/__pycache__/modeling_tf_electra.cpython-311.pyc,,
transformers/models/electra/__pycache__/tokenization_electra.cpython-311.pyc,,
transformers/models/electra/__pycache__/tokenization_electra_fast.cpython-311.pyc,,
transformers/models/electra/configuration_electra.py,sha256=j0tL2YeX3wByGhIoMbvqrm_fUbV0xSaj12WXHUCG5dw,9928
transformers/models/electra/convert_electra_original_tf_checkpoint_to_pytorch.py,sha256=iwbjp9v26TfI9iIRdR4KWv-zsrxVNbfgkUwn9N1WHaM,2862
transformers/models/electra/modeling_electra.py,sha256=B38DQBldMN9SorPDcblml57jWAtAFYYasBuWz-57L1k,76096
transformers/models/electra/modeling_flax_electra.py,sha256=S5TkUbjF-9GNOxeiGfXTjc3tnINV18R8CLLFf30A9zU,62268
transformers/models/electra/modeling_tf_electra.py,sha256=BsBguoN3EC79MBeyxBIJQdYQwwxErUfqUCbhnWMTVKQ,78698
transformers/models/electra/tokenization_electra.py,sha256=akGAo768alSgBPbqOS1fSiAj58RLwXrWCUqHBtWLhio,22774
transformers/models/electra/tokenization_electra_fast.py,sha256=VWsxtxMuI8yC8ARw6Bs0Hpiu8Hm3aHJGcNv484nMdIE,10507
transformers/models/encodec/__init__.py,sha256=LVz0exnSENNu1jnGsAoPoS7LfXgC-H7s3_lbwNEX_Dw,1910
transformers/models/encodec/__pycache__/__init__.cpython-311.pyc,,
transformers/models/encodec/__pycache__/configuration_encodec.cpython-311.pyc,,
transformers/models/encodec/__pycache__/convert_encodec_checkpoint_to_pytorch.cpython-311.pyc,,
transformers/models/encodec/__pycache__/feature_extraction_encodec.cpython-311.pyc,,
transformers/models/encodec/__pycache__/modeling_encodec.cpython-311.pyc,,
transformers/models/encodec/configuration_encodec.py,sha256=0cvCzXuIsZxC3o6K_GnSjuZEC9qlraigY5zg1ql2aqY,8750
transformers/models/encodec/convert_encodec_checkpoint_to_pytorch.py,sha256=zF2ZSOCFsiMNvtIvRhjoucoF2G3m0nW-cHXimF_2uwQ,15253
transformers/models/encodec/feature_extraction_encodec.py,sha256=luYd1uGvvQC_mDYlUsnMtSBn_S0dhbazYJ9zYGuQ1Kc,9873
transformers/models/encodec/modeling_encodec.py,sha256=I01RO2cKJx52R8WwHz5kUhVizOeryClbwOkqAj31Xu0,33256
transformers/models/encoder_decoder/__init__.py,sha256=bR1yPbuqKHUYXaxI_QuDz6ccBSWpCr0THhPBM3lnttA,2451
transformers/models/encoder_decoder/__pycache__/__init__.cpython-311.pyc,,
transformers/models/encoder_decoder/__pycache__/configuration_encoder_decoder.cpython-311.pyc,,
transformers/models/encoder_decoder/__pycache__/modeling_encoder_decoder.cpython-311.pyc,,
transformers/models/encoder_decoder/__pycache__/modeling_flax_encoder_decoder.cpython-311.pyc,,
transformers/models/encoder_decoder/__pycache__/modeling_tf_encoder_decoder.cpython-311.pyc,,
transformers/models/encoder_decoder/configuration_encoder_decoder.py,sha256=HaF1rtwzf_tDXJYrfycr4ktA8-LlBia_RdAWD60RTu8,4362
transformers/models/encoder_decoder/modeling_encoder_decoder.py,sha256=ei0kTsYZnlWVj1uI6TbBTCyvJR9Ky-RPwQksqiNnnuM,35362
transformers/models/encoder_decoder/modeling_flax_encoder_decoder.py,sha256=geeWvUTNF1OprImdmwdPclf2qUpHGQ_Z0TZzMMbqSsc,43529
transformers/models/encoder_decoder/modeling_tf_encoder_decoder.py,sha256=pVGR6W436j6W2QhrlcyRLJji_wP8nJi3vyrqW0Lv3xQ,34308
transformers/models/ernie/__init__.py,sha256=s0oBhpPU0MdftoAKWUbo3VR2D9VPTvjPde4NBylw5qI,2331
transformers/models/ernie/__pycache__/__init__.cpython-311.pyc,,
transformers/models/ernie/__pycache__/configuration_ernie.cpython-311.pyc,,
transformers/models/ernie/__pycache__/modeling_ernie.cpython-311.pyc,,
transformers/models/ernie/configuration_ernie.py,sha256=yTMlXd0sS-Cp0tqI0N1tRYBkgz0V4JWmgjNafe0Z9mM,8806
transformers/models/ernie/modeling_ernie.py,sha256=aNyTaMUpc5NW5aLsSoz-cW0gfvwdt6bfNWfTqLVlvEg,84284
transformers/models/ernie_m/__init__.py,sha256=0neb_RuFu2HBnM3QZ5XRTBI9j8jzppR90ssXHH9LpGA,2637
transformers/models/ernie_m/__pycache__/__init__.cpython-311.pyc,,
transformers/models/ernie_m/__pycache__/configuration_ernie_m.cpython-311.pyc,,
transformers/models/ernie_m/__pycache__/modeling_ernie_m.cpython-311.pyc,,
transformers/models/ernie_m/__pycache__/tokenization_ernie_m.cpython-311.pyc,,
transformers/models/ernie_m/configuration_ernie_m.py,sha256=-hdCJMjksmL4-sGJTyncHjzNiH0bmlKb4Rr5825C0Xo,6159
transformers/models/ernie_m/modeling_ernie_m.py,sha256=GAi1w9HQ0jscGJMBIl9NdfmuJ4jpHdXcHRktvrihN9s,48015
transformers/models/ernie_m/tokenization_ernie_m.py,sha256=OzShzVQqwVzqw9ltF1IdkMnc1R0YQbaFEwkeZy8MY10,17115
transformers/models/esm/__init__.py,sha256=IfHOSRyzJHTD8eVSelVu_ijHcYnRp0Umm6hZGsoFYHQ,2978
transformers/models/esm/__pycache__/__init__.cpython-311.pyc,,
transformers/models/esm/__pycache__/configuration_esm.cpython-311.pyc,,
transformers/models/esm/__pycache__/convert_esm.cpython-311.pyc,,
transformers/models/esm/__pycache__/modeling_esm.cpython-311.pyc,,
transformers/models/esm/__pycache__/modeling_esmfold.cpython-311.pyc,,
transformers/models/esm/__pycache__/modeling_tf_esm.cpython-311.pyc,,
transformers/models/esm/__pycache__/tokenization_esm.cpython-311.pyc,,
transformers/models/esm/configuration_esm.py,sha256=h1noIbq3Dp7UAOgDTK0RtqvsRBeMr8baP_Lr7hyYYc8,14559
transformers/models/esm/convert_esm.py,sha256=x0dfu2oexN80cndU3Zn81oVynsRuzfEtJZF20TK1y3k,18470
transformers/models/esm/modeling_esm.py,sha256=W1tf1vC7mG4heAouHh1uJv8DZiyh4d9aECZK_Cva-f8,55777
transformers/models/esm/modeling_esmfold.py,sha256=GgMkBeEhTvZBj61fNGqDkZsWTGeRwrhkHSGYa0otbJ4,86908
transformers/models/esm/modeling_tf_esm.py,sha256=sn3lHbinAn8l0I4prWGGblyLPUdRW6HoJCpmT3dEltk,69199
transformers/models/esm/openfold_utils/__init__.py,sha256=Xy2uqvFsLC8Ax-OOce5PgoBDiZgEJgJPqs__p5SBWUY,446
transformers/models/esm/openfold_utils/__pycache__/__init__.cpython-311.pyc,,
transformers/models/esm/openfold_utils/__pycache__/chunk_utils.cpython-311.pyc,,
transformers/models/esm/openfold_utils/__pycache__/data_transforms.cpython-311.pyc,,
transformers/models/esm/openfold_utils/__pycache__/feats.cpython-311.pyc,,
transformers/models/esm/openfold_utils/__pycache__/loss.cpython-311.pyc,,
transformers/models/esm/openfold_utils/__pycache__/protein.cpython-311.pyc,,
transformers/models/esm/openfold_utils/__pycache__/residue_constants.cpython-311.pyc,,
transformers/models/esm/openfold_utils/__pycache__/rigid_utils.cpython-311.pyc,,
transformers/models/esm/openfold_utils/__pycache__/tensor_utils.cpython-311.pyc,,
transformers/models/esm/openfold_utils/chunk_utils.py,sha256=eyd0NSdGIVBr9gLuI-3VI5cjJr46wYa9hlYBq1L1gCU,14392
transformers/models/esm/openfold_utils/data_transforms.py,sha256=F4wGANRhKLd6MLHrwg2IxpqCxCJEx8aFSxqAdsXsBMo,3764
transformers/models/esm/openfold_utils/feats.py,sha256=dgLcLJriW-eDIBdc0MyKPDT5w0POab9QLuN56qE8wsk,8376
transformers/models/esm/openfold_utils/loss.py,sha256=wY2ONqbuRvWMomjkpfPwfoa7dqCO2vFkM-kmNfhjivo,3705
transformers/models/esm/openfold_utils/protein.py,sha256=x9NK6bryLs9vNi3j8OfOlw0Jb1cFrwMhCi6JdxkDdQw,11490
transformers/models/esm/openfold_utils/residue_constants.py,sha256=KDcdOt5wkJ7cO7p-LtmS8sLIzfQ2ej7p40Re8EsTkv0,37993
transformers/models/esm/openfold_utils/rigid_utils.py,sha256=EF79POBO-abRsdXrfdKLaqJUVIPp4EOMFVt5oOjx504,41122
transformers/models/esm/openfold_utils/tensor_utils.py,sha256=A07D5psNs5lGgWJp_kzJgrY8cmWmaL3odDgKXN1NVAE,4798
transformers/models/esm/tokenization_esm.py,sha256=Zvnf6_bwoRE3U0xh5ssyyPL-URlo3qcYucKrJKqWb2M,5897
transformers/models/falcon/__init__.py,sha256=Sf4eyG7aJ4pQoqLJXStTSTxP7iEHks73GWe9QjAnU3w,2067
transformers/models/falcon/__pycache__/__init__.cpython-311.pyc,,
transformers/models/falcon/__pycache__/configuration_falcon.cpython-311.pyc,,
transformers/models/falcon/__pycache__/convert_custom_code_checkpoint.cpython-311.pyc,,
transformers/models/falcon/__pycache__/modeling_falcon.cpython-311.pyc,,
transformers/models/falcon/configuration_falcon.py,sha256=Vn_KKSitHhAbfuDDnrfuHHiLbmpyU5D0cOn8AeZ87eQ,9229
transformers/models/falcon/convert_custom_code_checkpoint.py,sha256=XPJ1owRjRno_Y1AD5UeoPE4oo6a-SeQR9w9u-EIUktE,3061
transformers/models/falcon/modeling_falcon.py,sha256=JrS4CzMkfg0QJyePNov2w_sWPKZcf93DDxRV1bOoyOw,75962
transformers/models/fastspeech2_conformer/__init__.py,sha256=eAZrmrz-mhay_crQQcN59ra1YBH341kxCGvR2h__YBE,2770
transformers/models/fastspeech2_conformer/__pycache__/__init__.cpython-311.pyc,,
transformers/models/fastspeech2_conformer/__pycache__/configuration_fastspeech2_conformer.cpython-311.pyc,,
transformers/models/fastspeech2_conformer/__pycache__/convert_fastspeech2_conformer_original_pytorch_checkpoint_to_pytorch.cpython-311.pyc,,
transformers/models/fastspeech2_conformer/__pycache__/convert_hifigan.cpython-311.pyc,,
transformers/models/fastspeech2_conformer/__pycache__/convert_model_with_hifigan.cpython-311.pyc,,
transformers/models/fastspeech2_conformer/__pycache__/modeling_fastspeech2_conformer.cpython-311.pyc,,
transformers/models/fastspeech2_conformer/__pycache__/tokenization_fastspeech2_conformer.cpython-311.pyc,,
transformers/models/fastspeech2_conformer/configuration_fastspeech2_conformer.py,sha256=PsKTOrf0rhgCDb1EvCHHsIfqXxBGXXHhBTycWYivbx0,24922
transformers/models/fastspeech2_conformer/convert_fastspeech2_conformer_original_pytorch_checkpoint_to_pytorch.py,sha256=-ToJHpwI-xoLLMzLYdqFrBL6j6nsSPlNbkQ3pfTgJ6Y,8939
transformers/models/fastspeech2_conformer/convert_hifigan.py,sha256=RC1PaVnl1cLx8c2LdYycNti7iYRhUM7_KrX2mF5WyCM,5431
transformers/models/fastspeech2_conformer/convert_model_with_hifigan.py,sha256=wT4pQGgEHVFoWI1Lb71L7_i6ujfNrSMDGYuDGb4oeh8,3471
transformers/models/fastspeech2_conformer/modeling_fastspeech2_conformer.py,sha256=WRkfZoWKslG2xiRUb7SYm3fxHcdxn-1DQzhwc_pO2Gk,77762
transformers/models/fastspeech2_conformer/tokenization_fastspeech2_conformer.py,sha256=FTpXoGUb11vzQnanEtmnAeiyh2eEqyy7r4NhmYyAOs4,6727
transformers/models/flaubert/__init__.py,sha256=neN63qn5CVIfPSr50g0WhbrcKDT7w0qIljyqSCxbqLI,3488
transformers/models/flaubert/__pycache__/__init__.cpython-311.pyc,,
transformers/models/flaubert/__pycache__/configuration_flaubert.cpython-311.pyc,,
transformers/models/flaubert/__pycache__/modeling_flaubert.cpython-311.pyc,,
transformers/models/flaubert/__pycache__/modeling_tf_flaubert.cpython-311.pyc,,
transformers/models/flaubert/__pycache__/tokenization_flaubert.cpython-311.pyc,,
transformers/models/flaubert/configuration_flaubert.py,sha256=9elLZSProrYsw3CwMsyM24Q3bbKCwmWRGg-RMcay9T0,11706
transformers/models/flaubert/modeling_flaubert.py,sha256=u1c-LFgLOGMJup-SNVg1vc66sHwqiA_-nHCceFy-OtY,57681
transformers/models/flaubert/modeling_tf_flaubert.py,sha256=2SDgMNj1TyXENXs8H0HLjGBtJR8JhezI5E5edIZQHT4,57208
transformers/models/flaubert/tokenization_flaubert.py,sha256=McA9rqAL7u2RZhigWbVRd5ls64HMlMGeY7TchEvW55Q,24028
transformers/models/flava/__init__.py,sha256=TtPrEOob3V4Lk_NK3rgacXw0jJ2ABWKPnLP8x4uSs4I,3030
transformers/models/flava/__pycache__/__init__.cpython-311.pyc,,
transformers/models/flava/__pycache__/configuration_flava.cpython-311.pyc,,
transformers/models/flava/__pycache__/convert_dalle_to_flava_codebook.cpython-311.pyc,,
transformers/models/flava/__pycache__/convert_flava_original_pytorch_to_hf.cpython-311.pyc,,
transformers/models/flava/__pycache__/feature_extraction_flava.cpython-311.pyc,,
transformers/models/flava/__pycache__/image_processing_flava.cpython-311.pyc,,
transformers/models/flava/__pycache__/modeling_flava.cpython-311.pyc,,
transformers/models/flava/__pycache__/processing_flava.cpython-311.pyc,,
transformers/models/flava/configuration_flava.py,sha256=Yi0FULiyi8UIbW7hu0X3Vnx1PlLSBxr8FTLj7KLm-p4,37228
transformers/models/flava/convert_dalle_to_flava_codebook.py,sha256=iEJM9W_cKk3HK0gKS6i2ygEMeyymWCMl18LDaQXRAhY,3428
transformers/models/flava/convert_flava_original_pytorch_to_hf.py,sha256=LilQpbe6qeN2P_uXljae6zEPx_KoepoRv4uvCEAo0QA,4372
transformers/models/flava/feature_extraction_flava.py,sha256=mA1uAn29yv9PV7gYXauz0VTAJDgcpl9DPHvH99Ed__s,1201
transformers/models/flava/image_processing_flava.py,sha256=My6UfmGYm8bCykLmI12Ic8ktKq2aDcV-EUvrEvlAifg,37347
transformers/models/flava/modeling_flava.py,sha256=r60cGNtD6kxacvjZkaH1l2-fB2wnbXj0gX4kyL6s9lw,96289
transformers/models/flava/processing_flava.py,sha256=fj9uFlMerVGFnB9hV1XJ61c3q82qstjPwmWUdMiL46U,6832
transformers/models/fnet/__init__.py,sha256=spzYrdM_-MVYRr6Axeh_adtgX1pCDAsUJEpR-cPdxgE,3179
transformers/models/fnet/__pycache__/__init__.cpython-311.pyc,,
transformers/models/fnet/__pycache__/configuration_fnet.cpython-311.pyc,,
transformers/models/fnet/__pycache__/convert_fnet_original_flax_checkpoint_to_pytorch.cpython-311.pyc,,
transformers/models/fnet/__pycache__/modeling_fnet.cpython-311.pyc,,
transformers/models/fnet/__pycache__/tokenization_fnet.cpython-311.pyc,,
transformers/models/fnet/__pycache__/tokenization_fnet_fast.cpython-311.pyc,,
transformers/models/fnet/configuration_fnet.py,sha256=PksavUlr0EwQCQtTr7ettn1FUC0am9UoAC7JI5PkfEQ,5840
transformers/models/fnet/convert_fnet_original_flax_checkpoint_to_pytorch.py,sha256=bxrdtJbyINwJtiIpagL3Ttkq0D5ujBK1Wi72fIR2vss,6912
transformers/models/fnet/modeling_fnet.py,sha256=89ongdrN1WaUIjIpY4ObBTq5ykYBAX8rQymuYzrNDww,49109
transformers/models/fnet/tokenization_fnet.py,sha256=dD6ozclSam2GDVX0HJ11tjtGY1EbX6JyMkOURXo0QNc,15037
transformers/models/fnet/tokenization_fnet_fast.py,sha256=bqRInDENeJ7CEgKyUJ32_kD4t3aXojCsJxMg3rMly9I,8783
transformers/models/focalnet/__init__.py,sha256=RPvCimVzndLWR8r1MfUbrAiQTJEvJ6VGTM1OFmAS9-A,1989
transformers/models/focalnet/__pycache__/__init__.cpython-311.pyc,,
transformers/models/focalnet/__pycache__/configuration_focalnet.cpython-311.pyc,,
transformers/models/focalnet/__pycache__/convert_focalnet_to_hf_format.cpython-311.pyc,,
transformers/models/focalnet/__pycache__/modeling_focalnet.cpython-311.pyc,,
transformers/models/focalnet/configuration_focalnet.py,sha256=M1zYkLND2JNIEw6cgUifxIFV23Xjs3F1O65DzvX9LHU,8179
transformers/models/focalnet/convert_focalnet_to_hf_format.py,sha256=xBoop7K4unfPawCbmlv7BTQHpbJkaUWasrwsw8dW_KI,9450
transformers/models/focalnet/modeling_focalnet.py,sha256=hkG3bN9LH6JjX6SqXaC_8bETpK5xMKzr-4ZPzAk2kWA,43243
transformers/models/fsmt/__init__.py,sha256=e0xh51cBRMFkSYEcmZzyINHoXBKwgonWv3zEPqZuMYE,1675
transformers/models/fsmt/__pycache__/__init__.cpython-311.pyc,,
transformers/models/fsmt/__pycache__/configuration_fsmt.cpython-311.pyc,,
transformers/models/fsmt/__pycache__/convert_fsmt_original_pytorch_checkpoint_to_pytorch.cpython-311.pyc,,
transformers/models/fsmt/__pycache__/modeling_fsmt.cpython-311.pyc,,
transformers/models/fsmt/__pycache__/tokenization_fsmt.cpython-311.pyc,,
transformers/models/fsmt/configuration_fsmt.py,sha256=YfKdrPTsGnom_7jfi9EnUp_HUWAEGH8aRBPJT6MHqN4,10106
transformers/models/fsmt/convert_fsmt_original_pytorch_checkpoint_to_pytorch.py,sha256=BWtn90XQAuWGp8k9zns5St9On_os395ESNgkaXy6y2g,11264
transformers/models/fsmt/modeling_fsmt.py,sha256=Qo_LDfeYujqdZs99eeb6LElHYMFtZd4e2Q7Fd96M8I0,58402
transformers/models/fsmt/tokenization_fsmt.py,sha256=cboPC7nRumxw5XXKRFOLIw8_TSVpUidqodZwvfkzjjQ,20174
transformers/models/funnel/__init__.py,sha256=QQgGGD4BfFL3j1qtC1oNuuagXUPYWw0KJ4XVKTzMvW0,4126
transformers/models/funnel/__pycache__/__init__.cpython-311.pyc,,
transformers/models/funnel/__pycache__/configuration_funnel.cpython-311.pyc,,
transformers/models/funnel/__pycache__/convert_funnel_original_tf_checkpoint_to_pytorch.cpython-311.pyc,,
transformers/models/funnel/__pycache__/modeling_funnel.cpython-311.pyc,,
transformers/models/funnel/__pycache__/modeling_tf_funnel.cpython-311.pyc,,
transformers/models/funnel/__pycache__/tokenization_funnel.cpython-311.pyc,,
transformers/models/funnel/__pycache__/tokenization_funnel_fast.cpython-311.pyc,,
transformers/models/funnel/configuration_funnel.py,sha256=ZFnFPlWkHb1B3d1PZgJGcdmKYtNiPbyCsU0v-zsCKyw,8894
transformers/models/funnel/convert_funnel_original_tf_checkpoint_to_pytorch.py,sha256=fdaL7-j0ZWjCKvvpS_gFYHBthQ8TFbGmkOmfd53enaI,2335
transformers/models/funnel/modeling_funnel.py,sha256=7GUU9ygjRaNBL08jfkA6YHVADwwiZMjmhwS89Pd__3k,70075
transformers/models/funnel/modeling_tf_funnel.py,sha256=JlOJ8RLdRi-wjBnpFObtAXV0J4dVEJWPOyF2QjhvcEE,80793
transformers/models/funnel/tokenization_funnel.py,sha256=5cHVmhKIPuKJxhVP-iOOWaOemGklG-or8LpPzw7Hmpg,24119
transformers/models/funnel/tokenization_funnel_fast.py,sha256=FjPOy1yZMOSe7J8TIrX4yRzqNeQPsypYiGviGJW6aH0,11806
transformers/models/fuyu/__init__.py,sha256=SLRcFqITZh127We258kiNPRKoegottQTbpuCZ72dTBU,2184
transformers/models/fuyu/__pycache__/__init__.cpython-311.pyc,,
transformers/models/fuyu/__pycache__/configuration_fuyu.cpython-311.pyc,,
transformers/models/fuyu/__pycache__/convert_fuyu_model_weights_to_hf.cpython-311.pyc,,
transformers/models/fuyu/__pycache__/image_processing_fuyu.cpython-311.pyc,,
transformers/models/fuyu/__pycache__/modeling_fuyu.cpython-311.pyc,,
transformers/models/fuyu/__pycache__/processing_fuyu.cpython-311.pyc,,
transformers/models/fuyu/configuration_fuyu.py,sha256=xhAKeF8AxXaTlAGOY_fGk9ujvWZ61z4PBuINMPEqqkU,10208
transformers/models/fuyu/convert_fuyu_model_weights_to_hf.py,sha256=c8A4qiUY47MfPeEG518qofxFdzut0me3EtFNizEHv6Q,4847
transformers/models/fuyu/image_processing_fuyu.py,sha256=smqlsqyLOOw3oF8aNpfG9DP8fOsDBdajiHA6NUt91R8,33340
transformers/models/fuyu/modeling_fuyu.py,sha256=KBl2Z4qa6NVjc-uzHBOkQDTjiBoFCpymEyeKTMmCFyY,17650
transformers/models/fuyu/processing_fuyu.py,sha256=DUTZ_Bxu5mHxnsnJYYHleKF79cx_XXVcCvZP6VguhZQ,32002
transformers/models/gemma/__init__.py,sha256=boIWLnLMFp69VbfjGEcoCMTSObbY_0OevWvwBOa29Xg,3339
transformers/models/gemma/__pycache__/__init__.cpython-311.pyc,,
transformers/models/gemma/__pycache__/configuration_gemma.cpython-311.pyc,,
transformers/models/gemma/__pycache__/convert_gemma_weights_to_hf.cpython-311.pyc,,
transformers/models/gemma/__pycache__/modeling_flax_gemma.cpython-311.pyc,,
transformers/models/gemma/__pycache__/modeling_gemma.cpython-311.pyc,,
transformers/models/gemma/__pycache__/tokenization_gemma.cpython-311.pyc,,
transformers/models/gemma/__pycache__/tokenization_gemma_fast.cpython-311.pyc,,
transformers/models/gemma/configuration_gemma.py,sha256=MQt0C4zDpDjGQcltYvM0rvKDhFVmxHIDxJ1bsxyrxdM,6677
transformers/models/gemma/convert_gemma_weights_to_hf.py,sha256=-WVAvo44VMCAtmhT2KuNvCliszen_yMPOki3KUIbSAo,7120
transformers/models/gemma/modeling_flax_gemma.py,sha256=_9EVZo4YOBiHs6IU9E6b0k0x2v2LN0LyCoDGucgLJa8,31738
transformers/models/gemma/modeling_gemma.py,sha256=QdyAOmkEYgT3iDqGI4nCLW-aI_YQkaIJ3Tq94TZvrC8,60182
transformers/models/gemma/tokenization_gemma.py,sha256=CXHdN19ZMBvqfFeIEyF-p92iJO1umUakJ1sfPLOOaiY,13981
transformers/models/gemma/tokenization_gemma_fast.py,sha256=bTIi46E_PXDmRwD8hQG0AI6HRlj-03Y7itWFA6tclQE,8279
transformers/models/git/__init__.py,sha256=KG0HrIdVgj64GVVUk32IdidJRaC5BcjQZt62oVRL5Eo,1888
transformers/models/git/__pycache__/__init__.cpython-311.pyc,,
transformers/models/git/__pycache__/configuration_git.cpython-311.pyc,,
transformers/models/git/__pycache__/convert_git_to_pytorch.cpython-311.pyc,,
transformers/models/git/__pycache__/modeling_git.cpython-311.pyc,,
transformers/models/git/__pycache__/processing_git.cpython-311.pyc,,
transformers/models/git/configuration_git.py,sha256=3qUPJQ2eelvgWmbiQRNXoJijt3_ldAniAHJU7_JjRnM,11352
transformers/models/git/convert_git_to_pytorch.py,sha256=HzsGAVKq7fhWCgI89QsSEDUO1IaQn0LNPkprFq3-vYk,22390
transformers/models/git/modeling_git.py,sha256=9SZLmLmz5dgybA9tfTkzyXCqWzGpHS7ecBHNYpyY5lg,69155
transformers/models/git/processing_git.py,sha256=oyuFkaTwqpdw_lpKXn4hAz5qfIfyI14Ug441OaK-Smk,5487
transformers/models/glpn/__init__.py,sha256=-5zqCuk1phx-Bjw3Mq-NJmPvusXfEYcNGIrFO27vr3s,2384
transformers/models/glpn/__pycache__/__init__.cpython-311.pyc,,
transformers/models/glpn/__pycache__/configuration_glpn.cpython-311.pyc,,
transformers/models/glpn/__pycache__/convert_glpn_to_pytorch.cpython-311.pyc,,
transformers/models/glpn/__pycache__/feature_extraction_glpn.cpython-311.pyc,,
transformers/models/glpn/__pycache__/image_processing_glpn.cpython-311.pyc,,
transformers/models/glpn/__pycache__/modeling_glpn.cpython-311.pyc,,
transformers/models/glpn/configuration_glpn.py,sha256=yP7RSxhZITNay7YOlT0csI5bTEudOC9YDMc9Gs6r1Z8,6185
transformers/models/glpn/convert_glpn_to_pytorch.py,sha256=dT5q2vCISTu1DjoTkLSyHmlcR75n_CGhXxxknL5KjJQ,8558
transformers/models/glpn/feature_extraction_glpn.py,sha256=S263LFeHVRym_jKt8KkTOjjtA1_BqARnUgbSFExgPN4,1172
transformers/models/glpn/image_processing_glpn.py,sha256=WYLlIeKATxgz6V3sFAVCUXlLnWFDFRlUJM3aNOj3aII,10613
transformers/models/glpn/modeling_glpn.py,sha256=3lc1xIpuLhOogdWFB_6b-UFuyxgAkpOTB9JpVdeRK34,31547
transformers/models/gpt2/__init__.py,sha256=d_QyBAIVXohGlkOMWC9r03kE9uS2IHwXwPCsxnMGGkg,4674
transformers/models/gpt2/__pycache__/__init__.cpython-311.pyc,,
transformers/models/gpt2/__pycache__/configuration_gpt2.cpython-311.pyc,,
transformers/models/gpt2/__pycache__/convert_gpt2_original_tf_checkpoint_to_pytorch.cpython-311.pyc,,
transformers/models/gpt2/__pycache__/modeling_flax_gpt2.cpython-311.pyc,,
transformers/models/gpt2/__pycache__/modeling_gpt2.cpython-311.pyc,,
transformers/models/gpt2/__pycache__/modeling_tf_gpt2.cpython-311.pyc,,
transformers/models/gpt2/__pycache__/tokenization_gpt2.cpython-311.pyc,,
transformers/models/gpt2/__pycache__/tokenization_gpt2_fast.cpython-311.pyc,,
transformers/models/gpt2/__pycache__/tokenization_gpt2_tf.cpython-311.pyc,,
transformers/models/gpt2/configuration_gpt2.py,sha256=37DXpCGfQbE4LtLTMji_-bb3eRIVR3aPqqbo7XlU3ZA,12567
transformers/models/gpt2/convert_gpt2_original_tf_checkpoint_to_pytorch.py,sha256=nRAxbikMz9v88rDqfrX8OwPvBKe7fiYC2fg-6BB8Mzk,2532
transformers/models/gpt2/modeling_flax_gpt2.py,sha256=6vAeL1SwHlYUxTwHmfHXEYLuvTJoLRq5zl_GwUm5PiE,32014
transformers/models/gpt2/modeling_gpt2.py,sha256=WkOm2JWwBrW-mslFn9KBQRrsX0vy0raITjoI_9NClDg,77098
transformers/models/gpt2/modeling_tf_gpt2.py,sha256=VZppA16GmloGC1uiJkQToP9hsVtlsrcxRDN0YY3NjfM,56887
transformers/models/gpt2/tokenization_gpt2.py,sha256=9R4orpHGa10xQlHJbe-c9nUhVp5agRcwj7xQbQm1UAA,15416
transformers/models/gpt2/tokenization_gpt2_fast.py,sha256=6K-hctpTtVox8vdH3JxmcOioxMTTFxXqf7tVfDBnRNY,8710
transformers/models/gpt2/tokenization_gpt2_tf.py,sha256=Ptg01f1bV0fAvI1JK6v-FE4lVKUPIiXrxxPrf8M7kgU,3833
transformers/models/gpt_bigcode/__init__.py,sha256=waW0WeT6jgb8gWpaGmMZBJCYoqKzCbaQbyjHZkuEARE,2037
transformers/models/gpt_bigcode/__pycache__/__init__.cpython-311.pyc,,
transformers/models/gpt_bigcode/__pycache__/configuration_gpt_bigcode.cpython-311.pyc,,
transformers/models/gpt_bigcode/__pycache__/modeling_gpt_bigcode.cpython-311.pyc,,
transformers/models/gpt_bigcode/configuration_gpt_bigcode.py,sha256=wx2iMdoaWmm8_aAIqyO0fEFNw3m3riQLBXc9sU3R2Sc,6448
transformers/models/gpt_bigcode/modeling_gpt_bigcode.py,sha256=2P-2cZ4FqsXhREAUF4tGoRJO0fOJkLuOL8ux8VjQrDM,69989
transformers/models/gpt_neo/__init__.py,sha256=tCBf4wXQijfaRh959WfU7_npuc1na00rwCZCgcxuTOo,2718
transformers/models/gpt_neo/__pycache__/__init__.cpython-311.pyc,,
transformers/models/gpt_neo/__pycache__/configuration_gpt_neo.cpython-311.pyc,,
transformers/models/gpt_neo/__pycache__/convert_gpt_neo_mesh_tf_to_pytorch.cpython-311.pyc,,
transformers/models/gpt_neo/__pycache__/modeling_flax_gpt_neo.cpython-311.pyc,,
transformers/models/gpt_neo/__pycache__/modeling_gpt_neo.cpython-311.pyc,,
transformers/models/gpt_neo/configuration_gpt_neo.py,sha256=yWLSJwjfaSINM9f-gQq_tE-KBAO4uIihDlK5QJs41qA,12059
transformers/models/gpt_neo/convert_gpt_neo_mesh_tf_to_pytorch.py,sha256=SSlCsIZmkN010Cu64F4lxwHcQRsqEGbb7a6PqCSWJY0,2589
transformers/models/gpt_neo/modeling_flax_gpt_neo.py,sha256=xgwE5UixFan9wDb9ScOd8DcEH-o1Iu-AX1bNkMWQFEA,28074
transformers/models/gpt_neo/modeling_gpt_neo.py,sha256=u_MEHo6lr2LpW9K7Lh1jPObBWdWV7Cwcf7CYU6U7N-U,58319
transformers/models/gpt_neox/__init__.py,sha256=NETOJyNfZJ1SXJ4jc1heeVs2TMqXjlbminmJQKSnLnA,2595
transformers/models/gpt_neox/__pycache__/__init__.cpython-311.pyc,,
transformers/models/gpt_neox/__pycache__/configuration_gpt_neox.cpython-311.pyc,,
transformers/models/gpt_neox/__pycache__/modeling_gpt_neox.cpython-311.pyc,,
transformers/models/gpt_neox/__pycache__/tokenization_gpt_neox_fast.cpython-311.pyc,,
transformers/models/gpt_neox/configuration_gpt_neox.py,sha256=rKVamUrQZDsiLUoz6SSI6PRbe38HwZzVnPk5O8gzbIU,9071
transformers/models/gpt_neox/modeling_gpt_neox.py,sha256=f4gHAH3YrVa1AacY6mS0TsMOUSvqgIU1qCz0c9BPMwk,64887
transformers/models/gpt_neox/tokenization_gpt_neox_fast.py,sha256=hgMTjp5DvI5WJuzZzebrqWma-gQmr8IpA-fPJ3t5I_U,6048
transformers/models/gpt_neox_japanese/__init__.py,sha256=7S5Q5Y8aQPbcoaPjIVo7s9ebHh0GLv3cA1TeAhzvFFA,2154
transformers/models/gpt_neox_japanese/__pycache__/__init__.cpython-311.pyc,,
transformers/models/gpt_neox_japanese/__pycache__/configuration_gpt_neox_japanese.cpython-311.pyc,,
transformers/models/gpt_neox_japanese/__pycache__/modeling_gpt_neox_japanese.cpython-311.pyc,,
transformers/models/gpt_neox_japanese/__pycache__/tokenization_gpt_neox_japanese.cpython-311.pyc,,
transformers/models/gpt_neox_japanese/configuration_gpt_neox_japanese.py,sha256=MydoF25igdWh-llKyXwAJYr3-pYCkjuJj-vY_4eKtfc,5730
transformers/models/gpt_neox_japanese/modeling_gpt_neox_japanese.py,sha256=YGke--RPMxlzNiveUYTo3uNCV_ok2Wa_FBbZbNi_VaY,32511
transformers/models/gpt_neox_japanese/tokenization_gpt_neox_japanese.py,sha256=FQmPqik3mKrBdIp87XXppUUWQuhxBGprjkNc9EACPpI,17622
transformers/models/gpt_sw3/__init__.py,sha256=qJj7vF8ES37BwsKbJE1zV2rPUdmM3vx8mckIFuWrJSU,1361
transformers/models/gpt_sw3/__pycache__/__init__.cpython-311.pyc,,
transformers/models/gpt_sw3/__pycache__/convert_megatron_to_pytorch.cpython-311.pyc,,
transformers/models/gpt_sw3/__pycache__/tokenization_gpt_sw3.cpython-311.pyc,,
transformers/models/gpt_sw3/convert_megatron_to_pytorch.py,sha256=11EGXgi73zwRchm4aMlHE7tCom4_oGLQSWF1YMpBBQA,8156
transformers/models/gpt_sw3/tokenization_gpt_sw3.py,sha256=G61x_ErU7ZNoA6wxZ3gymssQw0GUzRrCNVZKXi46ffM,14915
transformers/models/gptj/__init__.py,sha256=wBErGYabUQpzDULOVQSE9vEvefKWJvJFoU9p0t54qDU,3280
transformers/models/gptj/__pycache__/__init__.cpython-311.pyc,,
transformers/models/gptj/__pycache__/configuration_gptj.cpython-311.pyc,,
transformers/models/gptj/__pycache__/modeling_flax_gptj.cpython-311.pyc,,
transformers/models/gptj/__pycache__/modeling_gptj.cpython-311.pyc,,
transformers/models/gptj/__pycache__/modeling_tf_gptj.cpython-311.pyc,,
transformers/models/gptj/configuration_gptj.py,sha256=etH7ja7cRkDCYgLyTeR-OVlt_7ulU1w6OF3bfNvfnZ0,8997
transformers/models/gptj/modeling_flax_gptj.py,sha256=VaYTrxQosqkIqHcbKcDFinT_z3aofwdJLasWAqxjRlM,28525
transformers/models/gptj/modeling_gptj.py,sha256=zuKevClvsFTJMPxWnmleZE-bsozJAQb9dNutWSOM1BM,50428
transformers/models/gptj/modeling_tf_gptj.py,sha256=kOYv0PyDJlhY9fMwCMSrtorF2zOI4IkFm5fc2Te7mg4,48207
transformers/models/gptsan_japanese/__init__.py,sha256=gkfCyeWUjR_u2kxoe0nD-gLdcFoS4SwjhQBNufTY86w,2294
transformers/models/gptsan_japanese/__pycache__/__init__.cpython-311.pyc,,
transformers/models/gptsan_japanese/__pycache__/configuration_gptsan_japanese.cpython-311.pyc,,
transformers/models/gptsan_japanese/__pycache__/convert_gptsan_tf_checkpoint_to_pytorch.cpython-311.pyc,,
transformers/models/gptsan_japanese/__pycache__/modeling_gptsan_japanese.cpython-311.pyc,,
transformers/models/gptsan_japanese/__pycache__/tokenization_gptsan_japanese.cpython-311.pyc,,
transformers/models/gptsan_japanese/configuration_gptsan_japanese.py,sha256=3WE1dYGKgyADW5IQH9ga0geXzpzImxpKIMyXB5PoImY,7330
transformers/models/gptsan_japanese/convert_gptsan_tf_checkpoint_to_pytorch.py,sha256=syF4TCbLQByZhm5VqIFgXfzQ4zImmCua8UNjCYJP5t8,9793
transformers/models/gptsan_japanese/modeling_gptsan_japanese.py,sha256=Bmtc3nJ2wakSAwoTEf00061E3NzlB_IT4a_38YNpBRc,66681
transformers/models/gptsan_japanese/tokenization_gptsan_japanese.py,sha256=UvpwBISnjcxoGdPghSgVCamVBsLA3Mfn8o5EN7aHhIY,24762
transformers/models/graphormer/__init__.py,sha256=SCL3NOPe62lQVk-qWrJD1enP6JNBWyPreg5EGaifjbE,1873
transformers/models/graphormer/__pycache__/__init__.cpython-311.pyc,,
transformers/models/graphormer/__pycache__/collating_graphormer.cpython-311.pyc,,
transformers/models/graphormer/__pycache__/configuration_graphormer.cpython-311.pyc,,
transformers/models/graphormer/__pycache__/modeling_graphormer.cpython-311.pyc,,
transformers/models/graphormer/algos_graphormer.pyx,sha256=b_Qlm1hKCHnAqx6oOLGC9LkivAV0K_AZRGgXT9MmBas,3635
transformers/models/graphormer/collating_graphormer.py,sha256=1r_YqrFzC6uWCaPCsGMqNkvHNKs6SCV1bSw2qLyAYJA,6086
transformers/models/graphormer/configuration_graphormer.py,sha256=YmBNNGT13ZGp80sAk54LTNKvyAWaDcEYuHcuW9cP014,10651
transformers/models/graphormer/modeling_graphormer.py,sha256=w0AUgubPtFjWVkPo4z3n7LEYE5E5FLffWl7fFXHawGk,37223
transformers/models/groupvit/__init__.py,sha256=rO2THuhEVPYRh__0tgdPS9egtqSugEkoXU4lDMAg3q0,2875
transformers/models/groupvit/__pycache__/__init__.cpython-311.pyc,,
transformers/models/groupvit/__pycache__/configuration_groupvit.cpython-311.pyc,,
transformers/models/groupvit/__pycache__/convert_groupvit_nvlab_to_hf.cpython-311.pyc,,
transformers/models/groupvit/__pycache__/modeling_groupvit.cpython-311.pyc,,
transformers/models/groupvit/__pycache__/modeling_tf_groupvit.cpython-311.pyc,,
transformers/models/groupvit/configuration_groupvit.py,sha256=fO6qgd4-MznF-4vmvqyn-gnGvFyMuNe9r6VrHgkVUFU,20850
transformers/models/groupvit/convert_groupvit_nvlab_to_hf.py,sha256=9gQxkcjVNCP5lvV54SbbSsOjkKCHORcoiwq2gcczYCM,9775
transformers/models/groupvit/modeling_groupvit.py,sha256=dzhhud9iz5z2F9oqYjkZ0ORgvYfwRZic7ze3TxKrUlg,67941
transformers/models/groupvit/modeling_tf_groupvit.py,sha256=0s3CBj7RDrd5bKdtKFBl8KXYmLozBA3OG7VwtDtoZ9k,89905
transformers/models/herbert/__init__.py,sha256=Sp9gQIqlUhZHausuaL2MFYDqJW4vvsVGLbVryR-kNl0,1472
transformers/models/herbert/__pycache__/__init__.cpython-311.pyc,,
transformers/models/herbert/__pycache__/tokenization_herbert.cpython-311.pyc,,
transformers/models/herbert/__pycache__/tokenization_herbert_fast.cpython-311.pyc,,
transformers/models/herbert/tokenization_herbert.py,sha256=CIRo3RXl494ufqpISLTEqmEI6F92R5osJLRh1J5S22w,25665
transformers/models/herbert/tokenization_herbert_fast.py,sha256=RmXQlJtd_l75QrDCYTli6Gny4-brixe9LVJfjNFSL4s,6549
transformers/models/hubert/__init__.py,sha256=rfeBnkDY2iMz8xs_cZY4wSMSxoXQeVQov-C42xhA0eE,2536
transformers/models/hubert/__pycache__/__init__.cpython-311.pyc,,
transformers/models/hubert/__pycache__/configuration_hubert.cpython-311.pyc,,
transformers/models/hubert/__pycache__/convert_distilhubert_original_s3prl_checkpoint_to_pytorch.cpython-311.pyc,,
transformers/models/hubert/__pycache__/convert_hubert_original_pytorch_checkpoint_to_pytorch.cpython-311.pyc,,
transformers/models/hubert/__pycache__/convert_hubert_original_s3prl_checkpoint_to_pytorch.cpython-311.pyc,,
transformers/models/hubert/__pycache__/modeling_hubert.cpython-311.pyc,,
transformers/models/hubert/__pycache__/modeling_tf_hubert.cpython-311.pyc,,
transformers/models/hubert/configuration_hubert.py,sha256=4eLUW0q-UnVcyZR5K2t_JBV3vPqlDQYMc-O4YHGWFhA,14907
transformers/models/hubert/convert_distilhubert_original_s3prl_checkpoint_to_pytorch.py,sha256=ENEJNVBI7j5N6ajvUnNEAfSIM6VfEmpI8dF86R4EDog,8942
transformers/models/hubert/convert_hubert_original_pytorch_checkpoint_to_pytorch.py,sha256=tVrpW4Mqkymh6pcLdYdTtkl0ykhSkHNvfTefbBIpR7w,10380
transformers/models/hubert/convert_hubert_original_s3prl_checkpoint_to_pytorch.py,sha256=BtUOQ6Jf7kppeKreWA76AvQNdy_a63t2iuq0yHvEs4E,2895
transformers/models/hubert/modeling_hubert.py,sha256=1NpiOUPbatpppXObppE7aqlRp7SXJ_Vo5X3JtFY6qqY,60186
transformers/models/hubert/modeling_tf_hubert.py,sha256=bYfWbvc4Yxw_ybfNNVHF8gGuun1iPNtNFtThzJFiNV8,70842
transformers/models/ibert/__init__.py,sha256=uw-Mi7HIih0Or_1DeCK7Ooc20kBdmqokZ6GEDwOD9LU,2086
transformers/models/ibert/__pycache__/__init__.cpython-311.pyc,,
transformers/models/ibert/__pycache__/configuration_ibert.cpython-311.pyc,,
transformers/models/ibert/__pycache__/modeling_ibert.cpython-311.pyc,,
transformers/models/ibert/__pycache__/quant_modules.cpython-311.pyc,,
transformers/models/ibert/configuration_ibert.py,sha256=Y-SWRF56CuLQpoDRgayeOI6mVIFJtBQE80wGY75cT0g,7462
transformers/models/ibert/modeling_ibert.py,sha256=Q5_Cx9uiis6diaC6BA3vk3jVgyLZFKdNPc8CBW_DUvg,56785
transformers/models/ibert/quant_modules.py,sha256=ItU76CIx0XcZCPOR21dz99J9k5rK2fzffQz0jJCuNmM,30072
transformers/models/idefics/__init__.py,sha256=XnXH7RPak98A3W6H9eW1o8eiVgxgAMKoi6xAkKBOL8o,2360
transformers/models/idefics/__pycache__/__init__.cpython-311.pyc,,
transformers/models/idefics/__pycache__/configuration_idefics.cpython-311.pyc,,
transformers/models/idefics/__pycache__/image_processing_idefics.cpython-311.pyc,,
transformers/models/idefics/__pycache__/modeling_idefics.cpython-311.pyc,,
transformers/models/idefics/__pycache__/perceiver.cpython-311.pyc,,
transformers/models/idefics/__pycache__/processing_idefics.cpython-311.pyc,,
transformers/models/idefics/__pycache__/vision.cpython-311.pyc,,
transformers/models/idefics/configuration_idefics.py,sha256=JyKHuYXhQzBwFiVsHm9z3_W158JfR0z2NqWA6ICdrQ4,15625
transformers/models/idefics/image_processing_idefics.py,sha256=xcHYUAzAgIaXk92aU0YY83scvQdpQekN37UJll9utdg,7801
transformers/models/idefics/modeling_idefics.py,sha256=ovXRbK2MZ5kEEb4Yro-ev0tTRkz40G_i1ODCp_zyVz4,73093
transformers/models/idefics/perceiver.py,sha256=RtKLRu3IIjUHCYcLAgZyirDbxK-ZlKKts_to0fv1x6o,9432
transformers/models/idefics/processing_idefics.py,sha256=5bRpxhLCAZnZXvhomo6oVbIdyiuaOhODOVXO-iZeRV4,17904
transformers/models/idefics/vision.py,sha256=B27HyrQNrY9l9o--jMQmL9NdkJRVqYt2u36TXiyNQSs,22502
transformers/models/imagegpt/__init__.py,sha256=aPsv_YVn82O_HHaFDIsYqe8bR8hs3sk1RUlcCtaUWcc,2658
transformers/models/imagegpt/__pycache__/__init__.cpython-311.pyc,,
transformers/models/imagegpt/__pycache__/configuration_imagegpt.cpython-311.pyc,,
transformers/models/imagegpt/__pycache__/convert_imagegpt_original_tf2_to_pytorch.cpython-311.pyc,,
transformers/models/imagegpt/__pycache__/feature_extraction_imagegpt.cpython-311.pyc,,
transformers/models/imagegpt/__pycache__/image_processing_imagegpt.cpython-311.pyc,,
transformers/models/imagegpt/__pycache__/modeling_imagegpt.cpython-311.pyc,,
transformers/models/imagegpt/configuration_imagegpt.py,sha256=oVgzGNNlNNH_pYSnKA4Qd5iVX69p2csJeCcxvrVH0bM,8866
transformers/models/imagegpt/convert_imagegpt_original_tf2_to_pytorch.py,sha256=yneGtcrTR4Ui38NG8ogK7N_4dAyTiVBkmc8JQERb2bs,2691
transformers/models/imagegpt/feature_extraction_imagegpt.py,sha256=iCpQ4tU3Vml44KgO43kYJvv-RcZVxe8tc794gxUktuU,1200
transformers/models/imagegpt/image_processing_imagegpt.py,sha256=lqmfsFnzknpyuw0HwqVcbWh00o3B1zWIoeOJPc0k_-c,14251
transformers/models/imagegpt/modeling_imagegpt.py,sha256=9nPvSMdhi9xaH4uLsfBYzmDCRaV9IoidV9Frxh-9H58,53794
transformers/models/informer/__init__.py,sha256=VylZIY0U5EuIfEuvphPh-gCCgBtwRAByccv11nsTA5Q,1857
transformers/models/informer/__pycache__/__init__.cpython-311.pyc,,
transformers/models/informer/__pycache__/configuration_informer.cpython-311.pyc,,
transformers/models/informer/__pycache__/modeling_informer.cpython-311.pyc,,
transformers/models/informer/configuration_informer.py,sha256=zk8COGcN9U6Z27l8v-rnaydxhc1iCZIHDYegvkTmlcM,12685
transformers/models/informer/modeling_informer.py,sha256=LSGgX5OJl7MPXdA2oaEIhPnV1y2-bt31JCpQqBCtVXI,101601
transformers/models/instructblip/__init__.py,sha256=GpbqWHExuUvlsDeouDhVv-f_etjU9Dwm006DwFiAMEg,2279
transformers/models/instructblip/__pycache__/__init__.cpython-311.pyc,,
transformers/models/instructblip/__pycache__/configuration_instructblip.cpython-311.pyc,,
transformers/models/instructblip/__pycache__/convert_instructblip_original_to_pytorch.cpython-311.pyc,,
transformers/models/instructblip/__pycache__/modeling_instructblip.cpython-311.pyc,,
transformers/models/instructblip/__pycache__/processing_instructblip.cpython-311.pyc,,
transformers/models/instructblip/configuration_instructblip.py,sha256=677T_76UozXK3mP26tGNE2YFqJseuXdnFZez26WJIU8,17239
transformers/models/instructblip/convert_instructblip_original_to_pytorch.py,sha256=iustpBsjHHzjQzbAhPJvhI7ZBSXCDoa9njtK9m_gm_I,13399
transformers/models/instructblip/modeling_instructblip.py,sha256=MtO2ni9e_6o6zuMXes6SJTlLLuNC-mbZpGtc4wCVaUs,70301
transformers/models/instructblip/processing_instructblip.py,sha256=zJT2QvAzlJAFlADmSSr36VWNB6xLpazrqFmp3og5AE8,7856
transformers/models/jukebox/__init__.py,sha256=kZx3ZvfTUb90bEGC0UVrqOfoJvIWSBrUOR701WATaHI,2084
transformers/models/jukebox/__pycache__/__init__.cpython-311.pyc,,
transformers/models/jukebox/__pycache__/configuration_jukebox.cpython-311.pyc,,
transformers/models/jukebox/__pycache__/convert_jukebox.cpython-311.pyc,,
transformers/models/jukebox/__pycache__/modeling_jukebox.cpython-311.pyc,,
transformers/models/jukebox/__pycache__/tokenization_jukebox.cpython-311.pyc,,
transformers/models/jukebox/configuration_jukebox.py,sha256=L7XY1pQpu0mPRuFlzSO9lXCH3Yv8lVwby8Hs09HgzbU,27002
transformers/models/jukebox/convert_jukebox.py,sha256=RBgOPbwIMv_42mUFJYxRv4IAGZn4cAzjTqjrMI7HtVg,11789
transformers/models/jukebox/modeling_jukebox.py,sha256=agpQO9h99JBgyI41TCtCPWVcK0IuCb1C5UnZq1GykmA,119653
transformers/models/jukebox/tokenization_jukebox.py,sha256=u1VMDg1MdQRUWzHZ12llZEQavZIOdKTRzT2snKmVbH8,17892
transformers/models/kosmos2/__init__.py,sha256=jUzMFMa0nRBdsr0AdK08cnugtfuAWiZTFgOow25AY5o,1967
transformers/models/kosmos2/__pycache__/__init__.cpython-311.pyc,,
transformers/models/kosmos2/__pycache__/configuration_kosmos2.cpython-311.pyc,,
transformers/models/kosmos2/__pycache__/convert_kosmos2_original_pytorch_checkpoint_to_pytorch.cpython-311.pyc,,
transformers/models/kosmos2/__pycache__/modeling_kosmos2.cpython-311.pyc,,
transformers/models/kosmos2/__pycache__/processing_kosmos2.cpython-311.pyc,,
transformers/models/kosmos2/configuration_kosmos2.py,sha256=k9kUBAv8mhN2rX38h9j3ky2KZQfn-vf1ekx1KCmCXAI,13481
transformers/models/kosmos2/convert_kosmos2_original_pytorch_checkpoint_to_pytorch.py,sha256=3ejv6hUd6irzFnmSuFVI6Eu1NVWmtJf3_ql2h9P4AHk,2724
transformers/models/kosmos2/modeling_kosmos2.py,sha256=Lc95avzrwur-xuq3b4zctSkQt9yRu8XirXsClKHT2kI,95056
transformers/models/kosmos2/processing_kosmos2.py,sha256=wwLhLGgBBgpFeRWC3os8SXLI18od-NJagHFJMe9QROo,29760
transformers/models/layoutlm/__init__.py,sha256=x-7_rGXFn-NroxQIFjQru0Rz5VfmQmINEhahNPm7R8w,3787
transformers/models/layoutlm/__pycache__/__init__.cpython-311.pyc,,
transformers/models/layoutlm/__pycache__/configuration_layoutlm.cpython-311.pyc,,
transformers/models/layoutlm/__pycache__/modeling_layoutlm.cpython-311.pyc,,
transformers/models/layoutlm/__pycache__/modeling_tf_layoutlm.cpython-311.pyc,,
transformers/models/layoutlm/__pycache__/tokenization_layoutlm.cpython-311.pyc,,
transformers/models/layoutlm/__pycache__/tokenization_layoutlm_fast.cpython-311.pyc,,
transformers/models/layoutlm/configuration_layoutlm.py,sha256=9ZiQCdKVP6QCvlYOVBdq3UP7W39e_D_4cPHx1WBb_i0,9405
transformers/models/layoutlm/modeling_layoutlm.py,sha256=TIq4kbeg8CLnXKU2k6tvsaP7hTcCmuOLP6lZRaNynzY,60895
transformers/models/layoutlm/modeling_tf_layoutlm.py,sha256=wk4Dbg85D2qBCTy83c9jtSaNw_uMpM9jLd48YvDSLJE,73222
transformers/models/layoutlm/tokenization_layoutlm.py,sha256=5j9ZSKTEEngpVjSOMvD19nR2nqsXrrnKMi-SKhPiAKE,21795
transformers/models/layoutlm/tokenization_layoutlm_fast.py,sha256=imVKvRHe4UPql7w8EYIMXso7JrUEWehLRxgEO4c1q2k,8979
transformers/models/layoutlmv2/__init__.py,sha256=Ue5kj1_LyJNklq6UPXvNuaAXj_gadMT8lXxwQwIPsvY,3439
transformers/models/layoutlmv2/__pycache__/__init__.cpython-311.pyc,,
transformers/models/layoutlmv2/__pycache__/configuration_layoutlmv2.cpython-311.pyc,,
transformers/models/layoutlmv2/__pycache__/feature_extraction_layoutlmv2.cpython-311.pyc,,
transformers/models/layoutlmv2/__pycache__/image_processing_layoutlmv2.cpython-311.pyc,,
transformers/models/layoutlmv2/__pycache__/modeling_layoutlmv2.cpython-311.pyc,,
transformers/models/layoutlmv2/__pycache__/processing_layoutlmv2.cpython-311.pyc,,
transformers/models/layoutlmv2/__pycache__/tokenization_layoutlmv2.cpython-311.pyc,,
transformers/models/layoutlmv2/__pycache__/tokenization_layoutlmv2_fast.cpython-311.pyc,,
transformers/models/layoutlmv2/configuration_layoutlmv2.py,sha256=HK8u2pMV7PRdnfuSjtzChCp6JyDC7uWy937lhAENUZE,11247
transformers/models/layoutlmv2/feature_extraction_layoutlmv2.py,sha256=M9bDCpKBLI5paxor4ioa2JjEDhSH9Np-PTbgHh2V9KI,1195
transformers/models/layoutlmv2/image_processing_layoutlmv2.py,sha256=x8UCDd0_5gPsixlsHCFCbMWniKMlk1IpaIu8QkU_8g4,13372
transformers/models/layoutlmv2/modeling_layoutlmv2.py,sha256=XNI3WB4y1P0MRbpFPOcdlPc6aiShPpd2RIcZDeFN1Jc,60718
transformers/models/layoutlmv2/processing_layoutlmv2.py,sha256=xyhBq9pYYmNYOfK2c13gA-f1cWzu1fp0kO6FC7J9DfI,9292
transformers/models/layoutlmv2/tokenization_layoutlmv2.py,sha256=rT43zfh0DzxU9pgJpips5JTrliWXx7dHyPTFVLL_daA,72933
transformers/models/layoutlmv2/tokenization_layoutlmv2_fast.py,sha256=CcVmZyBTfxIksP9kLYhv49ifDPUF8mND8oqGwGtLf-I,38073
transformers/models/layoutlmv3/__init__.py,sha256=A4PpxK2Rhqx_ybVzlT5h9W6SyRSwndLqD5-eVKBz4ok,4512
transformers/models/layoutlmv3/__pycache__/__init__.cpython-311.pyc,,
transformers/models/layoutlmv3/__pycache__/configuration_layoutlmv3.cpython-311.pyc,,
transformers/models/layoutlmv3/__pycache__/feature_extraction_layoutlmv3.cpython-311.pyc,,
transformers/models/layoutlmv3/__pycache__/image_processing_layoutlmv3.cpython-311.pyc,,
transformers/models/layoutlmv3/__pycache__/modeling_layoutlmv3.cpython-311.pyc,,
transformers/models/layoutlmv3/__pycache__/modeling_tf_layoutlmv3.cpython-311.pyc,,
transformers/models/layoutlmv3/__pycache__/processing_layoutlmv3.cpython-311.pyc,,
transformers/models/layoutlmv3/__pycache__/tokenization_layoutlmv3.cpython-311.pyc,,
transformers/models/layoutlmv3/__pycache__/tokenization_layoutlmv3_fast.cpython-311.pyc,,
transformers/models/layoutlmv3/configuration_layoutlmv3.py,sha256=7G3su-ozDpPaqZHe-0FVW28ML5D4TuL6AEAZwUb9TzM,13363
transformers/models/layoutlmv3/feature_extraction_layoutlmv3.py,sha256=jWsmsi2mym0meek1lHWqfqxlJgMJdY3cgfQ_4ASEbto,1195
transformers/models/layoutlmv3/image_processing_layoutlmv3.py,sha256=EAhU_Go4D14eq7xC5EkjyrdNZtQ07klsFO8OrtYB_cg,18241
transformers/models/layoutlmv3/modeling_layoutlmv3.py,sha256=snVTYiTRoTSIvco7WRf8PDU16oBJVH5RrBWWptqprAU,59908
transformers/models/layoutlmv3/modeling_tf_layoutlmv3.py,sha256=K9uaYsxZwQLxdm4jSY5R-1V9UjWztLihAqkwRtFe2zk,76880
transformers/models/layoutlmv3/processing_layoutlmv3.py,sha256=ShtvBmZjGHbprdB14v2QsIgVir-74gEnTGHzvL31vCI,9143
transformers/models/layoutlmv3/tokenization_layoutlmv3.py,sha256=RX1jb7pk-amGv9Z2Ens_OsdTpkfNs81uelKB9z_BwBs,72834
transformers/models/layoutlmv3/tokenization_layoutlmv3_fast.py,sha256=FyBazrusyKa5dffeIwoLPhf1MQBkMb9NrFJrv4J0gQ8,40311
transformers/models/layoutxlm/__init__.py,sha256=AIvjzuqRPFXFuWXxnOlp9pBXaIT5Zzx7fwtg2KKVETA,2037
transformers/models/layoutxlm/__pycache__/__init__.cpython-311.pyc,,
transformers/models/layoutxlm/__pycache__/processing_layoutxlm.cpython-311.pyc,,
transformers/models/layoutxlm/__pycache__/tokenization_layoutxlm.cpython-311.pyc,,
transformers/models/layoutxlm/__pycache__/tokenization_layoutxlm_fast.cpython-311.pyc,,
transformers/models/layoutxlm/processing_layoutxlm.py,sha256=2xtffeErPXtu2tW_ya4YaHDoqWCljDPfoL2V1Jlo6JI,9242
transformers/models/layoutxlm/tokenization_layoutxlm.py,sha256=WstEa3t23U3VFe7yzAlcUG4aAsvCwOvmNdVQlgr8l4E,57502
transformers/models/layoutxlm/tokenization_layoutxlm_fast.py,sha256=szXdodi-JqFdT9_7Nx1sH8W2OOF4LlskDb-U3x4MUbY,39972
transformers/models/led/__init__.py,sha256=9CdjSo8a3H8LyFlzOxCmUUZG2icbvPJ_Q_hFcaKBf4E,3008
transformers/models/led/__pycache__/__init__.cpython-311.pyc,,
transformers/models/led/__pycache__/configuration_led.cpython-311.pyc,,
transformers/models/led/__pycache__/modeling_led.cpython-311.pyc,,
transformers/models/led/__pycache__/modeling_tf_led.cpython-311.pyc,,
transformers/models/led/__pycache__/tokenization_led.cpython-311.pyc,,
transformers/models/led/__pycache__/tokenization_led_fast.cpython-311.pyc,,
transformers/models/led/configuration_led.py,sha256=ejx2raEDvP2TlRid2bd9S9IijDA_VYQkAXZAuKz7c0U,7634
transformers/models/led/modeling_led.py,sha256=xxqCqmGd8T7e8lcoWzBRQIHe96Klai4EXJXkKG_CcKQ,139199
transformers/models/led/modeling_tf_led.py,sha256=drHWpT50oyMc1gLh2bNwE75K-IzP6-NYW5dj5QS5LAs,123072
transformers/models/led/tokenization_led.py,sha256=SilZj9XSZ6a4uOdceKE1BSCqnTK73t1hT1MS8XwNWlo,20406
transformers/models/led/tokenization_led_fast.py,sha256=0aN-BFq5w1AeNlZ_0C21Rr4VQ09du3aa9b-SoQJobks,15197
transformers/models/levit/__init__.py,sha256=bn2rphZqhhv59V7XPWBSS3nntAk8n8qi8o9uhqmi2do,2508
transformers/models/levit/__pycache__/__init__.cpython-311.pyc,,
transformers/models/levit/__pycache__/configuration_levit.cpython-311.pyc,,
transformers/models/levit/__pycache__/convert_levit_timm_to_pytorch.cpython-311.pyc,,
transformers/models/levit/__pycache__/feature_extraction_levit.cpython-311.pyc,,
transformers/models/levit/__pycache__/image_processing_levit.cpython-311.pyc,,
transformers/models/levit/__pycache__/modeling_levit.cpython-311.pyc,,
transformers/models/levit/configuration_levit.py,sha256=hzMJZuunH08GKdMcBvAq5S14369-kHSYRmQ8f8I-kys,5931
transformers/models/levit/convert_levit_timm_to_pytorch.py,sha256=HKjk4WPa6DO_2CM0Qy9R3mAEOdbf71DtS-T4uqoQJ9I,6258
transformers/models/levit/feature_extraction_levit.py,sha256=l2RHbrbg9MzRqKr_ErOo_AuiSv93Gj-Oq6w0v2p-Izw,1204
transformers/models/levit/image_processing_levit.py,sha256=-VhOz-pgMqbkj-QVLjG64N7XqdzgomJ6GveK2cB0I1Y,16512
transformers/models/levit/modeling_levit.py,sha256=8syijih7tNnJ6bgkNOnqv4DPRVliyLkLKiG1-ngM9p8,29462
transformers/models/lilt/__init__.py,sha256=bIm8VAW84HA1oTl3ZITLrjMZ9VIyJ4s6_x9R9N767nM,1909
transformers/models/lilt/__pycache__/__init__.cpython-311.pyc,,
transformers/models/lilt/__pycache__/configuration_lilt.cpython-311.pyc,,
transformers/models/lilt/__pycache__/modeling_lilt.cpython-311.pyc,,
transformers/models/lilt/configuration_lilt.py,sha256=zNzGbpigxwdB64FLxWK19-MRnYJHinuLsszgRE_tJjI,6879
transformers/models/lilt/modeling_lilt.py,sha256=Iu4_vDKPDt2hO9DPbzC7AzDMg1INYNWj40nx8GfDrBg,52762
transformers/models/llama/__init__.py,sha256=Jur2SZ5J29BTDTaoQfXv69e-ZpcX5NiKbzAP1DGV9-A,3349
transformers/models/llama/__pycache__/__init__.cpython-311.pyc,,
transformers/models/llama/__pycache__/configuration_llama.cpython-311.pyc,,
transformers/models/llama/__pycache__/convert_llama_weights_to_hf.cpython-311.pyc,,
transformers/models/llama/__pycache__/modeling_flax_llama.cpython-311.pyc,,
transformers/models/llama/__pycache__/modeling_llama.cpython-311.pyc,,
transformers/models/llama/__pycache__/tokenization_llama.cpython-311.pyc,,
transformers/models/llama/__pycache__/tokenization_llama_fast.cpython-311.pyc,,
transformers/models/llama/configuration_llama.py,sha256=SRbJfMR11VHPuOJIkbKhVuohDLHYlb1usl_teyzYJ7I,9418
transformers/models/llama/convert_llama_weights_to_hf.py,sha256=CC5jifkiq1F9LZZspnmW_G2TvGTaJ8o_KpjdmWq3uw8,14165
transformers/models/llama/modeling_flax_llama.py,sha256=zUtBTgm7Zlw1FYUDOMtvl-upkR0ed6HG-qaF9JvUpW4,30107
transformers/models/llama/modeling_llama.py,sha256=6PoglJZwdMw8nYNH4xTMVN3xm2SAnoBgXDIcl9JtO_Q,69792
transformers/models/llama/tokenization_llama.py,sha256=RAoInYOfRXATevsTUEOuJcYRv3u2OnN3CMrA01S3XmI,22524
transformers/models/llama/tokenization_llama_fast.py,sha256=HVLnRB6pKZJXc9hjWaN6OH5wuR1OgXYXEF4Qf_B1xuY,13489
transformers/models/llava/__init__.py,sha256=Mq-IiC-tQbmOoQvm3QHcrQ9vW01WxTl4GXaVgZf0ULg,1814
transformers/models/llava/__pycache__/__init__.cpython-311.pyc,,
transformers/models/llava/__pycache__/configuration_llava.cpython-311.pyc,,
transformers/models/llava/__pycache__/convert_llava_weights_to_hf.cpython-311.pyc,,
transformers/models/llava/__pycache__/modeling_llava.cpython-311.pyc,,
transformers/models/llava/__pycache__/processing_llava.cpython-311.pyc,,
transformers/models/llava/configuration_llava.py,sha256=v8jtu76OzbLxTr5bbnnz951xWrgKX-lGpKmsH0ofp-0,5490
transformers/models/llava/convert_llava_weights_to_hf.py,sha256=jqOHXrbRbkwXkpWF_elzKblom0oJgOKqA6r4C9ouCaA,5420
transformers/models/llava/modeling_llava.py,sha256=h5UfppabxL2vTS0oWjzx0TcnutNzKKhOzui7tHsWScU,29239
transformers/models/llava/processing_llava.py,sha256=4p8HUXPuPOVfmZtTG5m7y_7-hph-R_itodMcdynxOvI,7282
transformers/models/longformer/__init__.py,sha256=mbx6LG2-PW5i_Ntq3kFn1MhnegTVAs0_ZOKAGeMi5ps,4196
transformers/models/longformer/__pycache__/__init__.cpython-311.pyc,,
transformers/models/longformer/__pycache__/configuration_longformer.cpython-311.pyc,,
transformers/models/longformer/__pycache__/convert_longformer_original_pytorch_lightning_to_pytorch.cpython-311.pyc,,
transformers/models/longformer/__pycache__/modeling_longformer.cpython-311.pyc,,
transformers/models/longformer/__pycache__/modeling_tf_longformer.cpython-311.pyc,,
transformers/models/longformer/__pycache__/tokenization_longformer.cpython-311.pyc,,
transformers/models/longformer/__pycache__/tokenization_longformer_fast.cpython-311.pyc,,
transformers/models/longformer/configuration_longformer.py,sha256=y3kUvmuolhHVas30YwUebK9YtoJxPEv3dyDDAu25uBc,9565
transformers/models/longformer/convert_longformer_original_pytorch_lightning_to_pytorch.py,sha256=gKyYNmo8Of0j_h6x8JSHaYc6hTyzJFwWETi5KectvFM,3026
transformers/models/longformer/modeling_longformer.py,sha256=Xot-sEvfQdjQVOcWJ7kDgGZkOZnZeUSG4NAPJ-YERKk,114241
transformers/models/longformer/modeling_tf_longformer.py,sha256=c36aK7vpRyXiPRtyfi9b47wTG375w7aLl7fo-Ve-_eE,129721
transformers/models/longformer/tokenization_longformer.py,sha256=n4PynvFza6hr9-F9mREbXYoZSdHKv3DeMe9mhlDix-U,18961
transformers/models/longformer/tokenization_longformer_fast.py,sha256=j6eEkf_h6vYEurmvMMXFTHUcc0FjRjlgVFS1uHfCtF0,14725
transformers/models/longt5/__init__.py,sha256=nN2BIwcwmdcMffrxzPKx9oeVWsHu9wt1BUJYIPWfm3Y,2546
transformers/models/longt5/__pycache__/__init__.cpython-311.pyc,,
transformers/models/longt5/__pycache__/configuration_longt5.cpython-311.pyc,,
transformers/models/longt5/__pycache__/convert_longt5x_checkpoint_to_flax.cpython-311.pyc,,
transformers/models/longt5/__pycache__/modeling_flax_longt5.cpython-311.pyc,,
transformers/models/longt5/__pycache__/modeling_longt5.cpython-311.pyc,,
transformers/models/longt5/configuration_longt5.py,sha256=rF_7G9sUb8ouVoUewGlO6LUHmOrpGJpDFIsGdk6mKEg,8483
transformers/models/longt5/convert_longt5x_checkpoint_to_flax.py,sha256=5LQpQWNG_8Fc0tU62eYf66RmJzUcb-RynDdrvziZEqw,11089
transformers/models/longt5/modeling_flax_longt5.py,sha256=TBgoH7wMBAGNMilDvmg1U-394Z7ImK55Tm4saS-0CVs,105672
transformers/models/longt5/modeling_longt5.py,sha256=TZXVbJwBKkRwH0PtR_Xf2roIXyXMYMPkQ_61sKkDM0k,106119
transformers/models/luke/__init__.py,sha256=xuqWDYOtcrf1vEC71vfltl8ICWfW7GyU9sP8RWD-iU4,2383
transformers/models/luke/__pycache__/__init__.cpython-311.pyc,,
transformers/models/luke/__pycache__/configuration_luke.cpython-311.pyc,,
transformers/models/luke/__pycache__/convert_luke_original_pytorch_checkpoint_to_pytorch.cpython-311.pyc,,
transformers/models/luke/__pycache__/modeling_luke.cpython-311.pyc,,
transformers/models/luke/__pycache__/tokenization_luke.cpython-311.pyc,,
transformers/models/luke/configuration_luke.py,sha256=o9Ne3lf8etNXzx4GDdgdhS-kJC8wVRCD2FvQNSGuhNE,6846
transformers/models/luke/convert_luke_original_pytorch_checkpoint_to_pytorch.py,sha256=pfnDfBvJDRyCLBLdcsalZaKV01aEz0W1og2Z364hTDs,7467
transformers/models/luke/modeling_luke.py,sha256=8vr1zaXyOSug4Zxr8qNg7S_vJ5Siv_cn3uUdmWvxUWw,103936
transformers/models/luke/tokenization_luke.py,sha256=0C2cPd6z6Mah4E2RUPnWnauPiqS-R5oQdNoOqp0MhXY,85434
transformers/models/lxmert/__init__.py,sha256=3rn46z5WOBmOrbr6e7zoIWh4F8Bf3hFBASDY0vxlxbI,3396
transformers/models/lxmert/__pycache__/__init__.cpython-311.pyc,,
transformers/models/lxmert/__pycache__/configuration_lxmert.cpython-311.pyc,,
transformers/models/lxmert/__pycache__/convert_lxmert_original_tf_checkpoint_to_pytorch.cpython-311.pyc,,
transformers/models/lxmert/__pycache__/modeling_lxmert.cpython-311.pyc,,
transformers/models/lxmert/__pycache__/modeling_tf_lxmert.cpython-311.pyc,,
transformers/models/lxmert/__pycache__/tokenization_lxmert.cpython-311.pyc,,
transformers/models/lxmert/__pycache__/tokenization_lxmert_fast.cpython-311.pyc,,
transformers/models/lxmert/configuration_lxmert.py,sha256=QDxTbEEQJhEIEbC6PgCyz-LfL1UZWLSJYQ40jrWpvHo,9065
transformers/models/lxmert/convert_lxmert_original_tf_checkpoint_to_pytorch.py,sha256=T3vqC76pis49OXeHODsBSBBDGDe6qnUBckwGOWySmpc,2109
transformers/models/lxmert/modeling_lxmert.py,sha256=mgZ72KaaZ-E4MOHSfezHmWa0M8L9RBJ7H770logD1qw,65037
transformers/models/lxmert/modeling_tf_lxmert.py,sha256=lak1OT87gTFewNsULQvo8221A_jiDIdk9rv6O0hHS7s,72702
transformers/models/lxmert/tokenization_lxmert.py,sha256=W10FN7y_8E87Q6yBt5s1snJIr-D_M39JATVbuOFvGE8,21518
transformers/models/lxmert/tokenization_lxmert_fast.py,sha256=ZeYcRkFq2mNZYyBcm8JrFIIDl2Tq3dwakz84Izy1cIU,8449
transformers/models/m2m_100/__init__.py,sha256=fT84ZTHmw2vMrme8MqfSoPZWSECY-SLXDG0AR8Z1qRc,1992
transformers/models/m2m_100/__pycache__/__init__.cpython-311.pyc,,
transformers/models/m2m_100/__pycache__/configuration_m2m_100.cpython-311.pyc,,
transformers/models/m2m_100/__pycache__/convert_m2m100_original_checkpoint_to_pytorch.cpython-311.pyc,,
transformers/models/m2m_100/__pycache__/modeling_m2m_100.cpython-311.pyc,,
transformers/models/m2m_100/__pycache__/tokenization_m2m_100.cpython-311.pyc,,
transformers/models/m2m_100/configuration_m2m_100.py,sha256=3b-M2ZkofNfbpdCZOAjAPKqo4Sqbq3F7ma-0AzV8zFA,13583
transformers/models/m2m_100/convert_m2m100_original_checkpoint_to_pytorch.py,sha256=xNG8NE20odOve8Z1zKPDHJr5Ev8jM30N-mJsJqfsXtM,3159
transformers/models/m2m_100/modeling_m2m_100.py,sha256=mP4ZdLxAvj3aM9tEMTTU03GV0zXUI05YjvtgtTiEAnw,64262
transformers/models/m2m_100/tokenization_m2m_100.py,sha256=3ApI9kknKSGjsc8ICyQ7cVjh0rvkMLlYc3aEQeHlVyw,17316
transformers/models/marian/__init__.py,sha256=_aQPsVh7jA_BTVbCkRprc2NmnLlkhfEtfJW_1WIwUqI,3444
transformers/models/marian/__pycache__/__init__.cpython-311.pyc,,
transformers/models/marian/__pycache__/configuration_marian.cpython-311.pyc,,
transformers/models/marian/__pycache__/convert_marian_tatoeba_to_pytorch.cpython-311.pyc,,
transformers/models/marian/__pycache__/convert_marian_to_pytorch.cpython-311.pyc,,
transformers/models/marian/__pycache__/modeling_flax_marian.cpython-311.pyc,,
transformers/models/marian/__pycache__/modeling_marian.cpython-311.pyc,,
transformers/models/marian/__pycache__/modeling_tf_marian.cpython-311.pyc,,
transformers/models/marian/__pycache__/tokenization_marian.cpython-311.pyc,,
transformers/models/marian/configuration_marian.py,sha256=-52l6myCrR46BC2SEFDUTIeQ96tZB0ot9Z9gx43q1YU,18559
transformers/models/marian/convert_marian_tatoeba_to_pytorch.py,sha256=N_YEEFgsGy2W-4QxeGD3bIIGNl_oYv64GkTw0WDpiaU,36254
transformers/models/marian/convert_marian_to_pytorch.py,sha256=lggn1nlv2EBgLarnYE_SKkUnDPKDgngL_xOtBJxQIgY,26775
transformers/models/marian/modeling_flax_marian.py,sha256=vt7iI4WBYOAhz36UqJcXPIUu5q8U6xY-wwAphjOQsco,64262
transformers/models/marian/modeling_marian.py,sha256=OtI4y2nWkAY9BExzsPykzWHlrz42wkEu0Ag78O3z990,81832
transformers/models/marian/modeling_tf_marian.py,sha256=IEwr-j8xPUbuYNBN6mKzYmLyK0FrmbMVGoXRo4C944w,72682
transformers/models/marian/tokenization_marian.py,sha256=6V4R8SBGN90vMu1O4n-akLX2wCrRIV0EBFVybMMNJlM,17756
transformers/models/markuplm/__init__.py,sha256=RjQ4xza9uhSlHJ11ZIHA19o-cWoC88fJvts8zYDOznY,2806
transformers/models/markuplm/__pycache__/__init__.cpython-311.pyc,,
transformers/models/markuplm/__pycache__/configuration_markuplm.cpython-311.pyc,,
transformers/models/markuplm/__pycache__/feature_extraction_markuplm.cpython-311.pyc,,
transformers/models/markuplm/__pycache__/modeling_markuplm.cpython-311.pyc,,
transformers/models/markuplm/__pycache__/processing_markuplm.cpython-311.pyc,,
transformers/models/markuplm/__pycache__/tokenization_markuplm.cpython-311.pyc,,
transformers/models/markuplm/__pycache__/tokenization_markuplm_fast.cpython-311.pyc,,
transformers/models/markuplm/configuration_markuplm.py,sha256=VX8AW90kT3SnATh306fkXkVDxcIrEzM7k-pKRQNqzG0,7572
transformers/models/markuplm/feature_extraction_markuplm.py,sha256=3V8MR36mQskKYQeaGrWuqWo9w5JG67nhRvxzWu7fR9s,6404
transformers/models/markuplm/modeling_markuplm.py,sha256=ANbadaRLshPAm03fg-j3fh4ux_80FOt2du7KmgIDcVU,58305
transformers/models/markuplm/processing_markuplm.py,sha256=dCxh-u2OQvsoAeK0GWGDwMgZuLIgF7tu5Q7uERx5NwY,6348
transformers/models/markuplm/tokenization_markuplm.py,sha256=XmeJlKgev-gBhxPSrxDCfoOcxp7ihv3EmeGhEGHnqLw,69748
transformers/models/markuplm/tokenization_markuplm_fast.py,sha256=4Hd8ln5XrOO9nsqZcqbRYs3_Ylu5Xxre6rNJ2ddcxq0,43715
transformers/models/mask2former/__init__.py,sha256=_damTN4svyRG1tenZi3AEmsILg7QVyYbuWR_iXzrbXw,2357
transformers/models/mask2former/__pycache__/__init__.cpython-311.pyc,,
transformers/models/mask2former/__pycache__/configuration_mask2former.cpython-311.pyc,,
transformers/models/mask2former/__pycache__/convert_mask2former_original_pytorch_checkpoint_to_pytorch.cpython-311.pyc,,
transformers/models/mask2former/__pycache__/image_processing_mask2former.cpython-311.pyc,,
transformers/models/mask2former/__pycache__/modeling_mask2former.cpython-311.pyc,,
transformers/models/mask2former/configuration_mask2former.py,sha256=ob3jMxnJdkBCtJpAo3SffRdjbJOBo8E_H8defC5YGwE,12740
transformers/models/mask2former/convert_mask2former_original_pytorch_checkpoint_to_pytorch.py,sha256=v4a-VTdnEHxZLAykOn5AgqLXZ9yFZzhY4CUu4c3XHUE,45688
transformers/models/mask2former/image_processing_mask2former.py,sha256=htUbB6Tgw_2lUpSL2n7DD_8_xCp65bIC3XqJVK81zUw,56302
transformers/models/mask2former/modeling_mask2former.py,sha256=j3QvZGEZN_6_eKwm-AfE5gZ0hLMR3JD47b-A1zv_8Xk,120748
transformers/models/maskformer/__init__.py,sha256=Sy9sX8-Vb9Gnn9gjU34M4pDh3jJZd7vmr5aorB9N5lw,2945
transformers/models/maskformer/__pycache__/__init__.cpython-311.pyc,,
transformers/models/maskformer/__pycache__/configuration_maskformer.cpython-311.pyc,,
transformers/models/maskformer/__pycache__/configuration_maskformer_swin.cpython-311.pyc,,
transformers/models/maskformer/__pycache__/convert_maskformer_original_pytorch_checkpoint_to_pytorch.cpython-311.pyc,,
transformers/models/maskformer/__pycache__/convert_maskformer_resnet_to_pytorch.cpython-311.pyc,,
transformers/models/maskformer/__pycache__/convert_maskformer_swin_to_pytorch.cpython-311.pyc,,
transformers/models/maskformer/__pycache__/feature_extraction_maskformer.cpython-311.pyc,,
transformers/models/maskformer/__pycache__/image_processing_maskformer.cpython-311.pyc,,
transformers/models/maskformer/__pycache__/modeling_maskformer.cpython-311.pyc,,
transformers/models/maskformer/__pycache__/modeling_maskformer_swin.cpython-311.pyc,,
transformers/models/maskformer/configuration_maskformer.py,sha256=Ga_rHPsVjWdwH3cnFS9cIfIOGBosLsXpU2uo_iOAOCY,10632
transformers/models/maskformer/configuration_maskformer_swin.py,sha256=kdf9AuIG8DYqJPIsvz_2rlIqSEZJ3UBI0IXlFdao3YM,7217
transformers/models/maskformer/convert_maskformer_original_pytorch_checkpoint_to_pytorch.py,sha256=CEKaBhurc8x3mvE7YMqfULIoybxq0Guj0hGHJouG5s8,32237
transformers/models/maskformer/convert_maskformer_resnet_to_pytorch.py,sha256=iUMC5om4caBO1eSeivN3sZYsbEtYZAeJZE7I1NIygR4,20732
transformers/models/maskformer/convert_maskformer_swin_to_pytorch.py,sha256=-GWvua0iYDbJYZ7VUcywp0rf-jR7iKXz8az9N4r5k_0,20321
transformers/models/maskformer/feature_extraction_maskformer.py,sha256=MMPQuQY2EnK4vixDve-I-PIFqCDWQNYYeVdAYvIY8HY,1214
transformers/models/maskformer/image_processing_maskformer.py,sha256=gjOkO8jAcW1ZdKFqrm50FaKxVvyaFoL0uFCaxPWCN8E,58142
transformers/models/maskformer/modeling_maskformer.py,sha256=c2FJFiYQBlgYSa58H3-hiowQ_v8Gx3wBZAWEYv3nMQc,93547
transformers/models/maskformer/modeling_maskformer_swin.py,sha256=2DyRWtHLA077-GWY0Z2mngv62I0RpGVHKr3NhIJm3c8,40758
transformers/models/mbart/__init__.py,sha256=N1NqaZU1QPNt3r2VI3y4sv-XwdBkAtV-41REYSah7w4,4403
transformers/models/mbart/__pycache__/__init__.cpython-311.pyc,,
transformers/models/mbart/__pycache__/configuration_mbart.cpython-311.pyc,,
transformers/models/mbart/__pycache__/convert_mbart_original_checkpoint_to_pytorch.cpython-311.pyc,,
transformers/models/mbart/__pycache__/modeling_flax_mbart.cpython-311.pyc,,
transformers/models/mbart/__pycache__/modeling_mbart.cpython-311.pyc,,
transformers/models/mbart/__pycache__/modeling_tf_mbart.cpython-311.pyc,,
transformers/models/mbart/__pycache__/tokenization_mbart.cpython-311.pyc,,
transformers/models/mbart/__pycache__/tokenization_mbart_fast.cpython-311.pyc,,
transformers/models/mbart/configuration_mbart.py,sha256=zYZCbvRCEqmDZoxRvEILeXL3qEpp0jc4TRHy634RV5U,18388
transformers/models/mbart/convert_mbart_original_checkpoint_to_pytorch.py,sha256=xVW9Mj-jd7X_MImJCgS52Aok1CGPf-E6u8ptvG1hK8o,3035
transformers/models/mbart/modeling_flax_mbart.py,sha256=uUgTTL5zTGbJZX45q4YoPKiSbizfXNsx8jr-T7P2C_c,75090
transformers/models/mbart/modeling_mbart.py,sha256=QQZOk4Dq7BU1WoXPi9J0ZDZhJxwsdxSoIRJYFQ6qqfQ,101090
transformers/models/mbart/modeling_tf_mbart.py,sha256=JsKe79VRjtf9p1SgbH8dnbQGUd5fe5CnYgGNijT-Mys,74195
transformers/models/mbart/tokenization_mbart.py,sha256=DcRh3oa0zy7ZlcPc1t8OgUdNOPJWuxJoS0a25T4G99A,14719
transformers/models/mbart/tokenization_mbart_fast.py,sha256=Xoc_K63xih5PbrjtGdMepr33p8mpxzN_OP6UXT-Jy6A,11878
transformers/models/mbart50/__init__.py,sha256=5ekQCS9OkL3_5UJXnu7Z5cVeCi76pVgAxHkC8qQ8XKk,1847
transformers/models/mbart50/__pycache__/__init__.cpython-311.pyc,,
transformers/models/mbart50/__pycache__/tokenization_mbart50.cpython-311.pyc,,
transformers/models/mbart50/__pycache__/tokenization_mbart50_fast.cpython-311.pyc,,
transformers/models/mbart50/tokenization_mbart50.py,sha256=mRpnNkX7JP9wjFtTMUgSRj0USMLNfGlw0FM3Y2BlxxQ,16770
transformers/models/mbart50/tokenization_mbart50_fast.py,sha256=-YTkCwhpgmu9EywN4--3COQq9hF58vNYysUxZpJU-Q4,12258
transformers/models/mega/__init__.py,sha256=sJJLSLHF1HMGGOkDRFol40JHptUCxSDiB0yUUbvDVL4,2140
transformers/models/mega/__pycache__/__init__.cpython-311.pyc,,
transformers/models/mega/__pycache__/configuration_mega.cpython-311.pyc,,
transformers/models/mega/__pycache__/convert_mega_original_pytorch_checkpoint_to_pytorch.cpython-311.pyc,,
transformers/models/mega/__pycache__/modeling_mega.cpython-311.pyc,,
transformers/models/mega/configuration_mega.py,sha256=rgqfmeGucfEh9VWoO7sjNDfu-nML6rLrv16rMoiSt6A,12739
transformers/models/mega/convert_mega_original_pytorch_checkpoint_to_pytorch.py,sha256=FK9gAgMB5VEO2Fji39w100ywUJ8wA8utdmWRZFanb2c,13154
transformers/models/mega/modeling_mega.py,sha256=qKrJG1nEZa8F72zxhegVm2Adaaul4DufD_uzxbitOmQ,109558
transformers/models/megatron_bert/__init__.py,sha256=TUAneYZq0bKIQqKDcED_EuJhgnzOnWNrNrye_x8KX90,2506
transformers/models/megatron_bert/__pycache__/__init__.cpython-311.pyc,,
transformers/models/megatron_bert/__pycache__/configuration_megatron_bert.cpython-311.pyc,,
transformers/models/megatron_bert/__pycache__/convert_megatron_bert_checkpoint.cpython-311.pyc,,
transformers/models/megatron_bert/__pycache__/modeling_megatron_bert.cpython-311.pyc,,
transformers/models/megatron_bert/configuration_megatron_bert.py,sha256=FCG00vOd75Kq1lLHypifGfnVQtHMgKmOYv9H_lz5g88,6598
transformers/models/megatron_bert/convert_megatron_bert_checkpoint.py,sha256=VAMD1MFdVG8w9cQkRfmlZCEvaMgoo-lyFI9deunD5OA,13686
transformers/models/megatron_bert/modeling_megatron_bert.py,sha256=qAWHUPLMGI2XgWqL3K__1n9-X7OoDljkzDTZbHMQNMQ,83399
transformers/models/megatron_gpt2/__init__.py,sha256=WycFl9cUevoXIBhB76qKtnNRIPMk2LoTDkmkfAfOy9M,630
transformers/models/megatron_gpt2/__pycache__/__init__.cpython-311.pyc,,
transformers/models/megatron_gpt2/__pycache__/checkpoint_reshaping_and_interoperability.cpython-311.pyc,,
transformers/models/megatron_gpt2/__pycache__/convert_megatron_gpt2_checkpoint.cpython-311.pyc,,
transformers/models/megatron_gpt2/checkpoint_reshaping_and_interoperability.py,sha256=NPoWPPSaT29iHoGRoyc1B_hdc67QNoytsVj_glQF430,36692
transformers/models/megatron_gpt2/convert_megatron_gpt2_checkpoint.py,sha256=UPLXCjF4Fixnw_gy6kzxTK64ioxo_EIxwSVO6oKCqqQ,13661
transformers/models/mgp_str/__init__.py,sha256=YMCtFGSXL18Kh4Pm3KTBEgtxlaDDYwb3WnMFsEsaJ-4,2164
transformers/models/mgp_str/__pycache__/__init__.cpython-311.pyc,,
transformers/models/mgp_str/__pycache__/configuration_mgp_str.cpython-311.pyc,,
transformers/models/mgp_str/__pycache__/modeling_mgp_str.cpython-311.pyc,,
transformers/models/mgp_str/__pycache__/processing_mgp_str.cpython-311.pyc,,
transformers/models/mgp_str/__pycache__/tokenization_mgp_str.cpython-311.pyc,,
transformers/models/mgp_str/configuration_mgp_str.py,sha256=-DlPYCu9xFlSbtS1r3YnZu0x3PFa0_Q-OtQG2ZVHpXA,5937
transformers/models/mgp_str/modeling_mgp_str.py,sha256=E2mlwPBlHQAXuWAhaGfYyeHvi7i6Gbm9dRUJiO-U4xU,22053
transformers/models/mgp_str/processing_mgp_str.py,sha256=dh1MJ17yNZdoorG_Mi31Q7waqTnyRock-s4c2k_g0DQ,9298
transformers/models/mgp_str/tokenization_mgp_str.py,sha256=_EUA0gIB3kdAsj9Z-4ridLMAz-k57JecGGmk4kifWVE,4113
transformers/models/mistral/__init__.py,sha256=b9KtZaVe1auCaeEzoRC_zvykp9KwyW8vqNpww-3jgls,2428
transformers/models/mistral/__pycache__/__init__.cpython-311.pyc,,
transformers/models/mistral/__pycache__/configuration_mistral.cpython-311.pyc,,
transformers/models/mistral/__pycache__/convert_mistral_weights_to_hf.cpython-311.pyc,,
transformers/models/mistral/__pycache__/modeling_flax_mistral.cpython-311.pyc,,
transformers/models/mistral/__pycache__/modeling_mistral.cpython-311.pyc,,
transformers/models/mistral/configuration_mistral.py,sha256=hrZcftzIpp9B2YipjjTBY5bXsefDQSZ14w_UlWdy4yE,7170
transformers/models/mistral/convert_mistral_weights_to_hf.py,sha256=bG8KXwc1rd3kSd5IothmZGiDiOfhERfh3VrS6_wOaoM,10725
transformers/models/mistral/modeling_flax_mistral.py,sha256=1xBy97GmBslNjfZZ580ZAfqrRGviVILi0QGf1qbxDPE,31682
transformers/models/mistral/modeling_mistral.py,sha256=bnBupE5xxH3eCEREBYEFdS_sORJhrvd43efkU2Tqgt4,63489
transformers/models/mixtral/__init__.py,sha256=gUOb9IB2p_2uISpGaLaKXTWW0-nWVa4INgiTZmO8guE,1806
transformers/models/mixtral/__pycache__/__init__.cpython-311.pyc,,
transformers/models/mixtral/__pycache__/configuration_mixtral.cpython-311.pyc,,
transformers/models/mixtral/__pycache__/convert_mixtral_weights_to_hf.cpython-311.pyc,,
transformers/models/mixtral/__pycache__/modeling_mixtral.cpython-311.pyc,,
transformers/models/mixtral/configuration_mixtral.py,sha256=rAMo9hRFr7_uoZfuclwxpRd2_L-mlSjV3GP7DL_LqTE,8050
transformers/models/mixtral/convert_mixtral_weights_to_hf.py,sha256=WExicalIwkZccqWyRjUU2LBvbL6cM6yiOG_Oby6t3Ok,9156
transformers/models/mixtral/modeling_mixtral.py,sha256=XsxeAQbaruEUKIJ_O--afTi1bfrIMIjEquDWKlArGTg,73424
transformers/models/mluke/__init__.py,sha256=Pj0GBjIU6vYdhEzO7M8O35c5Jj4ivIIGAiLABhN4K7U,1356
transformers/models/mluke/__pycache__/__init__.cpython-311.pyc,,
transformers/models/mluke/__pycache__/convert_mluke_original_pytorch_checkpoint_to_pytorch.cpython-311.pyc,,
transformers/models/mluke/__pycache__/tokenization_mluke.cpython-311.pyc,,
transformers/models/mluke/convert_mluke_original_pytorch_checkpoint_to_pytorch.py,sha256=G6Z94-1_AiilSTU96PSjX_pdgFIx-b_bk8xlMKX5TuE,10185
transformers/models/mluke/tokenization_mluke.py,sha256=ZAvXTz5UeLCW3b22YWaJd2-4nXtp_qOopnKTuy0LVtY,81498
transformers/models/mobilebert/__init__.py,sha256=Gpd8kL6D0UrD5ufVg0MjcknSeHhtlLnD3Bkrzqao4Ok,4604
transformers/models/mobilebert/__pycache__/__init__.cpython-311.pyc,,
transformers/models/mobilebert/__pycache__/configuration_mobilebert.cpython-311.pyc,,
transformers/models/mobilebert/__pycache__/convert_mobilebert_original_tf_checkpoint_to_pytorch.cpython-311.pyc,,
transformers/models/mobilebert/__pycache__/modeling_mobilebert.cpython-311.pyc,,
transformers/models/mobilebert/__pycache__/modeling_tf_mobilebert.cpython-311.pyc,,
transformers/models/mobilebert/__pycache__/tokenization_mobilebert.cpython-311.pyc,,
transformers/models/mobilebert/__pycache__/tokenization_mobilebert_fast.cpython-311.pyc,,
transformers/models/mobilebert/configuration_mobilebert.py,sha256=KWThAd4EaQqWZ-WYrw_x2NxTNKxm5rET6psotuz4DFc,8587
transformers/models/mobilebert/convert_mobilebert_original_tf_checkpoint_to_pytorch.py,sha256=MRW9sorswIo4RiWq7PVVmaZsYm4wJEc1-DhcLzssDRU,2200
transformers/models/mobilebert/modeling_mobilebert.py,sha256=wP_NeNbqlMF1GzYmXwnyE_dTLNOLuZjLc5JdW1BJzMA,70540
transformers/models/mobilebert/modeling_tf_mobilebert.py,sha256=yDyhvOvQgUe00-7HR-yzp5H9GLGk95nhPFSzITunLO0,83886
transformers/models/mobilebert/tokenization_mobilebert.py,sha256=3_efEClwdcoZJNFeud1-gq9GzzD1ZTt1hChmzaY7NyY,21401
transformers/models/mobilebert/tokenization_mobilebert_fast.py,sha256=Jp83WhGI3-0dm6Hl0UL4fFsWXX2ysiOeL2mcTWRtKvk,8389
transformers/models/mobilenet_v1/__init__.py,sha256=rbZvH8u5nov7gMxVexJZTVa8yJSIwI4ZHilp8sTEw64,2735
transformers/models/mobilenet_v1/__pycache__/__init__.cpython-311.pyc,,
transformers/models/mobilenet_v1/__pycache__/configuration_mobilenet_v1.cpython-311.pyc,,
transformers/models/mobilenet_v1/__pycache__/convert_original_tf_checkpoint_to_pytorch.cpython-311.pyc,,
transformers/models/mobilenet_v1/__pycache__/feature_extraction_mobilenet_v1.cpython-311.pyc,,
transformers/models/mobilenet_v1/__pycache__/image_processing_mobilenet_v1.cpython-311.pyc,,
transformers/models/mobilenet_v1/__pycache__/modeling_mobilenet_v1.cpython-311.pyc,,
transformers/models/mobilenet_v1/configuration_mobilenet_v1.py,sha256=ze6KbgS1Ow0tIm43SijdxFXuZe8OaxPGdmIqUNz7woI,5238
transformers/models/mobilenet_v1/convert_original_tf_checkpoint_to_pytorch.py,sha256=XjGgfnPQBWp-0pNakJ1CNU1YnoYfeXCZ9WSIrTf02n8,4932
transformers/models/mobilenet_v1/feature_extraction_mobilenet_v1.py,sha256=goR0AC-IhWMrQlvzSK_0Zej42JYN-oswSGNQWnIOENU,1222
transformers/models/mobilenet_v1/image_processing_mobilenet_v1.py,sha256=j5EkclgV9EPcqY3l5yeIVhTv8bYNHl9DOAz2_u6yR68,15268
transformers/models/mobilenet_v1/modeling_mobilenet_v1.py,sha256=Ze_zq7rncOY95Nfss9i1s5j-1GLwtQJ-iL0nZAP3GeQ,18777
transformers/models/mobilenet_v2/__init__.py,sha256=p4OHu9O6JD4N2TcjOgLu7S2u151xEvGwvdHizbzevc0,2830
transformers/models/mobilenet_v2/__pycache__/__init__.cpython-311.pyc,,
transformers/models/mobilenet_v2/__pycache__/configuration_mobilenet_v2.cpython-311.pyc,,
transformers/models/mobilenet_v2/__pycache__/convert_original_tf_checkpoint_to_pytorch.cpython-311.pyc,,
transformers/models/mobilenet_v2/__pycache__/feature_extraction_mobilenet_v2.cpython-311.pyc,,
transformers/models/mobilenet_v2/__pycache__/image_processing_mobilenet_v2.cpython-311.pyc,,
transformers/models/mobilenet_v2/__pycache__/modeling_mobilenet_v2.cpython-311.pyc,,
transformers/models/mobilenet_v2/configuration_mobilenet_v2.py,sha256=0beA3D50hZE2Rbg6G-FYDwH_MYU52bYTwtaOuc2EEM8,7362
transformers/models/mobilenet_v2/convert_original_tf_checkpoint_to_pytorch.py,sha256=acsdT3rMMqCPV9whw2xyiVK1UOs8tr8ySvYRFNRmVWM,6402
transformers/models/mobilenet_v2/feature_extraction_mobilenet_v2.py,sha256=_IUVvyoMBsqymCoh-CVmoswZ4nOBpqFJlaoUfD8WQ3E,1222
transformers/models/mobilenet_v2/image_processing_mobilenet_v2.py,sha256=ZskiiHFSNRE66WbWkGV9WVi0YHkxTeiL006UbUTAOzk,17622
transformers/models/mobilenet_v2/modeling_mobilenet_v2.py,sha256=FMRJ4P9qiepWWECM5hlTMYdlbIlfm6bA0rZ6gSCCRrQ,34752
transformers/models/mobilevit/__init__.py,sha256=AN8UeJz0pDko_ezgS5J4cYAZT3P6Hv2EZKlqZGnkgSI,3492
transformers/models/mobilevit/__pycache__/__init__.cpython-311.pyc,,
transformers/models/mobilevit/__pycache__/configuration_mobilevit.cpython-311.pyc,,
transformers/models/mobilevit/__pycache__/convert_mlcvnets_to_pytorch.cpython-311.pyc,,
transformers/models/mobilevit/__pycache__/feature_extraction_mobilevit.cpython-311.pyc,,
transformers/models/mobilevit/__pycache__/image_processing_mobilevit.cpython-311.pyc,,
transformers/models/mobilevit/__pycache__/modeling_mobilevit.cpython-311.pyc,,
transformers/models/mobilevit/__pycache__/modeling_tf_mobilevit.cpython-311.pyc,,
transformers/models/mobilevit/configuration_mobilevit.py,sha256=-x1CSVXGgAItAt83iJXxU5gVP_qcEx3pIDVXcHDPX9k,8401
transformers/models/mobilevit/convert_mlcvnets_to_pytorch.py,sha256=Ng8zzr_CxIO9IFcf0ijXqR_EWJeAhhQ3HAkethSpCn4,12402
transformers/models/mobilevit/feature_extraction_mobilevit.py,sha256=na2H01bKIhQsyCHayPaVase5HRGRmmO7zVDDuY76Uj0,1207
transformers/models/mobilevit/image_processing_mobilevit.py,sha256=mNtXYqSd21hA93TboLKkcJF53VQtPpXfcpB5u2vjTWs,21388
transformers/models/mobilevit/modeling_mobilevit.py,sha256=LNfQxBUNZGubEEugkVgXVvWi8lZDNCKo50LJ74C1Bhc,40158
transformers/models/mobilevit/modeling_tf_mobilevit.py,sha256=_bJDLe7Qm6BzM56hQ2ZnUaJWnAzDToci91-nKTDlkvg,55029
transformers/models/mobilevitv2/__init__.py,sha256=kSj85QHMKZk8_MdSUYKIsFL6V8SCAJWQlzo1hlvlYw8,2111
transformers/models/mobilevitv2/__pycache__/__init__.cpython-311.pyc,,
transformers/models/mobilevitv2/__pycache__/configuration_mobilevitv2.cpython-311.pyc,,
transformers/models/mobilevitv2/__pycache__/convert_mlcvnets_to_pytorch.cpython-311.pyc,,
transformers/models/mobilevitv2/__pycache__/modeling_mobilevitv2.cpython-311.pyc,,
transformers/models/mobilevitv2/configuration_mobilevitv2.py,sha256=bGvTY_kJmIhO8FwkuDp1sYRJlXOoXHXVmTxUla8jfG8,7243
transformers/models/mobilevitv2/convert_mlcvnets_to_pytorch.py,sha256=ZzEtog7BRgGK8W0zwC_peXQOOaBkuduPO3Tbq9_xtjo,12557
transformers/models/mobilevitv2/modeling_mobilevitv2.py,sha256=7LLWDFIQkrKImKVvC-NFdLhNN8FskXEbfeVbi-csyu4,38366
transformers/models/mpnet/__init__.py,sha256=hyB4jNWDdoHWggavnqLZEF85f9a11vXSTKaLWTdPh-k,3875
transformers/models/mpnet/__pycache__/__init__.cpython-311.pyc,,
transformers/models/mpnet/__pycache__/configuration_mpnet.cpython-311.pyc,,
transformers/models/mpnet/__pycache__/modeling_mpnet.cpython-311.pyc,,
transformers/models/mpnet/__pycache__/modeling_tf_mpnet.cpython-311.pyc,,
transformers/models/mpnet/__pycache__/tokenization_mpnet.cpython-311.pyc,,
transformers/models/mpnet/__pycache__/tokenization_mpnet_fast.cpython-311.pyc,,
transformers/models/mpnet/configuration_mpnet.py,sha256=pDol_52Mz0I2b-aeWNGRTdQcwh5XVf_XadKtG7B6Rpk,5443
transformers/models/mpnet/modeling_mpnet.py,sha256=6Ps9FyoNpYEVhxmy1h0b55MZ4Hb4BBUSGbF5fzVQPPE,42630
transformers/models/mpnet/modeling_tf_mpnet.py,sha256=GwnQQwnCLWEQzxzHty4XlepQZPBnlTcUPghReI4j_Q4,55539
transformers/models/mpnet/tokenization_mpnet.py,sha256=8mlkAbEeNgVr1Z0OlYkM85j2fuCzlZZk74zb4vbkJt4,22650
transformers/models/mpnet/tokenization_mpnet_fast.py,sha256=-KokNeluUanSsREUrO7B9fhZYvEScGQcac7tGUmUuL8,9821
transformers/models/mpt/__init__.py,sha256=ZH7_XPJ100kSo0osi0XxzbkyFHj6HnS9ghjxpsqVXac,1977
transformers/models/mpt/__pycache__/__init__.cpython-311.pyc,,
transformers/models/mpt/__pycache__/configuration_mpt.cpython-311.pyc,,
transformers/models/mpt/__pycache__/modeling_mpt.cpython-311.pyc,,
transformers/models/mpt/configuration_mpt.py,sha256=_uywOO0DMrIPSnMmsnksZ4QbX3A0ubnKHycWhtiW1qA,11364
transformers/models/mpt/modeling_mpt.py,sha256=iiGvoIvivJDKWI0RRLtVM7I6prHIMidHoHuEzufgLJw,41067
transformers/models/mra/__init__.py,sha256=CotdFTXkFtz90MDv55my886vc-0VBxs8h3mnGs-z7WQ,2254
transformers/models/mra/__pycache__/__init__.cpython-311.pyc,,
transformers/models/mra/__pycache__/configuration_mra.cpython-311.pyc,,
transformers/models/mra/__pycache__/convert_mra_pytorch_to_pytorch.cpython-311.pyc,,
transformers/models/mra/__pycache__/modeling_mra.cpython-311.pyc,,
transformers/models/mra/configuration_mra.py,sha256=XDh80kXpUkpFtisIjh09CMt8HtBgenw_0qCzz3e5ErQ,6662
transformers/models/mra/convert_mra_pytorch_to_pytorch.py,sha256=LhaVlQ4q88gtewg-geRYZ748xQ3brLLhyDIo-OGWSdI,4247
transformers/models/mra/modeling_mra.py,sha256=926oZT_dDXNIVoQRv0m8vLhUxkvS4FyJpH1XkbYpWo0,61996
transformers/models/mt5/__init__.py,sha256=q5f0AWvlyU1eQjk0OXCpMZ4OM3qNDq35Pv6RuxrWQeI,3597
transformers/models/mt5/__pycache__/__init__.cpython-311.pyc,,
transformers/models/mt5/__pycache__/configuration_mt5.cpython-311.pyc,,
transformers/models/mt5/__pycache__/modeling_flax_mt5.cpython-311.pyc,,
transformers/models/mt5/__pycache__/modeling_mt5.cpython-311.pyc,,
transformers/models/mt5/__pycache__/modeling_tf_mt5.cpython-311.pyc,,
transformers/models/mt5/configuration_mt5.py,sha256=3G5sz5XV_HSRRV4pCDbPhbKlIdJgKxq7Yd6fcisPvXQ,7900
transformers/models/mt5/modeling_flax_mt5.py,sha256=1p8D9st-unpG0rcRGDrUQG__3GIFa77Wst8cYgOGVng,4243
transformers/models/mt5/modeling_mt5.py,sha256=_lYSGcqhZsS07DS2DlTT43d6nAUZ4iV8vl8yo1p4TcY,113306
transformers/models/mt5/modeling_tf_mt5.py,sha256=9Stq04drvy7iyZaptOzmDAWsUzXsKoTFTNsvCjceq_E,3326
transformers/models/musicgen/__init__.py,sha256=EY9dwTvFbwcUcdSclI-kp8xvRO24giI4UJMAmiOWIr0,2099
transformers/models/musicgen/__pycache__/__init__.cpython-311.pyc,,
transformers/models/musicgen/__pycache__/configuration_musicgen.cpython-311.pyc,,
transformers/models/musicgen/__pycache__/convert_musicgen_transformers.cpython-311.pyc,,
transformers/models/musicgen/__pycache__/modeling_musicgen.cpython-311.pyc,,
transformers/models/musicgen/__pycache__/processing_musicgen.cpython-311.pyc,,
transformers/models/musicgen/configuration_musicgen.py,sha256=Mp_26gBxRttiYP_Qy5V5DQ8fjIsK0d7GhN0ViJl_reg,10872
transformers/models/musicgen/convert_musicgen_transformers.py,sha256=F-F2BnXZYxNcRjxFDs6OjL1Zy1VxKXVtbHY2dZKXuPY,9397
transformers/models/musicgen/modeling_musicgen.py,sha256=iuKYLykUcg-x02NqdhkKI4LAsQTOIqXI02VU32_vHFk,124218
transformers/models/musicgen/processing_musicgen.py,sha256=wJE7gvyKPFVyMj5O_pD1Tg1BCC3RizsRIyHo_eV4_os,5666
transformers/models/mvp/__init__.py,sha256=w3eswhHeLn9gayC1Cl8kfkkMGtD036aJeZF2541NmqM,2536
transformers/models/mvp/__pycache__/__init__.cpython-311.pyc,,
transformers/models/mvp/__pycache__/configuration_mvp.cpython-311.pyc,,
transformers/models/mvp/__pycache__/modeling_mvp.cpython-311.pyc,,
transformers/models/mvp/__pycache__/tokenization_mvp.cpython-311.pyc,,
transformers/models/mvp/__pycache__/tokenization_mvp_fast.cpython-311.pyc,,
transformers/models/mvp/configuration_mvp.py,sha256=7WXWilTBmDQpEvO47ioq0NqJdRRYJy6UlGAn0SWjj4s,8534
transformers/models/mvp/modeling_mvp.py,sha256=5ySBnumKALAn2UBW_0NS7IKy1sHtRZdEFngR-Dw9tM0,93312
transformers/models/mvp/tokenization_mvp.py,sha256=1_FBipW0EUT4tATW8Ki24C_HvRoWKePX30nIrwgdv6o,16781
transformers/models/mvp/tokenization_mvp_fast.py,sha256=rxC_tZB2BMZ56nh7TZ16Js0eNKP40HUtQTq2hMTDy2U,12979
transformers/models/nat/__init__.py,sha256=YY8yjsIBbTC1eZRAnR4_p_gHQ3n4JyywB2G1JQuM4AQ,1776
transformers/models/nat/__pycache__/__init__.cpython-311.pyc,,
transformers/models/nat/__pycache__/configuration_nat.cpython-311.pyc,,
transformers/models/nat/__pycache__/modeling_nat.cpython-311.pyc,,
transformers/models/nat/configuration_nat.py,sha256=QyNMh11nnhEK76SfLiA71Ws5sDGustKU_HUO1XAxdzM,7195
transformers/models/nat/modeling_nat.py,sha256=sT3q9abXbW3fNQJh34oCh9-xOXrxDM-ovalvBINL8ew,40012
transformers/models/nezha/__init__.py,sha256=ae3hJzlO_gAa20enOImKo15phpgIXk2_Zt8tVLAY3MU,2233
transformers/models/nezha/__pycache__/__init__.cpython-311.pyc,,
transformers/models/nezha/__pycache__/configuration_nezha.cpython-311.pyc,,
transformers/models/nezha/__pycache__/modeling_nezha.cpython-311.pyc,,
transformers/models/nezha/configuration_nezha.py,sha256=VWlhTUiUP-vWfZ85eDX5Ue3BlBWUr3dystZRuql02Ec,5034
transformers/models/nezha/modeling_nezha.py,sha256=4Z4K2LdjUjmYntUudY8KmBXTig02fPfiTwBxe1usXbs,74845
transformers/models/nllb/__init__.py,sha256=tM7_FdmE7zOQm68GoRQiRt1jbYfPea9kC24QJSSMgIE,1868
transformers/models/nllb/__pycache__/__init__.cpython-311.pyc,,
transformers/models/nllb/__pycache__/tokenization_nllb.cpython-311.pyc,,
transformers/models/nllb/__pycache__/tokenization_nllb_fast.cpython-311.pyc,,
transformers/models/nllb/tokenization_nllb.py,sha256=JP6PZRpV8dmBZundGFJb9I1dkLr-U3ZoQG8m4-0huCk,21616
transformers/models/nllb/tokenization_nllb_fast.py,sha256=rNXyDL7cgyMTnMFneVQ8eNdOCDqxReR8nxfkp3uifOQ,17085
transformers/models/nllb_moe/__init__.py,sha256=ULdz8wrqlqfamWMIQpjmmkPJPPznr34f2JxkYkqquCQ,1978
transformers/models/nllb_moe/__pycache__/__init__.cpython-311.pyc,,
transformers/models/nllb_moe/__pycache__/configuration_nllb_moe.cpython-311.pyc,,
transformers/models/nllb_moe/__pycache__/convert_nllb_moe_sharded_original_checkpoint_to_pytorch.cpython-311.pyc,,
transformers/models/nllb_moe/__pycache__/modeling_nllb_moe.cpython-311.pyc,,
transformers/models/nllb_moe/configuration_nllb_moe.py,sha256=g91oXnPTLuH-cFhvBCpuXTamEwPpgASHCyXr4jhsaMc,11316
transformers/models/nllb_moe/convert_nllb_moe_sharded_original_checkpoint_to_pytorch.py,sha256=c9Zab9qVzNESk0U2exJNaoDwUQo_Q7ZpcZHViZjqTQQ,6477
transformers/models/nllb_moe/modeling_nllb_moe.py,sha256=6VSC3ufqJ7Md1ET58Syev3pCqB2BvOx0PRWRGmNPqxI,85212
transformers/models/nougat/__init__.py,sha256=2cSw40yf-T81USela2GvWs-NSXWHkOa6zJ_3BO7QSCY,1914
transformers/models/nougat/__pycache__/__init__.cpython-311.pyc,,
transformers/models/nougat/__pycache__/convert_nougat_to_hf.cpython-311.pyc,,
transformers/models/nougat/__pycache__/image_processing_nougat.cpython-311.pyc,,
transformers/models/nougat/__pycache__/processing_nougat.cpython-311.pyc,,
transformers/models/nougat/__pycache__/tokenization_nougat_fast.cpython-311.pyc,,
transformers/models/nougat/convert_nougat_to_hf.py,sha256=S6wb6SK-46EHmBvoNSu8n-C1RgbOwzL7XBtCSmTHLrM,10941
transformers/models/nougat/image_processing_nougat.py,sha256=h0F-vLA-JQkBuMPxFRQWJTeV50Xa7A2fVMj4DQy0p0k,23648
transformers/models/nougat/processing_nougat.py,sha256=65OZ7-XvFeiEwFjEi69ZDY931w6NvHTHGo9EixCVxKU,6731
transformers/models/nougat/tokenization_nougat_fast.py,sha256=Zm-g0KwMQA8M84NxjiCqyok8y4OuC3PaulzAe9udaLU,25080
transformers/models/nystromformer/__init__.py,sha256=80Fr1KQ5iZtS-bmWIrqfo26_Yp43SbHRv_YSloD2J4I,2337
transformers/models/nystromformer/__pycache__/__init__.cpython-311.pyc,,
transformers/models/nystromformer/__pycache__/configuration_nystromformer.cpython-311.pyc,,
transformers/models/nystromformer/__pycache__/convert_nystromformer_original_pytorch_checkpoint_to_pytorch.cpython-311.pyc,,
transformers/models/nystromformer/__pycache__/modeling_nystromformer.cpython-311.pyc,,
transformers/models/nystromformer/configuration_nystromformer.py,sha256=Afbe_7_5wu5jhmxsg6pTPj26ckHLyX4wgj8vxCQcYSg,6623
transformers/models/nystromformer/convert_nystromformer_original_pytorch_checkpoint_to_pytorch.py,sha256=8K5IGFosME-LAljFLuTc09oce1IwxZDcxw1KPHsamqc,4197
transformers/models/nystromformer/modeling_nystromformer.py,sha256=_XDfZfOAIkwJIv28GEgHrIo1WZTUwNh1VeoeCUPqOsE,48814
transformers/models/oneformer/__init__.py,sha256=mhWiuUMUOFF1ba9KLNdNJYPYScCLxlZ61WiyO995jjo,2402
transformers/models/oneformer/__pycache__/__init__.cpython-311.pyc,,
transformers/models/oneformer/__pycache__/configuration_oneformer.cpython-311.pyc,,
transformers/models/oneformer/__pycache__/convert_to_hf_oneformer.cpython-311.pyc,,
transformers/models/oneformer/__pycache__/image_processing_oneformer.cpython-311.pyc,,
transformers/models/oneformer/__pycache__/modeling_oneformer.cpython-311.pyc,,
transformers/models/oneformer/__pycache__/processing_oneformer.cpython-311.pyc,,
transformers/models/oneformer/configuration_oneformer.py,sha256=P3bUNSxfS8LMwrX9q71OVxMRoyo-u8fsjU5VdG4TlSo,13809
transformers/models/oneformer/convert_to_hf_oneformer.py,sha256=yBWS0SE1sGS9UqCzX2EdbhAiIWvBCumSBwutJ8VQFF4,50691
transformers/models/oneformer/image_processing_oneformer.py,sha256=Td75jJLP2riTOUjLucM4x4hpmVEUnlWpLjJDAW3vnj4,60792
transformers/models/oneformer/modeling_oneformer.py,sha256=51O-5KX6MIH7lMlwts4ns-VcBcaiIcYZYwdFme07bl4,143669
transformers/models/oneformer/processing_oneformer.py,sha256=WimwZxD8qx7f4tna3czw_Xx35qvTINa2cc485P6lDrU,9483
transformers/models/openai/__init__.py,sha256=5Y0BYw7AWmCFdxKdBMd4-wTi9wj6-8lX7Ii1WvFlfA8,3658
transformers/models/openai/__pycache__/__init__.cpython-311.pyc,,
transformers/models/openai/__pycache__/configuration_openai.cpython-311.pyc,,
transformers/models/openai/__pycache__/convert_openai_original_tf_checkpoint_to_pytorch.cpython-311.pyc,,
transformers/models/openai/__pycache__/modeling_openai.cpython-311.pyc,,
transformers/models/openai/__pycache__/modeling_tf_openai.cpython-311.pyc,,
transformers/models/openai/__pycache__/tokenization_openai.cpython-311.pyc,,
transformers/models/openai/__pycache__/tokenization_openai_fast.cpython-311.pyc,,
transformers/models/openai/configuration_openai.py,sha256=ZYZhOsrJVc-wmRtazyogAL-uG0zC3AYNjmfGhZ9z43w,7239
transformers/models/openai/convert_openai_original_tf_checkpoint_to_pytorch.py,sha256=nAomaHvwIi5gFuedK1WtT61GCu5tBxLE5zj6bY-fjGo,2666
transformers/models/openai/modeling_openai.py,sha256=GJ8wyn1-BZSB1AkiDEKSpQOQQSy8PNu0TGMkMKJcbLw,38429
transformers/models/openai/modeling_tf_openai.py,sha256=U1iH1BvmYbPRAJt0dZb5Gs6kQfXaXZnjYdVifUhJ6sA,41238
transformers/models/openai/tokenization_openai.py,sha256=mTKaubePKi1uGT2ARcudcJc-BGsu0jHDpYqEMce9gBs,15695
transformers/models/openai/tokenization_openai_fast.py,sha256=opyQ_zk4ZMThFLwiLJ32POEL5Y5arlgtJdbwplNePNg,3207
transformers/models/opt/__init__.py,sha256=MQ8MhQamtoySbkT8WbqZ48mMUxp5Ae_UGX2Sl3HKPEc,2977
transformers/models/opt/__pycache__/__init__.cpython-311.pyc,,
transformers/models/opt/__pycache__/configuration_opt.cpython-311.pyc,,
transformers/models/opt/__pycache__/convert_opt_original_pytorch_checkpoint_to_pytorch.cpython-311.pyc,,
transformers/models/opt/__pycache__/modeling_flax_opt.cpython-311.pyc,,
transformers/models/opt/__pycache__/modeling_opt.cpython-311.pyc,,
transformers/models/opt/__pycache__/modeling_tf_opt.cpython-311.pyc,,
transformers/models/opt/configuration_opt.py,sha256=iG9UEpwyKXGYzuk_SzeSy_jAtK7-TLRB5seoD3wv4pQ,7245
transformers/models/opt/convert_opt_original_pytorch_checkpoint_to_pytorch.py,sha256=7dHR6Tk9BBuFMEmHOxbu0jDf-gOnYFPsPLLH6SsA1gI,3858
transformers/models/opt/modeling_flax_opt.py,sha256=MHJpXRbl4u1JcgWkV58DmS6n0wEOTYpZBeOJQFzdBT0,31541
transformers/models/opt/modeling_opt.py,sha256=yn6-GQWMgAIZxcAUBjNRZkw_6YaKXJwfYbrLX0wUcFA,68954
transformers/models/opt/modeling_tf_opt.py,sha256=SoVD0Dmrgak3O6SH2Qtlgn_2LFgfmHMM5hhAibKcVBI,49554
transformers/models/owlv2/__init__.py,sha256=fvzKBoWfoB8-9hZKeId1Qvy3p_N9PLgsGoXzrg-fBzI,2606
transformers/models/owlv2/__pycache__/__init__.cpython-311.pyc,,
transformers/models/owlv2/__pycache__/configuration_owlv2.cpython-311.pyc,,
transformers/models/owlv2/__pycache__/convert_owlv2_to_hf.cpython-311.pyc,,
transformers/models/owlv2/__pycache__/image_processing_owlv2.cpython-311.pyc,,
transformers/models/owlv2/__pycache__/modeling_owlv2.cpython-311.pyc,,
transformers/models/owlv2/__pycache__/processing_owlv2.cpython-311.pyc,,
transformers/models/owlv2/configuration_owlv2.py,sha256=l65Zv-lmsPatGFTDv59V0YUcjxYoujTA3ANVTFslHB8,15625
transformers/models/owlv2/convert_owlv2_to_hf.py,sha256=rF02k9XWTswf4P4ZZ76ekB3be6pRsFJLtbuWaJpyx3Y,22018
transformers/models/owlv2/image_processing_owlv2.py,sha256=vfXjCDRxwtYS1lSqmQXxzrmdrx4GsFe61BEck5nBMpA,26368
transformers/models/owlv2/modeling_owlv2.py,sha256=qeEJ5rBAi6-k3PLAN8A8XHxe6kZ1UMatW4Olhy5JihA,82719
transformers/models/owlv2/processing_owlv2.py,sha256=ZnTH6-bZkd94Opf9TDnRBjOs4K2gZr0n-_B9fPyrLls,10152
transformers/models/owlvit/__init__.py,sha256=zBsZnxDQ28eWv3rpN77KfHfIQPv4sIurjn-kNoykQyo,2915
transformers/models/owlvit/__pycache__/__init__.cpython-311.pyc,,
transformers/models/owlvit/__pycache__/configuration_owlvit.cpython-311.pyc,,
transformers/models/owlvit/__pycache__/convert_owlvit_original_flax_to_hf.cpython-311.pyc,,
transformers/models/owlvit/__pycache__/feature_extraction_owlvit.cpython-311.pyc,,
transformers/models/owlvit/__pycache__/image_processing_owlvit.cpython-311.pyc,,
transformers/models/owlvit/__pycache__/modeling_owlvit.cpython-311.pyc,,
transformers/models/owlvit/__pycache__/processing_owlvit.cpython-311.pyc,,
transformers/models/owlvit/configuration_owlvit.py,sha256=azl7t6S1nfxlCs2PruOX7Czp8PPVQUt6QF2ZdZZSXbc,17044
transformers/models/owlvit/convert_owlvit_original_flax_to_hf.py,sha256=tofzNZcVROwfYoV7pV6u50Am3TFm-XmuJEAGwNvRT9o,13988
transformers/models/owlvit/feature_extraction_owlvit.py,sha256=yPO8FbUw3YabKbsV_ozKpIr6JixO9knVw1eMIHeiCtY,1186
transformers/models/owlvit/image_processing_owlvit.py,sha256=3tPe2U2u3e6eSoYpcsNXHfYHATW9LAeCw1Pur8Fk6n8,28059
transformers/models/owlvit/modeling_owlvit.py,sha256=rYYQBL6hUqW8p2lTaIGB6RKv0wtM5P8VdHuBrDl_Zho,76339
transformers/models/owlvit/processing_owlvit.py,sha256=XoD3T1ioapw8w2JvjK-Ju-M4juPmt_Y4LTt2YZT_qFk,11148
transformers/models/patchtsmixer/__init__.py,sha256=z9KtbxxAyoNMB0DkWBDvpxmgfZMzx5B056p1nlLjhIE,2204
transformers/models/patchtsmixer/__pycache__/__init__.cpython-311.pyc,,
transformers/models/patchtsmixer/__pycache__/configuration_patchtsmixer.cpython-311.pyc,,
transformers/models/patchtsmixer/__pycache__/modeling_patchtsmixer.cpython-311.pyc,,
transformers/models/patchtsmixer/configuration_patchtsmixer.py,sha256=SvoFgMzMAN-OeqhpUnFXomLr-I7yEUeFt6GStPV8j_c,12704
transformers/models/patchtsmixer/modeling_patchtsmixer.py,sha256=Zom5FLahlVOdzKx6LfMTaYe8bftcv-ScZ4yt4XkxCgg,88003
transformers/models/patchtst/__init__.py,sha256=AyK9VUDx2iphFn8IMvgt49apReqE0VBTxrjDwE6fRhc,2071
transformers/models/patchtst/__pycache__/__init__.cpython-311.pyc,,
transformers/models/patchtst/__pycache__/configuration_patchtst.cpython-311.pyc,,
transformers/models/patchtst/__pycache__/modeling_patchtst.cpython-311.pyc,,
transformers/models/patchtst/configuration_patchtst.py,sha256=fxONhc176CjlhxtfrwdsVA53ysuC1O5dfOLecWY2Lq0,12711
transformers/models/patchtst/modeling_patchtst.py,sha256=rLCLs-JcNnrBQMCOdLaSxMOLG6gN8-bCh6KmaybeHYM,91835
transformers/models/pegasus/__init__.py,sha256=SXHYeNzkJrHfERo9lhqyvu3S75BYDmqceiFfim50Y_g,4111
transformers/models/pegasus/__pycache__/__init__.cpython-311.pyc,,
transformers/models/pegasus/__pycache__/configuration_pegasus.cpython-311.pyc,,
transformers/models/pegasus/__pycache__/convert_pegasus_tf_to_pytorch.cpython-311.pyc,,
transformers/models/pegasus/__pycache__/modeling_flax_pegasus.cpython-311.pyc,,
transformers/models/pegasus/__pycache__/modeling_pegasus.cpython-311.pyc,,
transformers/models/pegasus/__pycache__/modeling_tf_pegasus.cpython-311.pyc,,
transformers/models/pegasus/__pycache__/tokenization_pegasus.cpython-311.pyc,,
transformers/models/pegasus/__pycache__/tokenization_pegasus_fast.cpython-311.pyc,,
transformers/models/pegasus/configuration_pegasus.py,sha256=gvZbwQgiMQU1H7w7bioQBfX5Win6AgsKUMc5LnF-1Vo,7694
transformers/models/pegasus/convert_pegasus_tf_to_pytorch.py,sha256=9geJowNAukZc9FE2OEq0pXQi6ynw9k-2NFtlmISxpUg,5359
transformers/models/pegasus/modeling_flax_pegasus.py,sha256=NbaPRG_BeTrZQbbZCxUOWxwdgSKSrHWkjTicOP3Yhvk,65974
transformers/models/pegasus/modeling_pegasus.py,sha256=vrV1qhugHo_7Vy2DP70kkbhN5KPF1CphZHLFE1nALY8,80711
transformers/models/pegasus/modeling_tf_pegasus.py,sha256=8dfcnMG6muIhoLDDU-p3LCmnFX5itzOzSQipqm5mIeo,74202
transformers/models/pegasus/tokenization_pegasus.py,sha256=Qg4F0h6IYPL7Kj-a-HSacSXVU7PZ23Z2nRwvAQQvP14,13478
transformers/models/pegasus/tokenization_pegasus_fast.py,sha256=4ZQe68xXmdB3egUhYPapeyYUPt8u6JFY56TYirWvcr0,10431
transformers/models/pegasus_x/__init__.py,sha256=M7Ef6UH-lQ53z-17c-XQi5nmmi-uVz8UKFHQe71LDVU,1828
transformers/models/pegasus_x/__pycache__/__init__.cpython-311.pyc,,
transformers/models/pegasus_x/__pycache__/configuration_pegasus_x.cpython-311.pyc,,
transformers/models/pegasus_x/__pycache__/modeling_pegasus_x.cpython-311.pyc,,
transformers/models/pegasus_x/configuration_pegasus_x.py,sha256=GP_98_7nhkDnBSGwt6pvPa1MIxTJVkvEM38ijrb6J_I,8420
transformers/models/pegasus_x/modeling_pegasus_x.py,sha256=OlekbVTWdPJBK7c9PiYSpscTSPdVq8RcNRF-bhFqqu0,75813
transformers/models/perceiver/__init__.py,sha256=y-6ZMYh3FfGpj9A1gZafPXrfGKJoGKEenKlJT9ZZEw8,3293
transformers/models/perceiver/__pycache__/__init__.cpython-311.pyc,,
transformers/models/perceiver/__pycache__/configuration_perceiver.cpython-311.pyc,,
transformers/models/perceiver/__pycache__/convert_perceiver_haiku_to_pytorch.cpython-311.pyc,,
transformers/models/perceiver/__pycache__/feature_extraction_perceiver.cpython-311.pyc,,
transformers/models/perceiver/__pycache__/image_processing_perceiver.cpython-311.pyc,,
transformers/models/perceiver/__pycache__/modeling_perceiver.cpython-311.pyc,,
transformers/models/perceiver/__pycache__/tokenization_perceiver.cpython-311.pyc,,
transformers/models/perceiver/configuration_perceiver.py,sha256=DvhPj8Pcm-kkMJh56FIWraYKhqxtuan2pjnV0WJZLCI,12397
transformers/models/perceiver/convert_perceiver_haiku_to_pytorch.py,sha256=f8p0sPVQv19tMDKkIM8IfTg60-SYX9MMABAzstxFt7k,21286
transformers/models/perceiver/feature_extraction_perceiver.py,sha256=0lW_qh5ONtUwr0ARM9RB9hizA76wL6fmeofDrhbIsXI,1207
transformers/models/perceiver/image_processing_perceiver.py,sha256=LFltu2XeB9EEFfi9k-lkxbC2rw-GOlg3KjvccjLUImM,17394
transformers/models/perceiver/modeling_perceiver.py,sha256=NzcUFXVQ8Sefez_ilxGk300GovMNhfPly_4Cx7fT6-o,146639
transformers/models/perceiver/tokenization_perceiver.py,sha256=VOWp64riIrTTB7oqvLBq7N6_U515ZWzaaVpwSx7SncI,8020
transformers/models/persimmon/__init__.py,sha256=gp5VkpnXik0R_PBRitY6UBMcBDMmL41N8o1LjPW_Hmo,1835
transformers/models/persimmon/__pycache__/__init__.cpython-311.pyc,,
transformers/models/persimmon/__pycache__/configuration_persimmon.cpython-311.pyc,,
transformers/models/persimmon/__pycache__/convert_persimmon_weights_to_hf.cpython-311.pyc,,
transformers/models/persimmon/__pycache__/modeling_persimmon.cpython-311.pyc,,
transformers/models/persimmon/configuration_persimmon.py,sha256=E5mU1_NWby45NOrH3CV8HyfveoSF2vsPMlref437Eas,7839
transformers/models/persimmon/convert_persimmon_weights_to_hf.py,sha256=F3NFcbCWD-UxFwgp2h-Nv78_M0p0LELPq4re30ZNIjU,4644
transformers/models/persimmon/modeling_persimmon.py,sha256=3WjBbCWVV-WpYE1ygrSm68hZzugnSaE1yLrez8s9QVk,47096
transformers/models/phi/__init__.py,sha256=cSkf7i5ur4JTXt8gWalgbD-HFoJeFjMVTH3u5IOfICE,1971
transformers/models/phi/__pycache__/__init__.cpython-311.pyc,,
transformers/models/phi/__pycache__/configuration_phi.cpython-311.pyc,,
transformers/models/phi/__pycache__/convert_phi_weights_to_hf.cpython-311.pyc,,
transformers/models/phi/__pycache__/modeling_phi.cpython-311.pyc,,
transformers/models/phi/configuration_phi.py,sha256=wA9fRDiVMYzJyTG5HRZXjZ2xCh2dvUD2UiXJtcKKdF4,9422
transformers/models/phi/convert_phi_weights_to_hf.py,sha256=XrjgtZm6GZQx01rZ0q52g6e4ajyZhl8n02QNchAD6BQ,7685
transformers/models/phi/modeling_phi.py,sha256=4lFHscfzp95T21r-y9F0cKgOGnRmwtx0J_rBeU_JBJg,62600
transformers/models/phobert/__init__.py,sha256=JDAAoG6FOpN1o5kgFBbHkoko9NsiioFi-ZAeAgR79nY,955
transformers/models/phobert/__pycache__/__init__.cpython-311.pyc,,
transformers/models/phobert/__pycache__/tokenization_phobert.cpython-311.pyc,,
transformers/models/phobert/tokenization_phobert.py,sha256=5r7Kdf6SFcokEh_WHudOClN5XV41BxW3qVRsY5jxXrM,13814
transformers/models/pix2struct/__init__.py,sha256=VSpzQStsFkcbIF3aftcNle95WQ7-cZzuWwDhjgzK-IU,2701
transformers/models/pix2struct/__pycache__/__init__.cpython-311.pyc,,
transformers/models/pix2struct/__pycache__/configuration_pix2struct.cpython-311.pyc,,
transformers/models/pix2struct/__pycache__/convert_pix2struct_original_pytorch_to_hf.cpython-311.pyc,,
transformers/models/pix2struct/__pycache__/image_processing_pix2struct.cpython-311.pyc,,
transformers/models/pix2struct/__pycache__/modeling_pix2struct.cpython-311.pyc,,
transformers/models/pix2struct/__pycache__/processing_pix2struct.cpython-311.pyc,,
transformers/models/pix2struct/configuration_pix2struct.py,sha256=k5vPB0zk5Z34QRn1h3e_Yfj0mf_ETgjHf-nr2j64Ys8,17476
transformers/models/pix2struct/convert_pix2struct_original_pytorch_to_hf.py,sha256=m_S-9oxyN4PQafRbWQIP-G0NUDrTqxOmr8IwiHNCOuU,5886
transformers/models/pix2struct/image_processing_pix2struct.py,sha256=snQZl3jqenJyk_wbmXK_hZJKO2Z5PyYEVFdVn1oeI6o,19727
transformers/models/pix2struct/modeling_pix2struct.py,sha256=SJ4-vjEr466I9cktoqTxZq4-8SP3WJg98fOTLAmxKPM,83468
transformers/models/pix2struct/processing_pix2struct.py,sha256=YFwg3KSy0SKXAkBucCTOwsOFSm7pFYj-M6bCViLYVqU,6960
transformers/models/plbart/__init__.py,sha256=uNjyVJsOGh5eb2iNYSc7av9uNk-n3xB6rLv3BSRBKoY,2429
transformers/models/plbart/__pycache__/__init__.cpython-311.pyc,,
transformers/models/plbart/__pycache__/configuration_plbart.cpython-311.pyc,,
transformers/models/plbart/__pycache__/convert_plbart_original_checkpoint_to_torch.cpython-311.pyc,,
transformers/models/plbart/__pycache__/modeling_plbart.cpython-311.pyc,,
transformers/models/plbart/__pycache__/tokenization_plbart.cpython-311.pyc,,
transformers/models/plbart/configuration_plbart.py,sha256=uBJEf1G2ZSxJGig6nucB7_Jh-ODQH-RSEDwCYVayLZ0,8720
transformers/models/plbart/convert_plbart_original_checkpoint_to_torch.py,sha256=BOXNudNSr1xevmHnvNpa_4ya3Q89m6J4lndQhCWSLB8,3553
transformers/models/plbart/modeling_plbart.py,sha256=1Lru2QUS2mN6X6-BnC4Q0fDK3LBxSBgAf2Ccmr2AuAo,84505
transformers/models/plbart/tokenization_plbart.py,sha256=TFYonpfEv-HaRgPs8C1su_oRxN40Myv43LhU0evsBDk,21642
transformers/models/poolformer/__init__.py,sha256=fzMbnIpAxBApWl0QVCU965q9km5dySep9Hjhml26r68,2586
transformers/models/poolformer/__pycache__/__init__.cpython-311.pyc,,
transformers/models/poolformer/__pycache__/configuration_poolformer.cpython-311.pyc,,
transformers/models/poolformer/__pycache__/convert_poolformer_original_to_pytorch.cpython-311.pyc,,
transformers/models/poolformer/__pycache__/feature_extraction_poolformer.cpython-311.pyc,,
transformers/models/poolformer/__pycache__/image_processing_poolformer.cpython-311.pyc,,
transformers/models/poolformer/__pycache__/modeling_poolformer.cpython-311.pyc,,
transformers/models/poolformer/configuration_poolformer.py,sha256=zNmWnPCw24xDlZn-wkUWbOyxjSnwYQviNDrF8VM2-A4,5804
transformers/models/poolformer/convert_poolformer_original_to_pytorch.py,sha256=Vvlp7ju7kr2sg1NdXKma6vYGABjs4sVhPKhgFKPJRpk,7947
transformers/models/poolformer/feature_extraction_poolformer.py,sha256=KDL4tg7hxwzQKYmGc6jMZfzeD9UCTb00oNfbejIjzmk,1214
transformers/models/poolformer/image_processing_poolformer.py,sha256=uIF91AFMo5mDRWmnG3fgnK_iWpROjFgOlZn0mZRYYss,17755
transformers/models/poolformer/modeling_poolformer.py,sha256=b9nhiafn6_45Fbn2yeSygDZ9HfY8Uhs84yuzXXzuWrU,17896
transformers/models/pop2piano/__init__.py,sha256=wxMmbwwAuqcGF8MimtfwAf4JPJ5D8x8up-q4yRlwU5E,3819
transformers/models/pop2piano/__pycache__/__init__.cpython-311.pyc,,
transformers/models/pop2piano/__pycache__/configuration_pop2piano.cpython-311.pyc,,
transformers/models/pop2piano/__pycache__/convert_pop2piano_weights_to_hf.cpython-311.pyc,,
transformers/models/pop2piano/__pycache__/feature_extraction_pop2piano.cpython-311.pyc,,
transformers/models/pop2piano/__pycache__/modeling_pop2piano.cpython-311.pyc,,
transformers/models/pop2piano/__pycache__/processing_pop2piano.cpython-311.pyc,,
transformers/models/pop2piano/__pycache__/tokenization_pop2piano.cpython-311.pyc,,
transformers/models/pop2piano/configuration_pop2piano.py,sha256=dWudee4erbp44euxJE_Kje1DktlBibfanEmqMlUOKI8,6072
transformers/models/pop2piano/convert_pop2piano_weights_to_hf.py,sha256=eZuC9RFueLoOmsaGWMa-6hNQyLBLTg9WXlRQRuiQerA,8626
transformers/models/pop2piano/feature_extraction_pop2piano.py,sha256=SBNQB6aol_Uan2p_z33IQue9y4exatqd80XyzHGBoqY,19839
transformers/models/pop2piano/modeling_pop2piano.py,sha256=Ppgl8a2COI0zaI4PZLQhIB0fToaBFccPQCYfiBzidHY,65674
transformers/models/pop2piano/processing_pop2piano.py,sha256=ytBqku-v0wCqeK4_JVd-0SNCI7jmYltMb5wDzagn6V4,5525
transformers/models/pop2piano/tokenization_pop2piano.py,sha256=kAGnOroIWUsaPcVYHLeQ4hPneWO2RZbbeExgyEDi8SQ,32086
transformers/models/prophetnet/__init__.py,sha256=1w4cY9QLl0elN9_oFDScwrb0F12-54b5ylPrxCiqpFw,2157
transformers/models/prophetnet/__pycache__/__init__.cpython-311.pyc,,
transformers/models/prophetnet/__pycache__/configuration_prophetnet.cpython-311.pyc,,
transformers/models/prophetnet/__pycache__/convert_prophetnet_original_pytorch_checkpoint_to_pytorch.cpython-311.pyc,,
transformers/models/prophetnet/__pycache__/modeling_prophetnet.cpython-311.pyc,,
transformers/models/prophetnet/__pycache__/tokenization_prophetnet.cpython-311.pyc,,
transformers/models/prophetnet/configuration_prophetnet.py,sha256=eyjxg-fI6no0GwvYskI_INPmIkh5cf1Z-acGYLWfcOU,9063
transformers/models/prophetnet/convert_prophetnet_original_pytorch_checkpoint_to_pytorch.py,sha256=EzgNdUzWNQowTUpyfXO-_RBZEw0sa5sVA1b7jbqFUxU,7055
transformers/models/prophetnet/modeling_prophetnet.py,sha256=LO7-EUiSy_N_Xe_IwHjBqBQXg3RPv2renpvBMv3BTWQ,115529
transformers/models/prophetnet/tokenization_prophetnet.py,sha256=NMz1ByHT5IO0wa1cRLPK21bOBmOaR-MNMggpGpgV6D4,21489
transformers/models/pvt/__init__.py,sha256=FxRer-Bn0NI00eTjXYOlUzVNJMH50lB78JEWPk1BNuw,2384
transformers/models/pvt/__pycache__/__init__.cpython-311.pyc,,
transformers/models/pvt/__pycache__/configuration_pvt.cpython-311.pyc,,
transformers/models/pvt/__pycache__/convert_pvt_to_pytorch.cpython-311.pyc,,
transformers/models/pvt/__pycache__/image_processing_pvt.cpython-311.pyc,,
transformers/models/pvt/__pycache__/modeling_pvt.cpython-311.pyc,,
transformers/models/pvt/configuration_pvt.py,sha256=zQZXZmu9qvMjymLUn7n89gF1Usb3Q6WASGkZvE-9SgM,7098
transformers/models/pvt/convert_pvt_to_pytorch.py,sha256=1DIHp33moj_2LrWws9x02AZ9qRrVMCQ3jifvV3SxmFc,9738
transformers/models/pvt/image_processing_pvt.py,sha256=fLuf68AspdspY_PeMiVpiFMYUXrT054HwQOa0pAFgoI,13776
transformers/models/pvt/modeling_pvt.py,sha256=CE1Ro4flzxshzKCnw-w2xy2EoN1cqqNGHhmM80b9PHk,28393
transformers/models/qdqbert/__init__.py,sha256=x3xI7kd5kpsjAvYJT8SrR5_uCeInhVA8repNZFRtXhU,2402
transformers/models/qdqbert/__pycache__/__init__.cpython-311.pyc,,
transformers/models/qdqbert/__pycache__/configuration_qdqbert.cpython-311.pyc,,
transformers/models/qdqbert/__pycache__/modeling_qdqbert.cpython-311.pyc,,
transformers/models/qdqbert/configuration_qdqbert.py,sha256=l6LM92_PKNLGIS3VPlkrqTyVV_CDdoyDGFoZRnIp7p8,5967
transformers/models/qdqbert/modeling_qdqbert.py,sha256=LGFE3nyBl7r4LEZ9y3IZZujdMM-mpK-WmX7mN0eBwT8,77339
transformers/models/qwen2/__init__.py,sha256=9gokBZ-g_YdJeUBfioDa7ZRVQdTgZ_nNQA03axWYwEw,2354
transformers/models/qwen2/__pycache__/__init__.cpython-311.pyc,,
transformers/models/qwen2/__pycache__/configuration_qwen2.cpython-311.pyc,,
transformers/models/qwen2/__pycache__/modeling_qwen2.cpython-311.pyc,,
transformers/models/qwen2/__pycache__/tokenization_qwen2.cpython-311.pyc,,
transformers/models/qwen2/__pycache__/tokenization_qwen2_fast.cpython-311.pyc,,
transformers/models/qwen2/configuration_qwen2.py,sha256=j1iZbAA4SkJ1grGz_8zw7xNpI85H4MQByWRu3WE32ic,6771
transformers/models/qwen2/modeling_qwen2.py,sha256=lgZ3BWSZ3MUbzxzUAr_Eg-hw9n7UNNJtQ_F9dPR01J8,63976
transformers/models/qwen2/tokenization_qwen2.py,sha256=qRnVTZeFGoB2Qb1SvVHxfeu5ikX0Qs8_xcFojwRKAi0,14248
transformers/models/qwen2/tokenization_qwen2_fast.py,sha256=Cjhh40mvClKXPFCCnfDXgcUJ8TBLLfEZP-RbpNLc8sw,5664
transformers/models/rag/__init__.py,sha256=omMwtpcTWBHYKZvt8NIxbACHhICmYWfeTgiC7O4U88g,2426
transformers/models/rag/__pycache__/__init__.cpython-311.pyc,,
transformers/models/rag/__pycache__/configuration_rag.cpython-311.pyc,,
transformers/models/rag/__pycache__/modeling_rag.cpython-311.pyc,,
transformers/models/rag/__pycache__/modeling_tf_rag.cpython-311.pyc,,
transformers/models/rag/__pycache__/retrieval_rag.cpython-311.pyc,,
transformers/models/rag/__pycache__/tokenization_rag.cpython-311.pyc,,
transformers/models/rag/configuration_rag.py,sha256=R29aZiFq0Aykstyd0nBhz6AfpoUJdw14cn2Bf7wUigw,8259
transformers/models/rag/modeling_rag.py,sha256=W7LF6vOkSJRUvaT5tD_sel-7a7VeW7wc8x5V7QdpCjg,85797
transformers/models/rag/modeling_tf_rag.py,sha256=kEbSfcPwE94BqHh_h94XjoPd5OJcN5aQ8vNu23-rkUU,88806
transformers/models/rag/retrieval_rag.py,sha256=SRMr021qyYg27cLpuAXnaLPJuGaDiuLjFfwPwg9IEx0,29661
transformers/models/rag/tokenization_rag.py,sha256=O5gPSIP0dOyYEe5k4VjcCttsbAoAAZ6338z0IsWF690,4576
transformers/models/realm/__init__.py,sha256=k3gccDAsk5YJYrjrd8hOZCc1q8KJR2EMoGhvEdF-OTU,2675
transformers/models/realm/__pycache__/__init__.cpython-311.pyc,,
transformers/models/realm/__pycache__/configuration_realm.cpython-311.pyc,,
transformers/models/realm/__pycache__/modeling_realm.cpython-311.pyc,,
transformers/models/realm/__pycache__/retrieval_realm.cpython-311.pyc,,
transformers/models/realm/__pycache__/tokenization_realm.cpython-311.pyc,,
transformers/models/realm/__pycache__/tokenization_realm_fast.cpython-311.pyc,,
transformers/models/realm/configuration_realm.py,sha256=jkPKtWRMP0CriC9qnd7TGfJCj6cUzpc888ZUEUkxNUU,8743
transformers/models/realm/modeling_realm.py,sha256=taematzWMSOF1ckhKBnVsdk0dDvE_oKKLzA748xw-nU,84408
transformers/models/realm/retrieval_realm.py,sha256=86jQyu1U8QePlahXS8rGD_E6TlvEqQeqg21qSsAno-M,6370
transformers/models/realm/tokenization_realm.py,sha256=qVlgAB0Kur0xq5qKdPZldhICqsf1z8CZMhq817DOW-0,25476
transformers/models/realm/tokenization_realm_fast.py,sha256=8HRiBJaYGHz9zyYgQkkcCbi-ATUoHYediIuh77lLRpE,14587
transformers/models/reformer/__init__.py,sha256=MKhG4aefK429UY32oYQbVTLm1T2L_SIYS_TNnrWnTwA,3139
transformers/models/reformer/__pycache__/__init__.cpython-311.pyc,,
transformers/models/reformer/__pycache__/configuration_reformer.cpython-311.pyc,,
transformers/models/reformer/__pycache__/convert_reformer_trax_checkpoint_to_pytorch.cpython-311.pyc,,
transformers/models/reformer/__pycache__/modeling_reformer.cpython-311.pyc,,
transformers/models/reformer/__pycache__/tokenization_reformer.cpython-311.pyc,,
transformers/models/reformer/__pycache__/tokenization_reformer_fast.cpython-311.pyc,,
transformers/models/reformer/configuration_reformer.py,sha256=62h-3phwg2n_GgPBJ-xTN4Gv3Y1uKEoLP8N6jfFnDoo,13464
transformers/models/reformer/convert_reformer_trax_checkpoint_to_pytorch.py,sha256=axn3FvdtVSdQT5V5u1-sfJ3sMV3YpEU6r5B10bTYZ8o,7818
transformers/models/reformer/modeling_reformer.py,sha256=4r5vwrPJeoDLGKOzfvkaX69GcqbGIoIqag0fliGmCDE,115319
transformers/models/reformer/tokenization_reformer.py,sha256=fIMEtwgoDeoLRCG8_exbgMVMu4ev7fysHT_LBcDYE0w,7173
transformers/models/reformer/tokenization_reformer_fast.py,sha256=pLzMRju8y7WktPg9Q1M1VGqZUuyTxWBwlJbZfV-yoQU,4886
transformers/models/regnet/__init__.py,sha256=KQR1LgyjMxE0d-7nACPCHiRXo0rSm93vfcy8puDXbuE,3168
transformers/models/regnet/__pycache__/__init__.cpython-311.pyc,,
transformers/models/regnet/__pycache__/configuration_regnet.cpython-311.pyc,,
transformers/models/regnet/__pycache__/convert_regnet_seer_10b_to_pytorch.cpython-311.pyc,,
transformers/models/regnet/__pycache__/convert_regnet_to_pytorch.cpython-311.pyc,,
transformers/models/regnet/__pycache__/modeling_flax_regnet.cpython-311.pyc,,
transformers/models/regnet/__pycache__/modeling_regnet.cpython-311.pyc,,
transformers/models/regnet/__pycache__/modeling_tf_regnet.cpython-311.pyc,,
transformers/models/regnet/configuration_regnet.py,sha256=6WuMP1n1I67kdqfhnNPzb4LvxWJ9Kt2S1nZH-Hkrn6g,4089
transformers/models/regnet/convert_regnet_seer_10b_to_pytorch.py,sha256=zDPbUZRiO0lJl7hdUztm9JnUAbOI1Wv5wyHZdCKQ-d0,11770
transformers/models/regnet/convert_regnet_to_pytorch.py,sha256=lvSaB1ny0EKvS4KfhTpbNjdrYI6xE1zmYctM_O_a_Ak,18719
transformers/models/regnet/modeling_flax_regnet.py,sha256=2Ao7eODWcHufpZoNbGC4FbX6tZVE2bfWWrZSMbPGcMg,28410
transformers/models/regnet/modeling_regnet.py,sha256=UcDlWr4BdiiW3EI3tshY0gpSYlLRB2CtoWZEscMOQ_A,17332
transformers/models/regnet/modeling_tf_regnet.py,sha256=vhEfVLz_ITL1-TjemTy73Xv6MWNNKXPRwLiKP3E1iTQ,24452
transformers/models/rembert/__init__.py,sha256=XC3xr6aUNReL6SzFXr6TyAWPg9EXiBFl4o225gmkNQQ,4514
transformers/models/rembert/__pycache__/__init__.cpython-311.pyc,,
transformers/models/rembert/__pycache__/configuration_rembert.cpython-311.pyc,,
transformers/models/rembert/__pycache__/convert_rembert_tf_checkpoint_to_pytorch.cpython-311.pyc,,
transformers/models/rembert/__pycache__/modeling_rembert.cpython-311.pyc,,
transformers/models/rembert/__pycache__/modeling_tf_rembert.cpython-311.pyc,,
transformers/models/rembert/__pycache__/tokenization_rembert.cpython-311.pyc,,
transformers/models/rembert/__pycache__/tokenization_rembert_fast.cpython-311.pyc,,
transformers/models/rembert/configuration_rembert.py,sha256=BN2YQK0h-wIkNdeopkMJEBHb1Me74wSR2ZHWsvBM7D8,7450
transformers/models/rembert/convert_rembert_tf_checkpoint_to_pytorch.py,sha256=C-TS1MrtQHTxK3j5HUKwlcrQItW24T7_iPvtt8KGbAU,2208
transformers/models/rembert/modeling_rembert.py,sha256=wCy5nEYGeOjGLbup1ODDMfOvX-g2RrsMXBk77VpSFIU,68287
transformers/models/rembert/modeling_tf_rembert.py,sha256=KwiGiwwVDdA6VEgNfBI6VB2wFLNT4scU3at_VlkaeUc,77830
transformers/models/rembert/tokenization_rembert.py,sha256=lJ0gRAsl75DaTrE0LzvfYmSB2Tu6XcBxr2YtgID4WVE,10954
transformers/models/rembert/tokenization_rembert_fast.py,sha256=RHLJO3J8up1dbVs977BQNBrynuQ7RtWnfDJux1quZzE,10483
transformers/models/resnet/__init__.py,sha256=n63hjzrOOmaIXaDS0F9thB531jarpWDBkXmgFaMBRbo,3216
transformers/models/resnet/__pycache__/__init__.cpython-311.pyc,,
transformers/models/resnet/__pycache__/configuration_resnet.cpython-311.pyc,,
transformers/models/resnet/__pycache__/convert_resnet_to_pytorch.cpython-311.pyc,,
transformers/models/resnet/__pycache__/modeling_flax_resnet.cpython-311.pyc,,
transformers/models/resnet/__pycache__/modeling_resnet.cpython-311.pyc,,
transformers/models/resnet/__pycache__/modeling_tf_resnet.cpython-311.pyc,,
transformers/models/resnet/configuration_resnet.py,sha256=7WmqNMI9gh0wmRfqola_t21CfLUXjMXCTeLuWEngWYA,6158
transformers/models/resnet/convert_resnet_to_pytorch.py,sha256=ShZl8Ob5ElrgRujQCoGXWdIY_99UICrWqiHdSzFdOHc,7287
transformers/models/resnet/modeling_flax_resnet.py,sha256=uJMz2FgVXm6ffwjiorCHkuPbCRra8VdN1vYILRuIgxY,24607
transformers/models/resnet/modeling_resnet.py,sha256=BHkhHk1gFdqMUfPKU0vLlIWpVK_G0O2JxrXgTj39bq8,19410
transformers/models/resnet/modeling_tf_resnet.py,sha256=Lzsx9V6wq_v1uZsq_U1urKiYZCW-ocw8qjH0KhqSOao,23800
transformers/models/roberta/__init__.py,sha256=GvGX0z6XPZtwkfCh4K2xagGOK0tlW0DT91QVQhTcA4o,5091
transformers/models/roberta/__pycache__/__init__.cpython-311.pyc,,
transformers/models/roberta/__pycache__/configuration_roberta.cpython-311.pyc,,
transformers/models/roberta/__pycache__/convert_roberta_original_pytorch_checkpoint_to_pytorch.cpython-311.pyc,,
transformers/models/roberta/__pycache__/modeling_flax_roberta.cpython-311.pyc,,
transformers/models/roberta/__pycache__/modeling_roberta.cpython-311.pyc,,
transformers/models/roberta/__pycache__/modeling_tf_roberta.cpython-311.pyc,,
transformers/models/roberta/__pycache__/tokenization_roberta.cpython-311.pyc,,
transformers/models/roberta/__pycache__/tokenization_roberta_fast.cpython-311.pyc,,
transformers/models/roberta/configuration_roberta.py,sha256=JI-feh8d32wWyLvmvYEykH4MPtOt9lDaXyhEs8PiRho,8057
transformers/models/roberta/convert_roberta_original_pytorch_checkpoint_to_pytorch.py,sha256=MmHtq9AhcXXd-V8Fz0XWC8n-PL-S1MSdFhTCVM6Cksk,8002
transformers/models/roberta/modeling_flax_roberta.py,sha256=Bz5VgKKwWnVVmRFyHD11Ug7IlvgwOLIMbGI0lBkMHt8,56976
transformers/models/roberta/modeling_roberta.py,sha256=nNU4p0p_OStkcE7thCAu9gwjsxgjA7UvPV2kUnASRcE,71455
transformers/models/roberta/modeling_tf_roberta.py,sha256=svlmaFWyFtjMjF2Upehf_NBRN4OHr-U4I3EiP7MWQzc,80139
transformers/models/roberta/tokenization_roberta.py,sha256=np7Bzj9HqqCGk4PJLRN8xXzKy0Q_PRYivwoNBLD59z8,18575
transformers/models/roberta/tokenization_roberta_fast.py,sha256=uXTpqZUN-Z-PsuxQ-FF-TJEof85R97KOv8k-SugY3EE,14419
transformers/models/roberta_prelayernorm/__init__.py,sha256=C9bA_ah_10OCt_LUT1bsOJTUjSt6boV2frOKBtHCes4,5391
transformers/models/roberta_prelayernorm/__pycache__/__init__.cpython-311.pyc,,
transformers/models/roberta_prelayernorm/__pycache__/configuration_roberta_prelayernorm.cpython-311.pyc,,
transformers/models/roberta_prelayernorm/__pycache__/convert_roberta_prelayernorm_original_pytorch_checkpoint_to_pytorch.cpython-311.pyc,,
transformers/models/roberta_prelayernorm/__pycache__/modeling_flax_roberta_prelayernorm.cpython-311.pyc,,
transformers/models/roberta_prelayernorm/__pycache__/modeling_roberta_prelayernorm.cpython-311.pyc,,
transformers/models/roberta_prelayernorm/__pycache__/modeling_tf_roberta_prelayernorm.cpython-311.pyc,,
transformers/models/roberta_prelayernorm/configuration_roberta_prelayernorm.py,sha256=P1sANhnYuIRTKKoMBwuNXvXp7a9JEOpoKMR9TwTYst0,8008
transformers/models/roberta_prelayernorm/convert_roberta_prelayernorm_original_pytorch_checkpoint_to_pytorch.py,sha256=ti9rttSVMs3SemlZrVQFkDKKHBubrk29d4lQkpkI3Ro,2975
transformers/models/roberta_prelayernorm/modeling_flax_roberta_prelayernorm.py,sha256=zMZKU2wl45qTh4ex3R9bf1PUVF12uC5vaVxIXQNqLNk,60537
transformers/models/roberta_prelayernorm/modeling_roberta_prelayernorm.py,sha256=RySHzKhmsWmlOrNE2UfaYOgzvPQ7uLtpVm4YQzXIiDg,74174
transformers/models/roberta_prelayernorm/modeling_tf_roberta_prelayernorm.py,sha256=Cg0sAIp94heuxTIu3ZiIVczjg4Sc0IPC_F5hARotmlg,83542
transformers/models/roc_bert/__init__.py,sha256=ItDlyJx76hWJLT_159wnQgdWC82bT-TG_FpFzjRqXaU,2875
transformers/models/roc_bert/__pycache__/__init__.cpython-311.pyc,,
transformers/models/roc_bert/__pycache__/configuration_roc_bert.cpython-311.pyc,,
transformers/models/roc_bert/__pycache__/modeling_roc_bert.cpython-311.pyc,,
transformers/models/roc_bert/__pycache__/tokenization_roc_bert.cpython-311.pyc,,
transformers/models/roc_bert/configuration_roc_bert.py,sha256=dwGTwo4yj0qgub88AiM2wOs1bpem6FpwRTQYsDUJ7aU,8657
transformers/models/roc_bert/modeling_roc_bert.py,sha256=fVFfM19EKTF9Z85qioSyDrf2ezNHE1uZGfnY4E9EdyY,93052
transformers/models/roc_bert/tokenization_roc_bert.py,sha256=j8h8or4nC3Btd4QGjRcYmTHJ2LgX9ZM9cPhhKGUryw0,51087
transformers/models/roformer/__init__.py,sha256=1EFy2Zdn9AdraO-fmIpYg1q_HLYq-7rT5qDL_8Gurnc,5333
transformers/models/roformer/__pycache__/__init__.cpython-311.pyc,,
transformers/models/roformer/__pycache__/configuration_roformer.cpython-311.pyc,,
transformers/models/roformer/__pycache__/convert_roformer_original_tf_checkpoint_to_pytorch.cpython-311.pyc,,
transformers/models/roformer/__pycache__/modeling_flax_roformer.cpython-311.pyc,,
transformers/models/roformer/__pycache__/modeling_roformer.cpython-311.pyc,,
transformers/models/roformer/__pycache__/modeling_tf_roformer.cpython-311.pyc,,
transformers/models/roformer/__pycache__/tokenization_roformer.cpython-311.pyc,,
transformers/models/roformer/__pycache__/tokenization_roformer_fast.cpython-311.pyc,,
transformers/models/roformer/__pycache__/tokenization_utils.cpython-311.pyc,,
transformers/models/roformer/configuration_roformer.py,sha256=GSh-FCvISaSmxr4Byz03NmfQBDtLGR-gIx4ZoHqlrxM,7733
transformers/models/roformer/convert_roformer_original_tf_checkpoint_to_pytorch.py,sha256=G57qbbWpRH06sm041u6D3BdNE7mCPSDvlaNLOZjWdvY,2240
transformers/models/roformer/modeling_flax_roformer.py,sha256=9kBP35oCuJteX63gvk1HnEgGW7NcxmDHm_HRbEYm3xU,39468
transformers/models/roformer/modeling_roformer.py,sha256=LshlWtUk9Cdr3OhdDSnVvISYRKFu3BoXOmBTF_IqlHQ,69483
transformers/models/roformer/modeling_tf_roformer.py,sha256=sFmTQ0JtenCZoAvAM7XimCTxxsAlY7FD_x58zogQjig,66281
transformers/models/roformer/tokenization_roformer.py,sha256=0NcTJp0JT1uupyHd2d1mQ5nIweekqZX1GqrgNbVN0SA,23836
transformers/models/roformer/tokenization_roformer_fast.py,sha256=bOUghgBthwXFa8taxPPvFkr77M1_SrEBvxKOy9Vk7sA,8521
transformers/models/roformer/tokenization_utils.py,sha256=0ciH13qW2kCa5my1rPwfwAuSXX-jGzN0nzemvGvOBxw,2652
transformers/models/rwkv/__init__.py,sha256=2uUo3Zi2By-3QKG7YkrEqllvFG4_SqJZ-NeplOxHCD4,1780
transformers/models/rwkv/__pycache__/__init__.cpython-311.pyc,,
transformers/models/rwkv/__pycache__/configuration_rwkv.cpython-311.pyc,,
transformers/models/rwkv/__pycache__/convert_rwkv_checkpoint_to_hf.cpython-311.pyc,,
transformers/models/rwkv/__pycache__/modeling_rwkv.cpython-311.pyc,,
transformers/models/rwkv/configuration_rwkv.py,sha256=Dgz84bpJyy9LOJX11yEeGxpgyphlNTwJk4fmWCZY4nU,6210
transformers/models/rwkv/convert_rwkv_checkpoint_to_hf.py,sha256=oXXZN2tt_yWCRAkqpE6-7kDPMy4PyKaYmpMZwsH-IUE,6994
transformers/models/rwkv/modeling_rwkv.py,sha256=mzXWiT7JFzMaMMUvJcAGbKg50VjqngVbYOILhi6Xbh8,38091
transformers/models/sam/__init__.py,sha256=1wiFtdU-_NON6yx4QfFBk4vrfwN4hHv7JEA3CSGq_wU,2980
transformers/models/sam/__pycache__/__init__.cpython-311.pyc,,
transformers/models/sam/__pycache__/configuration_sam.cpython-311.pyc,,
transformers/models/sam/__pycache__/convert_sam_original_to_hf_format.cpython-311.pyc,,
transformers/models/sam/__pycache__/image_processing_sam.cpython-311.pyc,,
transformers/models/sam/__pycache__/modeling_sam.cpython-311.pyc,,
transformers/models/sam/__pycache__/modeling_tf_sam.cpython-311.pyc,,
transformers/models/sam/__pycache__/processing_sam.cpython-311.pyc,,
transformers/models/sam/configuration_sam.py,sha256=_M7Yt6_9JyBx-F4LwmXr8cTH03-j5UDBU-J4XBg3zUU,14112
transformers/models/sam/convert_sam_original_to_hf_format.py,sha256=EPsY1Ne6lziXTjV7kun80W10En23hs5PSC10hiR-dSA,6958
transformers/models/sam/image_processing_sam.py,sha256=cteHlsgIwLCeYt-vZMu7UOLMtsb9jyjcd5xJpe2eCF0,66659
transformers/models/sam/modeling_sam.py,sha256=NtUsptFbkNz-xEzO-Gmx7pSQ3QWcYiuD-nQ-9U6LWWk,64863
transformers/models/sam/modeling_tf_sam.py,sha256=fBv3iW0GvHzSdVqebcGwWXoXeqsZzVSw4YWOoEhlAPw,75652
transformers/models/sam/processing_sam.py,sha256=qPln4ga6UimrOQ-nf7_ATDvn5L7q3xMEG7YQaXmHWjc,10930
transformers/models/seamless_m4t/__init__.py,sha256=PRZMtfk0WN3i0ZSvQbv8wgqp4dOREyIvkgzx5obqn7I,3706
transformers/models/seamless_m4t/__pycache__/__init__.cpython-311.pyc,,
transformers/models/seamless_m4t/__pycache__/configuration_seamless_m4t.cpython-311.pyc,,
transformers/models/seamless_m4t/__pycache__/convert_fairseq2_to_hf.cpython-311.pyc,,
transformers/models/seamless_m4t/__pycache__/feature_extraction_seamless_m4t.cpython-311.pyc,,
transformers/models/seamless_m4t/__pycache__/modeling_seamless_m4t.cpython-311.pyc,,
transformers/models/seamless_m4t/__pycache__/processing_seamless_m4t.cpython-311.pyc,,
transformers/models/seamless_m4t/__pycache__/tokenization_seamless_m4t.cpython-311.pyc,,
transformers/models/seamless_m4t/__pycache__/tokenization_seamless_m4t_fast.cpython-311.pyc,,
transformers/models/seamless_m4t/configuration_seamless_m4t.py,sha256=-EqdAc-LVOiLByHLizgUMhI55uPQ0GbTpuuyRe1rfys,23722
transformers/models/seamless_m4t/convert_fairseq2_to_hf.py,sha256=F2AQrS9rfpktVBSXvFLmND9gMtASSEOMlYPQ6v8VDdU,15960
transformers/models/seamless_m4t/feature_extraction_seamless_m4t.py,sha256=pSStJq6iPGHLWGDiIWN-ZuGBmYSbTkT2ISrFK7Bj7W8,13561
transformers/models/seamless_m4t/modeling_seamless_m4t.py,sha256=bOVOeIZLt7FB2bMgShcZL3uikmJLWQQil3xl78vXhdI,201550
transformers/models/seamless_m4t/processing_seamless_m4t.py,sha256=OrPvDJkAAIuoWglyxt1Z4H993tm-AyX3OxDcu4Gmps0,5893
transformers/models/seamless_m4t/tokenization_seamless_m4t.py,sha256=BeqF6TWlQiVZzdcPwAMRp9XV6lzYNGx10Y025joruLE,26501
transformers/models/seamless_m4t/tokenization_seamless_m4t_fast.py,sha256=XS5nPEft96ZzLUyDZ7eMZ29jKmikPygeH9xUuoIP-iI,20447
transformers/models/seamless_m4t_v2/__init__.py,sha256=eIGJqmaWPYi--eaUhctnu8W9EIihWP-uJsOORWLKVxg,2159
transformers/models/seamless_m4t_v2/__pycache__/__init__.cpython-311.pyc,,
transformers/models/seamless_m4t_v2/__pycache__/configuration_seamless_m4t_v2.cpython-311.pyc,,
transformers/models/seamless_m4t_v2/__pycache__/convert_fairseq2_to_hf.cpython-311.pyc,,
transformers/models/seamless_m4t_v2/__pycache__/modeling_seamless_m4t_v2.cpython-311.pyc,,
transformers/models/seamless_m4t_v2/configuration_seamless_m4t_v2.py,sha256=ydAfQOs0ask-NSWlkoG6SxKoKY9Ak6NsiPR4VxWbKO0,24434
transformers/models/seamless_m4t_v2/convert_fairseq2_to_hf.py,sha256=B3ChRBL4biKHRNsLhAKRsZ547XyxI1uwiywDUC6jKXo,15084
transformers/models/seamless_m4t_v2/modeling_seamless_m4t_v2.py,sha256=ewjV9ZAufGxWveAAs9EbnYdkbqiZjBMM9hCVkY0NrhI,228223
transformers/models/segformer/__init__.py,sha256=T1k_hhB2iCL8zOY3rcG9erX0JbBS--OgU27-G0ZxR2o,3676
transformers/models/segformer/__pycache__/__init__.cpython-311.pyc,,
transformers/models/segformer/__pycache__/configuration_segformer.cpython-311.pyc,,
transformers/models/segformer/__pycache__/convert_segformer_original_to_pytorch.cpython-311.pyc,,
transformers/models/segformer/__pycache__/feature_extraction_segformer.cpython-311.pyc,,
transformers/models/segformer/__pycache__/image_processing_segformer.cpython-311.pyc,,
transformers/models/segformer/__pycache__/modeling_segformer.cpython-311.pyc,,
transformers/models/segformer/__pycache__/modeling_tf_segformer.cpython-311.pyc,,
transformers/models/segformer/configuration_segformer.py,sha256=sUxxiCMIHR-liuMJa2UA4hoodvj0pA8LpF9MJ3Es1wo,7652
transformers/models/segformer/convert_segformer_original_to_pytorch.py,sha256=UXWvoxIi_vor0L5yPuqD7wUuy-vzSNtypQcrpLkTZFc,17092
transformers/models/segformer/feature_extraction_segformer.py,sha256=yaRckmbmTyh1Oow3PnHLsjW4MURaWqddhTzG-PVcywk,1207
transformers/models/segformer/image_processing_segformer.py,sha256=9fUCgubXPXRauPQ91O862eFcB1pLbc79xUtuiQACnIY,22807
transformers/models/segformer/modeling_segformer.py,sha256=VDJhnBNnFyq7oOIZEhKETjfNi6cqfgkppjR6goEw_8k,35490
transformers/models/segformer/modeling_tf_segformer.py,sha256=M6BbF3JhNxMvDjV2YBg1rm76ja_7lgkPDFbh3g1e_Uw,43798
transformers/models/sew/__init__.py,sha256=VG7sYJFBweKB5Cb9lzyRYdjeG0olDM7cIQIUy4XQR8M,1778
transformers/models/sew/__pycache__/__init__.cpython-311.pyc,,
transformers/models/sew/__pycache__/configuration_sew.cpython-311.pyc,,
transformers/models/sew/__pycache__/convert_sew_original_pytorch_checkpoint_to_pytorch.cpython-311.pyc,,
transformers/models/sew/__pycache__/modeling_sew.cpython-311.pyc,,
transformers/models/sew/configuration_sew.py,sha256=RdBcWdW3_c1bT7Wq50RrX2FqrhEvB-wQzfm5vUt-uqw,14390
transformers/models/sew/convert_sew_original_pytorch_checkpoint_to_pytorch.py,sha256=TzlAoTl1DQUm3bhNxDlpXoxe-u1ZcMMbhrQsefGbFog,12745
transformers/models/sew/modeling_sew.py,sha256=Sf8Q_b6Qo-xO3z8rOfVSM5RlLVRjGUKRo98lJQdr_Q8,53440
transformers/models/sew_d/__init__.py,sha256=5d5VSrW-sTwr3H0e2js1KsRL7SM4GPiRPY9Hl_gVjWk,1804
transformers/models/sew_d/__pycache__/__init__.cpython-311.pyc,,
transformers/models/sew_d/__pycache__/configuration_sew_d.cpython-311.pyc,,
transformers/models/sew_d/__pycache__/convert_sew_d_original_pytorch_checkpoint_to_pytorch.cpython-311.pyc,,
transformers/models/sew_d/__pycache__/modeling_sew_d.cpython-311.pyc,,
transformers/models/sew_d/configuration_sew_d.py,sha256=8cvybhCAMW2dYMbZrZyOir64gnQxsnITTunk9SDqoIQ,16568
transformers/models/sew_d/convert_sew_d_original_pytorch_checkpoint_to_pytorch.py,sha256=OeszH3N5vz1FbXoF-d-w6wDJ2A2MxvUMn9uDMpU7bro,13575
transformers/models/sew_d/modeling_sew_d.py,sha256=dINQXBpNR1RIDlcJgaUCngDdGydpicBW6AJj1ssHeVc,74003
transformers/models/siglip/__init__.py,sha256=vuoROawTSIHtXkVVxhysxf-Cx7s3QCEMfvkUsJCxO7M,3124
transformers/models/siglip/__pycache__/__init__.cpython-311.pyc,,
transformers/models/siglip/__pycache__/configuration_siglip.cpython-311.pyc,,
transformers/models/siglip/__pycache__/convert_siglip_to_hf.cpython-311.pyc,,
transformers/models/siglip/__pycache__/image_processing_siglip.cpython-311.pyc,,
transformers/models/siglip/__pycache__/modeling_siglip.cpython-311.pyc,,
transformers/models/siglip/__pycache__/processing_siglip.cpython-311.pyc,,
transformers/models/siglip/__pycache__/tokenization_siglip.cpython-311.pyc,,
transformers/models/siglip/configuration_siglip.py,sha256=JCfPK2zfyzhl65heGLE2-CcQXporBc94jmJaxwYTRgk,13691
transformers/models/siglip/convert_siglip_to_hf.py,sha256=Rg5BhRWVeIKxc9Dz0ZUCjhG3hasNtrORlIcOYoV7xS0,20830
transformers/models/siglip/image_processing_siglip.py,sha256=lPFuoWcRr8ep1TXTTlQIcLWEOGISykPgoSPaKbhiK4Y,11284
transformers/models/siglip/modeling_siglip.py,sha256=hgzCoxpFlD1T2-aT1nLj-_CWKb-eCxuXvxyeUzhDb70,55451
transformers/models/siglip/processing_siglip.py,sha256=kgQsv9ADo6j966hTrugppgV7kq_fnITDONpWp4bvcT0,7408
transformers/models/siglip/tokenization_siglip.py,sha256=VAkDPXalb6cY0QiPcMvjHz0cEGwgAL1i_p5EvTxkFcA,16353
transformers/models/speech_encoder_decoder/__init__.py,sha256=987NzBteEbQy0IYY43B_JKolw2BbyX6Ox9s__xH0daQ,2037
transformers/models/speech_encoder_decoder/__pycache__/__init__.cpython-311.pyc,,
transformers/models/speech_encoder_decoder/__pycache__/configuration_speech_encoder_decoder.cpython-311.pyc,,
transformers/models/speech_encoder_decoder/__pycache__/convert_mbart_wav2vec2_seq2seq_original_to_pytorch.cpython-311.pyc,,
transformers/models/speech_encoder_decoder/__pycache__/convert_speech_to_text_wav2vec2_seq2seq_original_to_pytorch.cpython-311.pyc,,
transformers/models/speech_encoder_decoder/__pycache__/modeling_flax_speech_encoder_decoder.cpython-311.pyc,,
transformers/models/speech_encoder_decoder/__pycache__/modeling_speech_encoder_decoder.cpython-311.pyc,,
transformers/models/speech_encoder_decoder/configuration_speech_encoder_decoder.py,sha256=7hzCE73LcHbiq3b4pTsMdSwjtl4izOtoZE-ldVs8Bx4,4575
transformers/models/speech_encoder_decoder/convert_mbart_wav2vec2_seq2seq_original_to_pytorch.py,sha256=EtCwDPHsete4dhXGu8OwkbRx7-47vbHRKUrb8j-6M2c,14754
transformers/models/speech_encoder_decoder/convert_speech_to_text_wav2vec2_seq2seq_original_to_pytorch.py,sha256=04swyKsxEHHieCLUFPKzubV4W0ES1mZtbkgv-UDt7po,11971
transformers/models/speech_encoder_decoder/modeling_flax_speech_encoder_decoder.py,sha256=i8GFLLxYQSh2uj6IAZNkGglUOt5C3VbSNvevYsoqSOs,44643
transformers/models/speech_encoder_decoder/modeling_speech_encoder_decoder.py,sha256=U064X5_0R8t-uuU6z1S3025DqGhgRF7wz3Rg4cg7Kx4,32266
transformers/models/speech_to_text/__init__.py,sha256=y2bX48UezdcJd_0EyTBq6xLWHL0vup-noE235__AYw8,3491
transformers/models/speech_to_text/__pycache__/__init__.cpython-311.pyc,,
transformers/models/speech_to_text/__pycache__/configuration_speech_to_text.cpython-311.pyc,,
transformers/models/speech_to_text/__pycache__/convert_s2t_fairseq_to_tfms.cpython-311.pyc,,
transformers/models/speech_to_text/__pycache__/feature_extraction_speech_to_text.cpython-311.pyc,,
transformers/models/speech_to_text/__pycache__/modeling_speech_to_text.cpython-311.pyc,,
transformers/models/speech_to_text/__pycache__/modeling_tf_speech_to_text.cpython-311.pyc,,
transformers/models/speech_to_text/__pycache__/processing_speech_to_text.cpython-311.pyc,,
transformers/models/speech_to_text/__pycache__/tokenization_speech_to_text.cpython-311.pyc,,
transformers/models/speech_to_text/configuration_speech_to_text.py,sha256=xrvtRmjXjdFz8YK4wOAS1jbX3SClEQATFkR49qd6j8A,10060
transformers/models/speech_to_text/convert_s2t_fairseq_to_tfms.py,sha256=v-5aSPwuCKCtqwU8gREj9wA2nm14Z97tg6wQ3S47gos,4478
transformers/models/speech_to_text/feature_extraction_speech_to_text.py,sha256=bW4mXxoo1FKXFhfvstyPbWm8fMRMN1G7KXwkGN-vdxw,13176
transformers/models/speech_to_text/modeling_speech_to_text.py,sha256=Rme29l5q062YUqzj_VtkSEqw9FSGTZrTJzge76Vu-OU,64582
transformers/models/speech_to_text/modeling_tf_speech_to_text.py,sha256=F7rGFFj_rU7R8U0VAFimOxgD52zDZKooa4-FdjDeays,74500
transformers/models/speech_to_text/processing_speech_to_text.py,sha256=dtDsYvPg-jn-O5iiVDPH5154wOEDglsODuF4dPn7XYc,4818
transformers/models/speech_to_text/tokenization_speech_to_text.py,sha256=xK37kRPN-deOCb_OpTPiyfLC701ykicTdkO-ndRhtbs,11917
transformers/models/speech_to_text_2/__init__.py,sha256=zkmS9-WZTXByVUJqkt094wHCOT4zyVLO4Rn3B0JBCSo,2166
transformers/models/speech_to_text_2/__pycache__/__init__.cpython-311.pyc,,
transformers/models/speech_to_text_2/__pycache__/configuration_speech_to_text_2.cpython-311.pyc,,
transformers/models/speech_to_text_2/__pycache__/modeling_speech_to_text_2.cpython-311.pyc,,
transformers/models/speech_to_text_2/__pycache__/processing_speech_to_text_2.cpython-311.pyc,,
transformers/models/speech_to_text_2/__pycache__/tokenization_speech_to_text_2.cpython-311.pyc,,
transformers/models/speech_to_text_2/configuration_speech_to_text_2.py,sha256=Jkq7CP0ecvv-zqtIcXedbTQs6YgGpKgz5LWtcqZM1Mw,6282
transformers/models/speech_to_text_2/modeling_speech_to_text_2.py,sha256=LroB-3saZ3F33_8zKenztHYUI8OU_7kWgNWwxDO-rf0,44296
transformers/models/speech_to_text_2/processing_speech_to_text_2.py,sha256=J3Uv4HX7Y5zndYa3ZIROcEuLEfrw2piJC53AZmSkGnY,4790
transformers/models/speech_to_text_2/tokenization_speech_to_text_2.py,sha256=VmKsOBo7vpQYoMystmIeLzBAT6yLtNuioOM0g0hR7Rc,9211
transformers/models/speecht5/__init__.py,sha256=rI6eMJ1n9U8Mtn17i83U2qOhvcOQJudmFYU9roGYUno,2971
transformers/models/speecht5/__pycache__/__init__.cpython-311.pyc,,
transformers/models/speecht5/__pycache__/configuration_speecht5.cpython-311.pyc,,
transformers/models/speecht5/__pycache__/convert_hifigan.cpython-311.pyc,,
transformers/models/speecht5/__pycache__/convert_speecht5_original_pytorch_checkpoint_to_pytorch.cpython-311.pyc,,
transformers/models/speecht5/__pycache__/feature_extraction_speecht5.cpython-311.pyc,,
transformers/models/speecht5/__pycache__/modeling_speecht5.cpython-311.pyc,,
transformers/models/speecht5/__pycache__/number_normalizer.cpython-311.pyc,,
transformers/models/speecht5/__pycache__/processing_speecht5.cpython-311.pyc,,
transformers/models/speecht5/__pycache__/tokenization_speecht5.cpython-311.pyc,,
transformers/models/speecht5/configuration_speecht5.py,sha256=bLoe6LIlF1eD8vLKUGecwqcJrH3ZJqW8KfgjoqrpjJs,23901
transformers/models/speecht5/convert_hifigan.py,sha256=CL9GSX_bimjm_hU2rE55MaNvTUjTtWD6qCtqNMaXy7I,4241
transformers/models/speecht5/convert_speecht5_original_pytorch_checkpoint_to_pytorch.py,sha256=AyAjaeibe3002YZRT2maq1Yi8-iP1j7Ahs5qxYMjiJ0,17194
transformers/models/speecht5/feature_extraction_speecht5.py,sha256=lcKx3NaIXx0PGITRKP0kA8SZK75kd1Sn8PNHLBn-ST0,17809
transformers/models/speecht5/modeling_speecht5.py,sha256=E4KUQUhPMs9UOFkXAR9nUmROD8Rj6vsnMQ7OlwCASXg,153484
transformers/models/speecht5/number_normalizer.py,sha256=cxnEUdHSISW5eAo15cLuVkZa65zMFuMFaJ8zAOQCsAA,7019
transformers/models/speecht5/processing_speecht5.py,sha256=smqFdqKJQp9Vm1FDfmj7EvJeAZKSPB6u2AZMfsjsQa0,7562
transformers/models/speecht5/tokenization_speecht5.py,sha256=BY35C_v1iBcQncMDYh8YpZxB0xacCTtKOZBuyBrCxJc,9584
transformers/models/splinter/__init__.py,sha256=vo990AmnOkGy7xWuzB4qaAfJNrtFFLOImR4mlSl_jJ8,2532
transformers/models/splinter/__pycache__/__init__.cpython-311.pyc,,
transformers/models/splinter/__pycache__/configuration_splinter.cpython-311.pyc,,
transformers/models/splinter/__pycache__/modeling_splinter.cpython-311.pyc,,
transformers/models/splinter/__pycache__/tokenization_splinter.cpython-311.pyc,,
transformers/models/splinter/__pycache__/tokenization_splinter_fast.cpython-311.pyc,,
transformers/models/splinter/configuration_splinter.py,sha256=mCebEZ3EZq86Z7kLWVMgXGc8sslehGUb0O0jSPx2QcM,6120
transformers/models/splinter/modeling_splinter.py,sha256=2aHtIIxMRCNAMTl3_mi88gQzJKMtN6XAe8uoHDKkkyg,53386
transformers/models/splinter/tokenization_splinter.py,sha256=qM9mEO_nF6YL_ZqTTR-8W_h9-Cxd6Oau-V1bG8KPU8k,22012
transformers/models/splinter/tokenization_splinter_fast.py,sha256=vlc_KIuy5sNJt2XrY8pavBIlkQaiGeAm46dJyEnpWOo,9657
transformers/models/squeezebert/__init__.py,sha256=G8bhLM5DmRO6oIXmZT-W71i8hZK9589XpyLuwIs6W3M,2996
transformers/models/squeezebert/__pycache__/__init__.cpython-311.pyc,,
transformers/models/squeezebert/__pycache__/configuration_squeezebert.cpython-311.pyc,,
transformers/models/squeezebert/__pycache__/modeling_squeezebert.cpython-311.pyc,,
transformers/models/squeezebert/__pycache__/tokenization_squeezebert.cpython-311.pyc,,
transformers/models/squeezebert/__pycache__/tokenization_squeezebert_fast.cpython-311.pyc,,
transformers/models/squeezebert/configuration_squeezebert.py,sha256=CNa0VlJQKxKeYzp_gsqwdZtudk71DgXMmOCIl_IC-8w,7911
transformers/models/squeezebert/modeling_squeezebert.py,sha256=xaC-OqwERTEnmSMLf7xBer38w4mKlx0hNnaR4D26RB4,45093
transformers/models/squeezebert/tokenization_squeezebert.py,sha256=OfEklLH2QAUiJlypidV5_4zT49yfm4BhCtMTKkn7jgM,21986
transformers/models/squeezebert/tokenization_squeezebert_fast.py,sha256=-BZskQnhqHHvqpeRFKpyx3H3isZzh36YLJff2pTaGPg,9409
transformers/models/stablelm/__init__.py,sha256=DfGQ8YT2zSeiNRGOhIhypn-IFNOkXmqIt4BHzq8KnSU,1824
transformers/models/stablelm/__pycache__/__init__.cpython-311.pyc,,
transformers/models/stablelm/__pycache__/configuration_stablelm.cpython-311.pyc,,
transformers/models/stablelm/__pycache__/modeling_stablelm.cpython-311.pyc,,
transformers/models/stablelm/configuration_stablelm.py,sha256=WWILbXAomdCU51crUYo4WxdC9Ag1i7GX8Liz_JNCW3M,9050
transformers/models/stablelm/modeling_stablelm.py,sha256=xCdUvOjgNTa29sS3eR-Sg5tAMkUapyKxDI-zDiysWGA,57762
transformers/models/swiftformer/__init__.py,sha256=y3EVx2oOV5GldnIhqN1uK316Lf68wv3IsTE4HGd2DSc,1990
transformers/models/swiftformer/__pycache__/__init__.cpython-311.pyc,,
transformers/models/swiftformer/__pycache__/configuration_swiftformer.cpython-311.pyc,,
transformers/models/swiftformer/__pycache__/convert_swiftformer_original_to_hf.cpython-311.pyc,,
transformers/models/swiftformer/__pycache__/modeling_swiftformer.cpython-311.pyc,,
transformers/models/swiftformer/configuration_swiftformer.py,sha256=-zmyD74MBLjuePvzdP3jIBnXER5koT9E77CzVvkdY1U,5351
transformers/models/swiftformer/convert_swiftformer_original_to_hf.py,sha256=HsppMeVG__p-Z4sCLcGLnDhXP-AFe6ewWiifyEFL-xA,6239
transformers/models/swiftformer/modeling_swiftformer.py,sha256=ITQ6yNKAqLtXKoLPQHrcfKyvJSiw359D6HdDlLBxEFk,23150
transformers/models/swin/__init__.py,sha256=lsSSO-igADN2rI7RV55GBIB-GG8mRQNnsT9A6J8IFtk,2703
transformers/models/swin/__pycache__/__init__.cpython-311.pyc,,
transformers/models/swin/__pycache__/configuration_swin.cpython-311.pyc,,
transformers/models/swin/__pycache__/convert_swin_simmim_to_pytorch.cpython-311.pyc,,
transformers/models/swin/__pycache__/convert_swin_timm_to_pytorch.cpython-311.pyc,,
transformers/models/swin/__pycache__/modeling_swin.cpython-311.pyc,,
transformers/models/swin/__pycache__/modeling_tf_swin.cpython-311.pyc,,
transformers/models/swin/configuration_swin.py,sha256=rL3oMIVT6bUuQDGjkvVJ6ZA5YAf1T3KBISIfF816YUA,8170
transformers/models/swin/convert_swin_simmim_to_pytorch.py,sha256=Zb67GMulOozvN1L66EmQ9gKtLVUmyaWYgq_zPPdbGKs,6627
transformers/models/swin/convert_swin_timm_to_pytorch.py,sha256=WKAiiEOxnv4_yjbLVsU9M50iwE_x0QEvbXrMZK1W_7Q,5805
transformers/models/swin/modeling_swin.py,sha256=q85pb0n4aAD89Cvh9hwikvDx-w7UGyC4BXdhukWUkj4,60153
transformers/models/swin/modeling_tf_swin.py,sha256=P5vcJibOQ_vB3qdd0IWKZywLkV0Cug1DjSDbLhx-HNg,70837
transformers/models/swin2sr/__init__.py,sha256=Nx5kG4ltMIhcqaGLYh7VYoju_qViNNYZGdGE0p-rz_4,2277
transformers/models/swin2sr/__pycache__/__init__.cpython-311.pyc,,
transformers/models/swin2sr/__pycache__/configuration_swin2sr.cpython-311.pyc,,
transformers/models/swin2sr/__pycache__/convert_swin2sr_original_to_pytorch.cpython-311.pyc,,
transformers/models/swin2sr/__pycache__/image_processing_swin2sr.cpython-311.pyc,,
transformers/models/swin2sr/__pycache__/modeling_swin2sr.cpython-311.pyc,,
transformers/models/swin2sr/configuration_swin2sr.py,sha256=akg8KGYVj4-YUxCzT6ESaM2gUjd4AvqqwI7p4rdKFT8,6997
transformers/models/swin2sr/convert_swin2sr_original_to_pytorch.py,sha256=eZ1q75t9Na8iF_KkMXK9hHb0O0KyX9Bv1JhO3r94ZLA,11355
transformers/models/swin2sr/image_processing_swin2sr.py,sha256=oOZZNkOyijP1_5aha5bwGlHFb4NEou-MTqy7anqE85U,9155
transformers/models/swin2sr/modeling_swin2sr.py,sha256=DczdHjClU1MpUoJBTLIAjY8BBee9y7dZtorSU__vlHE,50769
transformers/models/swinv2/__init__.py,sha256=wYBHIbUFdjRY2cLLBWgHOOvE1ZNk6UD6Hj2qYYR2i5Q,1921
transformers/models/swinv2/__pycache__/__init__.cpython-311.pyc,,
transformers/models/swinv2/__pycache__/configuration_swinv2.cpython-311.pyc,,
transformers/models/swinv2/__pycache__/convert_swinv2_timm_to_pytorch.cpython-311.pyc,,
transformers/models/swinv2/__pycache__/modeling_swinv2.cpython-311.pyc,,
transformers/models/swinv2/configuration_swinv2.py,sha256=bocazmZ2T9k6wVihqigPLJUEVIsseh6mKVXTjZRQDq4,7719
transformers/models/swinv2/convert_swinv2_timm_to_pytorch.py,sha256=OMyAAcVPs9DTojiHQCvLo7uTtaChsd1ANTY4IkS7iUY,7687
transformers/models/swinv2/modeling_swinv2.py,sha256=RCD9wdQkZFiaahZttY-h91e-SCM1VNdw3SHAeeufkyQ,63880
transformers/models/switch_transformers/__init__.py,sha256=71GlCMK0XfSUSoxmTxWjj-vmLJImHjlJjtUWkptdalA,2484
transformers/models/switch_transformers/__pycache__/__init__.cpython-311.pyc,,
transformers/models/switch_transformers/__pycache__/configuration_switch_transformers.cpython-311.pyc,,
transformers/models/switch_transformers/__pycache__/convert_big_switch.cpython-311.pyc,,
transformers/models/switch_transformers/__pycache__/convert_switch_transformers_original_flax_checkpoint_to_pytorch.cpython-311.pyc,,
transformers/models/switch_transformers/__pycache__/modeling_switch_transformers.cpython-311.pyc,,
transformers/models/switch_transformers/configuration_switch_transformers.py,sha256=i3RsWJnAYBv-Cy-Gp10Id6XvO2U3IrHyjjfmGD7ZhVk,9159
transformers/models/switch_transformers/convert_big_switch.py,sha256=wjMGjHXAqVool6fZQhdG_Av2Ujx9EDoZrtHC8RdDLk4,7659
transformers/models/switch_transformers/convert_switch_transformers_original_flax_checkpoint_to_pytorch.py,sha256=AAJNkPcr_THjPN_8RUnOdBYbbYc6GOqXdgdjhx9FZyw,7593
transformers/models/switch_transformers/modeling_switch_transformers.py,sha256=25YbAmkrBqhDNH7Gfzy1laclhrVUxBTA6LuFF2A71LQ,87972
transformers/models/t5/__init__.py,sha256=-WUyKPr21y-Gi15sZ8aW3vmykCW8tu5qZ6yKmOcOHso,4492
transformers/models/t5/__pycache__/__init__.cpython-311.pyc,,
transformers/models/t5/__pycache__/configuration_t5.cpython-311.pyc,,
transformers/models/t5/__pycache__/convert_t5_original_tf_checkpoint_to_pytorch.cpython-311.pyc,,
transformers/models/t5/__pycache__/convert_t5x_checkpoint_to_flax.cpython-311.pyc,,
transformers/models/t5/__pycache__/convert_t5x_checkpoint_to_pytorch.cpython-311.pyc,,
transformers/models/t5/__pycache__/modeling_flax_t5.cpython-311.pyc,,
transformers/models/t5/__pycache__/modeling_t5.cpython-311.pyc,,
transformers/models/t5/__pycache__/modeling_tf_t5.cpython-311.pyc,,
transformers/models/t5/__pycache__/tokenization_t5.cpython-311.pyc,,
transformers/models/t5/__pycache__/tokenization_t5_fast.cpython-311.pyc,,
transformers/models/t5/configuration_t5.py,sha256=2mz8gIWDzMN-VS6fey-OPdA_CVbJbr0J6tUXFsOzlhY,7780
transformers/models/t5/convert_t5_original_tf_checkpoint_to_pytorch.py,sha256=83tKCwYRSRW7zXtm9cmszqtPhpw44cH8Cj0SWUSBgN0,2120
transformers/models/t5/convert_t5x_checkpoint_to_flax.py,sha256=CET5s9wlNOt-VxT9eu-NOMdNS22kX6mhEZQ-ox2mLK0,10538
transformers/models/t5/convert_t5x_checkpoint_to_pytorch.py,sha256=GTF0FYHDDDBl2tcYgHcirqHOI2KOE2YkDG4ekzjh_Ao,10483
transformers/models/t5/modeling_flax_t5.py,sha256=QhELmI-3YNpbMz75xqrUxTLCrPgYowKh0pJVaiJvDCo,74166
transformers/models/t5/modeling_t5.py,sha256=Sc1a6Ls4xLx41f37KdO7Lw5mUQqVQAsDlcAIm5L_so4,108793
transformers/models/t5/modeling_tf_t5.py,sha256=6ZUWrk5N_dMJ_B0gUw81zQeo8J7wMSWylzcZeiVoa0A,77313
transformers/models/t5/tokenization_t5.py,sha256=eRjkTKSZjEvhIhH4GYA9Y1ZrNlzzsybmeNwADPiY7o4,20956
transformers/models/t5/tokenization_t5_fast.py,sha256=7TZhbGLWBn7Uzm1Tihu1HP4rE1lLlqhfuYWGBdh9Ck4,11521
transformers/models/table_transformer/__init__.py,sha256=WHdzgCB7BwXZeZveOSQ2fBQKNsrsRmpdP1f5C2MfYn4,2065
transformers/models/table_transformer/__pycache__/__init__.cpython-311.pyc,,
transformers/models/table_transformer/__pycache__/configuration_table_transformer.cpython-311.pyc,,
transformers/models/table_transformer/__pycache__/convert_table_transformer_to_hf.cpython-311.pyc,,
transformers/models/table_transformer/__pycache__/convert_table_transformer_to_hf_no_timm.cpython-311.pyc,,
transformers/models/table_transformer/__pycache__/modeling_table_transformer.cpython-311.pyc,,
transformers/models/table_transformer/configuration_table_transformer.py,sha256=5VQ49gg3Wc6KE34wDdy_Otj2_tSFtPa2BTMR5pxKUIQ,13441
transformers/models/table_transformer/convert_table_transformer_to_hf.py,sha256=ItWZNI8n3yj-0fP-kbly0kq8yrb7Bc5Nz2HeInHnPdA,15095
transformers/models/table_transformer/convert_table_transformer_to_hf_no_timm.py,sha256=IJWfYRPya5zeVUqynktWlkiD7seeQdyU4kagQFXV4pU,21186
transformers/models/table_transformer/modeling_table_transformer.py,sha256=9gG-122RLbsQTWI0XQ7q-c9bA0BNfUJDHfW6RibZYf8,95336
transformers/models/tapas/__init__.py,sha256=uGhdu01xgzBDD5edwGpuFl94A2WmFd6FA_U2YWJZReA,2952
transformers/models/tapas/__pycache__/__init__.cpython-311.pyc,,
transformers/models/tapas/__pycache__/configuration_tapas.cpython-311.pyc,,
transformers/models/tapas/__pycache__/convert_tapas_original_tf_checkpoint_to_pytorch.cpython-311.pyc,,
transformers/models/tapas/__pycache__/modeling_tapas.cpython-311.pyc,,
transformers/models/tapas/__pycache__/modeling_tf_tapas.cpython-311.pyc,,
transformers/models/tapas/__pycache__/tokenization_tapas.cpython-311.pyc,,
transformers/models/tapas/configuration_tapas.py,sha256=75LYt8VkU1jQ0M3bPrMpeeVtcJilgq4xkdEGqpEIRlY,12900
transformers/models/tapas/convert_tapas_original_tf_checkpoint_to_pytorch.py,sha256=OeIyLEtDJr1z2BEKH0bJNJOR5ZrxRyGM8RpMSC3TgHQ,5049
transformers/models/tapas/modeling_tapas.py,sha256=PtmNPiVnDsTscbjdC2KmjdjrolmJjlB3yY8HxoEb_Ts,111492
transformers/models/tapas/modeling_tf_tapas.py,sha256=nraYivFAaXrdsu9vmN7ZAn_AlFY06ggaH9TB7ShJ_FU,113210
transformers/models/tapas/tokenization_tapas.py,sha256=bhXfKL0YBn3yGr_PeangJkbHXWtPLlhesEAfbN4Fz7s,121382
transformers/models/time_series_transformer/__init__.py,sha256=dtXXYFY750gxXLggZYQWy2iaq88scX8TYl021UEZHVs,2069
transformers/models/time_series_transformer/__pycache__/__init__.cpython-311.pyc,,
transformers/models/time_series_transformer/__pycache__/configuration_time_series_transformer.cpython-311.pyc,,
transformers/models/time_series_transformer/__pycache__/modeling_time_series_transformer.cpython-311.pyc,,
transformers/models/time_series_transformer/configuration_time_series_transformer.py,sha256=jksabDQIia7ZpYFueQFynq5peBWyc80p1QvX6RbEbBE,12004
transformers/models/time_series_transformer/modeling_time_series_transformer.py,sha256=4oCMTx2NXutEL9LDGsZMoM_OL_CdmIhqnHZT69gFCnk,88782
transformers/models/timesformer/__init__.py,sha256=eugQ_QcHxuxaGByRRLWyZZ_0ic66Mcz5qdwW_Qt-Nyg,1862
transformers/models/timesformer/__pycache__/__init__.cpython-311.pyc,,
transformers/models/timesformer/__pycache__/configuration_timesformer.cpython-311.pyc,,
transformers/models/timesformer/__pycache__/convert_timesformer_to_pytorch.cpython-311.pyc,,
transformers/models/timesformer/__pycache__/modeling_timesformer.cpython-311.pyc,,
transformers/models/timesformer/configuration_timesformer.py,sha256=AtGzMATKCDXRfQKKe1mtukp9ON82HzK4IAOO2sjpHsc,5684
transformers/models/timesformer/convert_timesformer_to_pytorch.py,sha256=TjOfPbEC4oVb5tlOgU2m9g36OBizDEEjm0bbcZz6Mq8,10176
transformers/models/timesformer/modeling_timesformer.py,sha256=ZccPRtMMHQbwfAtNJuQYkrEzybtsGlRlnqd_twzOe7E,35332
transformers/models/timm_backbone/__init__.py,sha256=rn9y1wXicP1g6IiI_tSWu7fnt5q_x6hfu3g9yQvovEU,1624
transformers/models/timm_backbone/__pycache__/__init__.cpython-311.pyc,,
transformers/models/timm_backbone/__pycache__/configuration_timm_backbone.cpython-311.pyc,,
transformers/models/timm_backbone/__pycache__/modeling_timm_backbone.cpython-311.pyc,,
transformers/models/timm_backbone/configuration_timm_backbone.py,sha256=PR-F13KbCSBdKgA8ASNh-gok8TLUFY1_7ke32AaasmA,3153
transformers/models/timm_backbone/modeling_timm_backbone.py,sha256=AXDH5tWEWZYY7mTOWCwsiEvoImk-NdXBLw-EUEMqH4M,6614
transformers/models/trocr/__init__.py,sha256=jevvndvNkGFaA2smYGtlhOnpGG5U6gIhmuwONgXNyeM,1818
transformers/models/trocr/__pycache__/__init__.cpython-311.pyc,,
transformers/models/trocr/__pycache__/configuration_trocr.cpython-311.pyc,,
transformers/models/trocr/__pycache__/convert_trocr_unilm_to_pytorch.cpython-311.pyc,,
transformers/models/trocr/__pycache__/modeling_trocr.cpython-311.pyc,,
transformers/models/trocr/__pycache__/processing_trocr.cpython-311.pyc,,
transformers/models/trocr/configuration_trocr.py,sha256=6fOvj0yFGg0uZ3ueJru-_8t3CNDfnmGFD-mJyX2RFUc,6779
transformers/models/trocr/convert_trocr_unilm_to_pytorch.py,sha256=7I6jyQ1hl9k_fweOgeMgKypDSSf4zL-7tjIoY09sprk,10166
transformers/models/trocr/modeling_trocr.py,sha256=PGyu8e8KsBtk_13WxA59X7UElhRPmiqIfepDwMO1deE,45437
transformers/models/trocr/processing_trocr.py,sha256=-iyJv7DCOlG-iKtKhtKmgbQKyU4eGydKGJDeLmBFML4,5745
transformers/models/tvlt/__init__.py,sha256=3hHJeODpJMJ9_06AAz0fAV7QCRljLoJcfXc69YypO9M,2687
transformers/models/tvlt/__pycache__/__init__.cpython-311.pyc,,
transformers/models/tvlt/__pycache__/configuration_tvlt.cpython-311.pyc,,
transformers/models/tvlt/__pycache__/feature_extraction_tvlt.cpython-311.pyc,,
transformers/models/tvlt/__pycache__/image_processing_tvlt.cpython-311.pyc,,
transformers/models/tvlt/__pycache__/modeling_tvlt.cpython-311.pyc,,
transformers/models/tvlt/__pycache__/processing_tvlt.cpython-311.pyc,,
transformers/models/tvlt/configuration_tvlt.py,sha256=znMgnkxk8DpZj68SUs-NHd6zRHUJwSZT5UUPuT2Wro4,8761
transformers/models/tvlt/feature_extraction_tvlt.py,sha256=peyeHHDn8S6X6bQIf3rWs4fWwPYSjabGC0f106x35W4,10555
transformers/models/tvlt/image_processing_tvlt.py,sha256=GtQd0K-gVQn8WMb2MiUlA6-bA6x71Jkugbe588Uzp4A,19463
transformers/models/tvlt/modeling_tvlt.py,sha256=-JyA9klp_uGmoeOzHINB76dz0fbXmycpG44yeuVgudY,57418
transformers/models/tvlt/processing_tvlt.py,sha256=JaLjfV68tRz-Ts55YzccFCltQO4yZDTNW6DAreychSQ,3506
transformers/models/tvp/__init__.py,sha256=nMCJ05vKe35hpbNHygmLeBkYUXDH2ZZLB5U5Ij0DG6A,2366
transformers/models/tvp/__pycache__/__init__.cpython-311.pyc,,
transformers/models/tvp/__pycache__/configuration_tvp.cpython-311.pyc,,
transformers/models/tvp/__pycache__/image_processing_tvp.cpython-311.pyc,,
transformers/models/tvp/__pycache__/modeling_tvp.cpython-311.pyc,,
transformers/models/tvp/__pycache__/processing_tvp.cpython-311.pyc,,
transformers/models/tvp/configuration_tvp.py,sha256=XCwParHrM16sqYANJO5pa12m8vz5P0O841-P413NuaE,10142
transformers/models/tvp/image_processing_tvp.py,sha256=0tQ-M-FM2VxxzrybhfFIr8jALTgXqcCKlbAf6EABa4Y,22494
transformers/models/tvp/modeling_tvp.py,sha256=dR8H_16AEEPqMuaKqZElguQ1pMZS5_nUMu-j8lCkNEg,38841
transformers/models/tvp/processing_tvp.py,sha256=6fJAgekPIOw95GpQ7b1_y76KGbC03upX9uH8XlbGdKE,6981
transformers/models/umt5/__init__.py,sha256=wcKbkdS_suuZCQs52Oz0lBegIa0QDSPZW2Q-XBpM3ns,1908
transformers/models/umt5/__pycache__/__init__.cpython-311.pyc,,
transformers/models/umt5/__pycache__/configuration_umt5.cpython-311.pyc,,
transformers/models/umt5/__pycache__/convert_umt5_checkpoint_to_pytorch.cpython-311.pyc,,
transformers/models/umt5/__pycache__/modeling_umt5.cpython-311.pyc,,
transformers/models/umt5/configuration_umt5.py,sha256=bMogaUqZD5LuDEy7bhBoO_1CoWJpKqDsY25XExO0Nmc,7843
transformers/models/umt5/convert_umt5_checkpoint_to_pytorch.py,sha256=mKcFjDTUYzC4S2faD9UMTQTIl5nwGbOp4QkcFxEEdv8,12070
transformers/models/umt5/modeling_umt5.py,sha256=UZ7AFCi3sYo1ilhRVPADZ7R_RqxhW1R3xhg0akfbiII,86424
transformers/models/unispeech/__init__.py,sha256=n4jtlc-pPF37uUx7mgB1GDnL2lQ-eKDI8xOLVVp840E,2018
transformers/models/unispeech/__pycache__/__init__.cpython-311.pyc,,
transformers/models/unispeech/__pycache__/configuration_unispeech.cpython-311.pyc,,
transformers/models/unispeech/__pycache__/convert_unispeech_original_pytorch_checkpoint_to_pytorch.cpython-311.pyc,,
transformers/models/unispeech/__pycache__/modeling_unispeech.cpython-311.pyc,,
transformers/models/unispeech/configuration_unispeech.py,sha256=uFGEUbMMrIod4xOe8wTQo9fsF3jGrEoVwDu27I57ppc,17727
transformers/models/unispeech/convert_unispeech_original_pytorch_checkpoint_to_pytorch.py,sha256=bwfIAusfhFih5WJEIIokApShfuYhJoirPltvRz2-T7Y,11340
transformers/models/unispeech/modeling_unispeech.py,sha256=KfasIq2y3gY-uOhnXTmRO0tnkgWSf3yTV1WHnQZQeM8,72707
transformers/models/unispeech_sat/__init__.py,sha256=gAf8t9qZaufCDyIyJICzCQTvrmV825BDZUKQoa08DhE,2267
transformers/models/unispeech_sat/__pycache__/__init__.cpython-311.pyc,,
transformers/models/unispeech_sat/__pycache__/configuration_unispeech_sat.cpython-311.pyc,,
transformers/models/unispeech_sat/__pycache__/convert_unispeech_original_s3prl_checkpoint_to_pytorch.cpython-311.pyc,,
transformers/models/unispeech_sat/__pycache__/convert_unispeech_sat_original_pytorch_checkpoint_to_pytorch.cpython-311.pyc,,
transformers/models/unispeech_sat/__pycache__/modeling_unispeech_sat.cpython-311.pyc,,
transformers/models/unispeech_sat/configuration_unispeech_sat.py,sha256=GPqX-zfRB3bW13L7P04Cw56oebIh86b26Yzu0_JnCv4,19096
transformers/models/unispeech_sat/convert_unispeech_original_s3prl_checkpoint_to_pytorch.py,sha256=CnSYjNr7S7Mqa7Feosf9Dx7eQTYScVHG-QprNkY8uLk,4870
transformers/models/unispeech_sat/convert_unispeech_sat_original_pytorch_checkpoint_to_pytorch.py,sha256=NK_vA71Eq2q9P1x3ol-2Jlqjkv-Mi3NlXO9Ra7QUQsQ,9289
transformers/models/unispeech_sat/modeling_unispeech_sat.py,sha256=M-wjjRn0TtS5vRfR4h-fpCZInxx3Z0MXTBCiUohyaZE,86766
transformers/models/univnet/__init__.py,sha256=aeEydP4QFet-MOxxwOZMKE-jGUG1spoCfXwMmESP27Y,1842
transformers/models/univnet/__pycache__/__init__.cpython-311.pyc,,
transformers/models/univnet/__pycache__/configuration_univnet.cpython-311.pyc,,
transformers/models/univnet/__pycache__/convert_univnet.cpython-311.pyc,,
transformers/models/univnet/__pycache__/feature_extraction_univnet.cpython-311.pyc,,
transformers/models/univnet/__pycache__/modeling_univnet.cpython-311.pyc,,
transformers/models/univnet/configuration_univnet.py,sha256=3KkI7VWfneomecD4yrFgRCZ7ZFoFpszKzfDn8YSio-M,6869
transformers/models/univnet/convert_univnet.py,sha256=R2gqXfz8Oq2rwIUU01V7T_oSoDGG2A4Gety-R80Yn24,6364
transformers/models/univnet/feature_extraction_univnet.py,sha256=snAVdQ5ClFX_Sw7upgvWyzJq4bUNRelRQaxcWxgHIgA,22821
transformers/models/univnet/modeling_univnet.py,sha256=AiQYeDhzFqIvdigMWUrQme-31U7ucoBUhVrGYz7W5IE,26922
transformers/models/upernet/__init__.py,sha256=z2avy6tP_WpANiGPA5RCxT_9yPp0PfEDlfUjL9rQsXM,1535
transformers/models/upernet/__pycache__/__init__.cpython-311.pyc,,
transformers/models/upernet/__pycache__/configuration_upernet.cpython-311.pyc,,
transformers/models/upernet/__pycache__/convert_convnext_upernet_to_pytorch.cpython-311.pyc,,
transformers/models/upernet/__pycache__/convert_swin_upernet_to_pytorch.cpython-311.pyc,,
transformers/models/upernet/__pycache__/modeling_upernet.cpython-311.pyc,,
transformers/models/upernet/configuration_upernet.py,sha256=SoforpobnR_iSTAHHWAOON_zUZ8F5674SqjDMVyy2Ts,6719
transformers/models/upernet/convert_convnext_upernet_to_pytorch.py,sha256=l_CJoXwANEE9rm5mwpHwbusIoJLmN8jNGjxsj6WhZrk,10271
transformers/models/upernet/convert_swin_upernet_to_pytorch.py,sha256=lHV8SE_bZnxOo-zEJ21S2nY449uPVc3bpcl2JGKNEjA,14026
transformers/models/upernet/modeling_upernet.py,sha256=lDt7NX67F6bq2fP5ldTUzBxYAjo1xAMUhEJPVsJx8rM,17297
transformers/models/videomae/__init__.py,sha256=Yrv0_yOkvyL6slti-bw1oFR8t8VO8-6b40yF0Lf2uV4,2519
transformers/models/videomae/__pycache__/__init__.cpython-311.pyc,,
transformers/models/videomae/__pycache__/configuration_videomae.cpython-311.pyc,,
transformers/models/videomae/__pycache__/convert_videomae_to_pytorch.cpython-311.pyc,,
transformers/models/videomae/__pycache__/feature_extraction_videomae.cpython-311.pyc,,
transformers/models/videomae/__pycache__/image_processing_videomae.cpython-311.pyc,,
transformers/models/videomae/__pycache__/modeling_videomae.cpython-311.pyc,,
transformers/models/videomae/configuration_videomae.py,sha256=N1H14wlc-YIum5otc2nAO5Nj9KaXQ6NNN5QMT3UD-js,6718
transformers/models/videomae/convert_videomae_to_pytorch.py,sha256=rq2nT2ZJekra1G38kM2DH_qOvcZBDQFNgbCvH3mKZjY,13989
transformers/models/videomae/feature_extraction_videomae.py,sha256=Hg5wmFhkbncqR3nfvtevV6msaUEqvLBf4mtO4aICYTI,1200
transformers/models/videomae/image_processing_videomae.py,sha256=8nnca4_s_PjDHh5dlfsAc0y57JTWjpZqZDHA1gvSuqY,16454
transformers/models/videomae/modeling_videomae.py,sha256=Ye98lJGGtjkLTRVDeJclVBOVN_4n4pRjbvyfPoWqC6k,47436
transformers/models/vilt/__init__.py,sha256=-fruuGWD0urXmb7STgXnrF3QY8J6Z6lfJuTneeL_BsM,2788
transformers/models/vilt/__pycache__/__init__.cpython-311.pyc,,
transformers/models/vilt/__pycache__/configuration_vilt.cpython-311.pyc,,
transformers/models/vilt/__pycache__/convert_vilt_original_to_pytorch.cpython-311.pyc,,
transformers/models/vilt/__pycache__/feature_extraction_vilt.cpython-311.pyc,,
transformers/models/vilt/__pycache__/image_processing_vilt.cpython-311.pyc,,
transformers/models/vilt/__pycache__/modeling_vilt.cpython-311.pyc,,
transformers/models/vilt/__pycache__/processing_vilt.cpython-311.pyc,,
transformers/models/vilt/configuration_vilt.py,sha256=UMueOP-UpkW7uWV4cwKovdLEgvX7qXpFkkSjrwHObgY,6929
transformers/models/vilt/convert_vilt_original_to_pytorch.py,sha256=IUSgkjLMZRUBuozW7OzL6TtD_jkO7ZfH51H6x6Qgjdk,12882
transformers/models/vilt/feature_extraction_vilt.py,sha256=dC0Glwc_rDX7zqp8BxRtzaLogQGI4I4CjQCgxU7UORw,1172
transformers/models/vilt/image_processing_vilt.py,sha256=xqfEzSUt_W80WMnSTAXfBr8YHPI5by9N7qRERVsNvqQ,23078
transformers/models/vilt/modeling_vilt.py,sha256=4ksTNiibO2X25ZehjgTkvi0JyuGwN49nuUIt5usNhtI,65017
transformers/models/vilt/processing_vilt.py,sha256=0iOal8dCaE7JCQlZjbJ1-sHGxpDPZgUkMowEbxFRF2Q,6079
transformers/models/vipllava/__init__.py,sha256=6lR_RtZD-Jzj6ZMOjo3JYuFRaBjVKmXquzPOB38z33k,1740
transformers/models/vipllava/__pycache__/__init__.cpython-311.pyc,,
transformers/models/vipllava/__pycache__/configuration_vipllava.cpython-311.pyc,,
transformers/models/vipllava/__pycache__/convert_vipllava_weights_to_hf.cpython-311.pyc,,
transformers/models/vipllava/__pycache__/modeling_vipllava.cpython-311.pyc,,
transformers/models/vipllava/configuration_vipllava.py,sha256=xVE3jjvw4UwxI7w7QyySWa9rbHeHzzSqGjCVr2BYP20,5525
transformers/models/vipllava/convert_vipllava_weights_to_hf.py,sha256=u64-lOXDE0JMGhkGYJEtyrOh3gpeJtxSDC_dC08mc2c,4794
transformers/models/vipllava/modeling_vipllava.py,sha256=b3O5W0GHxlyeWpJZ6YmkFTZ8cd2AMhHtUX2rgflC_EU,29626
transformers/models/vision_encoder_decoder/__init__.py,sha256=IRQsS-4Bz-cm6B97rSoeC62Z1l1wns0XVDZwBn1KBIU,2627
transformers/models/vision_encoder_decoder/__pycache__/__init__.cpython-311.pyc,,
transformers/models/vision_encoder_decoder/__pycache__/configuration_vision_encoder_decoder.cpython-311.pyc,,
transformers/models/vision_encoder_decoder/__pycache__/modeling_flax_vision_encoder_decoder.cpython-311.pyc,,
transformers/models/vision_encoder_decoder/__pycache__/modeling_tf_vision_encoder_decoder.cpython-311.pyc,,
transformers/models/vision_encoder_decoder/__pycache__/modeling_vision_encoder_decoder.cpython-311.pyc,,
transformers/models/vision_encoder_decoder/configuration_vision_encoder_decoder.py,sha256=6x7tdTBOrsvKOMy12NCtbPatY2qaqOJaVIGGxy3uPDw,8273
transformers/models/vision_encoder_decoder/modeling_flax_vision_encoder_decoder.py,sha256=q2Tzd_KS4rB81YZk3zzb3KjtghP6vaPY4Snz_Kh52qQ,41535
transformers/models/vision_encoder_decoder/modeling_tf_vision_encoder_decoder.py,sha256=-7ASqN2Qu4Ehcwr0WF0MTnrb28Fj3fCGFzGinhuQXak,36239
transformers/models/vision_encoder_decoder/modeling_vision_encoder_decoder.py,sha256=bOHXlyNB4_bjU05Ft6cmP_FDg0QaTszM-l5RZhaJWQ8,34593
transformers/models/vision_text_dual_encoder/__init__.py,sha256=kULrtY2Ie2eigdn63xnoEqRUlmKm31D9mUCJs4F62Lo,2730
transformers/models/vision_text_dual_encoder/__pycache__/__init__.cpython-311.pyc,,
transformers/models/vision_text_dual_encoder/__pycache__/configuration_vision_text_dual_encoder.cpython-311.pyc,,
transformers/models/vision_text_dual_encoder/__pycache__/modeling_flax_vision_text_dual_encoder.cpython-311.pyc,,
transformers/models/vision_text_dual_encoder/__pycache__/modeling_tf_vision_text_dual_encoder.cpython-311.pyc,,
transformers/models/vision_text_dual_encoder/__pycache__/modeling_vision_text_dual_encoder.cpython-311.pyc,,
transformers/models/vision_text_dual_encoder/__pycache__/processing_vision_text_dual_encoder.cpython-311.pyc,,
transformers/models/vision_text_dual_encoder/configuration_vision_text_dual_encoder.py,sha256=E7pT_zGc0uq9uzfKSBE6QiYjgSAotq0zYuC1bnzE5F0,4895
transformers/models/vision_text_dual_encoder/modeling_flax_vision_text_dual_encoder.py,sha256=JP4ppqdIEvRfbpCtf0b3bJQcURI8YVvyTHe8wDRCRJg,26314
transformers/models/vision_text_dual_encoder/modeling_tf_vision_text_dual_encoder.py,sha256=stdg94SN9NhHPelgqWBOJt-X7c4fBohXcBhDIl_TE68,28641
transformers/models/vision_text_dual_encoder/modeling_vision_text_dual_encoder.py,sha256=kqB-zueOo28U1qXKRoR1njEyX6xRm45r0faBUKYH4wQ,24939
transformers/models/vision_text_dual_encoder/processing_vision_text_dual_encoder.py,sha256=G1QQYQLLxPDVX4I5aOb0iG6PwrlDqjN7GNLZyqXUNqo,7035
transformers/models/visual_bert/__init__.py,sha256=OSQEpz1R0NjH9WvGkfsXKq_9LJTGfrHscqYd2xl9S_4,2235
transformers/models/visual_bert/__pycache__/__init__.cpython-311.pyc,,
transformers/models/visual_bert/__pycache__/configuration_visual_bert.cpython-311.pyc,,
transformers/models/visual_bert/__pycache__/convert_visual_bert_original_pytorch_checkpoint_to_pytorch.cpython-311.pyc,,
transformers/models/visual_bert/__pycache__/modeling_visual_bert.cpython-311.pyc,,
transformers/models/visual_bert/configuration_visual_bert.py,sha256=WuetOMTbrOClAEbqMz3adIYsc7M1-_OnoPy4sEub66I,7942
transformers/models/visual_bert/convert_visual_bert_original_pytorch_checkpoint_to_pytorch.py,sha256=BpXgEZ-5LdGIa0NK6BDZd_5VhKCqeWuu2oOQyUqcSRQ,5158
transformers/models/visual_bert/modeling_visual_bert.py,sha256=zyyJNbflhURLCtTDU5jpokgiRtKnB9PuBlCu9ymFiH4,69624
transformers/models/vit/__init__.py,sha256=Kw3Pan4rUcu6RQsA7u-DpxMlmbzdmrA7GA3ha3nYO5k,3598
transformers/models/vit/__pycache__/__init__.cpython-311.pyc,,
transformers/models/vit/__pycache__/configuration_vit.cpython-311.pyc,,
transformers/models/vit/__pycache__/convert_dino_to_pytorch.cpython-311.pyc,,
transformers/models/vit/__pycache__/convert_vit_timm_to_pytorch.cpython-311.pyc,,
transformers/models/vit/__pycache__/feature_extraction_vit.cpython-311.pyc,,
transformers/models/vit/__pycache__/image_processing_vit.cpython-311.pyc,,
transformers/models/vit/__pycache__/modeling_flax_vit.cpython-311.pyc,,
transformers/models/vit/__pycache__/modeling_tf_vit.cpython-311.pyc,,
transformers/models/vit/__pycache__/modeling_vit.cpython-311.pyc,,
transformers/models/vit/configuration_vit.py,sha256=rup8oV5auTfuXRal3DI0JIU6h61PaxYPKlQmjhwLdWc,5830
transformers/models/vit/convert_dino_to_pytorch.py,sha256=CIkbWDBEgW5jmSWWoPZOosLLqCFiUz8oYgnj48JdtSM,8854
transformers/models/vit/convert_vit_timm_to_pytorch.py,sha256=LY_UklTkw47xwnCcY8AzVFH-6g5B8t3GTuQ0PbyZyn0,10890
transformers/models/vit/feature_extraction_vit.py,sha256=R-W_HNOybSpKxKGKfo4iDB4zGTRHeW1cq-29iwnbVl4,1165
transformers/models/vit/image_processing_vit.py,sha256=20qN0RlqpkVgosJw_3Jd9TU4x_BqIqBCSdZ59PviQa8,13694
transformers/models/vit/modeling_flax_vit.py,sha256=KsTqlse5b5euRgYXhrXoNqCNvo0LEPBGuU_b0uNO0yo,25340
transformers/models/vit/modeling_tf_vit.py,sha256=Ycwa5F6KssyHFtmpVhbOxq2XY1q36PX8wzOVGrOlgqA,37328
transformers/models/vit/modeling_vit.py,sha256=xJiruJIEOcAOBdTQlOXS-_XjmDynWx014OUHnuwfmlY,35645
transformers/models/vit_hybrid/__init__.py,sha256=kJffDq49Rz34fkQnLISzCp18xqXkVFOIWciOsZMjc2I,2316
transformers/models/vit_hybrid/__pycache__/__init__.cpython-311.pyc,,
transformers/models/vit_hybrid/__pycache__/configuration_vit_hybrid.cpython-311.pyc,,
transformers/models/vit_hybrid/__pycache__/convert_vit_hybrid_timm_to_pytorch.cpython-311.pyc,,
transformers/models/vit_hybrid/__pycache__/image_processing_vit_hybrid.cpython-311.pyc,,
transformers/models/vit_hybrid/__pycache__/modeling_vit_hybrid.cpython-311.pyc,,
transformers/models/vit_hybrid/configuration_vit_hybrid.py,sha256=2iIhcp1q9427V62BJ4JmkUvqNn-9DZ5zd0eXiKoVWVU,8465
transformers/models/vit_hybrid/convert_vit_hybrid_timm_to_pytorch.py,sha256=MymDN5E1N5g1g5k0mK0M-F2VeYy_Me-hRWdVNTRFocA,13413
transformers/models/vit_hybrid/image_processing_vit_hybrid.py,sha256=Ejy-qXAEXyAetDyZ23q0rFECEaXD_j7lTf0vZKz7SWU,15814
transformers/models/vit_hybrid/modeling_vit_hybrid.py,sha256=nTV9g-c3fP03qbROBUVLsyXr0bhpLeNRoZcsiek0qVw,31933
transformers/models/vit_mae/__init__.py,sha256=-w9MTkUgGkYCX6q37upqBk7x-8g247YxYGVVAEJkIzk,2428
transformers/models/vit_mae/__pycache__/__init__.cpython-311.pyc,,
transformers/models/vit_mae/__pycache__/configuration_vit_mae.cpython-311.pyc,,
transformers/models/vit_mae/__pycache__/convert_vit_mae_to_pytorch.cpython-311.pyc,,
transformers/models/vit_mae/__pycache__/modeling_tf_vit_mae.cpython-311.pyc,,
transformers/models/vit_mae/__pycache__/modeling_vit_mae.cpython-311.pyc,,
transformers/models/vit_mae/configuration_vit_mae.py,sha256=ouYg3MpVUUvu1oZLefYPdU2TF28YEyyEt0oKWs6wYIU,6568
transformers/models/vit_mae/convert_vit_mae_to_pytorch.py,sha256=Nj4Y5LS8H7xbyWNeLE9Vn0NFyXSQQYEcj1QQMzN1Hdg,7516
transformers/models/vit_mae/modeling_tf_vit_mae.py,sha256=QBtXTmOdrC21lPOajqx6WCWKl2JVeDpeUM31oUFMYJ8,52979
transformers/models/vit_mae/modeling_vit_mae.py,sha256=9GrsJgWxZAPYUCznQ0aWlr3CX0Xnq802NrTNVVnNXKU,42822
transformers/models/vit_msn/__init__.py,sha256=4VVe0aSuBzHjTg4X2nuVet-9DgD5_dWlFkbLAr4bilc,1783
transformers/models/vit_msn/__pycache__/__init__.cpython-311.pyc,,
transformers/models/vit_msn/__pycache__/configuration_vit_msn.cpython-311.pyc,,
transformers/models/vit_msn/__pycache__/convert_msn_to_pytorch.cpython-311.pyc,,
transformers/models/vit_msn/__pycache__/modeling_vit_msn.cpython-311.pyc,,
transformers/models/vit_msn/configuration_vit_msn.py,sha256=jOEIjeWBCMC9cQTAG6BQkhbwS9yLa8_I4uX9iHwH1SY,5063
transformers/models/vit_msn/convert_msn_to_pytorch.py,sha256=1xBjqvbviFkGxhi_xq2956R7qZpFEBdKPNOQYb-SoIA,9841
transformers/models/vit_msn/modeling_vit_msn.py,sha256=H7j7eS8Vh6jyE7rBGbVCNoZOzFu4tQbT_4LAX6ijCo4,29713
transformers/models/vitdet/__init__.py,sha256=Vaafapb4IUbKPzQUqPjhX6nvt14CTKlV51QneeQpTmc,1764
transformers/models/vitdet/__pycache__/__init__.cpython-311.pyc,,
transformers/models/vitdet/__pycache__/configuration_vitdet.cpython-311.pyc,,
transformers/models/vitdet/__pycache__/modeling_vitdet.cpython-311.pyc,,
transformers/models/vitdet/configuration_vitdet.py,sha256=T8lKQYS8r3qXFmLXMtrgI_pzIKmh5mdoAF5dOQedf_A,7660
transformers/models/vitdet/modeling_vitdet.py,sha256=aa2HthZAXSIeCeh1L5AuUZ_JrVZeAiLSUNPWmsTGHnw,34568
transformers/models/vitmatte/__init__.py,sha256=tl-h8_VOAHRT7VtJJJ-SFSl5lkHxfVEdDaCtm4ksJIg,2239
transformers/models/vitmatte/__pycache__/__init__.cpython-311.pyc,,
transformers/models/vitmatte/__pycache__/configuration_vitmatte.cpython-311.pyc,,
transformers/models/vitmatte/__pycache__/convert_vitmatte_to_hf.cpython-311.pyc,,
transformers/models/vitmatte/__pycache__/image_processing_vitmatte.cpython-311.pyc,,
transformers/models/vitmatte/__pycache__/modeling_vitmatte.cpython-311.pyc,,
transformers/models/vitmatte/configuration_vitmatte.py,sha256=eD9QZOwmHs9955U3cUfmkKTHe7FNXFaGu-yL3wFJBnI,6521
transformers/models/vitmatte/convert_vitmatte_to_hf.py,sha256=1xctm78nmCLelPMqGJepxSyq5saKgA4by5CTzyxRPvc,6404
transformers/models/vitmatte/image_processing_vitmatte.py,sha256=sW-Jrn26OSakg9lAHN8TvJXCsSXVjg7N1kOCyR1A9_Q,13390
transformers/models/vitmatte/modeling_vitmatte.py,sha256=vBzXtDgSxrO1KeBAL-kUYYjpR0ZOwrwN0cXbj2-fg7E,12896
transformers/models/vits/__init__.py,sha256=JoVFhlJ0-hhxN3ND-JsESyEcsihDbT6j0WPmIH9DjCA,1887
transformers/models/vits/__pycache__/__init__.cpython-311.pyc,,
transformers/models/vits/__pycache__/configuration_vits.cpython-311.pyc,,
transformers/models/vits/__pycache__/convert_original_checkpoint.cpython-311.pyc,,
transformers/models/vits/__pycache__/modeling_vits.cpython-311.pyc,,
transformers/models/vits/__pycache__/tokenization_vits.cpython-311.pyc,,
transformers/models/vits/configuration_vits.py,sha256=gDQaik8TS5a-yxBzz4ms-s9diUAMsTRaVXsr-gBU8DY,14001
transformers/models/vits/convert_original_checkpoint.py,sha256=N6rRzBaJlMxRwT7u33kUyJKy-4fFTWTD6nu_RTTOGt0,18610
transformers/models/vits/modeling_vits.py,sha256=GphdjyWBNSlrO2_4QERikbolZhx7zYcmk_TsTKQXYos,66373
transformers/models/vits/tokenization_vits.py,sha256=2b6cXsaN7AtWA2qGfzGaYhqRgYS62gnxZUJpomogcjQ,9376
transformers/models/vivit/__init__.py,sha256=Ajx0pvLrGGMBJruIaFHvqJiQyAM9BI9qLRi-5kyRT10,2441
transformers/models/vivit/__pycache__/__init__.cpython-311.pyc,,
transformers/models/vivit/__pycache__/configuration_vivit.cpython-311.pyc,,
transformers/models/vivit/__pycache__/convert_vivit_flax_to_pytorch.cpython-311.pyc,,
transformers/models/vivit/__pycache__/image_processing_vivit.cpython-311.pyc,,
transformers/models/vivit/__pycache__/modeling_vivit.cpython-311.pyc,,
transformers/models/vivit/configuration_vivit.py,sha256=yfbgcKTBM8ci8gQfEMAhMwbPJFSdOsGPaNC-s2-QPKA,5369
transformers/models/vivit/convert_vivit_flax_to_pytorch.py,sha256=yIwLQOx8eT-8AuYf_3KTfLwabCBdC1z_Z0WZDr4a7mM,9111
transformers/models/vivit/image_processing_vivit.py,sha256=_pg9Dr9WpkanKDG-gdqqDJpJMvQRIX8h-rKfSQ9c_CI,18984
transformers/models/vivit/modeling_vivit.py,sha256=Hoyd876GGw41o24AVlDyDg-Dwv-q6TgLNXrI2-iFTKw,30035
transformers/models/wav2vec2/__init__.py,sha256=eN9LbGY56T2Kz38zw3ChsiOkOHprtc4CgQjT8DSrUds,4139
transformers/models/wav2vec2/__pycache__/__init__.cpython-311.pyc,,
transformers/models/wav2vec2/__pycache__/configuration_wav2vec2.cpython-311.pyc,,
transformers/models/wav2vec2/__pycache__/convert_wav2vec2_original_pytorch_checkpoint_to_pytorch.cpython-311.pyc,,
transformers/models/wav2vec2/__pycache__/convert_wav2vec2_original_s3prl_checkpoint_to_pytorch.cpython-311.pyc,,
transformers/models/wav2vec2/__pycache__/feature_extraction_wav2vec2.cpython-311.pyc,,
transformers/models/wav2vec2/__pycache__/modeling_flax_wav2vec2.cpython-311.pyc,,
transformers/models/wav2vec2/__pycache__/modeling_tf_wav2vec2.cpython-311.pyc,,
transformers/models/wav2vec2/__pycache__/modeling_wav2vec2.cpython-311.pyc,,
transformers/models/wav2vec2/__pycache__/processing_wav2vec2.cpython-311.pyc,,
transformers/models/wav2vec2/__pycache__/tokenization_wav2vec2.cpython-311.pyc,,
transformers/models/wav2vec2/configuration_wav2vec2.py,sha256=-3Z27Yf1GdSd_vuLtY3a-hM0vdePEFvqd_kZLZzuTE0,20288
transformers/models/wav2vec2/convert_wav2vec2_original_pytorch_checkpoint_to_pytorch.py,sha256=hhc_QSStY43_pj4bIQf0TUWfiJo1KGkPuMTl16dP-ng,14293
transformers/models/wav2vec2/convert_wav2vec2_original_s3prl_checkpoint_to_pytorch.py,sha256=CMjcWPEsvvPpX-OlMUJQxHNDErbJbDVqVSCoqo-9hDk,4838
transformers/models/wav2vec2/feature_extraction_wav2vec2.py,sha256=D-yqFIpwjn_7LYJUmdnelRsn4qsoUrkZGX4Qsp5Y9CY,11511
transformers/models/wav2vec2/modeling_flax_wav2vec2.py,sha256=iLm6d5m0LYQs0qKqg3Tdx7I6vgCB5QCmFY6MYrKu0RA,57331
transformers/models/wav2vec2/modeling_tf_wav2vec2.py,sha256=4M-tOYrK19cZP--iNJeq6ABDPNi5Wufkr2RAkg6qK_M,78890
transformers/models/wav2vec2/modeling_wav2vec2.py,sha256=VAkJWhcb7q41VftqW7iDLre3NF401zqMY_5FITMnInk,106881
transformers/models/wav2vec2/processing_wav2vec2.py,sha256=82JBzFgQxV5ZQgRYmMj3gqf3pxL8Q8nfdwnhsuUUZjU,7137
transformers/models/wav2vec2/tokenization_wav2vec2.py,sha256=SrXQ2esXpCxUokOgY8roTrf79RCi1DKlSLA74aGS8so,38780
transformers/models/wav2vec2_bert/__init__.py,sha256=yBuhwgvNayh1tKpyXnLCSmw877fgVbtI16Xag8BK6Wo,2300
transformers/models/wav2vec2_bert/__pycache__/__init__.cpython-311.pyc,,
transformers/models/wav2vec2_bert/__pycache__/configuration_wav2vec2_bert.cpython-311.pyc,,
transformers/models/wav2vec2_bert/__pycache__/convert_wav2vec2_seamless_checkpoint.cpython-311.pyc,,
transformers/models/wav2vec2_bert/__pycache__/modeling_wav2vec2_bert.cpython-311.pyc,,
transformers/models/wav2vec2_bert/__pycache__/processing_wav2vec2_bert.cpython-311.pyc,,
transformers/models/wav2vec2_bert/configuration_wav2vec2_bert.py,sha256=oeyhyuT90uniqSmX8nevguP_kVBQl-iOr4tG-W6hqE0,18230
transformers/models/wav2vec2_bert/convert_wav2vec2_seamless_checkpoint.py,sha256=MFwGdbwNt4jDlGDG6cc9T5PhKEd-PjFMUOci533PLG8,7420
transformers/models/wav2vec2_bert/modeling_wav2vec2_bert.py,sha256=i6ghiSThnUpJCUAUYal_pzVFkiY3n019sasDuiL1TWU,74642
transformers/models/wav2vec2_bert/processing_wav2vec2_bert.py,sha256=DWMQCIdzOHFXFQA8ReGS-HLHfQYhUTpuj7jLMHZ8th0,7449
transformers/models/wav2vec2_conformer/__init__.py,sha256=w6Z-Rd5ONNTFI-ioN5VvNPhW842-_rKASoHN6lGeJx4,2375
transformers/models/wav2vec2_conformer/__pycache__/__init__.cpython-311.pyc,,
transformers/models/wav2vec2_conformer/__pycache__/configuration_wav2vec2_conformer.cpython-311.pyc,,
transformers/models/wav2vec2_conformer/__pycache__/convert_wav2vec2_conformer_original_pytorch_checkpoint_to_pytorch.cpython-311.pyc,,
transformers/models/wav2vec2_conformer/__pycache__/modeling_wav2vec2_conformer.cpython-311.pyc,,
transformers/models/wav2vec2_conformer/configuration_wav2vec2_conformer.py,sha256=m8hEklbbwIbRXEw5RukSIdgFi0yhnzQG4BRLEPBnQ1Y,21065
transformers/models/wav2vec2_conformer/convert_wav2vec2_conformer_original_pytorch_checkpoint_to_pytorch.py,sha256=D8rojgR8DRaqVTZwYXd2qykIKlKf7EnMM6h3PzYPS0M,13382
transformers/models/wav2vec2_conformer/modeling_wav2vec2_conformer.py,sha256=CQaub6_Kv53v9fKLRiNU-xBh7zAOlEYmXvmhd0A8qUY,95691
transformers/models/wav2vec2_phoneme/__init__.py,sha256=E2xRyViyzCISV8XE7YQ1gx5Wlx9_ACoPDB6ZZEm9bWo,993
transformers/models/wav2vec2_phoneme/__pycache__/__init__.cpython-311.pyc,,
transformers/models/wav2vec2_phoneme/__pycache__/tokenization_wav2vec2_phoneme.cpython-311.pyc,,
transformers/models/wav2vec2_phoneme/tokenization_wav2vec2_phoneme.py,sha256=shwcrq0sD4Co9YPHBDGkliVHfV4ZZaS-nPLzDeTVeCY,23822
transformers/models/wav2vec2_with_lm/__init__.py,sha256=d_lvk8QAia4BIKN7d_Uy3HdRqrDp_ZJHTDZ-nkHKwPA,981
transformers/models/wav2vec2_with_lm/__pycache__/__init__.cpython-311.pyc,,
transformers/models/wav2vec2_with_lm/__pycache__/processing_wav2vec2_with_lm.cpython-311.pyc,,
transformers/models/wav2vec2_with_lm/processing_wav2vec2_with_lm.py,sha256=rB38_Sef9FlkFFd_AqJwbEraRdcp5wi1fNV1e7he7F8,29522
transformers/models/wavlm/__init__.py,sha256=puMYnJLkFpkYKq7oH_ziapvzFYZMOyTHDqpN8IxzJPw,1959
transformers/models/wavlm/__pycache__/__init__.cpython-311.pyc,,
transformers/models/wavlm/__pycache__/configuration_wavlm.cpython-311.pyc,,
transformers/models/wavlm/__pycache__/convert_wavlm_original_pytorch_checkpoint_to_pytorch.cpython-311.pyc,,
transformers/models/wavlm/__pycache__/convert_wavlm_original_s3prl_checkpoint_to_pytorch.cpython-311.pyc,,
transformers/models/wavlm/__pycache__/modeling_wavlm.cpython-311.pyc,,
transformers/models/wavlm/configuration_wavlm.py,sha256=DJ0kUxva4kf7O6glp_OLmHnUWMGhws-ju7Q7lHmpAko,18753
transformers/models/wavlm/convert_wavlm_original_pytorch_checkpoint_to_pytorch.py,sha256=tYQiS5CUNYoMWyxKnmkmDG6VW0lwapFxTrDSz4Pprm0,8580
transformers/models/wavlm/convert_wavlm_original_s3prl_checkpoint_to_pytorch.py,sha256=Yo4K3ZxH5KXS3gCD7KTakUviJABV-gJGJHXFeV5Sc9I,4814
transformers/models/wavlm/modeling_wavlm.py,sha256=gS016s8ua7XngaIQXjiqBctCwn7gMC6J8j_OwFg54uU,78701
transformers/models/whisper/__init__.py,sha256=Y9nksRYJ-dCwFFdnagINwcqEMrdRG7AtPKWRB4uXlmM,4346
transformers/models/whisper/__pycache__/__init__.cpython-311.pyc,,
transformers/models/whisper/__pycache__/configuration_whisper.cpython-311.pyc,,
transformers/models/whisper/__pycache__/convert_openai_to_hf.cpython-311.pyc,,
transformers/models/whisper/__pycache__/english_normalizer.cpython-311.pyc,,
transformers/models/whisper/__pycache__/feature_extraction_whisper.cpython-311.pyc,,
transformers/models/whisper/__pycache__/generation_whisper.cpython-311.pyc,,
transformers/models/whisper/__pycache__/modeling_flax_whisper.cpython-311.pyc,,
transformers/models/whisper/__pycache__/modeling_tf_whisper.cpython-311.pyc,,
transformers/models/whisper/__pycache__/modeling_whisper.cpython-311.pyc,,
transformers/models/whisper/__pycache__/processing_whisper.cpython-311.pyc,,
transformers/models/whisper/__pycache__/tokenization_whisper.cpython-311.pyc,,
transformers/models/whisper/__pycache__/tokenization_whisper_fast.cpython-311.pyc,,
transformers/models/whisper/configuration_whisper.py,sha256=Cw3Xr7emdiqW55iNwgdVDb1yybY6445plLZbiYWnrho,17053
transformers/models/whisper/convert_openai_to_hf.py,sha256=Jk3YoAGCTEHIvwGZSc5qetw7LU0jbjewYXG-YNdjiZk,14891
transformers/models/whisper/english_normalizer.py,sha256=MTJ16OhstprR2X8owfEJmONqkoSHHyzztENejmEhSBM,22822
transformers/models/whisper/feature_extraction_whisper.py,sha256=V55NllKQKV3knNSc0acjV7WQg9bNtfcM0n19XPPeUL8,12886
transformers/models/whisper/generation_whisper.py,sha256=saGv3-0LFFba-FDyGi_-OYZyfwwFw2p2Dy2VMQDDVQE,86917
transformers/models/whisper/modeling_flax_whisper.py,sha256=s4sI__pmItZAAJxzmgU8f1jy3Dk4fAn9uGyy6TAaJnM,73587
transformers/models/whisper/modeling_tf_whisper.py,sha256=0VVrRgN2gaD1kgs1Tl0ZU5uGzfdOAYQKKEQpr97tav0,84918
transformers/models/whisper/modeling_whisper.py,sha256=U32pP6xHEjreQIjKJk3dAOifbzYzTHPzcIzLCyDE06Q,105588
transformers/models/whisper/processing_whisper.py,sha256=pO6wtcywcJq-lkA2rNrdINEvj7_6fjWvAUv7HWn70gE,3891
transformers/models/whisper/tokenization_whisper.py,sha256=z-VaWo7kviXjxXAfdc3Mz_pKJWsH8FZMBevwOmFN44M,55033
transformers/models/whisper/tokenization_whisper_fast.py,sha256=p-PiFuygRqcGDkR1fgRzCTZ_68wwyJn1mUWCJQWsi-A,32327
transformers/models/x_clip/__init__.py,sha256=zWhh0KIKf1OaB3EezBv6YkgaxTESvEesITGqhiZYgHs,2053
transformers/models/x_clip/__pycache__/__init__.cpython-311.pyc,,
transformers/models/x_clip/__pycache__/configuration_x_clip.cpython-311.pyc,,
transformers/models/x_clip/__pycache__/convert_x_clip_original_pytorch_to_hf.cpython-311.pyc,,
transformers/models/x_clip/__pycache__/modeling_x_clip.cpython-311.pyc,,
transformers/models/x_clip/__pycache__/processing_x_clip.cpython-311.pyc,,
transformers/models/x_clip/configuration_x_clip.py,sha256=dFyaSkdASUCtiqrtRT3sY2xfcbBwIAKSlQC1zykHFRE,20469
transformers/models/x_clip/convert_x_clip_original_pytorch_to_hf.py,sha256=WzXe8IKqSz4Bi78EIvRA6C3QiLL4c-SpARggHjIWtt4,18066
transformers/models/x_clip/modeling_x_clip.py,sha256=U0yxrzeP6JDD_uOEeRr5wAPiI9vapet9vFDHqwB5Da8,70242
transformers/models/x_clip/processing_x_clip.py,sha256=vuwuN_pNagPMfdvGJrSbhQVTslOHBMGFgYV2xD9BHsw,6897
transformers/models/xglm/__init__.py,sha256=gSzCOADmOA0n4CxfKEhESj32_WqQ6ae6e0QjYyaJ-gs,3871
transformers/models/xglm/__pycache__/__init__.cpython-311.pyc,,
transformers/models/xglm/__pycache__/configuration_xglm.cpython-311.pyc,,
transformers/models/xglm/__pycache__/convert_xglm_original_ckpt_to_trfms.cpython-311.pyc,,
transformers/models/xglm/__pycache__/modeling_flax_xglm.cpython-311.pyc,,
transformers/models/xglm/__pycache__/modeling_tf_xglm.cpython-311.pyc,,
transformers/models/xglm/__pycache__/modeling_xglm.cpython-311.pyc,,
transformers/models/xglm/__pycache__/tokenization_xglm.cpython-311.pyc,,
transformers/models/xglm/__pycache__/tokenization_xglm_fast.cpython-311.pyc,,
transformers/models/xglm/configuration_xglm.py,sha256=qyFJy0gX9voVN_A3LoPwP2pXYReErZqxWI276SFulAQ,6056
transformers/models/xglm/convert_xglm_original_ckpt_to_trfms.py,sha256=9fjXP40nMFbiI9H0VV66Buqk9JQrPhAFERCOBYHl_7g,2325
transformers/models/xglm/modeling_flax_xglm.py,sha256=5-ubc4mqp9vhZFUUcyy8FzwwbS_xHpIA6pWIC9keOcg,33117
transformers/models/xglm/modeling_tf_xglm.py,sha256=9C9nIX_l4AmnS2TnZt_eri74CS0zydSC-dGHoOc6X9o,45420
transformers/models/xglm/modeling_xglm.py,sha256=0JTlAxCmHHtgkoofAP6VujRV8os4hGSbsqFFs5g5Neg,38740
transformers/models/xglm/tokenization_xglm.py,sha256=2abuTSeamfthzQKk3Y5N3ti-kUyj4gVT9_s5PmEUfWc,12859
transformers/models/xglm/tokenization_xglm_fast.py,sha256=Zj7cHc4V7-1n1jii3dhuVjMDDa1GGosLwXAA7zcfdrU,8100
transformers/models/xlm/__init__.py,sha256=tYpOIDQrMDWgJJ-OTPmX2NZngDrxqo47NRfA1dyNQgY,3292
transformers/models/xlm/__pycache__/__init__.cpython-311.pyc,,
transformers/models/xlm/__pycache__/configuration_xlm.cpython-311.pyc,,
transformers/models/xlm/__pycache__/convert_xlm_original_pytorch_checkpoint_to_pytorch.cpython-311.pyc,,
transformers/models/xlm/__pycache__/modeling_tf_xlm.cpython-311.pyc,,
transformers/models/xlm/__pycache__/modeling_xlm.cpython-311.pyc,,
transformers/models/xlm/__pycache__/tokenization_xlm.cpython-311.pyc,,
transformers/models/xlm/configuration_xlm.py,sha256=jrKh_HS0pOaQv6ycwKz7uHAqiSC-qhGy3KbWawOj3Zw,12217
transformers/models/xlm/convert_xlm_original_pytorch_checkpoint_to_pytorch.py,sha256=R2wBMzp-IIiBhTOHrgYacy3bX79BN1dh_DdHcO7fE1Y,2934
transformers/models/xlm/modeling_tf_xlm.py,sha256=KeAtFCSl3fzMM9HvxsOHIGdzWy72a5AxNdy5oRm410c,56888
transformers/models/xlm/modeling_xlm.py,sha256=BrauGz6UfcOELwUnuB7Fhn-fT7F-ypWGFzp6HhOyjtw,55064
transformers/models/xlm/tokenization_xlm.py,sha256=FwSiFN54qlK3EhS9oJlu5v4HroKBSj9nFR4g6DMO54M,35650
transformers/models/xlm_prophetnet/__init__.py,sha256=_YI-mEgntKjkMoW1RztiRlYdwvonIVpmO2ZQjm6Gezc,2615
transformers/models/xlm_prophetnet/__pycache__/__init__.cpython-311.pyc,,
transformers/models/xlm_prophetnet/__pycache__/configuration_xlm_prophetnet.cpython-311.pyc,,
transformers/models/xlm_prophetnet/__pycache__/modeling_xlm_prophetnet.cpython-311.pyc,,
transformers/models/xlm_prophetnet/__pycache__/tokenization_xlm_prophetnet.cpython-311.pyc,,
transformers/models/xlm_prophetnet/configuration_xlm_prophetnet.py,sha256=Wb5PH7qlCsUuS1052kc-zyMjFgf5IJIFmKenbg2cwU8,9126
transformers/models/xlm_prophetnet/modeling_xlm_prophetnet.py,sha256=vdd7NQvl5abkZp4jbQVwyCoW3uP4FoNn5yXuTjIlsis,119494
transformers/models/xlm_prophetnet/tokenization_xlm_prophetnet.py,sha256=9C3CMsEJl7EX8aLBqiZERVC5LWN7CqcIWzap67BFW_0,13848
transformers/models/xlm_roberta/__init__.py,sha256=Uhk9z5Xv2w8KrHfe0Hzc5ndpgmn5k6_dcZw6OCWye1A,5825
transformers/models/xlm_roberta/__pycache__/__init__.cpython-311.pyc,,
transformers/models/xlm_roberta/__pycache__/configuration_xlm_roberta.cpython-311.pyc,,
transformers/models/xlm_roberta/__pycache__/modeling_flax_xlm_roberta.cpython-311.pyc,,
transformers/models/xlm_roberta/__pycache__/modeling_tf_xlm_roberta.cpython-311.pyc,,
transformers/models/xlm_roberta/__pycache__/modeling_xlm_roberta.cpython-311.pyc,,
transformers/models/xlm_roberta/__pycache__/tokenization_xlm_roberta.cpython-311.pyc,,
transformers/models/xlm_roberta/__pycache__/tokenization_xlm_roberta_fast.cpython-311.pyc,,
transformers/models/xlm_roberta/configuration_xlm_roberta.py,sha256=qrzKEpmYR9XkxUqUWwoGvBvvTNKPaH34a_6VIwIG5Ko,8523
transformers/models/xlm_roberta/modeling_flax_xlm_roberta.py,sha256=cGx5G8d8vHVzH8FewHLZ9l-FU3g7wVOsDIsJgjtsa5c,58655
transformers/models/xlm_roberta/modeling_tf_xlm_roberta.py,sha256=pIeRDgR2q0V1YGLVrg2uA8yWfv5zrlZmXgJ5rA3BO5E,82122
transformers/models/xlm_roberta/modeling_xlm_roberta.py,sha256=SIpZboHEumQGUN6C4iwsyOfQRyWxYZpTwIMYdvfuHG8,73228
transformers/models/xlm_roberta/tokenization_xlm_roberta.py,sha256=nfDIOZVN_Jom7d8vcg8BOIG87hjwVgdcaoGDMDTgvno,14374
transformers/models/xlm_roberta/tokenization_xlm_roberta_fast.py,sha256=TEWemInytw8x6X5o3xLdf1xVWYSowZV0AaTf3Jvj0XU,10655
transformers/models/xlm_roberta_xl/__init__.py,sha256=Q3eFSJ5cKAt-2cJLXKdWW28TLujRqjebIBzlqSvK0U4,2405
transformers/models/xlm_roberta_xl/__pycache__/__init__.cpython-311.pyc,,
transformers/models/xlm_roberta_xl/__pycache__/configuration_xlm_roberta_xl.cpython-311.pyc,,
transformers/models/xlm_roberta_xl/__pycache__/convert_xlm_roberta_xl_original_pytorch_checkpoint_to_pytorch.cpython-311.pyc,,
transformers/models/xlm_roberta_xl/__pycache__/modeling_xlm_roberta_xl.cpython-311.pyc,,
transformers/models/xlm_roberta_xl/configuration_xlm_roberta_xl.py,sha256=ggKolxBgq5c8df-S4lEM2KAtnf_8bs3nYIUYNLW0RiY,7620
transformers/models/xlm_roberta_xl/convert_xlm_roberta_xl_original_pytorch_checkpoint_to_pytorch.py,sha256=zVa6azx9rd33D3JkH2uqJ6W20TosJyWi9eLm3LNtc5U,8228
transformers/models/xlm_roberta_xl/modeling_xlm_roberta_xl.py,sha256=-qBzJF0HwG4-9FQAIS1-xvAGIvU8k-erRcOf1cpp--U,69089
transformers/models/xlnet/__init__.py,sha256=-jvIW4RkN8qTjJPEEmIvK6pO8c9NB0Q4JlzY7CWHWUI,4288
transformers/models/xlnet/__pycache__/__init__.cpython-311.pyc,,
transformers/models/xlnet/__pycache__/configuration_xlnet.cpython-311.pyc,,
transformers/models/xlnet/__pycache__/convert_xlnet_original_tf_checkpoint_to_pytorch.cpython-311.pyc,,
transformers/models/xlnet/__pycache__/modeling_tf_xlnet.cpython-311.pyc,,
transformers/models/xlnet/__pycache__/modeling_xlnet.cpython-311.pyc,,
transformers/models/xlnet/__pycache__/tokenization_xlnet.cpython-311.pyc,,
transformers/models/xlnet/__pycache__/tokenization_xlnet_fast.cpython-311.pyc,,
transformers/models/xlnet/configuration_xlnet.py,sha256=jbiUEL4xR7zJxGxz8dpyGgncjF6KuqKZpyUGgMZdB2I,11179
transformers/models/xlnet/convert_xlnet_original_tf_checkpoint_to_pytorch.py,sha256=iodIP1W2FNMjel9V31jR7RcHqs8zGX8TK3YdQ65lEbk,3688
transformers/models/xlnet/modeling_tf_xlnet.py,sha256=bKi1w4ZQRZQQTHXsVG6sRPjWH0qE9t9YK30wfA3jWUA,77785
transformers/models/xlnet/modeling_xlnet.py,sha256=mco8HYrN2_xFIeNb43yi2eSNfiKK_im40GF07poAcW4,93018
transformers/models/xlnet/tokenization_xlnet.py,sha256=VQLYy-3SRjoQ8uFDX4H95hbFoqqQj6GWSoyIvNwZ1FY,16228
transformers/models/xlnet/tokenization_xlnet_fast.py,sha256=6KJnM1Sb-S4pQURvDIry_n1R2al8rLmxO9wCszghVh4,10147
transformers/models/xmod/__init__.py,sha256=uoKu7ACrFCEwDUwL06kwYCcUbHt9P3bLIcHLtMtjw-I,2325
transformers/models/xmod/__pycache__/__init__.cpython-311.pyc,,
transformers/models/xmod/__pycache__/configuration_xmod.cpython-311.pyc,,
transformers/models/xmod/__pycache__/convert_xmod_original_pytorch_checkpoint_to_pytorch.cpython-311.pyc,,
transformers/models/xmod/__pycache__/modeling_xmod.cpython-311.pyc,,
transformers/models/xmod/configuration_xmod.py,sha256=yerKDbvIIFcEvQI6pMdYTQ7guJBz_9jTMxz68ZTZRvo,10146
transformers/models/xmod/convert_xmod_original_pytorch_checkpoint_to_pytorch.py,sha256=yFSAtXjxbAy6uXBg2XinRbk3VSEBOsWj1ugBhVNrGjQ,9859
transformers/models/xmod/modeling_xmod.py,sha256=a0nwYeJPmNqmRZlX9PYAsxyrKQzEQa7y7I9WpPvsii8,76604
transformers/models/yolos/__init__.py,sha256=DwUvf4HvS249i-g_ykayoDwxJnO7yH4pUJ7UhDE36iY,2400
transformers/models/yolos/__pycache__/__init__.cpython-311.pyc,,
transformers/models/yolos/__pycache__/configuration_yolos.cpython-311.pyc,,
transformers/models/yolos/__pycache__/convert_yolos_to_pytorch.cpython-311.pyc,,
transformers/models/yolos/__pycache__/feature_extraction_yolos.cpython-311.pyc,,
transformers/models/yolos/__pycache__/image_processing_yolos.cpython-311.pyc,,
transformers/models/yolos/__pycache__/modeling_yolos.cpython-311.pyc,,
transformers/models/yolos/configuration_yolos.py,sha256=pmfelaEqc6lAPdzkV9irKVjdd79vRLOKUKr8YBwDFf0,7784
transformers/models/yolos/convert_yolos_to_pytorch.py,sha256=g9sI7E-yfoyuXLc2OlN5bFxkc6ZTM243T1Wi8eUwnT0,11259
transformers/models/yolos/feature_extraction_yolos.py,sha256=0ebN1Be4y86C2yyN2rMQ9AbguEDjcQ7fkabropUpwcs,1481
transformers/models/yolos/image_processing_yolos.py,sha256=dyLNEdWSXOh-T32mD3Cxb6EZ7RYhp47m639n2vJrGE0,62259
transformers/models/yolos/modeling_yolos.py,sha256=ZPl7p9fZt_T-AtWOLQlRxuLKDAluIYW-9QNyrg2JQZg,58507
transformers/models/yoso/__init__.py,sha256=oV8Bo29EwsQRWVZy2nIaea2ArpOnhkENfp0nFfSKcB4,2074
transformers/models/yoso/__pycache__/__init__.cpython-311.pyc,,
transformers/models/yoso/__pycache__/configuration_yoso.cpython-311.pyc,,
transformers/models/yoso/__pycache__/convert_yoso_pytorch_to_pytorch.cpython-311.pyc,,
transformers/models/yoso/__pycache__/modeling_yoso.cpython-311.pyc,,
transformers/models/yoso/configuration_yoso.py,sha256=mampuz_78u0g-V3veEBg8A6RhO-4AwTAycg2HKv4cIU,6902
transformers/models/yoso/convert_yoso_pytorch_to_pytorch.py,sha256=VjPOSLINfkiaHx8M3dTNMdC8hXh3M1yyhIQ9t4Vzqk0,4115
transformers/models/yoso/modeling_yoso.py,sha256=he8pr83c64x7Hddf9eKETuWSAU55zBG4lNZ-0i8PTXs,55083
transformers/onnx/__init__.py,sha256=wALLY4TPOK2iPrFcfZf_WiEmTRAU6dAWHElxGdexr58,1548
transformers/onnx/__main__.py,sha256=JZ9ZmeRsnDitwTMWb-dFT8W9AEmMoMKLQ3SvbyCkY0w,9497
transformers/onnx/__pycache__/__init__.cpython-311.pyc,,
transformers/onnx/__pycache__/__main__.cpython-311.pyc,,
transformers/onnx/__pycache__/config.cpython-311.pyc,,
transformers/onnx/__pycache__/convert.cpython-311.pyc,,
transformers/onnx/__pycache__/features.cpython-311.pyc,,
transformers/onnx/__pycache__/utils.cpython-311.pyc,,
transformers/onnx/config.py,sha256=zPDgC_HSLmMeqPkcLv_Y8EfbfLLEDLqPrvrfQCRyhl8,32556
transformers/onnx/convert.py,sha256=ZSh9jQE6B6cCxhlSbKLHxNmj48HkXXdl-HF7iGtZy5k,19369
transformers/onnx/features.py,sha256=GSuwZj760THxAkDmJYROt43La0GaY-bA19j2bE-XYVI,28264
transformers/onnx/utils.py,sha256=39Uw_GkFBsTb6ZvMIHRTnI289aQDhc6hwfEapaBGE-o,3625
transformers/optimization.py,sha256=ZbiBbFkZNgBJt6bPNIwwUsDCpWb1khyAqOcHVsJrwv8,32390
transformers/optimization_tf.py,sha256=HCVXeXok1IdVtFxO_SodBQ2TAvfkF_YkhdU7hXuy9Dg,16855
transformers/pipelines/__init__.py,sha256=tfx9qUgbL5LEVBxa_Hxm3gWequpQjpFZ_k_2fBldCVg,51197
transformers/pipelines/__pycache__/__init__.cpython-311.pyc,,
transformers/pipelines/__pycache__/audio_classification.cpython-311.pyc,,
transformers/pipelines/__pycache__/audio_utils.cpython-311.pyc,,
transformers/pipelines/__pycache__/automatic_speech_recognition.cpython-311.pyc,,
transformers/pipelines/__pycache__/base.cpython-311.pyc,,
transformers/pipelines/__pycache__/conversational.cpython-311.pyc,,
transformers/pipelines/__pycache__/depth_estimation.cpython-311.pyc,,
transformers/pipelines/__pycache__/document_question_answering.cpython-311.pyc,,
transformers/pipelines/__pycache__/feature_extraction.cpython-311.pyc,,
transformers/pipelines/__pycache__/fill_mask.cpython-311.pyc,,
transformers/pipelines/__pycache__/image_classification.cpython-311.pyc,,
transformers/pipelines/__pycache__/image_feature_extraction.cpython-311.pyc,,
transformers/pipelines/__pycache__/image_segmentation.cpython-311.pyc,,
transformers/pipelines/__pycache__/image_to_image.cpython-311.pyc,,
transformers/pipelines/__pycache__/image_to_text.cpython-311.pyc,,
transformers/pipelines/__pycache__/mask_generation.cpython-311.pyc,,
transformers/pipelines/__pycache__/object_detection.cpython-311.pyc,,
transformers/pipelines/__pycache__/pt_utils.cpython-311.pyc,,
transformers/pipelines/__pycache__/question_answering.cpython-311.pyc,,
transformers/pipelines/__pycache__/table_question_answering.cpython-311.pyc,,
transformers/pipelines/__pycache__/text2text_generation.cpython-311.pyc,,
transformers/pipelines/__pycache__/text_classification.cpython-311.pyc,,
transformers/pipelines/__pycache__/text_generation.cpython-311.pyc,,
transformers/pipelines/__pycache__/text_to_audio.cpython-311.pyc,,
transformers/pipelines/__pycache__/token_classification.cpython-311.pyc,,
transformers/pipelines/__pycache__/video_classification.cpython-311.pyc,,
transformers/pipelines/__pycache__/visual_question_answering.cpython-311.pyc,,
transformers/pipelines/__pycache__/zero_shot_audio_classification.cpython-311.pyc,,
transformers/pipelines/__pycache__/zero_shot_classification.cpython-311.pyc,,
transformers/pipelines/__pycache__/zero_shot_image_classification.cpython-311.pyc,,
transformers/pipelines/__pycache__/zero_shot_object_detection.cpython-311.pyc,,
transformers/pipelines/audio_classification.py,sha256=bWia-wQ7hNfj0RsR7BuG7Yq_B1-Vwka7E-xVVAZB820,8821
transformers/pipelines/audio_utils.py,sha256=x5JXEWedeMlYcz32JS5HLWBTpy0FPXJvCns_WnXYOnA,9137
transformers/pipelines/automatic_speech_recognition.py,sha256=XflNLYZBme2OkyN-ULdybk2-1O_RQ3CDZjw8xHJkxgQ,37545
transformers/pipelines/base.py,sha256=6oR96xyGIlBRiTB4l5hejQIW0iayjTRXQIsd4rfqcFk,54621
transformers/pipelines/conversational.py,sha256=KhvJYw3_XejRSt7VKeA2gdJvKTegGpxBIa63U-BCp44,15011
transformers/pipelines/depth_estimation.py,sha256=cghYx32OHn4xlqFSlzQ8ryA8fyDC7dt6c-X3ll8xEkA,4477
transformers/pipelines/document_question_answering.py,sha256=Wg1xNxSB2dRC4Ocjaq4VGa7CC7pTXifm4Dww28QuQt4,23515
transformers/pipelines/feature_extraction.py,sha256=9MgsT72_ssUW0ZRPyTobCxe4q4-uT6_xainWe8-NYLo,3373
transformers/pipelines/fill_mask.py,sha256=jnZMK5aZyxlttXtzUISh3ZgvbcI7dIj-nB3Fk37N7Qw,11634
transformers/pipelines/image_classification.py,sha256=VZgMpoN0Q0wVvdRRSVkzn_B_B6BonvgUA3-ptjVl6w0,8591
transformers/pipelines/image_feature_extraction.py,sha256=tmOpSL9d7TaUf-Wmn2gbS9acHI95_8msb889bl4-Ro8,3952
transformers/pipelines/image_segmentation.py,sha256=ABQM2DBouXYAqQyvofMvybwcVLRdM-YqrHsM6yKJf_s,9124
transformers/pipelines/image_to_image.py,sha256=phQzbKf01swnGcSfWcm3dQ4ZMrxIW99s8_HTQj533ts,4938
transformers/pipelines/image_to_text.py,sha256=nUP_-skkV-nsfhd3q6ojlns_427lCKzELSN-qr8kMxY,8197
transformers/pipelines/mask_generation.py,sha256=kJtIjpCHPouBeLD88JpSV1lROXLctgY7Bqy3XFJ_Jj0,13108
transformers/pipelines/object_detection.py,sha256=TFPHpG6u1cdxvvM_XEv7eXo79KV8_aobOuRsh47IBpM,7931
transformers/pipelines/pt_utils.py,sha256=-ZGaHid7ln3Q3UUQxUAYEWup6qESu8UJ3aZMLazddfI,12613
transformers/pipelines/question_answering.py,sha256=BMmqntQHVdDukTmluGTKYnZnfbcy8EKYZE31nmaE06U,29886
transformers/pipelines/table_question_answering.py,sha256=acfhWxHYRCNiMjx4Qv3XKsgBlVZFkK6eKnKCx_n1G3E,19942
transformers/pipelines/text2text_generation.py,sha256=XfaCd5zKtAWfmZEIi8FfWFGWDqkbBoFliJw73kw6Q2c,17230
transformers/pipelines/text_classification.py,sha256=cgFIdqKgECDotbgU4ZBMq8KqSfyhzq2M2xxdDXAVgeE,10437
transformers/pipelines/text_generation.py,sha256=fIUpe6FkR6NpDs6SuBRLcLxunfGWvTZt8vs8sD83_F0,18203
transformers/pipelines/text_to_audio.py,sha256=rM_dkLd07pind_jTwv4ChoWmeuKJSrRjg3_iEMGbrZQ,8186
transformers/pipelines/token_classification.py,sha256=nw-DEE_Pw8gZHjYi3xAONcLcAIQikgwrJRpchq6PxtU,26713
transformers/pipelines/video_classification.py,sha256=8uAZl67psSn4pwWgV2NZIccxv-NfgSgdpo1_-KB8oA0,5058
transformers/pipelines/visual_question_answering.py,sha256=zqeQD0brkNxNHLsclZXc3bvybUoqsBhwPREaxumthJo,6779
transformers/pipelines/zero_shot_audio_classification.py,sha256=2nKSfId2lC5wshViAMbSzS-iD-r53r8gOGzi_CNl3XI,6561
transformers/pipelines/zero_shot_classification.py,sha256=WLgjtF0fOEaCiQb9QUu9vcNfJLP9M5nRnJGTgXgRKKU,12347
transformers/pipelines/zero_shot_image_classification.py,sha256=gTo4C1fMa_Ljhing7OMUf7pxX7NH8Wert-tO-2CRybY,6844
transformers/pipelines/zero_shot_object_detection.py,sha256=MBs9217WUE3Fc_Jdc-gOtO2i38B3-2yVxAsnlXaVyks,9472
transformers/processing_utils.py,sha256=-gERmK7YE0yI0gA_Xw1A0-E5ZWM2-V2kTKTmZ1P3OtI,22729
transformers/pytorch_utils.py,sha256=fx3m75h4BmJuVDfaMHvqODB5Q_G3io82WJ_HcHyA8Dw,11856
transformers/quantizers/__init__.py,sha256=hCprQnoI20-O1FSMSRgD-P9_NKEzN7kEfY66_BrQxz0,699
transformers/quantizers/__pycache__/__init__.cpython-311.pyc,,
transformers/quantizers/__pycache__/auto.cpython-311.pyc,,
transformers/quantizers/__pycache__/base.cpython-311.pyc,,
transformers/quantizers/__pycache__/quantizer_aqlm.cpython-311.pyc,,
transformers/quantizers/__pycache__/quantizer_awq.cpython-311.pyc,,
transformers/quantizers/__pycache__/quantizer_bnb_4bit.cpython-311.pyc,,
transformers/quantizers/__pycache__/quantizer_bnb_8bit.cpython-311.pyc,,
transformers/quantizers/__pycache__/quantizer_gptq.cpython-311.pyc,,
transformers/quantizers/__pycache__/quantizers_utils.cpython-311.pyc,,
transformers/quantizers/auto.py,sha256=DEQL0iNH-cI8Yik-IGpkFDcJGS-cLaNnUPVx9adooH0,6724
transformers/quantizers/base.py,sha256=s_6s3LukvQDFD1ZDOPP3UXm-HSt7FP6p8btdrHcRQVE,8704
transformers/quantizers/quantizer_aqlm.py,sha256=_dZPM69a9Iey9TUvfpQPRWkGNDMxkvMXEYmTHm_jM5M,3194
transformers/quantizers/quantizer_awq.py,sha256=beE6ry7KoFO83Q5yIVbVnW1N-VH1Q-loxre3sAi7nmM,4652
transformers/quantizers/quantizer_bnb_4bit.py,sha256=xm46vknEu9InpgdYNZkEO61bLRJ2iD9Fh8BY-pP_Hp8,14402
transformers/quantizers/quantizer_bnb_8bit.py,sha256=Acv4Dbcw95DkHmGaRp5OGnB-Iqxehjrvi13oeSDDvKI,12380
transformers/quantizers/quantizer_gptq.py,sha256=ZWNQY2WF6mzUV-SwYg1PZIM0kZ3JJyYGe3gF2mZcZ58,3878
transformers/quantizers/quantizers_utils.py,sha256=6bgmf8mLxow6gXonTFX7PLfqFsf6plUj7DOeXnXhwMM,1066
transformers/safetensors_conversion.py,sha256=ckxzVoyWtP5XmwuiS9KfmP-EoEwWi09GZeX09a68rpM,4315
transformers/sagemaker/__init__.py,sha256=fKtKAHamz_CLL9jPGCa2E-1n8RmuS-58qGtzZuKc3qg,730
transformers/sagemaker/__pycache__/__init__.cpython-311.pyc,,
transformers/sagemaker/__pycache__/trainer_sm.cpython-311.pyc,,
transformers/sagemaker/__pycache__/training_args_sm.cpython-311.pyc,,
transformers/sagemaker/trainer_sm.py,sha256=7GsKLtjdMfKp98OwHD7RcBsl745OOwHAaBswkfLkfsE,1044
transformers/sagemaker/training_args_sm.py,sha256=4ZnQhITfMwT0y2Y2MvkI11PEB_yfTX5Z7WrPKt0VXD8,5389
transformers/testing_utils.py,sha256=H9nUiDO-j17AbV6eDto1Mm3ZvrCahJ2lMsZSnP03bew,81734
transformers/tf_utils.py,sha256=9TlTj8qlWobJ0e-lNx47m3Pu1eDY6S6dm5AIIekyNtw,10091
transformers/time_series_utils.py,sha256=LjOgIvLmP0v6fJoqGo8lCD1kr3sXx9O_jmI-qJejtPU,7520
transformers/tokenization_utils.py,sha256=SuyV-6xCXMhOqDdXExtGeXWUkjWt4gV3fz3PWjbjkuA,44595
transformers/tokenization_utils_base.py,sha256=oW0hVfG4oovsladrA-7Yuz1DpMRjfk9UXODW6MWk420,198090
transformers/tokenization_utils_fast.py,sha256=tpErvsUzI0RSiZJJtdmi7LbEuIltXnul9FrhAFCuIoM,37523
transformers/tools/__init__.py,sha256=hI6M7zNUTyRE3BiZtL1VM8CcpYqxTrFR7lS0U6T7InM,2955
transformers/tools/__pycache__/__init__.cpython-311.pyc,,
transformers/tools/__pycache__/agent_types.cpython-311.pyc,,
transformers/tools/__pycache__/agents.cpython-311.pyc,,
transformers/tools/__pycache__/base.cpython-311.pyc,,
transformers/tools/__pycache__/document_question_answering.cpython-311.pyc,,
transformers/tools/__pycache__/evaluate_agent.cpython-311.pyc,,
transformers/tools/__pycache__/image_captioning.cpython-311.pyc,,
transformers/tools/__pycache__/image_question_answering.cpython-311.pyc,,
transformers/tools/__pycache__/image_segmentation.cpython-311.pyc,,
transformers/tools/__pycache__/prompts.cpython-311.pyc,,
transformers/tools/__pycache__/python_interpreter.cpython-311.pyc,,
transformers/tools/__pycache__/speech_to_text.cpython-311.pyc,,
transformers/tools/__pycache__/text_classification.cpython-311.pyc,,
transformers/tools/__pycache__/text_question_answering.cpython-311.pyc,,
transformers/tools/__pycache__/text_summarization.cpython-311.pyc,,
transformers/tools/__pycache__/text_to_speech.cpython-311.pyc,,
transformers/tools/__pycache__/translation.cpython-311.pyc,,
transformers/tools/agent_types.py,sha256=6ZVzmPwWiMtJXKUZ33fKzfUFp-v_qfI901MKj2pbQRY,9093
transformers/tools/agents.py,sha256=1t7eUTYriK4jIQMFcJvtYzsivDR3XEkeaFv_LcFVhCo,30737
transformers/tools/base.py,sha256=b575ve6Vy3ih8dTzxpeuxYMdbUJDErgyz1grwmaiSbk,29625
transformers/tools/document_question_answering.py,sha256=7qSMr0fQYadiGOoVMXNrImls3_O-hcdDbLrlSc3cvxU,3337
transformers/tools/evaluate_agent.py,sha256=JvMKk9NoJLZTRnY_VAC_cSHWAO-Rx-Dl8Vt31kpBbfw,24721
transformers/tools/image_captioning.py,sha256=x1PfWpDozWSZuue633XwEPPBTr_zEX9mgrYar-8LqXQ,1745
transformers/tools/image_question_answering.py,sha256=UNOzIcmkckh1W1bqlj31h61eXGAZ1TZ831iqytyO4NQ,1969
transformers/tools/image_segmentation.py,sha256=1BbHSYTz3q8DlTMHBnKdibp7JCHZydPdNoyl7TObfN8,2103
transformers/tools/prompts.py,sha256=1YXY_A5Zfyd_rudKzB4ShQ9OR_E5bHeh9bcgBVt1ltQ,1558
transformers/tools/python_interpreter.py,sha256=aSn1bnuQT9_xteXNcJdlmi39IzX1FZRqSaoGEQRS-PE,9999
transformers/tools/speech_to_text.py,sha256=m3LCJxMpJykL9aD8rZ4H3ROGtt59LcLozw-6963XjCE,1482
transformers/tools/text_classification.py,sha256=snyBoLTERnfl7YKKAgZctWhow6sEXQdS4bcWYUxJnyU,2475
transformers/tools/text_question_answering.py,sha256=mGO3E0nL71Jzn4jeC2_RgLRDtuqbld77mQ2T7jw4aPc,1967
transformers/tools/text_summarization.py,sha256=-8TY4P4LL4c7bQcD9y8Vi5Rfiaw8nAiY_aP5yXicq_g,1691
transformers/tools/text_to_speech.py,sha256=vuJU2dC2d5J1kVdGjSBJCBdsTiOli2J7OabAposOFfA,2424
transformers/tools/translation.py,sha256=fu05jVYbJUFmNvmwd4mjQOqzGt1JSy6QbpuAd2uChOE,8493
transformers/trainer.py,sha256=dXAmaXmNIh-svZVTskq1ja1kOxYj4CMLsk6LPl1lGFM,201853
transformers/trainer_callback.py,sha256=Ycb7NEhtg0fBKoobRZkrbAtunzljvBDKjYDNlllO9jc,24890
transformers/trainer_pt_utils.py,sha256=xTdey1hg9EAJibBI7mkaJNYCYe3WhK1ZAWubm5YNddU,53040
transformers/trainer_seq2seq.py,sha256=EmIOCWcX12AQhz5EGc5FX652c0f5jNfvEU0wOYkSWDU,16409
transformers/trainer_utils.py,sha256=rzA-wV4G54nWVzr0vjWUZK5ZNm_PHWS9-h-inCN35nA,27325
transformers/training_args.py,sha256=58GJcN0wGLNtJDq9lIsonhKTDfIMPySVDwguJYujzr8,140716
transformers/training_args_seq2seq.py,sha256=k8qyPQAo5GWlcToN3tnzW7dE4xyP7i7HRjP_sgxlllA,4308
transformers/training_args_tf.py,sha256=esUsNAj6kNNMu1LJLxfELJAJiTq7HD6fHz3GvI_mKJg,14570
transformers/utils/__init__.py,sha256=WLilfyz7wl59WOdg6B_DiTeBrGpXHR8rCBkuFhm2PNQ,7472
transformers/utils/__pycache__/__init__.cpython-311.pyc,,
transformers/utils/__pycache__/backbone_utils.cpython-311.pyc,,
transformers/utils/__pycache__/bitsandbytes.cpython-311.pyc,,
transformers/utils/__pycache__/constants.cpython-311.pyc,,
transformers/utils/__pycache__/doc.cpython-311.pyc,,
transformers/utils/__pycache__/dummy_detectron2_objects.cpython-311.pyc,,
transformers/utils/__pycache__/dummy_essentia_and_librosa_and_pretty_midi_and_scipy_and_torch_objects.cpython-311.pyc,,
transformers/utils/__pycache__/dummy_flax_objects.cpython-311.pyc,,
transformers/utils/__pycache__/dummy_keras_nlp_objects.cpython-311.pyc,,
transformers/utils/__pycache__/dummy_music_objects.cpython-311.pyc,,
transformers/utils/__pycache__/dummy_pt_objects.cpython-311.pyc,,
transformers/utils/__pycache__/dummy_sentencepiece_and_tokenizers_objects.cpython-311.pyc,,
transformers/utils/__pycache__/dummy_sentencepiece_objects.cpython-311.pyc,,
transformers/utils/__pycache__/dummy_speech_objects.cpython-311.pyc,,
transformers/utils/__pycache__/dummy_tensorflow_text_objects.cpython-311.pyc,,
transformers/utils/__pycache__/dummy_tf_objects.cpython-311.pyc,,
transformers/utils/__pycache__/dummy_tokenizers_objects.cpython-311.pyc,,
transformers/utils/__pycache__/dummy_vision_objects.cpython-311.pyc,,
transformers/utils/__pycache__/fx.cpython-311.pyc,,
transformers/utils/__pycache__/generic.cpython-311.pyc,,
transformers/utils/__pycache__/hp_naming.cpython-311.pyc,,
transformers/utils/__pycache__/hub.cpython-311.pyc,,
transformers/utils/__pycache__/import_utils.cpython-311.pyc,,
transformers/utils/__pycache__/logging.cpython-311.pyc,,
transformers/utils/__pycache__/model_parallel_utils.cpython-311.pyc,,
transformers/utils/__pycache__/notebook.cpython-311.pyc,,
transformers/utils/__pycache__/peft_utils.cpython-311.pyc,,
transformers/utils/__pycache__/quantization_config.cpython-311.pyc,,
transformers/utils/__pycache__/sentencepiece_model_pb2.cpython-311.pyc,,
transformers/utils/__pycache__/sentencepiece_model_pb2_new.cpython-311.pyc,,
transformers/utils/__pycache__/versions.cpython-311.pyc,,
transformers/utils/backbone_utils.py,sha256=ruggZsHu9IJ3IVPa4Dvvvqx9Sj1mB-8P24C2VV7RPTo,16309
transformers/utils/bitsandbytes.py,sha256=LzOKwcHWAxxZZv-7Ts9Q0vlEYvHd18affVgVbiR3Tzs,1040
transformers/utils/constants.py,sha256=sZsUwOnA3CbtN1svs9YoaNLTTsAc9RVaITsgpf8K4iI,282
transformers/utils/doc.py,sha256=eObKDEpC1z-05BNXHi1hYNjQMPsWSN1SNMa7IFkRmN8,40737
transformers/utils/dummy_detectron2_objects.py,sha256=gr1gUAeK_j7ygeFW2X4_aZYy9QmY-MKqH1k7UlQxyIk,391
transformers/utils/dummy_essentia_and_librosa_and_pretty_midi_and_scipy_and_torch_objects.py,sha256=n6pY4s7zCII3dzo7Ejd0RviHa_pMateuDEwbbHgsTUY,902
transformers/utils/dummy_flax_objects.py,sha256=ANFq3CYhCByAWqcFIY2z-DzVNizlaH6oGSMX0XmIz_Y,33561
transformers/utils/dummy_keras_nlp_objects.py,sha256=AVWt2orICCUXi754bkavvqPzYO91PjER-FlUZAw2jZc,294
transformers/utils/dummy_music_objects.py,sha256=1lxIebYUOdHJWMQ_T5IQgPgcO_wp_8YM_HGc3skuGVg,458
transformers/utils/dummy_pt_objects.py,sha256=QyU-V31luDGOKsntHJHn8XhtN3v4Qf9ekyE-7nHBVlQ,223372
transformers/utils/dummy_sentencepiece_and_tokenizers_objects.py,sha256=BgPLr8Wz8A-17K86x04N21CKXtWNQLJEWx2c4aZRqaA,286
transformers/utils/dummy_sentencepiece_objects.py,sha256=-N-N1tqDIndSFORF8jdVN4zRJOo-94Kf-ezgS43Xp6w,6282
transformers/utils/dummy_speech_objects.py,sha256=9eFm1cjdsYOPBoAz9JTgP35Bg8WF2C9AZ_y1hFpKZdQ,465
transformers/utils/dummy_tensorflow_text_objects.py,sha256=43V0IA2kb9gtuL0S1OL1eRFFxzQwKg4pPjMVuXUB5qg,306
transformers/utils/dummy_tf_objects.py,sha256=iiGT_sxe3L0ScWLBo_Vruon6exAvJsYR6kYkzpU-QF8,68060
transformers/utils/dummy_tokenizers_objects.py,sha256=hm2wde4sVtEhCXMzeYtZKXOv4-dYfroma-F52gbe0ko,11112
transformers/utils/dummy_vision_objects.py,sha256=YBNIRZIsD0kioQ7_tlThp6VLtnQdGCGtD-k4_J91nD0,14047
transformers/utils/fx.py,sha256=KwgL17yvcCAYHtQwb3lKs7b-a1D1zWgukhORoWuZd44,50302
transformers/utils/generic.py,sha256=Tw0k83HY2Ri1fThvq879Ndj29CtKrBEV0jU2BJR2cao,23505
transformers/utils/hp_naming.py,sha256=vqcOXcDOyqbISWo8-ClUJUOBVbZM1h08EcymTwcRthc,4979
transformers/utils/hub.py,sha256=oD3BobiQ1NrqN1Fu7oGKFav735aHa-yIJDIDJIaLdXo,55839
transformers/utils/import_utils.py,sha256=TGTeK-IlBUK6qtVCoFoTIYeh-IkVZto5ToPOhGiM4JE,49551
transformers/utils/logging.py,sha256=X6FDZSn9Vbo81QHn80TGVsk9LGHe4OdWDCTCnCF5V7A,11609
transformers/utils/model_parallel_utils.py,sha256=XbGU9IlFF59K_aplRxUGVnTfIZ9mpbLomKqQ08ooTew,2272
transformers/utils/notebook.py,sha256=PiEiHpfuqxd3M1U3MPD8bmeO8bvtTbLfOxnL-cZWHQY,15558
transformers/utils/peft_utils.py,sha256=as1XSRYa4-skewnlVom74qb-vgoZkGJtcXeNEUndAlo,5217
transformers/utils/quantization_config.py,sha256=RFOGQspGMW_eoYLwX6Muh7m48Q6w-MpRKDb8BophmJk,36927
transformers/utils/sentencepiece_model_pb2.py,sha256=XiQs9uMEusfAZP6t6IBuTTX9yl7LiOyJEi7Ib-Wzmq0,50677
transformers/utils/sentencepiece_model_pb2_new.py,sha256=FwTW0nkCiPCErmGk0s27BniKmkORcfnNk-w7NBGkCuA,6621
transformers/utils/versions.py,sha256=C-Tqr4qGSHH64ygIBCSo8gA6azz7Dbzh8zdc_yjMkX8,4337
