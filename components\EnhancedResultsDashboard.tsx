"use client"

import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/hooks/use-auth';
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell
} from 'recharts';
import {
  FileText,
  Download,
  Eye,
  Filter,
  RefreshCw,
  TrendingUp,
  Users,
  CheckCircle,
  AlertCircle,
  Clock
} from 'lucide-react';

interface ExamResult {
  id: string;
  original_filename: string;
  exam_name: string;
  exam_type: string;
  subject: string;
  class_name: string;
  status: string;
  upload_time: string;
  processing_completed_at?: string;
  file_size: number;
  ocr_confidence?: number;
  ocr_model_used?: string;
  grade_summary?: {
    score: number;
    max_score: number;
    percentage: number;
    letter_grade: string;
    status: string;
  };
}

interface DashboardStats {
  total_exams: number;
  processed_exams: number;
  graded_exams: number;
  average_score: number;
  pass_rate: number;
}

const EnhancedResultsDashboard: React.FC = () => {
  const [exams, setExams] = useState<ExamResult[]>([]);
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [filter, setFilter] = useState({
    status: 'all',
    exam_type: 'all',
    subject: 'all'
  });

  const { toast } = useToast();
  const { accessToken } = useAuth();

  const statusColors = {
    uploaded: 'bg-blue-100 text-blue-800',
    processing: 'bg-yellow-100 text-yellow-800',
    processed: 'bg-green-100 text-green-800',
    graded: 'bg-purple-100 text-purple-800',
    completed: 'bg-green-100 text-green-800',
    error: 'bg-red-100 text-red-800'
  };

  const gradeColors = {
    A: '#22c55e',
    B: '#84cc16',
    C: '#eab308',
    D: '#f97316',
    F: '#ef4444'
  };

  useEffect(() => {
    fetchExams();
  }, [filter]);

  const fetchExams = async () => {
    try {
      setLoading(true);
      
      const params = new URLSearchParams();
      if (filter.status !== 'all') params.append('status_filter', filter.status);
      if (filter.exam_type !== 'all') params.append('exam_type_filter', filter.exam_type);
      
      const response = await fetch(`/api/v2/exams?${params.toString()}`, {
        headers: {
          'Authorization': `Bearer ${accessToken}`
        }
      });

      if (!response.ok) {
        throw new Error('Failed to fetch exams');
      }

      const data = await response.json();
      setExams(data.exams || []);
      
      // Calculate stats
      calculateStats(data.exams || []);
      
    } catch (error) {
      console.error('Error fetching exams:', error);
      toast({
        title: "Error",
        description: "Failed to fetch exam results",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  const calculateStats = (examList: ExamResult[]) => {
    const total = examList.length;
    const processed = examList.filter(e => ['processed', 'graded', 'completed'].includes(e.status)).length;
    const graded = examList.filter(e => e.grade_summary).length;
    
    const gradedExams = examList.filter(e => e.grade_summary);
    const totalScore = gradedExams.reduce((sum, e) => sum + (e.grade_summary?.percentage || 0), 0);
    const averageScore = graded > 0 ? totalScore / graded : 0;
    
    const passedExams = gradedExams.filter(e => (e.grade_summary?.percentage || 0) >= 60).length;
    const passRate = graded > 0 ? (passedExams / graded) * 100 : 0;

    setStats({
      total_exams: total,
      processed_exams: processed,
      graded_exams: graded,
      average_score: averageScore,
      pass_rate: passRate
    });
  };

  const getGradeDistribution = () => {
    const distribution: Record<string, number> = { A: 0, B: 0, C: 0, D: 0, F: 0 };
    
    exams.forEach(exam => {
      if (exam.grade_summary?.letter_grade) {
        const grade = exam.grade_summary.letter_grade;
        if (grade in distribution) {
          distribution[grade]++;
        }
      }
    });

    return Object.entries(distribution).map(([grade, count]) => ({
      name: grade,
      value: count,
      color: gradeColors[grade as keyof typeof gradeColors]
    }));
  };

  const getScoreDistribution = () => {
    const ranges = [
      { name: '90-100%', min: 90, max: 100, count: 0 },
      { name: '80-89%', min: 80, max: 89, count: 0 },
      { name: '70-79%', min: 70, max: 79, count: 0 },
      { name: '60-69%', min: 60, max: 69, count: 0 },
      { name: '0-59%', min: 0, max: 59, count: 0 }
    ];

    exams.forEach(exam => {
      if (exam.grade_summary?.percentage !== undefined) {
        const percentage = exam.grade_summary.percentage;
        const range = ranges.find(r => percentage >= r.min && percentage <= r.max);
        if (range) range.count++;
      }
    });

    return ranges;
  };

  const exportResults = async (format: 'csv' | 'pdf') => {
    try {
      const examIds = exams.map(e => e.id);
      
      const response = await fetch('/api/v2/export', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          exam_ids: examIds,
          format,
          include_details: true
        })
      });

      if (!response.ok) {
        throw new Error('Export failed');
      }

      // Handle file download
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `exam_results.${format}`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);

      toast({
        title: "Success",
        description: `Results exported as ${format.toUpperCase()}`,
        variant: "default"
      });

    } catch (error) {
      console.error('Export error:', error);
      toast({
        title: "Error",
        description: "Failed to export results",
        variant: "destructive"
      });
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const formatFileSize = (bytes: number) => {
    const mb = bytes / (1024 * 1024);
    return `${mb.toFixed(2)} MB`;
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <RefreshCw className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Stats Overview */}
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Total Exams</p>
                  <p className="text-2xl font-bold">{stats.total_exams}</p>
                </div>
                <FileText className="h-8 w-8 text-blue-500" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Processed</p>
                  <p className="text-2xl font-bold">{stats.processed_exams}</p>
                </div>
                <CheckCircle className="h-8 w-8 text-green-500" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Graded</p>
                  <p className="text-2xl font-bold">{stats.graded_exams}</p>
                </div>
                <TrendingUp className="h-8 w-8 text-purple-500" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Avg Score</p>
                  <p className="text-2xl font-bold">{stats.average_score.toFixed(1)}%</p>
                </div>
                <Users className="h-8 w-8 text-orange-500" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Pass Rate</p>
                  <p className="text-2xl font-bold">{stats.pass_rate.toFixed(1)}%</p>
                </div>
                <CheckCircle className="h-8 w-8 text-green-500" />
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Score Distribution</CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={getScoreDistribution()}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="name" />
                <YAxis />
                <Tooltip />
                <Bar dataKey="count" fill="#8884d8" />
              </BarChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Grade Distribution</CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={getGradeDistribution()}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  label={({ name, value }) => `${name}: ${value}`}
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="value"
                >
                  {getGradeDistribution().map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip />
              </PieChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>

      {/* Filters and Actions */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle>Exam Results</CardTitle>
            <div className="flex gap-2">
              <Button variant="outline" size="sm" onClick={() => exportResults('csv')}>
                <Download className="h-4 w-4 mr-2" />
                Export CSV
              </Button>
              <Button variant="outline" size="sm" onClick={() => exportResults('pdf')}>
                <Download className="h-4 w-4 mr-2" />
                Export PDF
              </Button>
              <Button variant="outline" size="sm" onClick={fetchExams}>
                <RefreshCw className="h-4 w-4 mr-2" />
                Refresh
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {/* Filter Controls */}
          <div className="flex gap-4 mb-6">
            <select
              value={filter.status}
              onChange={(e) => setFilter({ ...filter, status: e.target.value })}
              className="px-3 py-2 border rounded-md"
            >
              <option value="all">All Status</option>
              <option value="uploaded">Uploaded</option>
              <option value="processing">Processing</option>
              <option value="processed">Processed</option>
              <option value="graded">Graded</option>
              <option value="completed">Completed</option>
              <option value="error">Error</option>
            </select>

            <select
              value={filter.exam_type}
              onChange={(e) => setFilter({ ...filter, exam_type: e.target.value })}
              className="px-3 py-2 border rounded-md"
            >
              <option value="all">All Types</option>
              <option value="qcm">QCM</option>
              <option value="handwritten">Handwritten</option>
              <option value="mixed">Mixed</option>
              <option value="essay">Essay</option>
            </select>
          </div>

          {/* Results Table */}
          <div className="overflow-x-auto">
            <table className="w-full border-collapse">
              <thead>
                <tr className="border-b">
                  <th className="text-left p-2">Exam</th>
                  <th className="text-left p-2">Type</th>
                  <th className="text-left p-2">Subject</th>
                  <th className="text-left p-2">Status</th>
                  <th className="text-left p-2">Score</th>
                  <th className="text-left p-2">Grade</th>
                  <th className="text-left p-2">Uploaded</th>
                  <th className="text-left p-2">Actions</th>
                </tr>
              </thead>
              <tbody>
                {exams.map((exam) => (
                  <tr key={exam.id} className="border-b hover:bg-gray-50">
                    <td className="p-2">
                      <div>
                        <div className="font-medium">{exam.exam_name || exam.original_filename}</div>
                        <div className="text-sm text-gray-500">{exam.class_name}</div>
                      </div>
                    </td>
                    <td className="p-2">
                      <Badge variant="outline">{exam.exam_type.toUpperCase()}</Badge>
                    </td>
                    <td className="p-2">{exam.subject || '-'}</td>
                    <td className="p-2">
                      <Badge className={statusColors[exam.status as keyof typeof statusColors] || 'bg-gray-100 text-gray-800'}>
                        {exam.status}
                      </Badge>
                    </td>
                    <td className="p-2">
                      {exam.grade_summary ? (
                        <div className="flex items-center gap-2">
                          <span>{exam.grade_summary.score}/{exam.grade_summary.max_score}</span>
                          <span className="text-sm text-gray-500">
                            ({exam.grade_summary.percentage.toFixed(1)}%)
                          </span>
                        </div>
                      ) : (
                        '-'
                      )}
                    </td>
                    <td className="p-2">
                      {exam.grade_summary?.letter_grade ? (
                        <Badge 
                          style={{ 
                            backgroundColor: gradeColors[exam.grade_summary.letter_grade as keyof typeof gradeColors],
                            color: 'white'
                          }}
                        >
                          {exam.grade_summary.letter_grade}
                        </Badge>
                      ) : (
                        '-'
                      )}
                    </td>
                    <td className="p-2 text-sm text-gray-500">
                      {formatDate(exam.upload_time)}
                    </td>
                    <td className="p-2">
                      <div className="flex gap-1">
                        <Button variant="ghost" size="sm">
                          <Eye className="h-4 w-4" />
                        </Button>
                        <Button variant="ghost" size="sm">
                          <Download className="h-4 w-4" />
                        </Button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          {exams.length === 0 && (
            <div className="text-center py-8 text-gray-500">
              No exam results found
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default EnhancedResultsDashboard;
