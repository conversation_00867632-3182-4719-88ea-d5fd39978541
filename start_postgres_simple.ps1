# Script simple pour demarrer PostgreSQL avec Docker
Write-Host "Demarrage PostgreSQL avec Docker..." -ForegroundColor Green

# Verifier si Docker est disponible
try {
    docker --version | Out-Null
    Write-Host "Docker trouve" -ForegroundColor Green
} catch {
    Write-Host "Docker non trouve. Veuillez installer Docker Desktop." -ForegroundColor Red
    exit 1
}

# Arreter les conteneurs existants
Write-Host "Arret des conteneurs existants..." -ForegroundColor Yellow
docker stop auto-grade-postgres 2>$null
docker rm auto-grade-postgres 2>$null

# Demarrer PostgreSQL
Write-Host "Demarrage de PostgreSQL..." -ForegroundColor Yellow
docker run -d `
    --name auto-grade-postgres `
    -e POSTGRES_DB=gradegeniusdb `
    -e POSTGRES_USER=autograde `
    -e POSTGRES_PASSWORD=autograde123 `
    -p 5432:5432 `
    postgres:15

if ($LASTEXITCODE -eq 0) {
    Write-Host "PostgreSQL demarre avec succes" -ForegroundColor Green
} else {
    Write-Host "Erreur lors du demarrage de PostgreSQL" -ForegroundColor Red
    exit 1
}

# Attendre que PostgreSQL soit pret
Write-Host "Attente que PostgreSQL soit pret..." -ForegroundColor Yellow
Start-Sleep -Seconds 10

# Tester la connexion
Write-Host "Test de connexion..." -ForegroundColor Yellow
$maxAttempts = 10
$attempt = 0

do {
    $attempt++
    try {
        docker exec auto-grade-postgres pg_isready -U autograde -d gradegeniusdb 2>$null
        if ($LASTEXITCODE -eq 0) {
            Write-Host "PostgreSQL est pret!" -ForegroundColor Green
            break
        }
    } catch {
        # Continue a attendre
    }
    
    if ($attempt -ge $maxAttempts) {
        Write-Host "Timeout: PostgreSQL pas pret" -ForegroundColor Red
        exit 1
    }
    
    Write-Host "Tentative $attempt/$maxAttempts..." -ForegroundColor Yellow
    Start-Sleep -Seconds 3
} while ($true)

# Creer le fichier .env
Write-Host "Creation du fichier .env..." -ForegroundColor Yellow
$envContent = @"
# Configuration PostgreSQL pour Auto-Grade Scribe
ENVIRONMENT=development
DEBUG=true
DATABASE_URL=postgresql://autograde:autograde123@localhost:5432/gradegeniusdb
SECRET_KEY=your-secret-key-change-in-production
API_HOST=0.0.0.0
API_PORT=8000
CORS_ORIGINS=http://localhost:3000,http://127.0.0.1:3000
MAX_FILE_SIZE=52428800
UPLOAD_DIRECTORY=uploads
RESULTS_DIRECTORY=results
TEMP_DIRECTORY=temp
LOG_LEVEL=INFO
"@

$envContent | Out-File -FilePath ".env" -Encoding UTF8
Write-Host "Fichier .env cree" -ForegroundColor Green

Write-Host ""
Write-Host "PostgreSQL configure avec succes!" -ForegroundColor Green
Write-Host "Host: localhost" -ForegroundColor White
Write-Host "Port: 5432" -ForegroundColor White
Write-Host "Database: gradegeniusdb" -ForegroundColor White
Write-Host "User: autograde" -ForegroundColor White
Write-Host "Password: autograde123" -ForegroundColor White
Write-Host ""
Write-Host "Pour tester: python test_postgres_setup.py" -ForegroundColor Cyan
Write-Host "Pour demarrer l'app: cd backend && python -m uvicorn app:app --reload" -ForegroundColor Cyan
