#!/usr/bin/env python3
"""
Script de démarrage optimisé pour Auto-Grade Scribe Open-Source
Version 4.0.0 - 100% Open-Source
"""

import os
import sys
import subprocess
import time
import logging
from pathlib import Path

# Configuration du logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def check_python_version():
    """Vérifier la version de Python"""
    if sys.version_info < (3, 8):
        logger.error("❌ Python 3.8+ requis. Version actuelle: %s", sys.version)
        sys.exit(1)
    logger.info("✅ Python %s.%s.%s détecté", *sys.version_info[:3])

def check_dependencies():
    """Vérifier les dépendances"""
    try:
        import fastapi
        import uvicorn
        import sqlalchemy
        import psycopg2
        logger.info("✅ Dépendances principales installées")
        return True
    except ImportError as e:
        logger.error("❌ Dépendance manquante: %s", e)
        logger.info("💡 Installez les dépendances: pip install -r backend/requirements_opensource.txt")
        return False

def check_postgresql():
    """Vérifier la connexion PostgreSQL"""
    try:
        import psycopg2
        from backend.core.config import settings
        
        # Extraire les paramètres de connexion
        db_url = settings.database_url
        if db_url.startswith("postgresql://"):
            # Format: postgresql://user:password@host:port/database
            parts = db_url.replace("postgresql://", "").split("/")
            db_name = parts[1]
            user_host = parts[0].split("@")
            user_pass = user_host[0].split(":")
            host_port = user_host[1].split(":")
            
            user = user_pass[0]
            password = user_pass[1] if len(user_pass) > 1 else ""
            host = host_port[0]
            port = int(host_port[1]) if len(host_port) > 1 else 5432
            
            # Tester la connexion
            conn = psycopg2.connect(
                host=host,
                port=port,
                user=user,
                password=password,
                database=db_name
            )
            conn.close()
            logger.info("✅ Connexion PostgreSQL réussie")
            return True
            
    except Exception as e:
        logger.error("❌ Erreur PostgreSQL: %s", e)
        logger.info("💡 Démarrez PostgreSQL: docker-compose -f docker-compose-simple.yml up -d")
        return False

def create_directories():
    """Créer les répertoires nécessaires"""
    directories = [
        "uploads",
        "results", 
        "temp",
        "logs",
        "models/sentence_transformers",
        "models/transformers"
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
        logger.info("📁 Répertoire créé: %s", directory)

def start_application():
    """Démarrer l'application"""
    logger.info("🚀 Démarrage d'Auto-Grade Scribe Open-Source v4.0.0")
    
    # Changer vers le répertoire backend
    os.chdir("backend")
    
    # Démarrer avec uvicorn
    try:
        subprocess.run([
            sys.executable, "-m", "uvicorn",
            "main:app",
            "--host", "0.0.0.0",
            "--port", "8001",
            "--reload"
        ], check=True)
    except KeyboardInterrupt:
        logger.info("🛑 Application arrêtée par l'utilisateur")
    except subprocess.CalledProcessError as e:
        logger.error("❌ Erreur démarrage application: %s", e)

def main():
    """Fonction principale"""
    print("🆓 Auto-Grade Scribe Open-Source v4.0.0")
    print("=" * 50)
    print("✨ 100% Open-Source - 0€ de coûts d'API")
    print("🔍 OCR Multi-Provider: TrOCR + EasyOCR + PaddleOCR")
    print("🧠 IA de Correction: Sentence Transformers")
    print("📊 Base de données: PostgreSQL")
    print("=" * 50)
    
    # Vérifications préliminaires
    logger.info("🔍 Vérifications préliminaires...")
    
    check_python_version()
    
    if not check_dependencies():
        logger.error("❌ Dépendances manquantes. Installation requise.")
        sys.exit(1)
    
    create_directories()
    
    if not check_postgresql():
        logger.error("❌ PostgreSQL non accessible.")
        logger.info("💡 Commandes pour démarrer PostgreSQL:")
        logger.info("   docker-compose -f docker-compose-simple.yml up -d")
        logger.info("   Ou installez PostgreSQL localement")
        sys.exit(1)
    
    # Initialiser la base de données
    try:
        from backend.core.database import init_database
        init_database()
        logger.info("✅ Base de données initialisée")
    except Exception as e:
        logger.error("❌ Erreur initialisation base de données: %s", e)
        sys.exit(1)
    
    # Démarrer l'application
    logger.info("🎯 Toutes les vérifications passées!")
    logger.info("🌐 Application disponible sur: http://127.0.0.1:8001")
    logger.info("📚 Documentation API: http://127.0.0.1:8001/docs")
    logger.info("🔧 Interface admin: http://127.0.0.1:8001/admin")
    
    time.sleep(2)
    start_application()

if __name__ == "__main__":
    main()
