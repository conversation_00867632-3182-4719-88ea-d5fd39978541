"""
Application Auto-Grade Scribe Améliorée avec Solutions Open-Source
Version complète avec OCR avancé, correction IA open-source, et révision manuelle
"""

import os
import sys
import uuid
import logging
import asyncio
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, Optional

from fastapi import FastAPI, UploadFile, File, HTTPException, status, Depends
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from pydantic import BaseModel
import uvicorn

# Configuration du logging avancé
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/app.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("auto-grade-scribe")

# Ajouter le répertoire courant au path
sys.path.append(str(Path(__file__).parent))

# Importer les services améliorés
try:
    from services.enhanced_ocr_service import enhanced_ocr_service
    from services.intelligent_grading_service import intelligent_grading_service, grading_pipeline_service
    from services.database_service import init_database_service
    from services.manual_review_service import init_manual_review_service
    logger.info("Enhanced services imported successfully")
except ImportError as e:
    logger.warning(f"Could not import enhanced services: {e}")
    enhanced_ocr_service = None
    intelligent_grading_service = None
    grading_pipeline_service = None

# Modèles Pydantic pour les requêtes
class GradingRequest(BaseModel):
    file_id: str
    exam_type: str = "qcm"
    correct_answers: Dict[str, Any]
    grading_config: Optional[Dict[str, Any]] = None

class ManualReviewRequest(BaseModel):
    question_adjustments: Dict[str, Any]
    overall_comments: str = ""
    review_time_minutes: Optional[int] = None

class OCRRequest(BaseModel):
    file_id: str
    content_type: str = "mixed"
    force_reprocess: bool = False

# Créer l'application FastAPI
app = FastAPI(
    title="Auto Grade Scribe Open-Source",
    description="Application de correction automatique d'examens avec IA open-source et PostgreSQL",
    version="3.1.0"
)

# Configuration CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Créer les répertoires nécessaires
for directory in ["uploads", "results", "temp", "logs"]:
    os.makedirs(directory, exist_ok=True)

@app.get("/")
async def root():
    """Endpoint racine"""
    return {
        "message": "Auto Grade Scribe API is running",
        "version": "2.0.0",
        "status": "healthy",
        "timestamp": datetime.now().isoformat()
    }

@app.get("/health")
async def health_check():
    """Endpoint de vérification de santé"""
    try:
        # Tester la connexion à la base de données PostgreSQL
        try:
            from database import engine
            connection = engine.connect()
            connection.execute("SELECT 1")
            connection.close()
            db_status = "connected"
            db_type = "postgresql"
        except Exception as e:
            db_status = f"error: {str(e)}"
            db_type = "unknown"

        return {
            "status": "healthy",
            "database": {
                "status": db_status,
                "type": db_type
            },
            "directories": {
                "uploads": os.path.exists("uploads"),
                "results": os.path.exists("results"),
                "temp": os.path.exists("temp"),
                "logs": os.path.exists("logs")
            },
            "timestamp": datetime.now().isoformat(),
            "version": "2.0.0"
        }
    except Exception as e:
        return {
            "status": "unhealthy",
            "error": str(e),
            "timestamp": datetime.now().isoformat()
        }

@app.post("/api/upload")
async def upload_file(file: UploadFile = File(...)):
    """
    Endpoint d'upload corrigé qui retourne toujours un file_id
    """
    try:
        logger.info(f"Début upload: {file.filename}")

        # Générer un ID unique
        file_id = str(uuid.uuid4())
        original_filename = file.filename or "unknown_file"
        file_extension = os.path.splitext(original_filename)[1]
        unique_filename = f"{file_id}{file_extension}"
        file_path = os.path.join("uploads", unique_filename)

        # Lire et sauvegarder le fichier
        content = await file.read()
        with open(file_path, "wb") as buffer:
            buffer.write(content)

        # Vérifier que le fichier a été sauvegardé
        if not os.path.exists(file_path):
            raise HTTPException(
                status_code=500,
                detail="Erreur lors de la sauvegarde du fichier"
            )

        # Essayer de sauvegarder en base de données (optionnel)
        db_saved = False
        try:
            from database import engine, SessionLocal
            from models_simple import Exam, ExamStatus, ExamType

            exam = Exam(
                id=file_id,
                user_id=1,  # Utilisateur par défaut
                original_filename=original_filename,
                file_path=file_path,
                file_size=len(content),
                exam_type=ExamType.QCM,
                status=ExamStatus.UPLOADED,
                upload_time=datetime.utcnow()
            )

            db = SessionLocal()
            try:
                db.add(exam)
                db.commit()
                db.refresh(exam)
                db_saved = True
                logger.info(f"Exam sauvegardé en base: {file_id}")
            except Exception as db_error:
                logger.warning(f"Erreur base de données: {db_error}")
                db.rollback()
            finally:
                db.close()

        except Exception as model_error:
            logger.warning(f"Erreur modèles: {model_error}")

        # Préparer la réponse avec TOUJOURS un file_id
        response = {
            "success": True,
            "file_id": file_id,  # TOUJOURS présent
            "filename": original_filename,
            "file_path": file_path,
            "file_size": len(content),
            "upload_time": datetime.now().isoformat(),
            "database_saved": db_saved,
            "message": "Fichier téléchargé avec succès"
        }

        logger.info(f"Upload réussi: {file_id} - {original_filename} - DB: {db_saved}")
        return response

    except Exception as e:
        logger.error(f"Erreur upload: {str(e)}")
        # Même en cas d'erreur, essayer de retourner un file_id
        error_file_id = str(uuid.uuid4())
        return JSONResponse(
            status_code=500,
            content={
                "success": False,
                "file_id": error_file_id,  # ID d'erreur
                "error": str(e),
                "message": "Erreur lors du téléchargement",
                "timestamp": datetime.now().isoformat()
            }
        )

@app.post("/api/upload/test")
async def upload_test(file: UploadFile = File(...)):
    """Endpoint de test simple pour l'upload"""
    try:
        file_id = str(uuid.uuid4())
        content = await file.read()

        return {
            "success": True,
            "file_id": file_id,
            "filename": file.filename,
            "file_size": len(content),
            "content_type": file.content_type,
            "upload_time": datetime.now().isoformat(),
            "message": "Test upload réussi"
        }

    except Exception as e:
        return {
            "success": False,
            "file_id": str(uuid.uuid4()),  # ID même en cas d'erreur
            "error": str(e),
            "message": "Test upload échoué"
        }

@app.get("/api/files/{file_id}")
async def get_file_info(file_id: str):
    """Obtenir les informations d'un fichier"""
    try:
        # Chercher le fichier dans le répertoire uploads
        uploads_dir = Path("uploads")
        for file_path in uploads_dir.glob(f"{file_id}.*"):
            if file_path.is_file():
                stat = file_path.stat()
                return {
                    "success": True,
                    "file_id": file_id,
                    "filename": file_path.name,
                    "file_path": str(file_path),
                    "file_size": stat.st_size,
                    "created_time": datetime.fromtimestamp(stat.st_ctime).isoformat(),
                    "modified_time": datetime.fromtimestamp(stat.st_mtime).isoformat()
                }

        return {
            "success": False,
            "file_id": file_id,
            "error": "Fichier non trouvé",
            "message": f"Aucun fichier trouvé avec l'ID {file_id}"
        }

    except Exception as e:
        return {
            "success": False,
            "file_id": file_id,
            "error": str(e),
            "message": "Erreur lors de la récupération du fichier"
        }

@app.get("/api/files")
async def list_files():
    """Lister tous les fichiers uploadés"""
    try:
        uploads_dir = Path("uploads")
        files = []

        if uploads_dir.exists():
            for file_path in uploads_dir.iterdir():
                if file_path.is_file():
                    stat = file_path.stat()
                    # Extraire l'ID du nom de fichier
                    name_parts = file_path.stem.split('.')
                    file_id = name_parts[0] if name_parts else "unknown"

                    files.append({
                        "file_id": file_id,
                        "filename": file_path.name,
                        "file_size": stat.st_size,
                        "upload_time": datetime.fromtimestamp(stat.st_ctime).isoformat()
                    })

        return {
            "success": True,
            "files": files,
            "total": len(files),
            "timestamp": datetime.now().isoformat()
        }

    except Exception as e:
        return {
            "success": False,
            "error": str(e),
            "files": [],
            "total": 0
        }

@app.post("/api/v3/ocr/enhanced")
async def enhanced_ocr_extraction(request: OCRRequest):
    """
    Extraction OCR avancée avec multiple providers
    """
    try:
        # Trouver le fichier
        file_path = await _find_file_by_id(request.file_id)
        if not file_path:
            raise HTTPException(
                status_code=404,
                detail=f"File with ID {request.file_id} not found"
            )

        # Utiliser le service OCR avancé si disponible
        if enhanced_ocr_service:
            result = await enhanced_ocr_service.extract_text_multi_provider(
                file_path, request.content_type
            )
        else:
            # Fallback vers OCR simple
            result = await _simple_ocr_fallback(file_path)

        return {
            "success": result.get('success', False),
            "file_id": request.file_id,
            "extracted_text": result.get('text', ''),
            "confidence": result.get('confidence', 0.0),
            "provider_used": result.get('provider_used', 'unknown'),
            "content_type": request.content_type,
            "processing_details": result.get('all_results', {}),
            "timestamp": datetime.now().isoformat()
        }

    except Exception as e:
        logger.error(f"Enhanced OCR error: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/v3/grade/intelligent")
async def intelligent_grading(request: GradingRequest):
    """
    Correction intelligente avec IA avancée
    """
    try:
        # Trouver le fichier
        file_path = await _find_file_by_id(request.file_id)
        if not file_path:
            raise HTTPException(
                status_code=404,
                detail=f"File with ID {request.file_id} not found"
            )

        # Utiliser le pipeline de correction complet
        if grading_pipeline_service:
            result = await grading_pipeline_service.process_exam_complete(
                exam_id=request.file_id,
                file_path=file_path,
                correct_answers=request.correct_answers,
                exam_type=request.exam_type,
                config=request.grading_config
            )
        else:
            # Fallback vers correction simple
            result = await _simple_grading_fallback(
                request.file_id, file_path, request.correct_answers
            )

        return result

    except Exception as e:
        logger.error(f"Intelligent grading error: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/v3/review/dashboard")
async def get_review_dashboard(teacher_id: int = 1):
    """
    Tableau de bord pour la révision manuelle
    """
    try:
        if manual_review_service:
            dashboard = await manual_review_service.get_review_dashboard(teacher_id)
            return dashboard
        else:
            # Simulation de tableau de bord
            return {
                "success": True,
                "dashboard": {
                    "pending_reviews": {"exams": [], "total_count": 0},
                    "statistics": {"total_reviews_pending": 0},
                    "recent_reviews": [],
                    "teacher_id": teacher_id
                }
            }

    except Exception as e:
        logger.error(f"Review dashboard error: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/v3/review/exam/{exam_id}")
async def get_exam_for_review(exam_id: str, teacher_id: int = 1):
    """
    Récupérer un examen pour révision détaillée
    """
    try:
        if manual_review_service:
            exam_data = await manual_review_service.get_exam_for_review(exam_id, teacher_id)
            return exam_data
        else:
            # Simulation de données d'examen
            return {
                "success": True,
                "exam_id": exam_id,
                "exam_details": {"exam": {"id": exam_id}, "results": None},
                "review_data": {"questions": [], "overall_confidence": 0.5},
                "suggestions": {"priority_level": "medium"},
                "teacher_id": teacher_id
            }

    except Exception as e:
        logger.error(f"Get exam for review error: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/v3/review/submit/{exam_id}")
async def submit_manual_review(exam_id: str, request: ManualReviewRequest, teacher_id: int = 1):
    """
    Soumettre une révision manuelle
    """
    try:
        if manual_review_service:
            review_result = await manual_review_service.submit_manual_review(
                exam_id=exam_id,
                teacher_id=teacher_id,
                review_data=request.dict()
            )
            return review_result
        else:
            # Simulation de soumission
            return {
                "success": True,
                "exam_id": exam_id,
                "teacher_id": teacher_id,
                "message": "Manual review submitted (simulation)"
            }

    except Exception as e:
        logger.error(f"Submit manual review error: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/v3/results/{exam_id}/detailed")
async def get_detailed_results(exam_id: str):
    """
    Récupérer les résultats détaillés d'un examen
    """
    try:
        if database_service:
            results = await database_service.get_exam_results_detailed(exam_id)
            return results
        else:
            # Simulation de résultats
            return {
                "success": True,
                "exam": {"id": exam_id},
                "results": None,
                "history": []
            }

    except Exception as e:
        logger.error(f"Get detailed results error: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

# Fonctions utilitaires
async def _find_file_by_id(file_id: str) -> Optional[str]:
    """Trouver un fichier par son ID"""
    uploads_dir = Path("uploads")
    for ext in ['.jpg', '.jpeg', '.png', '.pdf', '.tiff', '.bmp']:
        file_path = uploads_dir / f"{file_id}{ext}"
        if file_path.exists():
            return str(file_path)
    return None

async def _simple_ocr_fallback(file_path: str) -> Dict[str, Any]:
    """OCR de secours simple"""
    try:
        import pytesseract
        from PIL import Image

        image = Image.open(file_path)
        text = pytesseract.image_to_string(image, lang='eng+fra')

        return {
            'success': True,
            'text': text.strip(),
            'confidence': 0.6,
            'provider_used': 'tesseract_fallback'
        }
    except Exception as e:
        return {
            'success': False,
            'error': str(e),
            'text': '',
            'confidence': 0.0
        }

async def _simple_grading_fallback(
    exam_id: str,
    file_path: str,
    correct_answers: Dict[str, Any]
) -> Dict[str, Any]:
    """Correction de secours simple"""
    try:
        # OCR simple
        ocr_result = await _simple_ocr_fallback(file_path)

        # Correction basique (simulation)
        return {
            'success': True,
            'exam_id': exam_id,
            'ocr_result': ocr_result,
            'grading_result': {
                'type': 'fallback',
                'results': {},
                'summary': {'total_questions': len(correct_answers), 'correct_answers': 0}
            },
            'final_score': {'score': 0, 'percentage': 0, 'grade': 'F', 'confidence': 0.3},
            'requires_manual_review': True,
            'message': 'Fallback grading used - manual review required'
        }
    except Exception as e:
        return {
            'success': False,
            'error': str(e),
            'exam_id': exam_id,
            'requires_manual_review': True
        }

# Initialisation des services
@app.on_event("startup")
async def startup_event():
    """Initialiser les services au démarrage"""
    try:
        # Initialiser la base de données si disponible
        try:
            from database import SessionLocal
            global database_service, manual_review_service
            database_service = init_database_service(SessionLocal)
            manual_review_service = init_manual_review_service(database_service)
            logger.info("Database and manual review services initialized")
        except ImportError:
            logger.warning("Database services not available")

        # Connecter le service OCR au pipeline
        if grading_pipeline_service and enhanced_ocr_service:
            grading_pipeline_service.ocr_service = enhanced_ocr_service
            logger.info("OCR service connected to grading pipeline")

        logger.info("Application startup completed")

    except Exception as e:
        logger.error(f"Startup error: {str(e)}")

# Point d'entrée pour le développement
if __name__ == "__main__":
    uvicorn.run(
        "app_working:app",
        host="127.0.0.1",
        port=8001,
        reload=True,
        log_level="info"
    )
