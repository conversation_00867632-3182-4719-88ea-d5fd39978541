"""
Application Auto-Grade Scribe fonctionnelle avec PostgreSQL
Version simplifiée qui corrige l'erreur d'upload
"""

import os
import sys
import uuid
import logging
from datetime import datetime
from pathlib import Path
from typing import Dict, Any

from fastapi import FastAPI, UploadFile, File, HTTPException, status
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import uvicorn

# Configuration du logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("auto-grade-scribe")

# Ajouter le répertoire courant au path
sys.path.append(str(Path(__file__).parent))

# Créer l'application FastAPI
app = FastAPI(
    title="Auto Grade Scribe",
    description="Application de correction automatique d'examens avec PostgreSQL",
    version="2.0.0"
)

# Configuration CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Créer les répertoires nécessaires
for directory in ["uploads", "results", "temp", "logs"]:
    os.makedirs(directory, exist_ok=True)

@app.get("/")
async def root():
    """Endpoint racine"""
    return {
        "message": "Auto Grade Scribe API is running",
        "version": "2.0.0",
        "status": "healthy",
        "timestamp": datetime.now().isoformat()
    }

@app.get("/health")
async def health_check():
    """Endpoint de vérification de santé"""
    try:
        # Tester la connexion à la base de données PostgreSQL
        try:
            from database import engine
            connection = engine.connect()
            connection.execute("SELECT 1")
            connection.close()
            db_status = "connected"
            db_type = "postgresql"
        except Exception as e:
            db_status = f"error: {str(e)}"
            db_type = "unknown"
        
        return {
            "status": "healthy",
            "database": {
                "status": db_status,
                "type": db_type
            },
            "directories": {
                "uploads": os.path.exists("uploads"),
                "results": os.path.exists("results"),
                "temp": os.path.exists("temp"),
                "logs": os.path.exists("logs")
            },
            "timestamp": datetime.now().isoformat(),
            "version": "2.0.0"
        }
    except Exception as e:
        return {
            "status": "unhealthy",
            "error": str(e),
            "timestamp": datetime.now().isoformat()
        }

@app.post("/api/upload")
async def upload_file(file: UploadFile = File(...)):
    """
    Endpoint d'upload corrigé qui retourne toujours un file_id
    """
    try:
        logger.info(f"Début upload: {file.filename}")
        
        # Générer un ID unique
        file_id = str(uuid.uuid4())
        original_filename = file.filename or "unknown_file"
        file_extension = os.path.splitext(original_filename)[1]
        unique_filename = f"{file_id}{file_extension}"
        file_path = os.path.join("uploads", unique_filename)

        # Lire et sauvegarder le fichier
        content = await file.read()
        with open(file_path, "wb") as buffer:
            buffer.write(content)

        # Vérifier que le fichier a été sauvegardé
        if not os.path.exists(file_path):
            raise HTTPException(
                status_code=500, 
                detail="Erreur lors de la sauvegarde du fichier"
            )

        # Essayer de sauvegarder en base de données (optionnel)
        db_saved = False
        try:
            from database import engine, SessionLocal
            from models_simple import Exam, ExamStatus, ExamType
            
            exam = Exam(
                id=file_id,
                user_id=1,  # Utilisateur par défaut
                original_filename=original_filename,
                file_path=file_path,
                file_size=len(content),
                exam_type=ExamType.QCM,
                status=ExamStatus.UPLOADED,
                upload_time=datetime.utcnow()
            )

            db = SessionLocal()
            try:
                db.add(exam)
                db.commit()
                db.refresh(exam)
                db_saved = True
                logger.info(f"Exam sauvegardé en base: {file_id}")
            except Exception as db_error:
                logger.warning(f"Erreur base de données: {db_error}")
                db.rollback()
            finally:
                db.close()
                
        except Exception as model_error:
            logger.warning(f"Erreur modèles: {model_error}")

        # Préparer la réponse avec TOUJOURS un file_id
        response = {
            "success": True,
            "file_id": file_id,  # TOUJOURS présent
            "filename": original_filename,
            "file_path": file_path,
            "file_size": len(content),
            "upload_time": datetime.now().isoformat(),
            "database_saved": db_saved,
            "message": "Fichier téléchargé avec succès"
        }
        
        logger.info(f"Upload réussi: {file_id} - {original_filename} - DB: {db_saved}")
        return response
        
    except Exception as e:
        logger.error(f"Erreur upload: {str(e)}")
        # Même en cas d'erreur, essayer de retourner un file_id
        error_file_id = str(uuid.uuid4())
        return JSONResponse(
            status_code=500,
            content={
                "success": False,
                "file_id": error_file_id,  # ID d'erreur
                "error": str(e),
                "message": "Erreur lors du téléchargement",
                "timestamp": datetime.now().isoformat()
            }
        )

@app.post("/api/upload/test")
async def upload_test(file: UploadFile = File(...)):
    """Endpoint de test simple pour l'upload"""
    try:
        file_id = str(uuid.uuid4())
        content = await file.read()
        
        return {
            "success": True,
            "file_id": file_id,
            "filename": file.filename,
            "file_size": len(content),
            "content_type": file.content_type,
            "upload_time": datetime.now().isoformat(),
            "message": "Test upload réussi"
        }
        
    except Exception as e:
        return {
            "success": False,
            "file_id": str(uuid.uuid4()),  # ID même en cas d'erreur
            "error": str(e),
            "message": "Test upload échoué"
        }

@app.get("/api/files/{file_id}")
async def get_file_info(file_id: str):
    """Obtenir les informations d'un fichier"""
    try:
        # Chercher le fichier dans le répertoire uploads
        uploads_dir = Path("uploads")
        for file_path in uploads_dir.glob(f"{file_id}.*"):
            if file_path.is_file():
                stat = file_path.stat()
                return {
                    "success": True,
                    "file_id": file_id,
                    "filename": file_path.name,
                    "file_path": str(file_path),
                    "file_size": stat.st_size,
                    "created_time": datetime.fromtimestamp(stat.st_ctime).isoformat(),
                    "modified_time": datetime.fromtimestamp(stat.st_mtime).isoformat()
                }
        
        return {
            "success": False,
            "file_id": file_id,
            "error": "Fichier non trouvé",
            "message": f"Aucun fichier trouvé avec l'ID {file_id}"
        }
        
    except Exception as e:
        return {
            "success": False,
            "file_id": file_id,
            "error": str(e),
            "message": "Erreur lors de la récupération du fichier"
        }

@app.get("/api/files")
async def list_files():
    """Lister tous les fichiers uploadés"""
    try:
        uploads_dir = Path("uploads")
        files = []
        
        if uploads_dir.exists():
            for file_path in uploads_dir.iterdir():
                if file_path.is_file():
                    stat = file_path.stat()
                    # Extraire l'ID du nom de fichier
                    name_parts = file_path.stem.split('.')
                    file_id = name_parts[0] if name_parts else "unknown"
                    
                    files.append({
                        "file_id": file_id,
                        "filename": file_path.name,
                        "file_size": stat.st_size,
                        "upload_time": datetime.fromtimestamp(stat.st_ctime).isoformat()
                    })
        
        return {
            "success": True,
            "files": files,
            "total": len(files),
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        return {
            "success": False,
            "error": str(e),
            "files": [],
            "total": 0
        }

# Point d'entrée pour le développement
if __name__ == "__main__":
    uvicorn.run(
        "app_working:app", 
        host="127.0.0.1", 
        port=8001, 
        reload=True,
        log_level="info"
    )
