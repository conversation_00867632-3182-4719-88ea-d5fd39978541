#!/usr/bin/env python3
"""
Script de nettoyage final pour Auto-Grade Scribe Open-Source
Supprime tous les fichiers inutiles et optimise la structure
"""

import os
import shutil
import logging
from pathlib import Path

# Configuration du logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def remove_if_exists(path):
    """Supprimer un fichier ou dossier s'il existe"""
    path_obj = Path(path)
    try:
        if path_obj.is_file():
            path_obj.unlink()
            logger.info(f"✅ Fichier supprimé: {path}")
        elif path_obj.is_dir():
            shutil.rmtree(path_obj)
            logger.info(f"✅ Dossier supprimé: {path}")
    except Exception as e:
        logger.warning(f"⚠️ Impossible de supprimer {path}: {e}")

def cleanup_cache_files():
    """Nettoyer les fichiers de cache"""
    logger.info("🧹 Nettoyage des fichiers de cache...")
    
    cache_patterns = [
        "**/__pycache__",
        "**/*.pyc",
        "**/*.pyo",
        "**/*.pyd",
        "**/.pytest_cache",
        "**/.coverage",
        "**/htmlcov",
        "**/.mypy_cache",
        "**/.tox",
        "**/node_modules",
        "**/.next",
        "**/dist",
        "**/build",
        "**/*.egg-info"
    ]
    
    for pattern in cache_patterns:
        for path in Path(".").glob(pattern):
            remove_if_exists(path)

def cleanup_old_files():
    """Supprimer les anciens fichiers"""
    logger.info("🗑️ Suppression des anciens fichiers...")
    
    old_files = [
        # Anciens fichiers d'application
        "backend/app.py",
        "backend/app_working.py", 
        "backend/simple_app.py",
        "backend/models.py",
        "backend/config.py",
        "backend/database.py",
        "backend/auth.py",
        "backend/init_db.py",
        "backend/init_postgres_db.py",
        "backend/test_app.py",
        "backend/test_database.py",
        
        # Anciens services
        "backend/services/ocr_service.py",
        "backend/services/grading_service.py",
        "backend/services/database_service.py",
        
        # Anciens dossiers AI
        "backend/ai",
        
        # Anciens fichiers de configuration
        ".env.example",
        ".env.opensource", 
        "requirements.txt",
        "docker-compose.postgres.yml",
        "docker-compose-postgres.yml",
        "docker-compose-db-only.yml",
        "init-db.sql",
        "init_postgres.sql",
        
        # Anciens scripts
        "test_enhanced_features.py",
        "test_postgres_setup.py",
        "test_upload_fix.ps1",
        "start_postgres_simple.ps1",
        "start-postgres.ps1", 
        "start_postgres_docker.ps1",
        "test_local_setup.py",
        "test_postgres_docker.py",
        "test_simple_setup.py",
        "check_postgres.py",
        "check_tesseract.py",
        "init_postgres.py",
        "setup_local_dev.ps1",
        "setup_postgres_user.sql",
        
        # Documentation obsolète
        "DEPLOYMENT_GUIDE.md",
        "ENHANCED_GRADING_GUIDE.md", 
        "QUICK_START_POSTGRES.md",
        "architecture.txt",
        "rapport_technique.tex",
        "test.txt",
        
        # Bases de données SQLite
        "autogradescribe.db",
        "backend/autogradescribe.db",
        "backend/gradegenius.db",
        "backend/gradegeniusdb.db",
        "gradegeniusdb.db",
        
        # Environnements virtuels
        "backend-env",
        "venv",
        "backend/venv",
        
        # Dossiers temporaires
        "backend/debug",
        "backend/temp",
        "backend/static",
        "backend/backend",
        "temp",
        "data",
        "debug"
    ]
    
    for file_path in old_files:
        remove_if_exists(file_path)

def cleanup_empty_directories():
    """Supprimer les dossiers vides"""
    logger.info("📁 Suppression des dossiers vides...")
    
    for root, dirs, files in os.walk(".", topdown=False):
        for directory in dirs:
            dir_path = Path(root) / directory
            try:
                if dir_path.is_dir() and not any(dir_path.iterdir()):
                    dir_path.rmdir()
                    logger.info(f"✅ Dossier vide supprimé: {dir_path}")
            except Exception as e:
                logger.debug(f"Impossible de supprimer {dir_path}: {e}")

def create_required_directories():
    """Créer les répertoires requis"""
    logger.info("📁 Création des répertoires requis...")
    
    required_dirs = [
        "uploads",
        "results", 
        "temp",
        "logs",
        "models",
        "models/sentence_transformers",
        "models/transformers"
    ]
    
    for directory in required_dirs:
        Path(directory).mkdir(parents=True, exist_ok=True)
        logger.info(f"✅ Répertoire créé: {directory}")

def create_gitignore():
    """Créer un fichier .gitignore optimisé"""
    logger.info("📝 Création du .gitignore optimisé...")
    
    gitignore_content = """# Auto-Grade Scribe Open-Source - .gitignore

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Virtual environments
venv/
env/
ENV/
backend-env/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
Thumbs.db

# Logs
logs/
*.log

# Database
*.db
*.sqlite3

# Uploads et résultats
uploads/
results/
temp/

# Modèles IA (trop volumineux pour git)
models/sentence_transformers/
models/transformers/

# Configuration locale
.env.local
.env.production

# Cache
.pytest_cache/
.coverage
htmlcov/
.mypy_cache/
.tox/

# Node.js (si frontend ajouté)
node_modules/
.next/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Docker
.dockerignore

# Backup files
*.bak
*.backup
*.old
"""
    
    with open(".gitignore", "w") as f:
        f.write(gitignore_content)
    
    logger.info("✅ .gitignore créé")

def generate_cleanup_report():
    """Générer un rapport de nettoyage"""
    logger.info("📊 Génération du rapport de nettoyage...")
    
    # Compter les fichiers restants
    total_files = 0
    total_size = 0
    
    for root, dirs, files in os.walk("."):
        # Ignorer les dossiers cachés et de cache
        dirs[:] = [d for d in dirs if not d.startswith('.') and d not in ['__pycache__', 'node_modules']]
        
        for file in files:
            if not file.startswith('.') and not file.endswith('.pyc'):
                file_path = Path(root) / file
                try:
                    size = file_path.stat().st_size
                    total_files += 1
                    total_size += size
                except:
                    pass
    
    report = f"""
# 📊 Rapport de Nettoyage - Auto-Grade Scribe Open-Source

## ✅ Nettoyage Terminé

### 📈 Statistiques Finales
- **Fichiers restants**: {total_files}
- **Taille totale**: {total_size / (1024*1024):.1f} MB
- **Structure**: Optimisée et nettoyée

### 🗑️ Éléments Supprimés
- Anciens fichiers d'application (app.py, etc.)
- Services obsolètes (ocr_service.py, etc.)
- Configurations dupliquées (.env.example, etc.)
- Scripts de test obsolètes
- Documentation redondante
- Fichiers de cache et temporaires
- Environnements virtuels
- Bases de données SQLite

### ✅ Structure Finale
```
auto-grade-scribe/
├── backend/
│   ├── core/                    # Configuration
│   ├── services/               # Services open-source
│   └── main.py                 # Application
├── models/                     # Modèles IA
├── uploads/                    # Fichiers uploadés
├── results/                    # Résultats
├── .env                        # Configuration
├── docker-compose-simple.yml  # PostgreSQL
├── setup_opensource_models.py # Installation
├── start.py                   # Démarrage
└── README_OPENSOURCE.md      # Documentation
```

### 🎯 Prochaines Étapes
1. `python setup_opensource_models.py` - Installer les modèles
2. `docker-compose -f docker-compose-simple.yml up -d` - Démarrer PostgreSQL
3. `python start.py` - Lancer l'application

## 🎉 Auto-Grade Scribe v4.0.0 - Prêt !
**Structure optimisée • Code nettoyé • 100% Open-Source**
"""
    
    with open("CLEANUP_REPORT.md", "w") as f:
        f.write(report)
    
    logger.info("✅ Rapport de nettoyage généré: CLEANUP_REPORT.md")

def main():
    """Fonction principale de nettoyage"""
    logger.info("🧹 Démarrage du nettoyage final d'Auto-Grade Scribe")
    logger.info("=" * 60)
    
    # Étapes de nettoyage
    cleanup_cache_files()
    cleanup_old_files()
    cleanup_empty_directories()
    create_required_directories()
    create_gitignore()
    generate_cleanup_report()
    
    logger.info("=" * 60)
    logger.info("🎉 Nettoyage terminé avec succès!")
    logger.info("✅ Auto-Grade Scribe v4.0.0 est maintenant optimisé")
    logger.info("📁 Structure nettoyée et prête pour la production")
    logger.info("🚀 Démarrez avec: python start.py")

if __name__ == "__main__":
    main()
