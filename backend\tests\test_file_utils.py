"""
Tests for file utilities
"""

import pytest
import os
import tempfile
from PIL import Image
from io import BytesIO
from utils.file_utils import FileUtils

class TestFileUtils:
    """Test cases for FileUtils"""
    
    def setup_method(self):
        """Set up test fixtures"""
        self.file_utils = FileUtils()
    
    def create_test_image(self, width=800, height=600, format='JPEG'):
        """Create a test image for testing"""
        image = Image.new('RGB', (width, height), color='white')
        buffer = BytesIO()
        image.save(buffer, format=format)
        return buffer.getvalue()
    
    def test_validate_file_valid_image(self):
        """Test validation of a valid image file"""
        image_data = self.create_test_image()
        filename = "test_image.jpg"
        
        result = self.file_utils.validate_file(image_data, filename)
        
        assert result["valid"] is True
        assert result["error"] is None
        assert result["file_info"]["mime_type"] in self.file_utils.supported_image_types
        assert result["file_info"]["filename"] == filename
        assert result["file_info"]["file_size"] == len(image_data)
    
    def test_validate_file_empty_file(self):
        """Test validation of an empty file"""
        empty_data = b""
        filename = "empty.jpg"
        
        result = self.file_utils.validate_file(empty_data, filename)
        
        assert result["valid"] is False
        assert "empty" in result["error"].lower()
    
    def test_validate_file_too_large(self):
        """Test validation of a file that's too large"""
        # Create a large file (simulate by setting a small max size)
        original_max_size = self.file_utils.max_file_size
        self.file_utils.max_file_size = 100  # 100 bytes
        
        try:
            large_data = b"x" * 200  # 200 bytes
            filename = "large_file.jpg"
            
            result = self.file_utils.validate_file(large_data, filename)
            
            assert result["valid"] is False
            assert "too large" in result["error"].lower()
        finally:
            # Restore original max size
            self.file_utils.max_file_size = original_max_size
    
    def test_validate_file_unsupported_type(self):
        """Test validation of an unsupported file type"""
        # Create a text file
        text_data = b"This is a text file"
        filename = "test.txt"
        
        result = self.file_utils.validate_file(text_data, filename)
        
        assert result["valid"] is False
        assert "unsupported" in result["error"].lower()
    
    def test_validate_image_valid_dimensions(self):
        """Test image validation with valid dimensions"""
        image_data = self.create_test_image(800, 600)
        
        result = self.file_utils._validate_image(image_data)
        
        assert result["valid"] is True
        assert result["error"] is None
        assert result["info"]["width"] == 800
        assert result["info"]["height"] == 600
    
    def test_validate_image_too_large(self):
        """Test image validation with dimensions too large"""
        # Temporarily reduce max dimensions for testing
        original_max_dims = self.file_utils.max_image_dimensions
        self.file_utils.max_image_dimensions = (500, 500)
        
        try:
            image_data = self.create_test_image(800, 600)
            
            result = self.file_utils._validate_image(image_data)
            
            assert result["valid"] is False
            assert "too large" in result["error"].lower()
        finally:
            # Restore original max dimensions
            self.file_utils.max_image_dimensions = original_max_dims
    
    def test_validate_image_small_dimensions_warning(self):
        """Test image validation with small dimensions (should warn)"""
        image_data = self.create_test_image(50, 50)
        
        result = self.file_utils._validate_image(image_data)
        
        assert result["valid"] is True
        assert len(result["warnings"]) > 0
        assert any("small" in warning.lower() for warning in result["warnings"])
    
    def test_ensure_directory_new(self):
        """Test creating a new directory"""
        with tempfile.TemporaryDirectory() as temp_dir:
            new_dir = os.path.join(temp_dir, "new_directory")
            
            result = self.file_utils.ensure_directory(new_dir)
            
            assert result is True
            assert os.path.exists(new_dir)
            assert os.path.isdir(new_dir)
    
    def test_ensure_directory_existing(self):
        """Test ensuring an existing directory"""
        with tempfile.TemporaryDirectory() as temp_dir:
            result = self.file_utils.ensure_directory(temp_dir)
            
            assert result is True
            assert os.path.exists(temp_dir)
    
    def test_safe_filename_basic(self):
        """Test basic filename sanitization"""
        unsafe_filename = "test file with spaces.jpg"
        safe_filename = self.file_utils.safe_filename(unsafe_filename)
        
        assert " " not in safe_filename
        assert safe_filename.endswith(".jpg")
    
    def test_safe_filename_special_characters(self):
        """Test filename sanitization with special characters"""
        unsafe_filename = "test@file#with$special%chars.jpg"
        safe_filename = self.file_utils.safe_filename(unsafe_filename)
        
        # Should remove special characters
        assert "@" not in safe_filename
        assert "#" not in safe_filename
        assert "$" not in safe_filename
        assert "%" not in safe_filename
        assert safe_filename.endswith(".jpg")
    
    def test_safe_filename_long_name(self):
        """Test filename sanitization with very long name"""
        long_name = "a" * 150 + ".jpg"
        safe_filename = self.file_utils.safe_filename(long_name)
        
        # Should be truncated but keep extension
        assert len(safe_filename) <= 104  # 100 chars + ".jpg"
        assert safe_filename.endswith(".jpg")
    
    def test_get_file_info_existing_file(self):
        """Test getting info for an existing file"""
        with tempfile.NamedTemporaryFile(delete=False, suffix=".txt") as temp_file:
            temp_file.write(b"Test content")
            temp_file_path = temp_file.name
        
        try:
            result = self.file_utils.get_file_info(temp_file_path)
            
            assert "error" not in result
            assert result["filename"] == os.path.basename(temp_file_path)
            assert result["size"] > 0
            assert "created" in result
            assert "modified" in result
        finally:
            os.unlink(temp_file_path)
    
    def test_get_file_info_nonexistent_file(self):
        """Test getting info for a non-existent file"""
        nonexistent_path = "/path/that/does/not/exist.txt"
        
        result = self.file_utils.get_file_info(nonexistent_path)
        
        assert "error" in result
        assert "not found" in result["error"].lower()
    
    def test_cleanup_old_files(self):
        """Test cleaning up old files"""
        with tempfile.TemporaryDirectory() as temp_dir:
            # Create some test files
            old_file = os.path.join(temp_dir, "old_file.txt")
            new_file = os.path.join(temp_dir, "new_file.txt")
            
            with open(old_file, "w") as f:
                f.write("old content")
            with open(new_file, "w") as f:
                f.write("new content")
            
            # Modify the old file's timestamp to make it appear old
            import time
            old_time = time.time() - (40 * 24 * 60 * 60)  # 40 days ago
            os.utime(old_file, (old_time, old_time))
            
            result = self.file_utils.cleanup_old_files(temp_dir, max_age_days=30)
            
            assert result["success"] is True
            assert result["files_deleted"] == 1
            assert "old_file.txt" in result["deleted_files"]
            assert not os.path.exists(old_file)
            assert os.path.exists(new_file)
    
    def test_cleanup_old_files_nonexistent_directory(self):
        """Test cleanup with non-existent directory"""
        nonexistent_dir = "/path/that/does/not/exist"
        
        result = self.file_utils.cleanup_old_files(nonexistent_dir)
        
        assert "error" in result
        assert "not found" in result["error"].lower()
