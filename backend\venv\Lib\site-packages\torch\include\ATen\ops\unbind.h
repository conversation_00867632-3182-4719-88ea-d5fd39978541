#pragma once

// @generated by torchgen/gen.py from Function.h

#include <ATen/Context.h>
#include <ATen/DeviceGuard.h>
#include <ATen/TensorUtils.h>
#include <ATen/TracerMode.h>
#include <ATen/core/Generator.h>
#include <ATen/core/Reduction.h>
#include <ATen/core/Tensor.h>
#include <c10/core/Scalar.h>
#include <c10/core/Storage.h>
#include <c10/core/TensorOptions.h>
#include <c10/util/Deprecated.h>
#include <c10/util/Optional.h>



#include <ATen/ops/unbind_ops.h>

namespace at {


// aten::unbind.int(Tensor(a -> *) self, int dim=0) -> Tensor(a)[]
inline ::std::vector<at::Tensor> unbind(const at::Tensor & self, int64_t dim=0) {
    return at::_ops::unbind_int::call(self, dim);
}

// aten::unbind.Dimname(Tensor(a -> *) self, Dimname dim) -> Tensor(a)[]
inline ::std::vector<at::Tensor> unbind(const at::Tensor & self, at::Dimname dim) {
    return at::_ops::unbind_Dimname::call(self, dim);
}

}
