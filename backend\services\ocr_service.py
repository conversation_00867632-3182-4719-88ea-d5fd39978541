"""
Enhanced OCR Service for Auto-Grade Scribe
Provides advanced text extraction with multiple OCR engines and preprocessing
"""

import os
import logging
from typing import Dict, Any, Optional, List, Tuple
from PIL import Image, ImageEnhance, ImageFilter
from datetime import datetime

try:
    import cv2
    import numpy as np
    CV2_AVAILABLE = True
except ImportError:
    CV2_AVAILABLE = False
    import numpy as np

try:
    import pytesseract
    TESSERACT_AVAILABLE = True
except ImportError:
    TESSERACT_AVAILABLE = False

from models import ExamType
from ai.gemini_service import gemini_service
from ai.tesseract_ocr_service import tesseract_ocr_service

logger = logging.getLogger("auto-grade-scribe.enhanced-ocr")

class EnhancedOCRService:
    """Enhanced OCR service with advanced preprocessing and multiple engines"""

    def __init__(self):
        self.gemini_available = gemini_service.is_available
        self.tesseract_service = tesseract_ocr_service
        self.cv2_available = CV2_AVAILABLE
        self.tesseract_available = TESSERACT_AVAILABLE

        # OCR configuration
        self.tesseract_config = {
            "standard": "--oem 3 --psm 6",
            "qcm": "--oem 3 --psm 6 -c tessedit_char_whitelist=ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789",
            "handwritten": "--oem 3 --psm 6",
            "dense_text": "--oem 3 --psm 4",
            "single_line": "--oem 3 --psm 7"
        }

        logger.info(f"Enhanced OCR Service initialized. Gemini: {self.gemini_available}, CV2: {self.cv2_available}, Tesseract: {self.tesseract_available}")

    async def extract_text_enhanced(
        self,
        image_path: str,
        exam_type: ExamType,
        ocr_settings: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Enhanced text extraction with preprocessing and multiple OCR engines

        Args:
            image_path: Path to the image file
            exam_type: Type of exam for optimized processing
            ocr_settings: Custom OCR settings

        Returns:
            Dictionary with extraction results and metadata
        """
        try:
            if not os.path.exists(image_path):
                return {"success": False, "error": "File not found"}

            start_time = datetime.utcnow()

            # Preprocess image
            preprocessed_images = await self._preprocess_image_advanced(image_path, exam_type)

            # Choose OCR strategy based on exam type and availability
            if exam_type == ExamType.HANDWRITTEN and self.gemini_available:
                result = await self._extract_with_gemini(image_path, preprocessed_images)
            elif exam_type == ExamType.QCM:
                result = await self._extract_qcm_optimized(image_path, preprocessed_images, ocr_settings)
            else:
                result = await self._extract_with_tesseract_enhanced(image_path, preprocessed_images, exam_type)

            # Add processing metadata
            processing_time = (datetime.utcnow() - start_time).total_seconds()
            result["processing_metadata"] = {
                "processing_time_seconds": processing_time,
                "preprocessing_applied": True,
                "exam_type": exam_type.value,
                "timestamp": datetime.utcnow().isoformat()
            }

            logger.info(f"OCR extraction completed in {processing_time:.2f}s for {image_path}")
            return result

        except Exception as e:
            logger.error(f"Error in enhanced OCR extraction: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "model": "enhanced_ocr"
            }

    async def _preprocess_image_advanced(
        self,
        image_path: str,
        exam_type: ExamType
    ) -> Dict[str, np.ndarray]:
        """
        Advanced image preprocessing for better OCR results

        Args:
            image_path: Path to the image
            exam_type: Type of exam for specialized preprocessing

        Returns:
            Dictionary of preprocessed images
        """
        try:
            if not self.cv2_available:
                # Fallback to basic PIL processing
                return await self._preprocess_image_basic(image_path)

            # Load image
            image = cv2.imread(image_path)
            if image is None:
                raise ValueError("Could not load image")

            preprocessed = {}

            # Convert to grayscale
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            preprocessed["grayscale"] = gray

            # Noise reduction
            denoised = cv2.fastNlMeansDenoising(gray)
            preprocessed["denoised"] = denoised

            # Contrast enhancement
            clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8, 8))
            enhanced = clahe.apply(denoised)
            preprocessed["enhanced"] = enhanced

            # Binarization with multiple methods
            _, binary_otsu = cv2.threshold(enhanced, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
            preprocessed["binary_otsu"] = binary_otsu

            # Adaptive thresholding
            binary_adaptive = cv2.adaptiveThreshold(
                enhanced, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, cv2.THRESH_BINARY, 11, 2
            )
            preprocessed["binary_adaptive"] = binary_adaptive

            # Exam-specific preprocessing
            if exam_type == ExamType.QCM:
                # Morphological operations for QCM
                kernel = np.ones((2, 2), np.uint8)
                qcm_processed = cv2.morphologyEx(binary_otsu, cv2.MORPH_CLOSE, kernel)
                preprocessed["qcm_optimized"] = qcm_processed

            elif exam_type == ExamType.HANDWRITTEN:
                # Dilation for handwritten text
                kernel = np.ones((1, 1), np.uint8)
                handwritten_processed = cv2.dilate(binary_otsu, kernel, iterations=1)
                preprocessed["handwritten_optimized"] = handwritten_processed

            # Skew correction
            try:
                corrected = self._correct_skew(binary_otsu)
                preprocessed["skew_corrected"] = corrected
            except Exception as e:
                logger.warning(f"Skew correction failed: {str(e)}")
                preprocessed["skew_corrected"] = binary_otsu

            return preprocessed

        except Exception as e:
            logger.error(f"Error in image preprocessing: {str(e)}")
            # Return basic preprocessing as fallback
            return await self._preprocess_image_basic(image_path)

    async def _preprocess_image_basic(self, image_path: str) -> Dict[str, np.ndarray]:
        """Basic image preprocessing using PIL when CV2 is not available"""
        try:
            from PIL import Image
            import numpy as np

            # Load image with PIL
            pil_image = Image.open(image_path)

            # Convert to grayscale
            if pil_image.mode != 'L':
                pil_image = pil_image.convert('L')

            # Convert to numpy array
            gray = np.array(pil_image)

            return {"grayscale": gray}

        except Exception as e:
            logger.error(f"Error in basic image preprocessing: {str(e)}")
            # Return empty dict as last resort
            return {}

    def _correct_skew(self, image: np.ndarray) -> np.ndarray:
        """Correct image skew using Hough line detection"""
        try:
            # Detect edges
            edges = cv2.Canny(image, 50, 150, apertureSize=3)

            # Detect lines
            lines = cv2.HoughLines(edges, 1, np.pi/180, threshold=100)

            if lines is not None:
                # Calculate average angle
                angles = []
                for rho, theta in lines[:10]:  # Use first 10 lines
                    angle = theta * 180 / np.pi
                    if angle < 45:
                        angles.append(angle)
                    elif angle > 135:
                        angles.append(angle - 180)

                if angles:
                    median_angle = np.median(angles)

                    # Rotate image
                    (h, w) = image.shape[:2]
                    center = (w // 2, h // 2)
                    M = cv2.getRotationMatrix2D(center, median_angle, 1.0)
                    rotated = cv2.warpAffine(image, M, (w, h), flags=cv2.INTER_CUBIC, borderMode=cv2.BORDER_REPLICATE)

                    return rotated

            return image

        except Exception as e:
            logger.warning(f"Skew correction failed: {str(e)}")
            return image

    async def _extract_with_gemini(
        self,
        image_path: str,
        preprocessed_images: Dict[str, np.ndarray]
    ) -> Dict[str, Any]:
        """Extract text using Gemini AI for handwritten content"""
        try:
            logger.info("Using Gemini AI for handwritten text extraction")

            # Use Gemini service
            result = gemini_service.analyze_handwritten_text(image_path)

            if result.get("success", False):
                result["model"] = "gemini"
                result["confidence"] = 0.9  # Gemini typically has high confidence
                return result
            else:
                # Fallback to Tesseract
                logger.warning("Gemini extraction failed, falling back to Tesseract")
                return await self._extract_with_tesseract_enhanced(
                    image_path, preprocessed_images, ExamType.HANDWRITTEN
                )

        except Exception as e:
            logger.error(f"Error in Gemini extraction: {str(e)}")
            # Fallback to Tesseract
            return await self._extract_with_tesseract_enhanced(
                image_path, preprocessed_images, ExamType.HANDWRITTEN
            )

    async def _extract_qcm_optimized(
        self,
        image_path: str,
        preprocessed_images: Dict[str, np.ndarray],
        ocr_settings: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Optimized QCM extraction with answer detection"""
        try:
            logger.info("Using optimized QCM extraction")

            # Use the QCM-optimized image if available
            best_image = preprocessed_images.get("qcm_optimized", preprocessed_images.get("binary_otsu"))

            # Extract text with QCM-specific configuration
            config = self.tesseract_config["qcm"]
            if ocr_settings and "tesseract_config" in ocr_settings:
                config = ocr_settings["tesseract_config"]

            # Extract text
            if not self.tesseract_available:
                return {
                    "success": False,
                    "error": "Tesseract OCR not available",
                    "model": "tesseract_qcm_optimized"
                }

            extracted_text = pytesseract.image_to_string(best_image, config=config, lang="eng+fra")

            # Extract student information and answers
            student_info = self._extract_student_info(extracted_text)
            answers = self._extract_qcm_answers_advanced(best_image, extracted_text)

            # Calculate confidence based on answer detection
            confidence = self._calculate_qcm_confidence(answers, extracted_text)

            return {
                "success": True,
                "extracted_text": extracted_text,
                "student_name": student_info.get("name", "Non détecté"),
                "student_id": student_info.get("id", ""),
                "extracted_answers": answers,
                "confidence": confidence,
                "model": "tesseract_qcm_optimized"
            }

        except Exception as e:
            logger.error(f"Error in QCM extraction: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "model": "tesseract_qcm_optimized"
            }

    async def _extract_with_tesseract_enhanced(
        self,
        image_path: str,
        preprocessed_images: Dict[str, np.ndarray],
        exam_type: ExamType
    ) -> Dict[str, Any]:
        """Enhanced Tesseract extraction with multiple preprocessing attempts"""
        try:
            if not self.tesseract_available:
                return {
                    "success": False,
                    "error": "Tesseract OCR not available",
                    "model": "tesseract_enhanced"
                }

            logger.info(f"Using enhanced Tesseract extraction for {exam_type.value}")

            best_result = None
            best_confidence = 0

            # Try different preprocessed images
            image_variants = [
                ("skew_corrected", preprocessed_images.get("skew_corrected")),
                ("enhanced", preprocessed_images.get("enhanced")),
                ("binary_otsu", preprocessed_images.get("binary_otsu")),
                ("binary_adaptive", preprocessed_images.get("binary_adaptive"))
            ]

            for variant_name, image in image_variants:
                if image is None:
                    continue

                try:
                    # Get appropriate config
                    config = self.tesseract_config.get(exam_type.value, self.tesseract_config["standard"])

                    # Extract text
                    extracted_text = pytesseract.image_to_string(image, config=config, lang="eng+fra")

                    # Get confidence data
                    confidence_data = pytesseract.image_to_data(image, config=config, output_type=pytesseract.Output.DICT)
                    avg_confidence = np.mean([int(conf) for conf in confidence_data['conf'] if int(conf) > 0])

                    if avg_confidence > best_confidence:
                        best_confidence = avg_confidence

                        # Extract additional information
                        student_info = self._extract_student_info(extracted_text)

                        best_result = {
                            "success": True,
                            "extracted_text": extracted_text,
                            "student_name": student_info.get("name", "Non détecté"),
                            "student_id": student_info.get("id", ""),
                            "extracted_answers": {},  # Will be filled by specific extractors
                            "confidence": avg_confidence / 100.0,  # Normalize to 0-1
                            "model": f"tesseract_enhanced_{variant_name}",
                            "preprocessing_variant": variant_name
                        }

                        # Extract answers if QCM
                        if exam_type == ExamType.QCM:
                            answers = self._extract_qcm_answers_advanced(image, extracted_text)
                            best_result["extracted_answers"] = answers

                except Exception as e:
                    logger.warning(f"Failed to process variant {variant_name}: {str(e)}")
                    continue

            if best_result:
                logger.info(f"Best result from variant: {best_result.get('preprocessing_variant')} with confidence: {best_confidence:.2f}")
                return best_result
            else:
                return {
                    "success": False,
                    "error": "All preprocessing variants failed",
                    "model": "tesseract_enhanced"
                }

        except Exception as e:
            logger.error(f"Error in enhanced Tesseract extraction: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "model": "tesseract_enhanced"
            }

    def _extract_student_info(self, text: str) -> Dict[str, str]:
        """Extract student name and ID from text"""
        import re

        student_info = {"name": "Non détecté", "id": ""}

        # Extract name patterns
        name_patterns = [
            r"[Nn]om\s*:?\s*([A-Za-zÀ-ÖØ-öø-ÿ\s\-\'\.]+)",
            r"[Nn]ame\s*:?\s*([A-Za-zÀ-ÖØ-öø-ÿ\s\-\'\.]+)",
            r"[Éé]tudiant\s*:?\s*([A-Za-zÀ-ÖØ-öø-ÿ\s\-\'\.]+)"
        ]

        for pattern in name_patterns:
            match = re.search(pattern, text)
            if match:
                student_info["name"] = match.group(1).strip()
                break

        # Extract ID patterns
        id_patterns = [
            r"[Ii][Dd]\s*:?\s*(\d+)",
            r"[Nn]uméro\s*:?\s*(\d+)",
            r"[Mm]atricule\s*:?\s*(\d+)"
        ]

        for pattern in id_patterns:
            match = re.search(pattern, text)
            if match:
                student_info["id"] = match.group(1).strip()
                break

        return student_info

    def _extract_qcm_answers_advanced(self, image: np.ndarray, text: str) -> Dict[int, str]:
        """Advanced QCM answer extraction using image processing and text analysis"""
        answers = {}

        try:
            # Text-based extraction
            text_answers = self._extract_answers_from_text(text)
            answers.update(text_answers)

            # Image-based extraction for marked boxes
            image_answers = self._extract_answers_from_image(image)

            # Merge results, preferring image-based detection
            for q_num, answer in image_answers.items():
                answers[q_num] = answer

        except Exception as e:
            logger.warning(f"Error in advanced QCM extraction: {str(e)}")

        return answers

    def _extract_answers_from_text(self, text: str) -> Dict[int, str]:
        """Extract answers from text using pattern matching"""
        import re

        answers = {}

        # Pattern for question-answer pairs
        patterns = [
            r"(?:Question|Q)?\s*(\d+)[\.:\)]\s*([A-Za-z])",
            r"(\d+)\s*[\.:\)]\s*([A-Za-z])",
            r"(\d+)\s*([A-Za-z])"
        ]

        for pattern in patterns:
            matches = re.findall(pattern, text)
            for match in matches:
                try:
                    q_num = int(match[0])
                    answer = match[1].upper()
                    if answer in ['A', 'B', 'C', 'D', 'E']:
                        answers[q_num] = answer
                except (ValueError, IndexError):
                    continue

        return answers

    def _extract_answers_from_image(self, image: np.ndarray) -> Dict[int, str]:
        """Extract answers by detecting marked boxes in the image"""
        # This is a simplified implementation
        # In a production system, this would use more sophisticated
        # computer vision techniques to detect marked checkboxes

        answers = {}

        try:
            # Find contours that might be checkboxes
            contours, _ = cv2.findContours(image, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

            # Filter contours by size and shape
            potential_boxes = []
            for contour in contours:
                area = cv2.contourArea(contour)
                if 100 < area < 1000:  # Reasonable checkbox size
                    x, y, w, h = cv2.boundingRect(contour)
                    aspect_ratio = w / h
                    if 0.5 < aspect_ratio < 2.0:  # Roughly square
                        potential_boxes.append((x, y, w, h, area))

            # Sort by position and analyze
            potential_boxes.sort(key=lambda x: (x[1], x[0]))  # Sort by y, then x

            # This is a simplified analysis
            # A full implementation would need more sophisticated logic
            # to determine which boxes are marked and map them to questions

        except Exception as e:
            logger.warning(f"Error in image-based answer extraction: {str(e)}")

        return answers

    def _calculate_qcm_confidence(self, answers: Dict[int, str], text: str) -> float:
        """Calculate confidence score for QCM extraction"""
        try:
            # Base confidence on number of answers found and text quality
            num_answers = len(answers)
            text_length = len(text.strip())

            # Simple heuristic
            if num_answers == 0:
                return 0.1
            elif num_answers < 5:
                return 0.3 + (num_answers * 0.1)
            elif num_answers < 10:
                return 0.6 + (num_answers * 0.02)
            else:
                return min(0.9, 0.8 + (text_length / 1000))

        except Exception:
            return 0.5

# Create singleton instance
enhanced_ocr_service = EnhancedOCRService()
