'use client';

import Link from 'next/link';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, Brain, FileText } from 'lucide-react';
import { Button } from '@/components/ui/button';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import { useAuth } from '@/providers/auth-provider';

export default function Home() {
  const { isAuthenticated } = useAuth();

  return (
    <div className="flex min-h-screen flex-col">
      <Header />

      {/* Hero Section */}
      <section className="relative py-20 md:py-32 overflow-hidden bg-gradient-to-br from-primary/5 to-accent/5">
        <div className="container mx-auto px-4">
          <div className="flex flex-col md:flex-row items-center">
            <div className="md:w-1/2 mb-12 md:mb-0">
              <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6 bg-gradient-to-r from-primary to-accent bg-clip-text text-transparent">
                Auto-Grade Scribe
              </h1>
              <p className="text-xl md:text-2xl mb-8 text-gray-700 dark:text-gray-300 max-w-xl">
                Correction automatique de copies d'examen avec analyse par Intelligence Artificielle
              </p>
              <div className="flex flex-col sm:flex-row gap-4">
                {isAuthenticated ? (
                  <Button size="lg" asChild>
                    <Link href="/dashboard" className="flex items-center gap-2">
                      Accéder au tableau de bord <ArrowRight className="h-4 w-4" />
                    </Link>
                  </Button>
                ) : (
                  <>
                    <Button size="lg" asChild>
                      <Link href="/login" className="flex items-center gap-2">
                        Commencer maintenant <ArrowRight className="h-4 w-4" />
                      </Link>
                    </Button>
                    <Button variant="outline" size="lg" asChild>
                      <Link href="#features">En savoir plus</Link>
                    </Button>
                  </>
                )}
              </div>
            </div>
            <div className="md:w-1/2 relative">
              <div className="relative z-10 bg-white dark:bg-gray-800 rounded-lg shadow-xl p-6 md:p-8 border border-gray-200 dark:border-gray-700">
                <div className="flex items-center mb-4">
                  <div className="h-10 w-10 rounded-full bg-primary/10 flex items-center justify-center mr-4">
                    <FileText className="h-5 w-5 text-primary" />
                  </div>
                  <div>
                    <h3 className="font-medium">Exemple de correction</h3>
                    <p className="text-sm text-gray-500">Analyse d'une copie d'examen</p>
                  </div>
                </div>
                <div className="space-y-4">
                  <div className="p-4 bg-gray-50 dark:bg-gray-900 rounded-md">
                    <p className="text-sm">
                      <span className="font-medium">Question 1:</span> Expliquez le concept de la gravité.
                    </p>
                    <p className="text-sm mt-2 pl-4 border-l-2 border-green-500">
                      La gravité est une force fondamentale qui attire les objets les uns vers les autres. Elle est proportionnelle à la masse des objets et inversement proportionnelle au carré de la distance qui les sépare.
                    </p>
                    <div className="flex justify-between items-center mt-2">
                      <span className="text-xs text-gray-500">Évaluation IA</span>
                      <span className="text-sm font-medium text-green-600">8/10 points</span>
                    </div>
                  </div>
                  <div className="p-4 bg-primary/5 rounded-md">
                    <div className="flex justify-between items-center mb-2">
                      <span className="font-medium">Note finale</span>
                      <span className="text-xl font-bold text-primary">B+</span>
                    </div>
                    <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2.5">
                      <div className="bg-primary h-2.5 rounded-full" style={{ width: '85%' }}></div>
                    </div>
                    <p className="text-sm mt-2 text-gray-600 dark:text-gray-400">
                      Bonne compréhension des concepts fondamentaux. Quelques imprécisions dans les explications.
                    </p>
                  </div>
                </div>
              </div>
              <div className="absolute -bottom-6 -right-6 w-64 h-64 bg-accent/10 rounded-full blur-3xl -z-10"></div>
              <div className="absolute -top-6 -left-6 w-64 h-64 bg-primary/10 rounded-full blur-3xl -z-10"></div>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section id="features" className="py-20 bg-white dark:bg-gray-900">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">Fonctionnalités principales</h2>
            <p className="text-xl text-gray-600 dark:text-gray-400 max-w-3xl mx-auto">
              Découvrez comment Auto-Grade Scribe révolutionne la correction des examens
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <div className="bg-white dark:bg-gray-800 p-8 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
              <div className="h-12 w-12 rounded-lg bg-primary/10 flex items-center justify-center mb-6">
                <Brain className="h-6 w-6 text-primary" />
              </div>
              <h3 className="text-xl font-bold mb-3">Intelligence Artificielle</h3>
              <p className="text-gray-600 dark:text-gray-400">
                Analyse avancée des réponses avec compréhension du contexte et évaluation précise des connaissances.
              </p>
            </div>

            <div className="bg-white dark:bg-gray-800 p-8 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
              <div className="h-12 w-12 rounded-lg bg-primary/10 flex items-center justify-center mb-6">
                <FileText className="h-6 w-6 text-primary" />
              </div>
              <h3 className="text-xl font-bold mb-3">OCR de pointe</h3>
              <p className="text-gray-600 dark:text-gray-400">
                Reconnaissance optique des caractères pour extraire le texte des copies manuscrites avec une précision exceptionnelle.
              </p>
            </div>

            <div className="bg-white dark:bg-gray-800 p-8 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
              <div className="h-12 w-12 rounded-lg bg-primary/10 flex items-center justify-center mb-6">
                <Award className="h-6 w-6 text-primary" />
              </div>
              <h3 className="text-xl font-bold mb-3">Évaluation détaillée</h3>
              <p className="text-gray-600 dark:text-gray-400">
                Feedback personnalisé pour chaque question avec suggestions d'amélioration et analyse des points forts.
              </p>
            </div>

            <div className="bg-white dark:bg-gray-800 p-8 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
              <div className="h-12 w-12 rounded-lg bg-primary/10 flex items-center justify-center mb-6">
                <CheckCircle className="h-6 w-6 text-primary" />
              </div>
              <h3 className="text-xl font-bold mb-3">QCM automatisé</h3>
              <p className="text-gray-600 dark:text-gray-400">
                Correction instantanée des questionnaires à choix multiples avec détection précise des réponses marquées.
              </p>
            </div>

            <div className="bg-white dark:bg-gray-800 p-8 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
              <div className="h-12 w-12 rounded-lg bg-primary/10 flex items-center justify-center mb-6">
                <BookOpen className="h-6 w-6 text-primary" />
              </div>
              <h3 className="text-xl font-bold mb-3">Analyse pédagogique</h3>
              <p className="text-gray-600 dark:text-gray-400">
                Identification des lacunes d'apprentissage et recommandations de ressources éducatives adaptées.
              </p>
            </div>

            <div className="bg-white dark:bg-gray-800 p-8 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
              <div className="h-12 w-12 rounded-lg bg-primary/10 flex items-center justify-center mb-6">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
              </div>
              <h3 className="text-xl font-bold mb-3">Statistiques avancées</h3>
              <p className="text-gray-600 dark:text-gray-400">
                Visualisation des performances et tendances pour suivre la progression des étudiants au fil du temps.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-br from-primary to-accent text-white">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-6">Prêt à révolutionner vos corrections ?</h2>
          <p className="text-xl mb-8 max-w-2xl mx-auto opacity-90">
            Rejoignez des milliers d'enseignants qui gagnent du temps et améliorent la qualité de leurs évaluations.
          </p>
          <Button size="lg" variant="secondary" asChild className="font-medium">
            <Link href="/login">Commencer gratuitement</Link>
          </Button>
        </div>
      </section>

      <Footer />
    </div>
  );
}
