import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';

// Liste des chemins publics qui ne nécessitent pas d'authentification
const publicPaths = ['/', '/login', '/about'];

export function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;

  // Vérifier si le chemin est public
  const isPublicPath = publicPaths.some(path =>
    pathname === path || pathname.startsWith(`${path}/`)
  );

  // Récupérer le token d'authentification depuis les cookies
  const authToken = request.cookies.get('accessToken')?.value;

  // Vérifier si la requête est une requête API
  const isApiRequest = pathname.startsWith('/api/');

  // Autoriser toutes les requêtes API à passer (l'authentification sera gérée par le backend)
  if (isApiRequest) {
    return NextResponse.next();
  }

  // Si le chemin n'est pas public et qu'il n'y a pas de token, rediriger vers la page de connexion
  if (!isPublicPath && !authToken) {
    console.log(`Middleware: Redirection vers /login depuis ${pathname} (non authentifié)`);
    const loginUrl = new URL('/login', request.url);
    loginUrl.searchParams.set('from', pathname);
    return NextResponse.redirect(loginUrl);
  }

  // Si l'utilisateur est déjà connecté et essaie d'accéder à la page de connexion, rediriger vers le tableau de bord
  if (pathname === '/login' && authToken) {
    console.log('Middleware: Redirection vers /dashboard depuis /login (déjà authentifié)');
    return NextResponse.redirect(new URL('/dashboard', request.url));
  }

  // Ajouter des en-têtes pour le débogage
  const response = NextResponse.next();
  response.headers.set('x-middleware-cache', 'no-cache');
  response.headers.set('x-auth-status', authToken ? 'authenticated' : 'unauthenticated');

  // Continuer avec la requête normale
  return response;
}

// Configurer les chemins sur lesquels le middleware doit s'exécuter
export const config = {
  matcher: [
    /*
     * Match all request paths except:
     * 1. /api routes
     * 2. /_next (Next.js internals)
     * 3. /fonts, /images (static files)
     * 4. /favicon.ico, /sitemap.xml (static files)
     */
    '/((?!api|_next|fonts|images|favicon.ico|sitemap.xml).*)',
  ],
};
