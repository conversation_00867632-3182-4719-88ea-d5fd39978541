#pragma once

// @generated by torchgen/gen.py from Function.h

#include <ATen/Context.h>
#include <ATen/DeviceGuard.h>
#include <ATen/TensorUtils.h>
#include <ATen/TracerMode.h>
#include <ATen/core/Generator.h>
#include <ATen/core/Reduction.h>
#include <ATen/core/Tensor.h>
#include <c10/core/Scalar.h>
#include <c10/core/Storage.h>
#include <c10/core/TensorOptions.h>
#include <c10/util/Deprecated.h>
#include <c10/util/Optional.h>



#include <ATen/ops/dsplit_ops.h>

namespace at {


// aten::dsplit.int(Tensor(a -> *) self, int sections) -> Tensor(a)[]
inline ::std::vector<at::Tensor> dsplit(const at::Tensor & self, int64_t sections) {
    return at::_ops::dsplit_int::call(self, sections);
}

// aten::dsplit.array(Tensor(a -> *) self, int[] indices) -> Tensor(a)[]
inline ::std::vector<at::Tensor> dsplit(const at::Tensor & self, at::IntArrayRef indices) {
    return at::_ops::dsplit_array::call(self, indices);
}

}
