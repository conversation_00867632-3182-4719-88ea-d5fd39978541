#pragma once

// @generated by torchgen/gen.py from Function.h

#include <ATen/Context.h>
#include <ATen/DeviceGuard.h>
#include <ATen/TensorUtils.h>
#include <ATen/TracerMode.h>
#include <ATen/core/Generator.h>
#include <ATen/core/Reduction.h>
#include <ATen/core/Tensor.h>
#include <c10/core/Scalar.h>
#include <c10/core/Storage.h>
#include <c10/core/TensorOptions.h>
#include <c10/util/Deprecated.h>
#include <c10/util/Optional.h>



#include <ATen/ops/relu6_ops.h>

namespace at {


// aten::relu6(Tensor self) -> Tensor
inline at::Tensor relu6(const at::Tensor & self) {
    return at::_ops::relu6::call(self);
}

// aten::relu6_(Tensor(a!) self) -> Tensor(a!)
inline at::Tensor & relu6_(at::Tensor & self) {
    return at::_ops::relu6_::call(self);
}

}
