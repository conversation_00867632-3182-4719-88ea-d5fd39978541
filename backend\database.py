from sqlalchemy import create_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
import os
from dotenv import load_dotenv
import sys

# Charger les variables d'environnement
load_dotenv()

# Utiliser l'URL de la base de données depuis les variables d'environnement
DATABASE_URL = os.getenv("DATABASE_URL", "sqlite:///./autogradescribe.db")
SQLITE_URL = "sqlite:///./autogradescribe.db"

# Créer le moteur de base de données
try:
    # Configurer le moteur en fonction de l'URL
    if DATABASE_URL.startswith("postgresql"):
        try:
            # Configuration pour PostgreSQL
            engine = create_engine(
                DATABASE_URL,
                pool_pre_ping=True,
                pool_size=10,
                max_overflow=20
            )

            # Tester la connexion
            connection = engine.connect()
            connection.close()
            print("Connexion à PostgreSQL réussie!")
        except Exception as e:
            print(f"Erreur lors de la configuration de la base de données PostgreSQL: {e}")
            print("Utilisation de SQLite comme solution de secours...")

            # Fallback to SQLite
            engine = create_engine(
                SQLITE_URL,
                pool_pre_ping=True,
                connect_args={"check_same_thread": False}
            )
    else:
        # Configuration pour SQLite
        engine = create_engine(
            DATABASE_URL,
            pool_pre_ping=True,
            connect_args={"check_same_thread": False}
        )
        print("Utilisation de SQLite comme base de données.")

    # Tester la connexion finale
    connection = engine.connect()
    connection.close()

except Exception as e:
    print(f"Erreur lors de la configuration de la base de données: {e}")
    sys.exit(1)

# Créer une session locale
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# Classe de base pour les modèles
Base = declarative_base()

# Fonction de dépendance pour obtenir la session DB
def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()