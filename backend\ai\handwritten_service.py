import os
import base64
from datetime import datetime
from typing import Dict, List, Any, Optional
from pathlib import Path
import json
import torch
from transformers import AutoModelForCausalLM, AutoTokenizer, pipeline
# Import du service OCR Gemini
from ai.ocr_service import ocr_service

# Configuration pour utiliser un modèle Hugging Face directement
MODEL_NAME = "TinyLlama/TinyLlama-1.1B-Chat-v1.0"  # Modèle léger et open source
# Pour utiliser Mistral-7B, décommentez la ligne suivante et assurez-vous d'être connecté à Hugging Face:
# 1. Exécutez `pip install huggingface_hub`
# 2. <PERSON>uis `huggingface-cli login` et suivez les instructions
# MODEL_NAME = "mistralai/Mistral-7B-Instruct-v0.2"  # Requiert une authentification Hugging Face
MAX_LENGTH = 2048
DEVICE = "cuda" if torch.cuda.is_available() else "cpu"

class HandwrittenExamGrader:
    def __init__(self):
        self.model_name = MODEL_NAME
        self.device = DEVICE
        self.tokenizer = None
        self.model = None
        self.generator = None
        self._initialize_model()

    def _initialize_model(self):
        """Initialise le modèle LLM depuis Hugging Face"""
        print(f"Chargement du modèle {self.model_name} sur {self.device}...")

        # Utilisation du pipeline de generation qui optimise les ressources
        try:
            model_kwargs = {
                # "device_map": "auto",
                "load_in_8bit": True if self.device == "cuda" else False,
                "torch_dtype": torch.float16 if self.device == "cuda" else torch.float32
            }

            self.generator = pipeline(
                "text-generation",
                model=self.model_name,
                tokenizer=self.model_name,
                model_kwargs=model_kwargs,
                max_length=MAX_LENGTH
            )
            print("Modèle chargé avec succès!")
        except Exception as e:
            print(f"Erreur lors du chargement du modèle: {e}")
            # Fallback sur un modèle plus petit si nécessaire
            try:
                print("Tentative de chargement d'un modèle plus léger...")
                self.model_name = "TinyLlama/TinyLlama-1.1B-Chat-v1.0"
                self.generator = pipeline(
                    "text-generation",
                    model=self.model_name,
                    tokenizer=self.model_name
                )
                print("Modèle de fallback chargé avec succès!")
            except Exception as e2:
                print(f"Échec également sur le modèle de fallback: {e2}")
                print("Le service fonctionnera en mode dégradé")

    def _encode_image(self, image_path: str) -> str:
        """Encode l'image en base64"""
        with open(image_path, "rb") as image_file:
            return base64.b64encode(image_file.read()).decode('utf-8')

    def _extract_text_with_gemini(self, image_path: str, mode: str = "handwritten") -> str:
        """
        Extrait le texte d'une image en utilisant Gemini Pro Vision

        Args:
            image_path: Chemin vers l'image
            mode: Mode d'extraction (handwritten, standard, exam, structured)

        Returns:
            str: Texte extrait de l'image
        """
        try:
            # Utiliser le service OCR Gemini
            ocr_result = ocr_service.extract_text(image_path, mode=mode)

            if not ocr_result["success"]:
                print(f"Erreur lors de l'extraction avec Gemini: {ocr_result.get('error', 'Erreur inconnue')}")
                return ""

            # Récupérer le texte extrait
            extracted_text = ocr_result.get("extracted_text", "")

            # Vérifier si le texte extrait est significatif
            if not extracted_text or len(extracted_text.strip()) < 10:
                print("Extraction avec Gemini a produit peu de résultats, essai avec un autre mode")
                # Essayer avec un autre mode
                ocr_result = ocr_service.extract_text(image_path, mode="standard")
                extracted_text = ocr_result.get("extracted_text", "")

            return extracted_text

        except Exception as e:
            print(f"Erreur lors de l'extraction de texte avec Gemini: {e}")
            return ""

    def _process_with_hf_model(self, prompt: str) -> str:
        """Traite le texte avec le modèle Hugging Face"""
        if not self.generator:
            # Si le modèle n'a pas pu être chargé, retourner une réponse générique
            return json.dumps({
                "score_total": 0,
                "note": "F",
                "commentaire_général": "Impossible d'évaluer la réponse (modèle non chargé)"
            })

        try:
            # Formatage du prompt pour modèle Mistral Instruct
            formatted_prompt = f"""<s>[INST] {prompt} [/INST]"""

            # Générer la réponse
            response = self.generator(
                formatted_prompt,
                max_new_tokens=1024,
                temperature=0.1,
                top_p=0.9,
                repetition_penalty=1.1,
                do_sample=True,
                num_return_sequences=1
            )

            generated_text = response[0]['generated_text']

            # Extraire la réponse après le prompt
            answer = generated_text.split("[/INST]")[-1].strip()

            return answer
        except Exception as e:
            print(f"Erreur lors du traitement avec le modèle Hugging Face: {e}")
            return json.dumps({
                "score_total": 0,
                "note": "F",
                "commentaire_général": f"Erreur lors de l'évaluation: {str(e)}"
            })

    def grade_handwritten_exam(self, image_path: str, rubric: Dict[str, Any]) -> Dict[str, Any]:
        """
        Analyse et note un examen manuscrit basé sur les critères fournis avec une évaluation améliorée

        Args:
            image_path: Chemin vers l'image de l'examen
            rubric: Dictionnaire contenant la rubrique d'évaluation avec les réponses correctes

        Returns:
            Dict: Résultat de l'évaluation avec score, commentaires détaillés et suggestions d'amélioration
        """
        try:
            # Utiliser le service OCR amélioré au lieu de Tesseract
            from ai.ocr_service import ocr_service

            # Extraire le texte avec le service OCR amélioré en mode examen manuscrit
            ocr_result = ocr_service.extract_text(image_path, mode="handwritten")

            if not ocr_result["success"]:
                return {
                    "success": False,
                    "error": ocr_result.get("error", "Impossible d'extraire le texte de l'image")
                }

            extracted_text = ocr_result["extracted_text"]

            if not extracted_text or len(extracted_text.strip()) < 10:
                # Essayer avec le mode standard si le mode manuscrit ne donne pas de bons résultats
                ocr_result = ocr_service.extract_text(image_path, mode="standard")
                extracted_text = ocr_result.get("extracted_text", "")

                if not extracted_text or len(extracted_text.strip()) < 10:
                    # Si toujours pas de résultats satisfaisants, essayer avec d'autres modes Gemini
                    print("Tentative avec le mode 'exam' de Gemini")
                    ocr_result = ocr_service.extract_text(image_path, mode="exam")
                    extracted_text = ocr_result.get("extracted_text", "")

            if not extracted_text:
                return {
                    "success": False,
                    "error": "Impossible d'extraire le texte de l'image avec les méthodes disponibles"
                }

            # Préparer un prompt amélioré pour l'évaluation par le LLM
            prompt = f"""
            En tant qu'évaluateur d'examen expert, évalue cette réponse d'étudiant selon les critères fournis.

            Réponse de l'étudiant:
            ```
            {extracted_text}
            ```

            Critères d'évaluation:
            ```json
            {json.dumps(rubric, indent=2, ensure_ascii=False)}
            ```

            Instructions d'évaluation:
            1. Analyse chaque question individuellement
            2. Attribue des points selon la rubrique fournie
            3. Identifie les forces et faiblesses de chaque réponse
            4. Fournis des commentaires constructifs et des suggestions d'amélioration
            5. Évalue la clarté, la précision et la pertinence des réponses
            6. Tiens compte des fautes d'orthographe mais ne pénalise pas excessivement

            Fournit le format de réponse suivant:

            {{
              "score_total": (nombre sur 100),
              "note": "(lettre correspondante A-F)",
              "évaluation_par_question": [
                {{
                  "question": "numéro/identifiant de la question",
                  "points_obtenus": (nombre),
                  "points_possibles": (nombre),
                  "commentaire": "explication détaillée de l'évaluation",
                  "forces": ["point fort 1", "point fort 2", ...],
                  "faiblesses": ["point faible 1", "point faible 2", ...],
                  "suggestions": "suggestions concrètes pour améliorer la réponse"
                }},
                ...
              ],
              "commentaire_général": "commentaire global sur la performance",
              "points_forts": ["point fort global 1", "point fort global 2", ...],
              "axes_amélioration": ["axe d'amélioration 1", "axe d'amélioration 2", ...],
              "conseils_pédagogiques": "conseils pour progresser"
            }}

            Réponds UNIQUEMENT avec ce JSON, sans texte supplémentaire.
            """

            # Traiter avec le LLM
            result_text = self._process_with_hf_model(prompt)

            try:
                # Essayer d'extraire le JSON de la réponse
                json_start = result_text.find('{')
                json_end = result_text.rfind('}') + 1
                if json_start >= 0 and json_end > json_start:
                    json_str = result_text[json_start:json_end]
                    grading_result = json.loads(json_str)
                else:
                    # Si aucun JSON n'est trouvé, faire une seconde tentative avec un prompt simplifié
                    simplified_prompt = f"""
                    Évalue cette réponse d'étudiant et génère un JSON avec le score et les commentaires.

                    Réponse: {extracted_text[:500]}...

                    Format JSON requis:
                    {{
                      "score_total": (nombre sur 100),
                      "note": "(lettre A-F)",
                      "commentaire_général": "commentaire"
                    }}

                    Réponds UNIQUEMENT avec ce JSON.
                    """

                    retry_result = self._process_with_hf_model(simplified_prompt)
                    json_start = retry_result.find('{')
                    json_end = retry_result.rfind('}') + 1

                    if json_start >= 0 and json_end > json_start:
                        json_str = retry_result[json_start:json_end]
                        grading_result = json.loads(json_str)
                    else:
                        # Si toujours pas de JSON, créer un résultat par défaut
                        grading_result = {
                            "score_total": 60,
                            "note": "C",
                            "commentaire_général": "Évaluation automatique basée sur le contenu extrait. Vérification manuelle recommandée.",
                            "évaluation_par_question": [
                                {
                                    "question": "global",
                                    "points_obtenus": 60,
                                    "points_possibles": 100,
                                    "commentaire": "Évaluation automatique basée sur le contenu extrait."
                                }
                            ]
                        }

                # Ajouter des métadonnées sur la qualité de l'extraction
                metadata = {
                    "extraction_quality": ocr_result.get("quality_metrics", {}),
                    "extraction_method": ocr_result.get("quality_metrics", {}).get("extraction_mode", "unknown"),
                    "processing_timestamp": datetime.now().isoformat(),
                    "model_used": self.model_name,
                    "confidence_score": 0.8 if json_start >= 0 and json_end > json_start else 0.5
                }

                return {
                    "success": True,
                    "grading_result": grading_result,
                    "extracted_text": extracted_text,
                    "metadata": metadata
                }

            except json.JSONDecodeError:
                # En cas d'erreur de décodage JSON, créer un résultat simplifié
                fallback_result = {
                    "score_total": 50,
                    "note": "C-",
                    "commentaire_général": "Évaluation partielle due à des difficultés d'analyse. Vérification manuelle recommandée.",
                    "texte_brut": result_text[:200] + "..." if len(result_text) > 200 else result_text
                }

                return {
                    "success": True,
                    "grading_result": fallback_result,
                    "extracted_text": extracted_text,
                    "warning": "Impossible de parser la réponse du LLM, résultat simplifié généré"
                }

        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "error_type": type(e).__name__,
                "suggestion": "Veuillez réessayer ou utiliser un autre format d'image"
            }

# Créer une instance du service
handwritten_grader = HandwrittenExamGrader()