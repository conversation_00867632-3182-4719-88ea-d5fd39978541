version: '3.8'

services:
  postgres:
    image: postgres:15
    container_name: auto-grade-postgres
    environment:
      POSTGRES_DB: gradegeniusdb
      POSTGRES_USER: autograde
      POSTGRES_PASSWORD: autograde123
      POSTGRES_HOST_AUTH_METHOD: trust
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init-db.sql:/docker-entrypoint-initdb.d/init-db.sql
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U autograde -d gradegeniusdb"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 10s
    networks:
      - auto-grade-network

  backend:
    build: 
      context: ./backend
      dockerfile: Dockerfile.simple
    container_name: auto-grade-backend
    environment:
      DATABASE_URL: *************************************************/gradegeniusdb
      ENVIRONMENT: development
      DEBUG: "true"
      SECRET_KEY: your-secret-key-change-in-production
      API_HOST: 0.0.0.0
      API_PORT: 8000
    ports:
      - "8000:8000"
    volumes:
      - ./uploads:/app/uploads
      - ./results:/app/results
      - ./logs:/app/logs
    depends_on:
      postgres:
        condition: service_healthy
    restart: unless-stopped
    networks:
      - auto-grade-network

volumes:
  postgres_data:
    driver: local

networks:
  auto-grade-network:
    driver: bridge
