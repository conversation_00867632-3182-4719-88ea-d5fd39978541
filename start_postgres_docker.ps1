# Script de démarrage rapide PostgreSQL avec Docker
Write-Host "🐘 Démarrage PostgreSQL avec Docker pour Auto-Grade Scribe" -ForegroundColor Green

# Vérifier si Docker est disponible
try {
    docker --version | Out-Null
    Write-Host "✅ Docker trouvé" -ForegroundColor Green
} catch {
    Write-Host "❌ Docker non trouvé. Veuillez installer Docker Desktop." -ForegroundColor Red
    Write-Host "Téléchargez depuis: https://www.docker.com/products/docker-desktop" -ForegroundColor Cyan
    exit 1
}

# Arrêter les conteneurs existants
Write-Host "🛑 Arrêt des conteneurs existants..." -ForegroundColor Yellow
docker stop auto-grade-postgres auto-grade-pgadmin 2>$null
docker rm auto-grade-postgres auto-grade-pgadmin 2>$null

# Démarrer PostgreSQL
Write-Host "🚀 Démarrage de PostgreSQL..." -ForegroundColor Yellow
docker run -d `
    --name auto-grade-postgres `
    -e POSTGRES_DB=gradegeniusdb `
    -e POSTGRES_USER=autograde `
    -e POSTGRES_PASSWORD=autograde123 `
    -e POSTGRES_HOST_AUTH_METHOD=trust `
    -p 5432:5432 `
    -v postgres_data:/var/lib/postgresql/data `
    postgres:15

if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ PostgreSQL démarré avec succès" -ForegroundColor Green
} else {
    Write-Host "❌ Erreur lors du démarrage de PostgreSQL" -ForegroundColor Red
    exit 1
}

# Attendre que PostgreSQL soit prêt
Write-Host "⏳ Attente que PostgreSQL soit prêt..." -ForegroundColor Yellow
$maxAttempts = 30
$attempt = 0

do {
    $attempt++
    Start-Sleep -Seconds 2
    
    try {
        $result = docker exec auto-grade-postgres pg_isready -U autograde -d gradegeniusdb 2>$null
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ PostgreSQL est prêt!" -ForegroundColor Green
            break
        }
    } catch {
        # Continue à attendre
    }
    
    if ($attempt -ge $maxAttempts) {
        Write-Host "❌ Timeout: PostgreSQL n'est pas prêt après $maxAttempts tentatives" -ForegroundColor Red
        exit 1
    }
    
    Write-Host "⏳ Tentative $attempt/$maxAttempts..." -ForegroundColor Yellow
} while ($true)

# Démarrer pgAdmin (optionnel)
$startPgAdmin = Read-Host "Voulez-vous démarrer pgAdmin? (y/n)"
if ($startPgAdmin -eq 'y') {
    Write-Host "🚀 Démarrage de pgAdmin..." -ForegroundColor Yellow
    docker run -d `
        --name auto-grade-pgadmin `
        -e PGADMIN_DEFAULT_EMAIL=<EMAIL> `
        -e PGADMIN_DEFAULT_PASSWORD=admin123 `
        -e PGADMIN_CONFIG_SERVER_MODE=False `
        -p 8080:80 `
        -v pgadmin_data:/var/lib/pgadmin `
        dpage/pgadmin4:latest
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ pgAdmin démarré avec succès" -ForegroundColor Green
        Write-Host "🌐 pgAdmin accessible sur: http://localhost:8080" -ForegroundColor Cyan
        Write-Host "📧 Email: <EMAIL>" -ForegroundColor White
        Write-Host "🔑 Mot de passe: admin123" -ForegroundColor White
    }
}

# Créer le fichier .env
Write-Host "⚙️ Création du fichier .env..." -ForegroundColor Yellow
$envContent = @"
# Configuration PostgreSQL Docker pour Auto-Grade Scribe
ENVIRONMENT=development
DEBUG=true

# Database PostgreSQL
DATABASE_URL=postgresql://autograde:autograde123@localhost:5432/gradegeniusdb

# Security
SECRET_KEY=your-secret-key-change-in-production
JWT_ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=60

# API Configuration
API_HOST=0.0.0.0
API_PORT=8000
CORS_ORIGINS=http://localhost:3000,http://127.0.0.1:3000

# File Upload
MAX_FILE_SIZE=52428800
UPLOAD_DIRECTORY=uploads
RESULTS_DIRECTORY=results
TEMP_DIRECTORY=temp

# OCR Configuration
TESSERACT_TIMEOUT=300
OCR_CONFIDENCE_THRESHOLD=0.6

# AI Configuration (ajoutez votre clé API Google ici)
# GOOGLE_API_KEY=your-google-api-key-here
GEMINI_MODEL=gemini-pro-vision
GEMINI_TIMEOUT=60

# Logging
LOG_LEVEL=INFO
LOG_FILE=logs/app.log

# Database Connection Pool
DATABASE_POOL_SIZE=10
DATABASE_MAX_OVERFLOW=20
DATABASE_POOL_TIMEOUT=30
"@

$envContent | Out-File -FilePath ".env" -Encoding UTF8
Write-Host "✅ Fichier .env créé" -ForegroundColor Green

# Tester la connexion
Write-Host "🧪 Test de connexion..." -ForegroundColor Yellow
try {
    docker exec auto-grade-postgres psql -U autograde -d gradegeniusdb -c "SELECT version();" | Out-Null
    Write-Host "✅ Connexion PostgreSQL réussie!" -ForegroundColor Green
} catch {
    Write-Host "⚠️ Erreur de test de connexion" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "🎉 PostgreSQL configuré avec succès!" -ForegroundColor Green
Write-Host ""
Write-Host "📋 Informations de connexion:" -ForegroundColor Cyan
Write-Host "  Host: localhost" -ForegroundColor White
Write-Host "  Port: 5432" -ForegroundColor White
Write-Host "  Database: gradegeniusdb" -ForegroundColor White
Write-Host "  User: autograde" -ForegroundColor White
Write-Host "  Password: autograde123" -ForegroundColor White
Write-Host ""
Write-Host "🚀 Pour tester la configuration:" -ForegroundColor Cyan
Write-Host "  python test_postgres_setup.py" -ForegroundColor White
Write-Host ""
Write-Host "🚀 Pour démarrer l'application:" -ForegroundColor Cyan
Write-Host "  cd backend" -ForegroundColor White
Write-Host "  python -m uvicorn app:app --host 0.0.0.0 --port 8000 --reload" -ForegroundColor White
Write-Host ""
Write-Host "🛑 Pour arrêter PostgreSQL:" -ForegroundColor Cyan
Write-Host "  docker stop auto-grade-postgres auto-grade-pgadmin" -ForegroundColor White
Write-Host ""
Write-Host "🗑️ Pour supprimer complètement:" -ForegroundColor Cyan
Write-Host "  docker rm auto-grade-postgres auto-grade-pgadmin" -ForegroundColor White
Write-Host "  docker volume rm postgres_data pgadmin_data" -ForegroundColor White
