version: '3.8'

services:
  postgres:
    image: postgres:14
    container_name: autogradedb
    restart: always
    environment:
      POSTGRES_USER: autograde
      POSTGRES_PASSWORD: autograde123
      POSTGRES_DB: gradegeniusdb
      POSTGRES_HOST_AUTH_METHOD: trust
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    command: >
      -c listen_addresses='*'
      -c max_connections=100
    networks:
      - autograde-network

  pgadmin:
    image: dpage/pgadmin4
    container_name: pgadmin
    restart: always
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: admin123
    ports:
      - "5050:80"
    depends_on:
      - postgres
    networks:
      - autograde-network

networks:
  autograde-network:
    driver: bridge

volumes:
  postgres_data:
