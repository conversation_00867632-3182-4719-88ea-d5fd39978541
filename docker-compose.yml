version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:14
    container_name: autogradedb
    restart: always
    environment:
      POSTGRES_USER: autograde
      POSTGRES_PASSWORD: autograde123
      POSTGRES_DB: gradegeniusdb
      POSTGRES_HOST_AUTH_METHOD: trust
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    command: >
      -c listen_addresses='*'
      -c max_connections=100
    networks:
      - autograde-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U autograde -d gradegeniusdb"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Backend API Service
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: autograde-backend
    restart: always
    depends_on:
      postgres:
        condition: service_healthy
    environment:
      - DATABASE_URL=*************************************************/gradegeniusdb
      - GOOGLE_API_KEY=${GOOGLE_API_KEY}
    volumes:
      - ./backend:/app
      - uploads_data:/app/uploads
      - results_data:/app/results
    ports:
      - "8000:8000"
    networks:
      - autograde-network

  # Frontend Service
  frontend:
    build:
      context: .
      dockerfile: Dockerfile.frontend
    container_name: autograde-frontend
    restart: always
    depends_on:
      - backend
    environment:
      - NEXT_PUBLIC_API_URL=http://localhost:8000
    ports:
      - "3000:3000"
    networks:
      - autograde-network

  # PgAdmin for database management
  pgadmin:
    image: dpage/pgadmin4
    container_name: pgadmin
    restart: always
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: admin123
    ports:
      - "5050:80"
    depends_on:
      - postgres
    networks:
      - autograde-network

networks:
  autograde-network:
    driver: bridge

volumes:
  postgres_data:
  uploads_data:
  results_data:
