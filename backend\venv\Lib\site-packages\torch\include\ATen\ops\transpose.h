#pragma once

// @generated by torchgen/gen.py from Function.h

#include <ATen/Context.h>
#include <ATen/DeviceGuard.h>
#include <ATen/TensorUtils.h>
#include <ATen/TracerMode.h>
#include <ATen/core/Generator.h>
#include <ATen/core/Reduction.h>
#include <ATen/core/Tensor.h>
#include <c10/core/Scalar.h>
#include <c10/core/Storage.h>
#include <c10/core/TensorOptions.h>
#include <c10/util/Deprecated.h>
#include <c10/util/Optional.h>



#include <ATen/ops/transpose_ops.h>

namespace at {


// aten::transpose.int(Tensor(a) self, int dim0, int dim1) -> Tensor(a)
inline at::Tensor transpose(const at::Tensor & self, int64_t dim0, int64_t dim1) {
    return at::_ops::transpose_int::call(self, dim0, dim1);
}

// aten::transpose.Dimname(Tensor(a) self, Dimname dim0, Dimname dim1) -> Tensor(a)
inline at::Tensor transpose(const at::Tensor & self, at::Dimname dim0, at::Dimname dim1) {
    return at::_ops::transpose_Dimname::call(self, dim0, dim1);
}

}
