"""
Service OCR Avancé pour Auto-Grade Scribe
Intègre plusieurs providers d'OCR pour une précision maximale
"""

import os
import logging
import asyncio
from typing import Dict, Any, List, Optional, Tuple
from PIL import Image, ImageEnhance, ImageFilter
import numpy as np
from datetime import datetime
import base64
import io

logger = logging.getLogger("auto-grade-scribe.enhanced-ocr")

class EnhancedOCRService:
    """Service OCR avancé avec multiple providers"""
    
    def __init__(self):
        self.providers = {}
        self.confidence_threshold = 0.7
        
        # Initialiser les providers disponibles
        self._init_tesseract()
        self._init_google_vision()
        self._init_openai_vision()
        self._init_azure_vision()
        
        logger.info(f"Enhanced OCR Service initialized with {len(self.providers)} providers")
    
    def _init_tesseract(self):
        """Initialiser Tesseract OCR"""
        try:
            import pytesseract
            self.providers['tesseract'] = {
                'available': True,
                'priority': 1,
                'best_for': ['printed_text', 'forms']
            }
            logger.info("Tesseract OCR initialized")
        except ImportError:
            logger.warning("Tesseract not available")
            self.providers['tesseract'] = {'available': False}
    
    def _init_google_vision(self):
        """Initialiser Google Vision API"""
        try:
            import google.cloud.vision as vision
            api_key = os.getenv('GOOGLE_API_KEY')
            if api_key:
                self.providers['google_vision'] = {
                    'available': True,
                    'priority': 2,
                    'best_for': ['handwritten_text', 'mixed_content']
                }
                logger.info("Google Vision API initialized")
            else:
                logger.warning("Google Vision API key not found")
                self.providers['google_vision'] = {'available': False}
        except ImportError:
            logger.warning("Google Vision not available")
            self.providers['google_vision'] = {'available': False}
    
    def _init_openai_vision(self):
        """Initialiser OpenAI GPT-4 Vision"""
        try:
            import openai
            api_key = os.getenv('OPENAI_API_KEY')
            if api_key:
                self.providers['openai_vision'] = {
                    'available': True,
                    'priority': 3,
                    'best_for': ['handwritten_text', 'complex_layouts', 'mathematical_expressions']
                }
                logger.info("OpenAI Vision initialized")
            else:
                logger.warning("OpenAI API key not found")
                self.providers['openai_vision'] = {'available': False}
        except ImportError:
            logger.warning("OpenAI not available")
            self.providers['openai_vision'] = {'available': False}
    
    def _init_azure_vision(self):
        """Initialiser Azure Computer Vision"""
        try:
            from azure.cognitiveservices.vision.computervision import ComputerVisionClient
            endpoint = os.getenv('AZURE_VISION_ENDPOINT')
            key = os.getenv('AZURE_VISION_KEY')
            if endpoint and key:
                self.providers['azure_vision'] = {
                    'available': True,
                    'priority': 4,
                    'best_for': ['printed_text', 'documents']
                }
                logger.info("Azure Vision initialized")
            else:
                logger.warning("Azure Vision credentials not found")
                self.providers['azure_vision'] = {'available': False}
        except ImportError:
            logger.warning("Azure Vision not available")
            self.providers['azure_vision'] = {'available': False}
    
    async def extract_text_multi_provider(
        self, 
        image_path: str, 
        content_type: str = 'mixed'
    ) -> Dict[str, Any]:
        """
        Extraire le texte en utilisant plusieurs providers OCR
        
        Args:
            image_path: Chemin vers l'image
            content_type: Type de contenu ('handwritten', 'printed', 'mixed', 'mathematical')
            
        Returns:
            Résultat consolidé avec le meilleur texte extrait
        """
        try:
            # Préprocesser l'image
            processed_images = await self._preprocess_image_advanced(image_path)
            
            # Sélectionner les meilleurs providers pour le type de contenu
            selected_providers = self._select_providers_for_content(content_type)
            
            # Extraire le texte avec chaque provider
            results = {}
            for provider_name in selected_providers:
                if self.providers[provider_name]['available']:
                    try:
                        result = await self._extract_with_provider(
                            provider_name, 
                            processed_images, 
                            content_type
                        )
                        results[provider_name] = result
                        logger.info(f"OCR {provider_name}: confidence={result.get('confidence', 0):.2f}")
                    except Exception as e:
                        logger.error(f"Error with {provider_name}: {str(e)}")
                        results[provider_name] = {
                            'text': '',
                            'confidence': 0.0,
                            'error': str(e)
                        }
            
            # Consolider les résultats
            consolidated = await self._consolidate_results(results, content_type)
            
            return {
                'success': True,
                'text': consolidated['text'],
                'confidence': consolidated['confidence'],
                'provider_used': consolidated['best_provider'],
                'all_results': results,
                'processing_time': consolidated.get('processing_time', 0),
                'content_type': content_type
            }
            
        except Exception as e:
            logger.error(f"Error in multi-provider OCR: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'text': '',
                'confidence': 0.0
            }
    
    def _select_providers_for_content(self, content_type: str) -> List[str]:
        """Sélectionner les meilleurs providers pour un type de contenu"""
        provider_ranking = {
            'handwritten': ['openai_vision', 'google_vision', 'tesseract'],
            'printed': ['tesseract', 'azure_vision', 'google_vision'],
            'mixed': ['google_vision', 'openai_vision', 'tesseract'],
            'mathematical': ['openai_vision', 'google_vision', 'tesseract']
        }
        
        selected = provider_ranking.get(content_type, ['tesseract', 'google_vision'])
        
        # Filtrer les providers disponibles
        available_providers = [
            p for p in selected 
            if p in self.providers and self.providers[p]['available']
        ]
        
        if not available_providers:
            # Fallback vers tous les providers disponibles
            available_providers = [
                p for p, config in self.providers.items() 
                if config['available']
            ]
        
        return available_providers
    
    async def _preprocess_image_advanced(self, image_path: str) -> Dict[str, np.ndarray]:
        """Préprocessing avancé de l'image pour améliorer l'OCR"""
        try:
            # Charger l'image
            image = Image.open(image_path)
            
            # Convertir en RGB si nécessaire
            if image.mode != 'RGB':
                image = image.convert('RGB')
            
            processed_images = {}
            
            # Image originale
            processed_images['original'] = np.array(image)
            
            # Amélioration du contraste
            enhancer = ImageEnhance.Contrast(image)
            contrast_enhanced = enhancer.enhance(1.5)
            processed_images['contrast_enhanced'] = np.array(contrast_enhanced)
            
            # Amélioration de la netteté
            sharpness_enhancer = ImageEnhance.Sharpness(contrast_enhanced)
            sharp_image = sharpness_enhancer.enhance(2.0)
            processed_images['sharp'] = np.array(sharp_image)
            
            # Conversion en niveaux de gris optimisée
            gray_image = image.convert('L')
            processed_images['grayscale'] = np.array(gray_image)
            
            # Binarisation adaptative
            from PIL import ImageOps
            binary_image = ImageOps.autocontrast(gray_image)
            processed_images['binary'] = np.array(binary_image)
            
            # Débruitage
            denoised = gray_image.filter(ImageFilter.MedianFilter(size=3))
            processed_images['denoised'] = np.array(denoised)
            
            return processed_images
            
        except Exception as e:
            logger.error(f"Error in image preprocessing: {str(e)}")
            # Retourner au moins l'image originale
            image = Image.open(image_path)
            return {'original': np.array(image)}
    
    async def _extract_with_provider(
        self, 
        provider_name: str, 
        processed_images: Dict[str, np.ndarray],
        content_type: str
    ) -> Dict[str, Any]:
        """Extraire le texte avec un provider spécifique"""
        
        if provider_name == 'tesseract':
            return await self._extract_with_tesseract(processed_images, content_type)
        elif provider_name == 'google_vision':
            return await self._extract_with_google_vision(processed_images, content_type)
        elif provider_name == 'openai_vision':
            return await self._extract_with_openai_vision(processed_images, content_type)
        elif provider_name == 'azure_vision':
            return await self._extract_with_azure_vision(processed_images, content_type)
        else:
            raise ValueError(f"Unknown provider: {provider_name}")
    
    async def _extract_with_tesseract(
        self, 
        processed_images: Dict[str, np.ndarray], 
        content_type: str
    ) -> Dict[str, Any]:
        """Extraction avec Tesseract OCR"""
        try:
            import pytesseract
            
            # Configuration Tesseract selon le type de contenu
            configs = {
                'handwritten': '--oem 3 --psm 6',
                'printed': '--oem 3 --psm 6',
                'mixed': '--oem 3 --psm 6',
                'mathematical': '--oem 3 --psm 6 -c tessedit_char_whitelist=0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz+-=()[]{}/<>.,;:!?'
            }
            
            config = configs.get(content_type, '--oem 3 --psm 6')
            
            best_result = {'text': '', 'confidence': 0.0}
            
            # Tester différentes versions préprocessées
            for image_type, image_array in processed_images.items():
                try:
                    # Convertir numpy array en PIL Image
                    if len(image_array.shape) == 3:
                        pil_image = Image.fromarray(image_array.astype('uint8'), 'RGB')
                    else:
                        pil_image = Image.fromarray(image_array.astype('uint8'), 'L')
                    
                    # Extraire le texte
                    text = pytesseract.image_to_string(pil_image, config=config, lang='eng+fra')
                    
                    # Calculer la confiance (approximative)
                    data = pytesseract.image_to_data(pil_image, config=config, output_type=pytesseract.Output.DICT)
                    confidences = [int(conf) for conf in data['conf'] if int(conf) > 0]
                    avg_confidence = sum(confidences) / len(confidences) if confidences else 0
                    
                    if avg_confidence > best_result['confidence']:
                        best_result = {
                            'text': text.strip(),
                            'confidence': avg_confidence / 100.0,  # Normaliser à 0-1
                            'image_type': image_type
                        }
                        
                except Exception as e:
                    logger.warning(f"Tesseract error with {image_type}: {str(e)}")
                    continue
            
            return best_result
            
        except Exception as e:
            logger.error(f"Tesseract extraction error: {str(e)}")
            return {'text': '', 'confidence': 0.0, 'error': str(e)}
    
    async def _extract_with_openai_vision(
        self, 
        processed_images: Dict[str, np.ndarray], 
        content_type: str
    ) -> Dict[str, Any]:
        """Extraction avec OpenAI GPT-4 Vision"""
        try:
            import openai
            
            client = openai.OpenAI(api_key=os.getenv('OPENAI_API_KEY'))
            
            # Utiliser la meilleure image (généralement contrast_enhanced)
            best_image = processed_images.get('contrast_enhanced', processed_images['original'])
            
            # Convertir en base64
            pil_image = Image.fromarray(best_image.astype('uint8'))
            buffer = io.BytesIO()
            pil_image.save(buffer, format='PNG')
            image_base64 = base64.b64encode(buffer.getvalue()).decode()
            
            # Prompt selon le type de contenu
            prompts = {
                'handwritten': "Extract all handwritten text from this exam paper. Focus on student answers. Return only the text, preserving the original structure.",
                'printed': "Extract all printed text from this document. Maintain the original formatting and structure.",
                'mixed': "Extract all text (both handwritten and printed) from this exam paper. Clearly distinguish between questions and student answers.",
                'mathematical': "Extract all text and mathematical expressions from this exam paper. Use standard mathematical notation."
            }
            
            prompt = prompts.get(content_type, prompts['mixed'])
            
            response = client.chat.completions.create(
                model="gpt-4-vision-preview",
                messages=[
                    {
                        "role": "user",
                        "content": [
                            {"type": "text", "text": prompt},
                            {
                                "type": "image_url",
                                "image_url": {
                                    "url": f"data:image/png;base64,{image_base64}",
                                    "detail": "high"
                                }
                            }
                        ]
                    }
                ],
                max_tokens=2000
            )
            
            extracted_text = response.choices[0].message.content
            
            # Estimer la confiance basée sur la longueur et la cohérence
            confidence = min(0.9, len(extracted_text) / 100.0) if extracted_text else 0.0
            
            return {
                'text': extracted_text.strip(),
                'confidence': confidence,
                'model': 'gpt-4-vision-preview'
            }
            
        except Exception as e:
            logger.error(f"OpenAI Vision extraction error: {str(e)}")
            return {'text': '', 'confidence': 0.0, 'error': str(e)}
    
    async def _extract_with_google_vision(
        self, 
        processed_images: Dict[str, np.ndarray], 
        content_type: str
    ) -> Dict[str, Any]:
        """Extraction avec Google Vision API"""
        try:
            import google.generativeai as genai
            
            genai.configure(api_key=os.getenv('GOOGLE_API_KEY'))
            model = genai.GenerativeModel('gemini-pro-vision')
            
            # Utiliser la meilleure image
            best_image = processed_images.get('sharp', processed_images['original'])
            pil_image = Image.fromarray(best_image.astype('uint8'))
            
            # Prompt selon le type de contenu
            prompts = {
                'handwritten': "Analyze this exam paper and extract all handwritten text. Focus on student answers and responses.",
                'printed': "Extract all printed text from this document with high accuracy.",
                'mixed': "Extract all text from this exam paper, both printed questions and handwritten answers.",
                'mathematical': "Extract all text and mathematical expressions, preserving mathematical notation."
            }
            
            prompt = prompts.get(content_type, prompts['mixed'])
            
            response = model.generate_content([prompt, pil_image])
            extracted_text = response.text if response.text else ""
            
            # Estimer la confiance
            confidence = 0.8 if extracted_text else 0.0
            
            return {
                'text': extracted_text.strip(),
                'confidence': confidence,
                'model': 'gemini-pro-vision'
            }
            
        except Exception as e:
            logger.error(f"Google Vision extraction error: {str(e)}")
            return {'text': '', 'confidence': 0.0, 'error': str(e)}
    
    async def _extract_with_azure_vision(
        self, 
        processed_images: Dict[str, np.ndarray], 
        content_type: str
    ) -> Dict[str, Any]:
        """Extraction avec Azure Computer Vision"""
        try:
            # Placeholder pour Azure Vision
            # Implementation similaire aux autres providers
            return {'text': '', 'confidence': 0.0, 'error': 'Azure Vision not implemented'}
            
        except Exception as e:
            logger.error(f"Azure Vision extraction error: {str(e)}")
            return {'text': '', 'confidence': 0.0, 'error': str(e)}
    
    async def _consolidate_results(
        self, 
        results: Dict[str, Dict[str, Any]], 
        content_type: str
    ) -> Dict[str, Any]:
        """Consolider les résultats de plusieurs providers"""
        try:
            if not results:
                return {
                    'text': '',
                    'confidence': 0.0,
                    'best_provider': 'none'
                }
            
            # Trouver le meilleur résultat basé sur la confiance
            best_result = {'confidence': 0.0, 'text': '', 'provider': 'none'}
            
            for provider, result in results.items():
                if result.get('confidence', 0) > best_result['confidence']:
                    best_result = {
                        'confidence': result.get('confidence', 0),
                        'text': result.get('text', ''),
                        'provider': provider
                    }
            
            # Si la confiance est faible, essayer de combiner les résultats
            if best_result['confidence'] < 0.6:
                combined_text = self._combine_low_confidence_results(results)
                if combined_text:
                    best_result['text'] = combined_text
                    best_result['confidence'] = 0.6  # Confiance modérée pour résultat combiné
                    best_result['provider'] = 'combined'
            
            return {
                'text': best_result['text'],
                'confidence': best_result['confidence'],
                'best_provider': best_result['provider']
            }
            
        except Exception as e:
            logger.error(f"Error consolidating results: {str(e)}")
            return {
                'text': '',
                'confidence': 0.0,
                'best_provider': 'error'
            }
    
    def _combine_low_confidence_results(self, results: Dict[str, Dict[str, Any]]) -> str:
        """Combiner les résultats de faible confiance"""
        try:
            texts = [result.get('text', '') for result in results.values() if result.get('text')]
            
            if not texts:
                return ''
            
            # Simple: retourner le texte le plus long
            return max(texts, key=len)
            
        except Exception as e:
            logger.error(f"Error combining results: {str(e)}")
            return ''

# Instance globale
enhanced_ocr_service = EnhancedOCRService()
