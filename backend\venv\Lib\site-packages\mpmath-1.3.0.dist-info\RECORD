mpmath-1.3.0.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
mpmath-1.3.0.dist-info/LICENSE,sha256=wmyugdpFCOXiSZhXd6M4IfGDIj67dNf4z7-Q_n7vL7c,1537
mpmath-1.3.0.dist-info/METADATA,sha256=RLZupES5wNGa6UgV01a_BHrmtoDBkmi1wmVofNaoFAY,8630
mpmath-1.3.0.dist-info/RECORD,,
mpmath-1.3.0.dist-info/WHEEL,sha256=2wepM1nk4DS4eFpYrW1TTqPcoGNfHhhO_i5m4cOimbo,92
mpmath-1.3.0.dist-info/top_level.txt,sha256=BUVWrh8EVlkOhM1n3X9S8msTaVcC-3s6Sjt60avHYus,7
mpmath/__init__.py,sha256=skFYTSwfwDBLChAV6pI3SdewgAQR3UBtyrfIK_Jdn-g,8765
mpmath/__pycache__/__init__.cpython-311.pyc,,
mpmath/__pycache__/ctx_base.cpython-311.pyc,,
mpmath/__pycache__/ctx_fp.cpython-311.pyc,,
mpmath/__pycache__/ctx_iv.cpython-311.pyc,,
mpmath/__pycache__/ctx_mp.cpython-311.pyc,,
mpmath/__pycache__/ctx_mp_python.cpython-311.pyc,,
mpmath/__pycache__/function_docs.cpython-311.pyc,,
mpmath/__pycache__/identification.cpython-311.pyc,,
mpmath/__pycache__/math2.cpython-311.pyc,,
mpmath/__pycache__/rational.cpython-311.pyc,,
mpmath/__pycache__/usertools.cpython-311.pyc,,
mpmath/__pycache__/visualization.cpython-311.pyc,,
mpmath/calculus/__init__.py,sha256=UAgCIJ1YmaeyTqpNzjBlCZGeIzLtUZMEEpl99VWNjus,162
mpmath/calculus/__pycache__/__init__.cpython-311.pyc,,
mpmath/calculus/__pycache__/approximation.cpython-311.pyc,,
mpmath/calculus/__pycache__/calculus.cpython-311.pyc,,
mpmath/calculus/__pycache__/differentiation.cpython-311.pyc,,
mpmath/calculus/__pycache__/extrapolation.cpython-311.pyc,,
mpmath/calculus/__pycache__/inverselaplace.cpython-311.pyc,,
mpmath/calculus/__pycache__/odes.cpython-311.pyc,,
mpmath/calculus/__pycache__/optimization.cpython-311.pyc,,
mpmath/calculus/__pycache__/polynomials.cpython-311.pyc,,
mpmath/calculus/__pycache__/quadrature.cpython-311.pyc,,
mpmath/calculus/approximation.py,sha256=vyzu3YI6r63Oq1KFHrQz02mGXAcH23emqNYhJuUaFZ4,8817
mpmath/calculus/calculus.py,sha256=A0gSp0hxSyEDfugJViY3CeWalF-vK701YftzrjSQzQ4,112
mpmath/calculus/differentiation.py,sha256=2L6CBj8xtX9iip98NPbKsLtwtRjxi571wYmTMHFeL90,20226
mpmath/calculus/extrapolation.py,sha256=xM0rvk2DFEF4iR1Jhl-Y3aS93iW9VVJX7y9IGpmzC-A,73306
mpmath/calculus/inverselaplace.py,sha256=5-pn8N_t0PtgBTXixsXZ4xxrihK2J5gYsVfTKfDx4gA,36056
mpmath/calculus/odes.py,sha256=gaHiw7IJjsONNTAa6izFPZpmcg9uyTp8MULnGdzTIGo,9908
mpmath/calculus/optimization.py,sha256=bKnShXElBOmVOIOlFeksDsYCp9fYSmYwKmXDt0z26MM,32856
mpmath/calculus/polynomials.py,sha256=D16BhU_SHbVi06IxNwABHR-H77IylndNsN3muPTuFYs,7877
mpmath/calculus/quadrature.py,sha256=n-avtS8E43foV-5tr5lofgOBaiMUYE8AJjQcWI9QcKk,42432
mpmath/ctx_base.py,sha256=rfjmfMyA55x8R_cWFINUwWVTElfZmyx5erKDdauSEVw,15985
mpmath/ctx_fp.py,sha256=ctUjx_NoU0iFWk05cXDYCL2ZtLZOlWs1n6Zao3pbG2g,6572
mpmath/ctx_iv.py,sha256=tqdMr-GDfkZk1EhoGeCAajy7pQv-RWtrVqhYjfI8r4g,17211
mpmath/ctx_mp.py,sha256=d3r4t7xHNqSFtmqsA9Btq1Npy3WTM-pcM2_jeCyECxY,49452
mpmath/ctx_mp_python.py,sha256=3olYWo4lk1SnQ0A_IaZ181qqG8u5pxGat_v-L4Qtn3Y,37815
mpmath/function_docs.py,sha256=g4PP8n6ILXmHcLyA50sxK6Tmp_Z4_pRN-wDErU8D1i4,283512
mpmath/functions/__init__.py,sha256=YXVdhqv-6LKm6cr5xxtTNTtuD9zDPKGQl8GmS0xz2xo,330
mpmath/functions/__pycache__/__init__.cpython-311.pyc,,
mpmath/functions/__pycache__/bessel.cpython-311.pyc,,
mpmath/functions/__pycache__/elliptic.cpython-311.pyc,,
mpmath/functions/__pycache__/expintegrals.cpython-311.pyc,,
mpmath/functions/__pycache__/factorials.cpython-311.pyc,,
mpmath/functions/__pycache__/functions.cpython-311.pyc,,
mpmath/functions/__pycache__/hypergeometric.cpython-311.pyc,,
mpmath/functions/__pycache__/orthogonal.cpython-311.pyc,,
mpmath/functions/__pycache__/qfunctions.cpython-311.pyc,,
mpmath/functions/__pycache__/rszeta.cpython-311.pyc,,
mpmath/functions/__pycache__/signals.cpython-311.pyc,,
mpmath/functions/__pycache__/theta.cpython-311.pyc,,
mpmath/functions/__pycache__/zeta.cpython-311.pyc,,
mpmath/functions/__pycache__/zetazeros.cpython-311.pyc,,
mpmath/functions/bessel.py,sha256=dUPLu8frlK-vmf3-irX_7uvwyw4xccv6EIizmIZ88kM,37938
mpmath/functions/elliptic.py,sha256=qz0yVMb4lWEeOTDL_DWz5u5awmGIPKAsuZFJXgwHJNU,42237
mpmath/functions/expintegrals.py,sha256=75X_MRdYc1F_X73bgNiOJqwRlS2hqAzcFLl3RM2tCDc,11644
mpmath/functions/factorials.py,sha256=8_6kCR7e4k1GwxiAOJu0NRadeF4jA28qx4hidhu4ILk,5273
mpmath/functions/functions.py,sha256=ub2JExvqzCWLkm5yAm72Fr6fdWmZZUknq9_3w9MEigI,18100
mpmath/functions/hypergeometric.py,sha256=Z0OMAMC4ylK42n_SnamyFVnUx6zHLyCLCoJDSZ1JrHY,51570
mpmath/functions/orthogonal.py,sha256=FabkxKfBoSseA5flWu1a3re-2BYaew9augqIsT8LaLw,16097
mpmath/functions/qfunctions.py,sha256=a3EHGKQt_jMd4x9I772Jz-TGFnGY-arWqPvZGz9QSe0,7633
mpmath/functions/rszeta.py,sha256=yuUVp4ilIyDmXyE3WTBxDDjwfEJNypJnbPS-xPH5How,46184
mpmath/functions/signals.py,sha256=ELotwQaW1CDpv-eeJzOZ5c23NhfaZcj9_Gkb3psvS0Q,703
mpmath/functions/theta.py,sha256=KggOocczoMG6_HMoal4oEP7iZ4SKOou9JFE-WzY2r3M,37320
mpmath/functions/zeta.py,sha256=ue7JY7GXA0oX8q08sQJl2CSRrZ7kOt8HsftpVjnTwrE,36410
mpmath/functions/zetazeros.py,sha256=uq6TVyZBcY2MLX7VSdVfn0TOkowBLM9fXtnySEwaNzw,30858
mpmath/identification.py,sha256=7aMdngRAaeL_MafDUNbmEIlGQSklHDZ8pmPFt-OLgkw,29253
mpmath/libmp/__init__.py,sha256=UCDjLZw4brbklaCmSixCcPdLdHkz8sF_-6F_wr0duAg,3790
mpmath/libmp/__pycache__/__init__.cpython-311.pyc,,
mpmath/libmp/__pycache__/backend.cpython-311.pyc,,
mpmath/libmp/__pycache__/gammazeta.cpython-311.pyc,,
mpmath/libmp/__pycache__/libelefun.cpython-311.pyc,,
mpmath/libmp/__pycache__/libhyper.cpython-311.pyc,,
mpmath/libmp/__pycache__/libintmath.cpython-311.pyc,,
mpmath/libmp/__pycache__/libmpc.cpython-311.pyc,,
mpmath/libmp/__pycache__/libmpf.cpython-311.pyc,,
mpmath/libmp/__pycache__/libmpi.cpython-311.pyc,,
mpmath/libmp/backend.py,sha256=26A8pUkaGov26vrrFNQVyWJ5LDtK8sl3UHrYLecaTjA,3360
mpmath/libmp/gammazeta.py,sha256=Xqdw6PMoswDaSca_sOs-IglRuk3fb8c9p43M_lbcrlc,71469
mpmath/libmp/libelefun.py,sha256=joBZP4FOdxPfieWso1LPtSr6dHydpG_LQiF_bYQYWMg,43861
mpmath/libmp/libhyper.py,sha256=J9fmdDF6u27EcssEWvBuVaAa3hFjPvPN1SgRgu1dEbc,36624
mpmath/libmp/libintmath.py,sha256=aIRT0rkUZ_sdGQf3TNCLd-pBMvtQWjssbvFLfK7U0jc,16688
mpmath/libmp/libmpc.py,sha256=KBndUjs5YVS32-Id3fflDfYgpdW1Prx6zfo8Ez5Qbrs,26875
mpmath/libmp/libmpf.py,sha256=vpP0kNVkScbCVoZogJ4Watl4I7Ce0d4dzHVjfVe57so,45021
mpmath/libmp/libmpi.py,sha256=u0I5Eiwkqa-4-dXETi5k7MuaxBeZbvCAPFtl93U9YF0,27622
mpmath/math2.py,sha256=O5Dglg81SsW0wfHDUJcXOD8-cCaLvbVIvyw0sVmRbpI,18561
mpmath/matrices/__init__.py,sha256=ETzGDciYbq9ftiKwaMbJ15EI-KNXHrzRb-ZHehhqFjs,94
mpmath/matrices/__pycache__/__init__.cpython-311.pyc,,
mpmath/matrices/__pycache__/calculus.cpython-311.pyc,,
mpmath/matrices/__pycache__/eigen.cpython-311.pyc,,
mpmath/matrices/__pycache__/eigen_symmetric.cpython-311.pyc,,
mpmath/matrices/__pycache__/linalg.cpython-311.pyc,,
mpmath/matrices/__pycache__/matrices.cpython-311.pyc,,
mpmath/matrices/calculus.py,sha256=PNRq-p2nxgT-fzC54K2depi8ddhdx6Q86G8qpUiHeUY,18609
mpmath/matrices/eigen.py,sha256=GbDXI3CixzEdXxr1G86uUWkAngAvd-05MmSQ-Tsu_5k,24394
mpmath/matrices/eigen_symmetric.py,sha256=FPKPeQr1cGYw6Y6ea32a1YdEWQDLP6JlQHEA2WfNLYg,58534
mpmath/matrices/linalg.py,sha256=04C3ijzMFom7ob5fXBCDfyPPdo3BIboIeE8x2A6vqF0,26958
mpmath/matrices/matrices.py,sha256=o78Eq62EHQnxcsR0LBoWDEGREOoN4L2iDM1q3dQrw0o,32331
mpmath/rational.py,sha256=64d56fvZXngYZT7nOAHeFRUX77eJ1A0R3rpfWBU-mSo,5976
mpmath/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mpmath/tests/__pycache__/__init__.cpython-311.pyc,,
mpmath/tests/__pycache__/extratest_gamma.cpython-311.pyc,,
mpmath/tests/__pycache__/extratest_zeta.cpython-311.pyc,,
mpmath/tests/__pycache__/runtests.cpython-311.pyc,,
mpmath/tests/__pycache__/test_basic_ops.cpython-311.pyc,,
mpmath/tests/__pycache__/test_bitwise.cpython-311.pyc,,
mpmath/tests/__pycache__/test_calculus.cpython-311.pyc,,
mpmath/tests/__pycache__/test_compatibility.cpython-311.pyc,,
mpmath/tests/__pycache__/test_convert.cpython-311.pyc,,
mpmath/tests/__pycache__/test_diff.cpython-311.pyc,,
mpmath/tests/__pycache__/test_division.cpython-311.pyc,,
mpmath/tests/__pycache__/test_eigen.cpython-311.pyc,,
mpmath/tests/__pycache__/test_eigen_symmetric.cpython-311.pyc,,
mpmath/tests/__pycache__/test_elliptic.cpython-311.pyc,,
mpmath/tests/__pycache__/test_fp.cpython-311.pyc,,
mpmath/tests/__pycache__/test_functions.cpython-311.pyc,,
mpmath/tests/__pycache__/test_functions2.cpython-311.pyc,,
mpmath/tests/__pycache__/test_gammazeta.cpython-311.pyc,,
mpmath/tests/__pycache__/test_hp.cpython-311.pyc,,
mpmath/tests/__pycache__/test_identify.cpython-311.pyc,,
mpmath/tests/__pycache__/test_interval.cpython-311.pyc,,
mpmath/tests/__pycache__/test_levin.cpython-311.pyc,,
mpmath/tests/__pycache__/test_linalg.cpython-311.pyc,,
mpmath/tests/__pycache__/test_matrices.cpython-311.pyc,,
mpmath/tests/__pycache__/test_mpmath.cpython-311.pyc,,
mpmath/tests/__pycache__/test_ode.cpython-311.pyc,,
mpmath/tests/__pycache__/test_pickle.cpython-311.pyc,,
mpmath/tests/__pycache__/test_power.cpython-311.pyc,,
mpmath/tests/__pycache__/test_quad.cpython-311.pyc,,
mpmath/tests/__pycache__/test_rootfinding.cpython-311.pyc,,
mpmath/tests/__pycache__/test_special.cpython-311.pyc,,
mpmath/tests/__pycache__/test_str.cpython-311.pyc,,
mpmath/tests/__pycache__/test_summation.cpython-311.pyc,,
mpmath/tests/__pycache__/test_trig.cpython-311.pyc,,
mpmath/tests/__pycache__/test_visualization.cpython-311.pyc,,
mpmath/tests/__pycache__/torture.cpython-311.pyc,,
mpmath/tests/extratest_gamma.py,sha256=xidhXUelILcxtiPGoTBHjqUOKIJzEaZ_v3nntGQyWZQ,7228
mpmath/tests/extratest_zeta.py,sha256=sg10j9RhjBpV2EdUqyYhGV2ERWvM--EvwwGIz6HTmlw,1003
mpmath/tests/runtests.py,sha256=7NUV82F3K_5AhU8mCLUFf5OibtT7uloFCwPyM3l71wM,5189
mpmath/tests/test_basic_ops.py,sha256=dsB8DRG-GrPzBaZ-bIauYabaeqXbfqBo9SIP9BqcTSs,15348
mpmath/tests/test_bitwise.py,sha256=-nLYhgQbhDza3SQM63BhktYntACagqMYx9ib3dPnTKM,7686
mpmath/tests/test_calculus.py,sha256=4oxtNfMpO4RLLoOzrv7r9-h8BcqfBsJIE6UpsHe7c4w,9187
mpmath/tests/test_compatibility.py,sha256=_t3ASZ3jhfAMnN1voWX7PDNIDzn-3PokkJGIdT1x7y0,2306
mpmath/tests/test_convert.py,sha256=JPcDcTJIWh5prIxjx5DM1aNWgqlUoF2KpHvAgK3uHi4,8834
mpmath/tests/test_diff.py,sha256=qjiF8NxQ8vueuZ5ZHGPQ-kjcj_I7Jh_fEdFtaA8DzEI,2466
mpmath/tests/test_division.py,sha256=6lUeZfmaBWvvszdqlWLMHgXPjVsxvW1WZpd4-jFWCpU,5340
mpmath/tests/test_eigen.py,sha256=2mnqVATGbsJkvSVHPpitfAk881twFfb3LsO3XikV9Hs,3905
mpmath/tests/test_eigen_symmetric.py,sha256=v0VimCicIU2owASDMBaP-t-30uq-pXcsglt95KBtNO4,8778
mpmath/tests/test_elliptic.py,sha256=Kjiwq9Bb6N_OOzzWewGQ1M_PMa7vRs42V0t90gloZxo,26225
mpmath/tests/test_fp.py,sha256=AJo0FTyH4BuUnUsv176LD956om308KGYndy-b54KGxM,89997
mpmath/tests/test_functions.py,sha256=b47VywdomoOX6KmMmz9-iv2IqVIydwKSuUw2pWlFHrY,30955
mpmath/tests/test_functions2.py,sha256=vlw2RWhL1oTcifnOMDx1a_YzN96UgNNIE5STeKRv1HY,96990
mpmath/tests/test_gammazeta.py,sha256=AB34O0DV7AlEf9Z4brnCadeQU5-uAwhWRw5FZas65DA,27917
mpmath/tests/test_hp.py,sha256=6hcENu6Te2klPEiTSeLBIRPlH7PADlJwFKbx8xpnOhg,10461
mpmath/tests/test_identify.py,sha256=lGUIPfrB2paTg0cFUo64GmMzF77F9gs9FQjX7gxGHV8,692
mpmath/tests/test_interval.py,sha256=TjYd7a9ca6iRJiLjw06isLeZTuGoGAPmgleDZ0cYfJ0,17527
mpmath/tests/test_levin.py,sha256=P8M11yV1dj_gdSNv5xuwCzFiF86QyRDtPMjURy6wJ28,5090
mpmath/tests/test_linalg.py,sha256=miKEnwB8iwWV13hi1bF1cg3hgB4rTKOR0fvDVfWmXds,10440
mpmath/tests/test_matrices.py,sha256=qyA4Ml2CvNvW034lzB01G6wVgNr7UrgZqh2wkMXtpzM,7944
mpmath/tests/test_mpmath.py,sha256=LVyJUeofiaxW-zLKWVBCz59L9UQsjlW0Ts9_oBiEv_4,196
mpmath/tests/test_ode.py,sha256=zAxexBH4fnmFNO4bvEHbug1NJWC5zqfFaVDlYijowkY,1822
mpmath/tests/test_pickle.py,sha256=Y8CKmDLFsJHUqG8CDaBw5ilrPP4YT1xijVduLpQ7XFE,401
mpmath/tests/test_power.py,sha256=sz_K02SmNxpa6Kb1uJLN_N4tXTJGdQ___vPRshEN7Gk,5227
mpmath/tests/test_quad.py,sha256=49Ltft0vZ_kdKLL5s-Kj-BzAVoF5LPVEUeNUzdOkghI,3893
mpmath/tests/test_rootfinding.py,sha256=umQegEaKHmYOEl5jEyoD-VLKDtXsTJJkepKEr4c0dC0,3132
mpmath/tests/test_special.py,sha256=YbMIoMIkJEvvKYIzS0CXthJFG0--j6un7-tcE6b7FPM,2848
mpmath/tests/test_str.py,sha256=0WsGD9hMPRi8zcuYMA9Cu2mOvQiCFskPwMsMf8lBDK4,544
mpmath/tests/test_summation.py,sha256=fdNlsvRVOsbWxbhlyDLDaEO2S8kTJrRMKIvB5-aNci0,2035
mpmath/tests/test_trig.py,sha256=zPtkIEnZaThxcWur4k7BX8-2Jmj-AhO191Svv7ANYUU,4799
mpmath/tests/test_visualization.py,sha256=1PqtkoUx-WsKYgTRiu5o9pBc85kwhf1lzU2eobDQCJM,944
mpmath/tests/torture.py,sha256=LD95oES7JY2KroELK-m-jhvtbvZaKChnt0Cq7kFMNCw,7868
mpmath/usertools.py,sha256=a-TDw7XSRsPdBEffxOooDV4WDFfuXnO58P75dcAD87I,3029
mpmath/visualization.py,sha256=pnnbjcd9AhFVRBZavYX5gjx4ytK_kXoDDisYR6EpXhs,10627
