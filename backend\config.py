"""
Configuration settings for Auto-Grade Scribe
Centralized configuration management for the enhanced application
"""

import os
import logging
from typing import Dict, Any, Optional
from pydantic import BaseSettings, validator
from enum import Enum

class Environment(str, Enum):
    """Application environment types"""
    DEVELOPMENT = "development"
    TESTING = "testing"
    STAGING = "staging"
    PRODUCTION = "production"

class LogLevel(str, Enum):
    """Logging levels"""
    DEBUG = "DEBUG"
    INFO = "INFO"
    WARNING = "WARNING"
    ERROR = "ERROR"
    CRITICAL = "CRITICAL"

class Settings(BaseSettings):
    """Application settings with validation"""
    
    # Environment
    environment: Environment = Environment.DEVELOPMENT
    debug: bool = False
    
    # Database
    database_url: str = "*************************************************/gradegeniusdb"
    database_pool_size: int = 10
    database_max_overflow: int = 20
    database_pool_timeout: int = 30
    
    # Security
    secret_key: str = "your-secret-key-change-in-production"
    jwt_algorithm: str = "HS256"
    access_token_expire_minutes: int = 60
    refresh_token_expire_days: int = 7
    
    # API Configuration
    api_host: str = "0.0.0.0"
    api_port: int = 8000
    api_workers: int = 1
    api_reload: bool = True
    cors_origins: list = ["http://localhost:3000", "http://127.0.0.1:3000"]
    
    # File Upload
    max_file_size: int = 50 * 1024 * 1024  # 50MB
    allowed_file_types: list = ["image/jpeg", "image/png", "image/tiff", "application/pdf"]
    upload_directory: str = "uploads"
    results_directory: str = "results"
    temp_directory: str = "temp"
    
    # OCR Configuration
    tesseract_cmd: Optional[str] = None
    tesseract_timeout: int = 300  # 5 minutes
    ocr_confidence_threshold: float = 0.6
    
    # AI Configuration
    google_api_key: Optional[str] = None
    gemini_model: str = "gemini-pro-vision"
    gemini_timeout: int = 60
    gemini_max_retries: int = 3
    
    # Processing
    max_concurrent_processes: int = 5
    processing_timeout: int = 600  # 10 minutes
    cleanup_interval_hours: int = 24
    cleanup_max_age_days: int = 30
    
    # Logging
    log_level: LogLevel = LogLevel.INFO
    log_format: str = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    log_file: Optional[str] = None
    log_max_size: int = 10 * 1024 * 1024  # 10MB
    log_backup_count: int = 5
    
    # Monitoring
    enable_metrics: bool = True
    metrics_port: int = 9090
    health_check_interval: int = 30
    
    # Email (for notifications)
    smtp_host: Optional[str] = None
    smtp_port: int = 587
    smtp_username: Optional[str] = None
    smtp_password: Optional[str] = None
    smtp_use_tls: bool = True
    
    # Redis (for caching and queues)
    redis_url: Optional[str] = None
    redis_password: Optional[str] = None
    cache_ttl: int = 3600  # 1 hour
    
    # Grading Configuration
    default_grade_scale: str = "standard"
    passing_threshold: float = 60.0
    grade_scales: Dict[str, Dict[int, str]] = {
        "standard": {90: "A", 80: "B", 70: "C", 60: "D", 0: "F"},
        "strict": {95: "A", 85: "B", 75: "C", 65: "D", 0: "F"},
        "lenient": {85: "A", 75: "B", 65: "C", 55: "D", 0: "F"}
    }
    
    # Pagination
    default_page_size: int = 20
    max_page_size: int = 1000
    
    # Rate Limiting
    rate_limit_requests: int = 100
    rate_limit_window: int = 60  # seconds
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False
    
    @validator("environment", pre=True)
    def validate_environment(cls, v):
        if isinstance(v, str):
            return Environment(v.lower())
        return v
    
    @validator("log_level", pre=True)
    def validate_log_level(cls, v):
        if isinstance(v, str):
            return LogLevel(v.upper())
        return v
    
    @validator("database_url")
    def validate_database_url(cls, v):
        if not v or not v.startswith(("postgresql://", "sqlite:///")):
            raise ValueError("Invalid database URL")
        return v
    
    @validator("cors_origins")
    def validate_cors_origins(cls, v):
        if isinstance(v, str):
            return [origin.strip() for origin in v.split(",")]
        return v
    
    @validator("allowed_file_types")
    def validate_file_types(cls, v):
        if isinstance(v, str):
            return [ft.strip() for ft in v.split(",")]
        return v
    
    @property
    def is_development(self) -> bool:
        return self.environment == Environment.DEVELOPMENT
    
    @property
    def is_production(self) -> bool:
        return self.environment == Environment.PRODUCTION
    
    @property
    def is_testing(self) -> bool:
        return self.environment == Environment.TESTING
    
    def get_database_config(self) -> Dict[str, Any]:
        """Get database configuration"""
        return {
            "url": self.database_url,
            "pool_size": self.database_pool_size,
            "max_overflow": self.database_max_overflow,
            "pool_timeout": self.database_pool_timeout,
            "echo": self.debug and self.is_development
        }
    
    def get_logging_config(self) -> Dict[str, Any]:
        """Get logging configuration"""
        config = {
            "version": 1,
            "disable_existing_loggers": False,
            "formatters": {
                "default": {
                    "format": self.log_format
                }
            },
            "handlers": {
                "console": {
                    "class": "logging.StreamHandler",
                    "formatter": "default",
                    "level": self.log_level.value
                }
            },
            "root": {
                "level": self.log_level.value,
                "handlers": ["console"]
            },
            "loggers": {
                "auto-grade-scribe": {
                    "level": self.log_level.value,
                    "handlers": ["console"],
                    "propagate": False
                },
                "uvicorn": {
                    "level": "INFO",
                    "handlers": ["console"],
                    "propagate": False
                },
                "sqlalchemy": {
                    "level": "WARNING",
                    "handlers": ["console"],
                    "propagate": False
                }
            }
        }
        
        # Add file handler if log file is specified
        if self.log_file:
            config["handlers"]["file"] = {
                "class": "logging.handlers.RotatingFileHandler",
                "filename": self.log_file,
                "maxBytes": self.log_max_size,
                "backupCount": self.log_backup_count,
                "formatter": "default",
                "level": self.log_level.value
            }
            
            # Add file handler to all loggers
            for logger_config in config["loggers"].values():
                logger_config["handlers"].append("file")
            config["root"]["handlers"].append("file")
        
        return config
    
    def get_ocr_config(self) -> Dict[str, Any]:
        """Get OCR configuration"""
        return {
            "tesseract_cmd": self.tesseract_cmd,
            "timeout": self.tesseract_timeout,
            "confidence_threshold": self.ocr_confidence_threshold,
            "google_api_key": self.google_api_key,
            "gemini_model": self.gemini_model,
            "gemini_timeout": self.gemini_timeout,
            "gemini_max_retries": self.gemini_max_retries
        }
    
    def get_file_config(self) -> Dict[str, Any]:
        """Get file handling configuration"""
        return {
            "max_size": self.max_file_size,
            "allowed_types": self.allowed_file_types,
            "upload_dir": self.upload_directory,
            "results_dir": self.results_directory,
            "temp_dir": self.temp_directory
        }
    
    def setup_directories(self):
        """Create necessary directories"""
        directories = [
            self.upload_directory,
            self.results_directory,
            self.temp_directory
        ]
        
        for directory in directories:
            os.makedirs(directory, exist_ok=True)
    
    def setup_logging(self):
        """Setup logging configuration"""
        import logging.config
        
        config = self.get_logging_config()
        logging.config.dictConfig(config)
        
        # Set up logger for the application
        logger = logging.getLogger("auto-grade-scribe")
        logger.info(f"Logging configured for {self.environment.value} environment")
        
        return logger

# Create global settings instance
settings = Settings()

# Setup logging
logger = settings.setup_logging()

# Setup directories
settings.setup_directories()

# Export commonly used configurations
DATABASE_CONFIG = settings.get_database_config()
OCR_CONFIG = settings.get_ocr_config()
FILE_CONFIG = settings.get_file_config()

logger.info("Configuration loaded successfully")
logger.info(f"Environment: {settings.environment.value}")
logger.info(f"Debug mode: {settings.debug}")
logger.info(f"Database: {settings.database_url.split('@')[-1] if '@' in settings.database_url else 'SQLite'}")
logger.info(f"Gemini AI: {'Enabled' if settings.google_api_key else 'Disabled'}")
