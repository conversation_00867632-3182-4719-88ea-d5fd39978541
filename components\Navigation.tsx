'use client';

import React from 'react';
import { Link } from '@/components/ui/link';
import { usePathname } from 'next/navigation';
import { cn } from '@/lib/utils';
import {
  Home,
  FileText,
  BarChart,
  Settings,
  Users,
  HelpCircle,
  Upload
} from 'lucide-react';

interface NavigationItem {
  name: string;
  href: string;
  icon: React.ElementType;
  requiresAuth?: boolean;
}

const navigationItems: NavigationItem[] = [
  { name: 'Accueil', href: '/', icon: Home },
  { name: 'Tableau de bord', href: '/dashboard', icon: BarChart, requiresAuth: true },
  { name: 'Résultats', href: '/results', icon: FileText, requiresAuth: true },
  { name: '<PERSON><PERSON><PERSON>verser', href: '/upload', icon: Upload, requiresAuth: true },
];

interface NavigationProps {
  className?: string;
  vertical?: boolean;
}

export function Navigation({ className, vertical = false }: NavigationProps) {
  const pathname = usePathname();

  return (
    <nav className={cn(
      "flex",
      vertical ? "flex-col space-y-1" : "space-x-4",
      className
    )}>
      {navigationItems.map((item) => {
        const isActive = pathname === item.href ||
                        (item.href !== '/' && pathname.startsWith(item.href));

        return (
          <Link
            key={item.name}
            href={item.href}
            className={cn(
              "flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors",
              isActive
                ? "bg-primary text-primary-foreground"
                : "text-muted-foreground hover:text-foreground hover:bg-accent",
              vertical ? "justify-start" : "justify-center"
            )}
            activeClassName="bg-primary text-primary-foreground"
          >
            <item.icon className={cn("h-5 w-5", vertical ? "mr-2" : "mr-0 md:mr-2")} />
            <span className={cn(vertical ? "block" : "hidden md:block")}>
              {item.name}
            </span>
          </Link>
        );
      })}
    </nav>
  );
}

export default Navigation;
