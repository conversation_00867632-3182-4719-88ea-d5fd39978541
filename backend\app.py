from fastapi import <PERSON><PERSON><PERSON>, File, UploadFile, HTTPException, Depends, Form, status
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse, FileResponse
from fastapi.staticfiles import StaticFiles
from fastapi.security import OAuth2PasswordRequestForm
from sqlalchemy.orm import Session, sessionmaker
import uvicorn
import os
import uuid
import json
import logging
from typing import Optional, List, Dict, Any
from pydantic import BaseModel
from dotenv import load_dotenv
from datetime import datetime, timedelta

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler()]
)
logger = logging.getLogger("auto-grade-scribe")

# Import des modules d'authentification et de base de données
from database import get_db, engine, Base
from models import User, Exam

# Utiliser le système d'authentification standard
from auth import (
    Token, UserCreate, UserResponse, authenticate_user,
    create_access_token, get_current_active_user, ACCESS_TOKEN_EXPIRE_MINUTES,
    get_password_hash
)

# Charger les variables d'environnement
load_dotenv()

# Importer les services
from ai.ocr_service import ocr_service
from ai.qcm_grading_service import qcm_grading_service

# Models
class OCRRequest(BaseModel):
    file_path: str

class OCRResponse(BaseModel):
    success: bool
    extracted_text: Optional[str] = None
    error: Optional[str] = None
    file_path: Optional[str] = None

class LoginRequest(BaseModel):
    email: str
    password: str

class LogoutResponse(BaseModel):
    message: str

class QCMGradeRequest(BaseModel):
    file_id: str
    correct_answers: Dict[str, str]

class HandwrittenGradeRequest(BaseModel):
    file_id: str
    rubric: Dict[str, Any]

class GradeRequest(BaseModel):
    file_id: str
    exam_template: Dict[str, Any]
    exam_type: str

class GradeSummary(BaseModel):
    score: float
    grade: str
    summary: str

class GradeResponse(BaseModel):
    grade_summary: GradeSummary
    details: Dict[str, Any]
    file_id: str

# Initialize FastAPI
app = FastAPI(title="Auto Grade Scribe API", version="1.0.0")

# CORS Configuration
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # In production, replace with specific origins
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Create directories if they don't exist
os.makedirs("uploads", exist_ok=True)
os.makedirs("results", exist_ok=True)
os.makedirs("static", exist_ok=True)

# Mount static files
app.mount("/uploads", StaticFiles(directory="uploads"), name="uploads")
app.mount("/static", StaticFiles(directory="static"), name="static")

# Routes d'authentification
@app.post("/token", response_model=Token)
async def login_for_access_token(form_data: OAuth2PasswordRequestForm = Depends(), db: Session = Depends(get_db)):
    user = authenticate_user(db, form_data.username, form_data.password)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Nom d'utilisateur ou mot de passe incorrect",
            headers={"WWW-Authenticate": "Bearer"},
        )
    access_token_expires = timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = create_access_token(
        data={"sub": user.username}, expires_delta=access_token_expires
    )
    return {"access_token": access_token, "token_type": "bearer"}

@app.post("/api/login", response_model=Dict[str, Any])
async def login(login_data: LoginRequest, db: Session = Depends(get_db)):
    user = db.query(User).filter(User.email == login_data.email).first()
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Email ou mot de passe incorrect"
        )

    if not authenticate_user(db, user.username, login_data.password):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Email ou mot de passe incorrect"
        )

    access_token_expires = timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = create_access_token(
        data={"sub": user.username}, expires_delta=access_token_expires
    )

    return {
        "access_token": access_token,
        "token_type": "bearer",
        "user": {
            "id": user.id,
            "username": user.username,
            "email": user.email,
            "role": user.role
        }
    }

@app.post("/api/logout", response_model=LogoutResponse)
async def logout():
    # Côté serveur, nous n'avons pas besoin de faire quoi que ce soit
    # car le token JWT sera simplement supprimé côté client
    return {"message": "Déconnexion réussie"}

@app.get("/api/user", response_model=Dict[str, Any])
async def get_current_user_info(current_user: User = Depends(get_current_active_user)):
    return {
        "user": {
            "id": current_user.id,
            "username": current_user.username,
            "email": current_user.email,
            "role": current_user.role
        }
    }

# Routes de l'API
@app.get("/")
async def root():
    return {"message": "Auto Grade Scribe API is running"}

@app.post("/api/upload", response_model=Dict[str, Any])
async def upload_file(file: UploadFile = File(...)):
    """Endpoint pour télécharger un fichier"""
    try:
        file_id = str(uuid.uuid4())
        original_filename = file.filename
        file_extension = os.path.splitext(original_filename)[1]
        unique_filename = f"{file_id}{file_extension}"
        file_path = os.path.join("uploads", unique_filename)

        with open(file_path, "wb") as buffer:
            buffer.write(await file.read())

        exam = Exam(
            id=file_id,
            user_id=1,
            original_filename=original_filename,
            file_path=file_path,
            status="uploaded"
        )

        SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
        db = SessionLocal()

        try:
            db.add(exam)
            db.commit()
            db.refresh(exam)
        finally:
            db.close()

        return {
            "success": True,
            "file_id": file_id,
            "file_path": file_path,
            "original_filename": original_filename,
            "upload_time": exam.upload_time.isoformat()
        }
    except Exception as e:
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e))

@app.post("/extract-text", response_model=OCRResponse)
async def extract_text(request: OCRRequest):
    """Endpoint pour extraire le texte d'une image"""
    try:
        if not os.path.exists(request.file_path):
            return OCRResponse(
                success=False,
                error=f"Le fichier {request.file_path} n'existe pas"
            )

        result = ocr_service.extract_text(request.file_path)

        return OCRResponse(
            success=result["success"],
            extracted_text=result.get("extracted_text"),
            error=result.get("error"),
            file_path=request.file_path
        )
    except Exception as e:
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e))

@app.post("/upload-and-extract", response_model=OCRResponse)
async def upload_and_extract(file: UploadFile = File(...)):
    """Endpoint pour télécharger et extraire le texte"""
    try:
        file_extension = os.path.splitext(file.filename)[1]
        unique_filename = f"{uuid.uuid4()}{file_extension}"
        file_path = os.path.join("uploads", unique_filename)

        with open(file_path, "wb") as buffer:
            buffer.write(await file.read())

        result = ocr_service.extract_text(file_path)

        return OCRResponse(
            success=result["success"],
            extracted_text=result.get("extracted_text"),
            error=result.get("error"),
            file_path=file_path
        )
    except Exception as e:
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e))

@app.get("/api/ocr/{file_id}/noauth", response_model=Dict[str, Any])
async def get_ocr_text_noauth(file_id: str):
    """Endpoint pour extraire le texte d'un fichier déjà téléchargé sans authentification"""
    try:
        # Rechercher le fichier dans la base de données
        SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
        db = SessionLocal()

        try:
            exam = db.query(Exam).filter(Exam.id == file_id).first()
            if not exam:
                # Si l'examen n'est pas trouvé, chercher le fichier directement
                # Essayer de trouver le fichier avec différentes extensions
                file_path = None
                for ext in ['.jpg', '.jpeg', '.png', '.pdf']:
                    potential_path = os.path.join("uploads", f"{file_id}{ext}")
                    if os.path.exists(potential_path):
                        file_path = potential_path
                        break

                if not file_path:
                    return {"success": False, "error": f"Fichier avec ID {file_id} non trouvé"}
            else:
                file_path = exam.file_path

            # Vérifier si le fichier existe
            if not os.path.exists(file_path):
                return {"success": False, "error": f"Le fichier {file_path} n'existe pas"}

            # Extraire le texte avec OCR
            result = ocr_service.extract_text(file_path)

            # Mettre à jour le statut de l'examen si trouvé
            if exam:
                exam.status = "processed"
                db.commit()

            return {
                "success": result["success"],
                "extracted_text": result.get("extracted_text", ""),
                "student_name": result.get("student_name", "Non détecté"),
                "extracted_answers": result.get("extracted_answers", {}),
                "file_path": file_path,
                "file_id": file_id,
                "mode": result.get("mode", "standard"),
                "model": result.get("model", "tesseract")
            }
        finally:
            db.close()
    except Exception as e:
        logger.error(f"Erreur lors de l'extraction OCR: {str(e)}")
        return {"success": False, "error": str(e)}

@app.post("/api/grade/noauth", response_model=Dict[str, Any])
async def grade_exam_noauth(request: Dict[str, Any]):
    """Endpoint pour corriger un examen sans authentification"""
    try:
        file_id = request.get("file_id")
        exam_type = request.get("exam_type", "standard")

        if not file_id:
            return {"success": False, "error": "ID de fichier manquant"}

        # Rechercher le fichier dans la base de données
        SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
        db = SessionLocal()

        try:
            exam = db.query(Exam).filter(Exam.id == file_id).first()
            if not exam:
                # Si l'examen n'est pas trouvé, chercher le fichier directement
                file_path = None
                for ext in ['.jpg', '.jpeg', '.png', '.pdf']:
                    potential_path = os.path.join("uploads", f"{file_id}{ext}")
                    if os.path.exists(potential_path):
                        file_path = potential_path
                        break

                if not file_path:
                    return {"success": False, "error": f"Fichier avec ID {file_id} non trouvé"}
            else:
                file_path = exam.file_path

            # Vérifier si le fichier existe
            if not os.path.exists(file_path):
                return {"success": False, "error": f"Le fichier {file_path} n'existe pas"}

            # Extraire le texte avec OCR si ce n'est pas déjà fait
            ocr_result = ocr_service.extract_text(file_path, mode=exam_type)

            # Simuler un processus de correction
            # Dans une implémentation réelle, vous appelleriez ici votre service de correction
            result = {
                "success": True,
                "file_id": file_id,
                "exam_type": exam_type,
                "ocr_result": ocr_result,
                "grade": {
                    "score": 75,  # Score simulé
                    "max_score": 100,
                    "percentage": 75,
                    "status": "passed"
                },
                "details": {
                    "process_status": {
                        "preprocessing": {"status": "completed", "timestamp": datetime.now().isoformat()},
                        "ocr_extraction": {"status": "completed", "timestamp": datetime.now().isoformat()},
                        "ai_analysis": {"status": "completed", "timestamp": datetime.now().isoformat()},
                        "final_grading": {"status": "completed", "timestamp": datetime.now().isoformat()}
                    }
                }
            }

            # Mettre à jour le statut de l'examen si trouvé
            if exam:
                exam.status = "graded"
                db.commit()

            return result
        finally:
            db.close()
    except Exception as e:
        logger.error(f"Erreur lors de la correction: {str(e)}")
        return {"success": False, "error": str(e)}

@app.get("/api/results/{file_id}/complete/noauth", response_model=Dict[str, Any])
async def get_complete_results_noauth(file_id: str):
    """Endpoint pour récupérer les résultats complets d'un examen sans authentification"""
    try:
        # Rechercher le fichier dans la base de données
        SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
        db = SessionLocal()

        try:
            exam = db.query(Exam).filter(Exam.id == file_id).first()
            if not exam:
                # Si l'examen n'est pas trouvé, retourner une erreur
                return {"success": False, "error": f"Examen avec ID {file_id} non trouvé"}

            # Vérifier si le fichier existe
            file_path = exam.file_path
            if not os.path.exists(file_path):
                return {"success": False, "error": f"Le fichier {file_path} n'existe pas"}

            # Extraire le texte avec OCR
            ocr_result = ocr_service.extract_text(file_path)

            # Simuler des résultats complets
            # Dans une implémentation réelle, vous récupéreriez les résultats de la base de données
            result = {
                "success": True,
                "file_id": file_id,
                "file_path": file_path,
                "original_filename": exam.original_filename,
                "status": exam.status,
                "ocr_result": ocr_result,
                "grade": {
                    "score": 75,  # Score simulé
                    "max_score": 100,
                    "percentage": 75,
                    "status": "passed"
                },
                "student": {
                    "name": ocr_result.get("student_name", "Non détecté"),
                    "answers": ocr_result.get("extracted_answers", {})
                },
                "details": {
                    "process_status": {
                        "preprocessing": {"status": "completed", "timestamp": datetime.now().isoformat()},
                        "ocr_extraction": {"status": "completed", "timestamp": datetime.now().isoformat()},
                        "ai_analysis": {"status": "completed", "timestamp": datetime.now().isoformat()},
                        "final_grading": {"status": "completed", "timestamp": datetime.now().isoformat()}
                    }
                }
            }

            return result
        finally:
            db.close()
    except Exception as e:
        logger.error(f"Erreur lors de la récupération des résultats: {str(e)}")
        return {"success": False, "error": str(e)}

@app.post("/api/grade/enhanced-qcm/noauth", response_model=Dict[str, Any])
async def grade_enhanced_qcm_noauth(request: Dict[str, Any]):
    """Endpoint pour corriger un QCM amélioré sans authentification"""
    try:
        file_id = request.get("file_id")
        correct_answers = request.get("correct_answers", {})

        if not file_id:
            return {"success": False, "error": "ID de fichier manquant"}

        # Rechercher le fichier dans la base de données
        SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
        db = SessionLocal()

        try:
            exam = db.query(Exam).filter(Exam.id == file_id).first()
            if not exam:
                # Si l'examen n'est pas trouvé, chercher le fichier directement
                file_path = None
                for ext in ['.jpg', '.jpeg', '.png', '.pdf']:
                    potential_path = os.path.join("uploads", f"{file_id}{ext}")
                    if os.path.exists(potential_path):
                        file_path = potential_path
                        break

                if not file_path:
                    return {"success": False, "error": f"Fichier avec ID {file_id} non trouvé"}
            else:
                file_path = exam.file_path

            # Vérifier si le fichier existe
            if not os.path.exists(file_path):
                return {"success": False, "error": f"Le fichier {file_path} n'existe pas"}

            # Extraire le texte avec OCR en mode QCM
            ocr_result = ocr_service.extract_text(file_path, mode="qcm")

            # Utiliser le service de correction QCM
            result = qcm_grading_service.grade_qcm(
                file_id=file_id,
                file_path=file_path,
                ocr_result=ocr_result,
                correct_answers=correct_answers
            )

            # Vérifier si la correction a réussi
            if not result.get("success", False):
                return result

            # Mettre à jour le statut de l'examen si trouvé
            if exam:
                exam.status = "graded"
                db.commit()

            return result
        finally:
            db.close()
    except Exception as e:
        logger.error(f"Erreur lors de la correction du QCM: {str(e)}")
        return {"success": False, "error": str(e)}

# Initialisation de la base de données au démarrage
@app.on_event("startup")
async def startup_event():
    Base.metadata.create_all(bind=engine)

if __name__ == "__main__":
    uvicorn.run("app:app", host="0.0.0.0", port=8000, reload=True)
