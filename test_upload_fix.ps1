# Script de test pour vérifier la correction de l'upload
Write-Host "🧪 Test de la correction de l'upload Auto-Grade Scribe" -ForegroundColor Green

# Vérifier que l'API fonctionne
Write-Host "1. Test de l'API..." -ForegroundColor Yellow
try {
    $response = Invoke-RestMethod -Uri "http://127.0.0.1:8001/" -Method Get
    Write-Host "   ✅ API accessible: $($response.message)" -ForegroundColor Green
} catch {
    Write-Host "   ❌ API non accessible: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Test de santé
Write-Host "2. Test de santé..." -ForegroundColor Yellow
try {
    $health = Invoke-RestMethod -Uri "http://127.0.0.1:8001/health" -Method Get
    Write-Host "   ✅ Statut: $($health.status)" -ForegroundColor Green
    Write-Host "   📁 Répertoires: uploads=$($health.directories.uploads), results=$($health.directories.results)" -ForegroundColor Cyan
} catch {
    Write-Host "   ❌ Erreur santé: $($_.Exception.Message)" -ForegroundColor Red
}

# Créer un fichier de test
Write-Host "3. Création d'un fichier de test..." -ForegroundColor Yellow
$testContent = "Ceci est un fichier de test pour Auto-Grade Scribe`nQuestion 1: A`nQuestion 2: B`nQuestion 3: C"
$testFile = "test_exam.txt"
$testContent | Out-File -FilePath $testFile -Encoding UTF8
Write-Host "   ✅ Fichier de test créé: $testFile" -ForegroundColor Green

# Test d'upload simple
Write-Host "4. Test d'upload simple..." -ForegroundColor Yellow
try {
    $form = @{
        file = Get-Item $testFile
    }
    
    $uploadResponse = Invoke-RestMethod -Uri "http://127.0.0.1:8001/api/upload/test" -Method Post -Form $form
    
    if ($uploadResponse.success -and $uploadResponse.file_id) {
        Write-Host "   ✅ Upload test réussi!" -ForegroundColor Green
        Write-Host "   📄 File ID: $($uploadResponse.file_id)" -ForegroundColor Cyan
        Write-Host "   📁 Filename: $($uploadResponse.filename)" -ForegroundColor Cyan
        Write-Host "   📊 Size: $($uploadResponse.file_size) bytes" -ForegroundColor Cyan
    } else {
        Write-Host "   ❌ Upload test échoué: pas de file_id" -ForegroundColor Red
    }
} catch {
    Write-Host "   ❌ Erreur upload test: $($_.Exception.Message)" -ForegroundColor Red
}

# Test d'upload principal
Write-Host "5. Test d'upload principal..." -ForegroundColor Yellow
try {
    $form = @{
        file = Get-Item $testFile
    }
    
    $mainUploadResponse = Invoke-RestMethod -Uri "http://127.0.0.1:8001/api/upload" -Method Post -Form $form
    
    if ($mainUploadResponse.success -and $mainUploadResponse.file_id) {
        Write-Host "   ✅ Upload principal réussi!" -ForegroundColor Green
        Write-Host "   📄 File ID: $($mainUploadResponse.file_id)" -ForegroundColor Cyan
        Write-Host "   📁 Filename: $($mainUploadResponse.filename)" -ForegroundColor Cyan
        Write-Host "   📊 Size: $($mainUploadResponse.file_size) bytes" -ForegroundColor Cyan
        Write-Host "   💾 DB Saved: $($mainUploadResponse.database_saved)" -ForegroundColor Cyan
        
        # Tester la récupération du fichier
        Write-Host "6. Test récupération fichier..." -ForegroundColor Yellow
        try {
            $fileInfo = Invoke-RestMethod -Uri "http://127.0.0.1:8001/api/files/$($mainUploadResponse.file_id)" -Method Get
            if ($fileInfo.success) {
                Write-Host "   ✅ Fichier récupéré avec succès!" -ForegroundColor Green
                Write-Host "   📁 Path: $($fileInfo.file_path)" -ForegroundColor Cyan
            } else {
                Write-Host "   ⚠️ Fichier non trouvé: $($fileInfo.error)" -ForegroundColor Yellow
            }
        } catch {
            Write-Host "   ❌ Erreur récupération: $($_.Exception.Message)" -ForegroundColor Red
        }
        
    } else {
        Write-Host "   ❌ Upload principal échoué: pas de file_id" -ForegroundColor Red
        Write-Host "   📄 Réponse: $($mainUploadResponse | ConvertTo-Json)" -ForegroundColor Yellow
    }
} catch {
    Write-Host "   ❌ Erreur upload principal: $($_.Exception.Message)" -ForegroundColor Red
}

# Lister tous les fichiers
Write-Host "7. Liste des fichiers uploadés..." -ForegroundColor Yellow
try {
    $filesList = Invoke-RestMethod -Uri "http://127.0.0.1:8001/api/files" -Method Get
    if ($filesList.success) {
        Write-Host "   ✅ $($filesList.total) fichier(s) trouvé(s)" -ForegroundColor Green
        foreach ($file in $filesList.files) {
            Write-Host "   📄 $($file.filename) (ID: $($file.file_id))" -ForegroundColor Cyan
        }
    } else {
        Write-Host "   ⚠️ Erreur liste: $($filesList.error)" -ForegroundColor Yellow
    }
} catch {
    Write-Host "   ❌ Erreur liste fichiers: $($_.Exception.Message)" -ForegroundColor Red
}

# Nettoyer le fichier de test
Remove-Item $testFile -ErrorAction SilentlyContinue

Write-Host ""
Write-Host "🎉 Test terminé!" -ForegroundColor Green
Write-Host ""
Write-Host "📋 Résumé:" -ForegroundColor Cyan
Write-Host "   - L'API fonctionne sur http://127.0.0.1:8001" -ForegroundColor White
Write-Host "   - L'upload retourne maintenant TOUJOURS un file_id" -ForegroundColor White
Write-Host "   - Les fichiers sont sauvegardés dans le répertoire uploads/" -ForegroundColor White
Write-Host "   - La base de données PostgreSQL est optionnelle" -ForegroundColor White
Write-Host ""
Write-Host "🌐 Endpoints disponibles:" -ForegroundColor Cyan
Write-Host "   - GET  /                    - Page d'accueil" -ForegroundColor White
Write-Host "   - GET  /health              - Vérification santé" -ForegroundColor White
Write-Host "   - POST /api/upload          - Upload principal" -ForegroundColor White
Write-Host "   - POST /api/upload/test     - Upload de test" -ForegroundColor White
Write-Host "   - GET  /api/files           - Liste des fichiers" -ForegroundColor White
Write-Host "   - GET  /api/files/{id}      - Info fichier" -ForegroundColor White
Write-Host ""
Write-Host "📚 Documentation: http://127.0.0.1:8001/docs" -ForegroundColor Cyan
