import os
import cv2
import numpy as np
import pytesseract
import logging
import re
import traceback
from typing import Dict, List, Any, Optional

# Get logger
logger = logging.getLogger("auto-grade-scribe.ocr.tesseract")

# Configure Tesseract path if needed
tesseract_paths = [
    r'C:\Program Files\Tesseract-OCR\tesseract.exe',
    r'C:\Program Files (x86)\Tesseract-OCR\tesseract.exe',
    r'C:\Users\<USER>\AppData\Local\Programs\Tesseract-OCR\tesseract.exe'
]

for path in tesseract_paths:
    if os.path.exists(path):
        pytesseract.pytesseract.tesseract_cmd = path
        break

class TesseractOCRService:
    def __init__(self):
        self.lang = "eng+fra"  # Default languages: English and French
        self.answer_symbols = ["A", "B", "C", "D", "E"]
        self.last_student_name = "Non détecté"
        self.last_student_id = ""

    def _preprocess_image(self, image_path: str, mode: str = "standard") -> np.ndarray:
        """
        Preprocess the image for better OCR results
        """
        # Read the image
        image = cv2.imread(image_path)
        if image is None:
            raise ValueError(f"Failed to read image: {image_path}")

        # Convert to grayscale
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)

        # Create debug directory
        debug_dir = "debug"
        os.makedirs(debug_dir, exist_ok=True)
        cv2.imwrite(os.path.join(debug_dir, "original.png"), image)
        cv2.imwrite(os.path.join(debug_dir, "gray.png"), gray)

        if mode == "qcm":
            # For QCM forms, enhance contrast and apply adaptive thresholding
            # Apply CLAHE (Contrast Limited Adaptive Histogram Equalization)
            clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8, 8))
            enhanced = clahe.apply(gray)

            # Apply bilateral filter to reduce noise while preserving edges
            filtered = cv2.bilateralFilter(enhanced, 11, 17, 17)

            # Apply adaptive thresholding
            thresh = cv2.adaptiveThreshold(
                filtered, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C,
                cv2.THRESH_BINARY_INV, 11, 2
            )

            # Apply morphological operations to enhance marks
            kernel = np.ones((2, 2), np.uint8)
            processed = cv2.morphologyEx(thresh, cv2.MORPH_CLOSE, kernel)

            # Save debug images
            cv2.imwrite(os.path.join(debug_dir, "qcm_enhanced.png"), enhanced)
            cv2.imwrite(os.path.join(debug_dir, "qcm_filtered.png"), filtered)
            cv2.imwrite(os.path.join(debug_dir, "qcm_thresh.png"), thresh)
            cv2.imwrite(os.path.join(debug_dir, "qcm_processed.png"), processed)

            return processed

        elif mode == "handwritten":
            # For handwritten text
            filtered = cv2.bilateralFilter(gray, 11, 17, 17)
            clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8, 8))
            enhanced = clahe.apply(filtered)
            _, thresh = cv2.threshold(enhanced, 0, 255, cv2.THRESH_BINARY_INV + cv2.THRESH_OTSU)

            # Save debug images
            cv2.imwrite(os.path.join(debug_dir, "handwritten_filtered.png"), filtered)
            cv2.imwrite(os.path.join(debug_dir, "handwritten_enhanced.png"), enhanced)
            cv2.imwrite(os.path.join(debug_dir, "handwritten_thresh.png"), thresh)

            return thresh

        else:  # standard mode
            blurred = cv2.GaussianBlur(gray, (5, 5), 0)
            _, thresh = cv2.threshold(blurred, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)

            # Save debug images
            cv2.imwrite(os.path.join(debug_dir, "standard_blurred.png"), blurred)
            cv2.imwrite(os.path.join(debug_dir, "standard_thresh.png"), thresh)

            return thresh

    def _preprocess_for_handwriting(self, image) -> np.ndarray:
        """
        Specialized preprocessing for handwritten text
        """
        # Convert to grayscale if needed
        if len(image.shape) == 3:
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        else:
            gray = image.copy()

        # Apply CLAHE for better contrast
        clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8, 8))
        enhanced = clahe.apply(gray)

        # Denoise the image
        denoised = cv2.fastNlMeansDenoising(enhanced, None, 10, 7, 21)

        # Apply adaptive thresholding
        thresh = cv2.adaptiveThreshold(denoised, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C,
                                      cv2.THRESH_BINARY_INV, 11, 2)

        # Apply morphological operations to connect broken strokes
        kernel = np.ones((1, 1), np.uint8)
        dilated = cv2.dilate(thresh, kernel, iterations=1)

        # Invert back for OCR
        result = cv2.bitwise_not(dilated)

        return result

    def _preprocess_for_digits(self, image) -> np.ndarray:
        """
        Specialized preprocessing for digit recognition
        """
        # Convert to grayscale if needed
        if len(image.shape) == 3:
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        else:
            gray = image.copy()

        # Apply bilateral filter to reduce noise while preserving edges
        filtered = cv2.bilateralFilter(gray, 11, 17, 17)

        # Apply Otsu's thresholding
        _, thresh = cv2.threshold(filtered, 0, 255, cv2.THRESH_BINARY_INV + cv2.THRESH_OTSU)

        # Apply morphological operations to enhance digits
        kernel = np.ones((2, 2), np.uint8)
        dilated = cv2.dilate(thresh, kernel, iterations=1)

        # Invert back for OCR
        result = cv2.bitwise_not(dilated)

        return result

    def _locate_field_region(self, image, field_labels) -> Optional[tuple]:
        """
        Locate a specific field (like name or ID) in the image
        Returns (x, y, w, h) of the region or None if not found
        """
        try:
            # Convert to grayscale if needed
            if len(image.shape) == 3:
                gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            else:
                gray = image.copy()

            # Apply OCR to the entire image to find field labels
            text_data = pytesseract.image_to_data(gray, output_type=pytesseract.Output.DICT)

            # Look for field labels in the OCR results
            for i, text in enumerate(text_data['text']):
                # Check if this text matches any of our field labels
                for label in field_labels:
                    if label.lower() in text.lower():
                        # Found a label, get its bounding box
                        x = text_data['left'][i]
                        y = text_data['top'][i]
                        w = text_data['width'][i]
                        h = text_data['height'][i]

                        # Expand the region to include the value field (to the right or below)
                        # Assume the value is to the right of the label
                        value_region_width = int(w * 4)  # Adjust as needed
                        value_region_height = int(h * 1.5)  # Adjust as needed

                        # Return the expanded region
                        return (x, y, value_region_width, value_region_height)

            # If no label found, try to guess based on form structure
            # For example, name is often at the top of the form
            height, width = gray.shape

            # Assume name field is in the top 20% of the form
            name_region_height = int(height * 0.2)

            # Check if any of the labels are in our list
            for label in field_labels:
                if label.lower() in ["nom", "name", "étudiant", "student"]:
                    # Return a region for the name field
                    return (0, 0, width, name_region_height)
                elif label.lower() in ["id", "n°", "numéro", "number"]:
                    # Return a region for the ID field (often near the name)
                    return (width // 2, 0, width // 2, name_region_height)

            return None

        except Exception as e:
            logging.error(f"Error locating field region: {e}")
            return None

    def extract_text(self, file_path: str, mode: str = "standard") -> Dict[str, Any]:
        """
        Extract text from an image using Tesseract OCR
        """
        try:
            if not os.path.exists(file_path):
                return {"error": "Le fichier n'existe pas", "success": False}

            # Reset student info
            self.last_student_name = "Non détecté"
            self.last_student_id = ""

            # Preprocess the image
            processed_image = self._preprocess_image(file_path, mode)

            # Configure Tesseract based on the mode
            config = ""
            if mode == "standard":
                config = "--oem 3 --psm 6"  # Single uniform block of text
            elif mode == "qcm":
                config = "--oem 3 --psm 4"  # Single column of text
            elif mode == "handwritten":
                config = "--oem 3 --psm 6"  # Single uniform block of text
            elif mode == "exam":
                config = "--oem 3 --psm 1"  # Automatic page segmentation

            # Extract text using Tesseract
            extracted_text = pytesseract.image_to_string(processed_image, config=config, lang=self.lang)

            # For QCM mode, also extract student answers
            answers = {}

            if mode == "qcm" or mode == "standard":
                # Extract answers using pattern matching and image processing
                answers = self._extract_answers_from_text(file_path)

                # If no student name was detected in _extract_answers_from_text, try to extract it from the text
                if self.last_student_name == "Non détecté":
                    lines = extracted_text.split('\n')
                    for i, line in enumerate(lines):
                        if "nom" in line.lower() or "étudiant" in line.lower() or "student" in line.lower():
                            if i+1 < len(lines) and lines[i+1].strip():
                                self.last_student_name = lines[i+1].strip()
                                break

            result = {
                "success": True,
                "extracted_text": extracted_text,
                "student_name": self.last_student_name,
                "student_id": self.last_student_id,
                "extracted_answers": answers,
                "mode": mode,
                "model": "tesseract"
            }

            return result

        except Exception as e:
            return {
                "error": str(e),
                "success": False
            }



    def _extract_answers_from_text(self, image_path: str) -> Dict[int, str]:
        """
        Extract QCM answers using OCR and image processing
        """
        try:
            # Load the image
            image = cv2.imread(image_path)
            if image is None:
                return {}

            # Use Tesseract with specific configuration for forms
            config = "--oem 3 --psm 6"
            extracted_text = pytesseract.image_to_string(
                image,
                config=config,
                lang=self.lang
            )

            # Extract student name and ID with enhanced techniques
            student_name = "Non détecté"
            student_id = ""

            # Apply specific OCR configuration for text regions
            # This helps with handwritten text recognition
            text_config = "--oem 3 --psm 4 -l eng+fra"

            # Try to locate the name field in the image
            # First, look for common labels like "Nom:" or "Name:"
            name_labels = ["Nom", "Name", "Étudiant", "Student"]
            name_region = self._locate_field_region(image, name_labels)

            if name_region is not None:
                # Extract just the name region and apply OCR with optimized settings
                x, y, w, h = name_region
                name_roi = image[y:y+h, x:x+w]

                # Apply preprocessing specific for handwritten text
                name_roi_processed = self._preprocess_for_handwriting(name_roi)

                # Save debug image of the name region
                debug_dir = "debug"
                os.makedirs(debug_dir, exist_ok=True)
                cv2.imwrite(os.path.join(debug_dir, "name_region.png"), name_roi)
                cv2.imwrite(os.path.join(debug_dir, "name_region_processed.png"), name_roi_processed)

                # Apply OCR specifically to the name region
                name_text = pytesseract.image_to_string(
                    name_roi_processed,
                    config=text_config
                )

                logging.info(f"Extracted name text from region: {name_text}")

                # Look for the actual name in the extracted text
                # Try different patterns to extract the name
                name_patterns = [
                    r'[Nn]om\s*:?\s*([A-Za-zÀ-ÖØ-öø-ÿ\s\-\'\.]+)',
                    r'[Nn]ame\s*:?\s*([A-Za-zÀ-ÖØ-öø-ÿ\s\-\'\.]+)',
                    r'^([A-Za-zÀ-ÖØ-öø-ÿ\s\-\'\.]+)$'  # Just the name by itself
                ]

                for pattern in name_patterns:
                    name_match = re.search(pattern, name_text)
                    if name_match:
                        student_name = name_match.group(1).strip()
                        # Clean up the name
                        student_name = re.sub(r'\s+', ' ', student_name)  # Replace multiple spaces with one
                        student_name = student_name.strip()
                        break

            # If name not found in the specific region, try the full text
            if student_name == "Non détecté":
                name_patterns = [
                    r'[Nn]om\s*:?\s*([A-Za-zÀ-ÖØ-öø-ÿ\s\-\'\.]+)',
                    r'[Nn]ame\s*:?\s*([A-Za-zÀ-ÖØ-öø-ÿ\s\-\'\.]+)'
                ]

                for pattern in name_patterns:
                    name_match = re.search(pattern, extracted_text)
                    if name_match:
                        student_name = name_match.group(1).strip()
                        # Clean up the name
                        student_name = re.sub(r'\s+', ' ', student_name)
                        student_name = student_name.strip()
                        break

            # Look for ID field
            id_labels = ["ID", "N°", "Numéro", "Number"]
            id_region = self._locate_field_region(image, id_labels)

            if id_region is not None:
                # Extract just the ID region
                x, y, w, h = id_region
                id_roi = image[y:y+h, x:x+w]

                # Apply preprocessing optimized for digits
                id_roi_processed = self._preprocess_for_digits(id_roi)

                # Save debug image
                cv2.imwrite(os.path.join(debug_dir, "id_region.png"), id_roi)
                cv2.imwrite(os.path.join(debug_dir, "id_region_processed.png"), id_roi_processed)

                # Apply OCR with digit-optimized settings
                id_config = "--oem 3 --psm 7 -c tessedit_char_whitelist=0123456789"
                id_text = pytesseract.image_to_string(
                    id_roi_processed,
                    config=id_config
                )

                logging.info(f"Extracted ID text from region: {id_text}")

                # Extract digits
                id_match = re.search(r'(\d+)', id_text)
                if id_match:
                    student_id = id_match.group(1).strip()

            # If ID not found in specific region, try patterns in the full text
            if not student_id:
                id_patterns = [
                    r'[Ii][Dd]\s*:?\s*(\d+)',
                    r'[Nn]°\s*:?\s*(\d+)',
                    r'[Nn]umero\s*:?\s*(\d+)',
                    r'[Nn]umber\s*:?\s*(\d+)'
                ]

                for pattern in id_patterns:
                    id_match = re.search(pattern, extracted_text)
                    if id_match:
                        student_id = id_match.group(1).strip()
                        break

            # If still no ID found, look for a sequence of digits that might be an ID
            if not student_id:
                id_match = re.search(r'(\d{5,})', extracted_text)
                if id_match:
                    student_id = id_match.group(1).strip()

            # Try to detect answers using image processing
            answers = self._detect_answers_from_image(image)

            # If image processing didn't work well, fall back to text extraction
            if len(answers) < 5:  # Arbitrary threshold
                # Look for patterns like "1. A", "2. B", "3. C", etc.
                patterns = [
                    r'(\d+)[\.\)\-:]\s*([A-E])',  # 1. A, 1) A, 1-A, 1: A
                    r'[Qq](\d+)[\.\)\-:]\s*([A-E])',  # Q1. A, Q1) A, Q1-A, Q1: A
                    r'[Qq]uestion\s*(\d+)[\.\)\-:]\s*([A-E])'  # Question 1. A, etc.
                ]

                for pattern in patterns:
                    matches = re.findall(pattern, extracted_text)
                    for match in matches:
                        question_num = int(match[0])
                        answer = match[1]
                        answers[question_num] = answer

                # If still not enough answers, try a more aggressive approach
                if len(answers) < 5:
                    lines = extracted_text.split('\n')
                    for line in lines:
                        question_match = re.search(r'[Qq]uestion\s*(\d+)', line)
                        if question_match:
                            question_num = int(question_match.group(1))
                            for letter in self.answer_symbols:
                                if letter in line:
                                    answers[question_num] = letter
                                    break

            # Store student info for later use
            self.last_student_name = student_name
            self.last_student_id = student_id

            return answers

        except Exception as e:
            print(f"Error extracting answers: {e}")
            return {}

    def _detect_answers_from_image(self, image) -> Dict[int, str]:
        """
        Detect QCM answers by analyzing the image for marked checkboxes
        Uses multiple techniques to improve detection accuracy
        """
        try:
            # Convert to grayscale
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)

            # Apply adaptive thresholding to handle different lighting conditions
            binary = cv2.adaptiveThreshold(gray, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C,
                                          cv2.THRESH_BINARY_INV, 11, 2)

            # Apply morphological operations to enhance the marks
            kernel = np.ones((2, 2), np.uint8)
            binary = cv2.morphologyEx(binary, cv2.MORPH_CLOSE, kernel)

            # Save debug images
            debug_dir = "debug"
            os.makedirs(debug_dir, exist_ok=True)
            cv2.imwrite(os.path.join(debug_dir, "debug_binary.png"), binary)

            # Create a copy of the original image for visualization
            debug_image = image.copy()

            # Method 1: Template matching approach for checkbox detection
            answers_method1 = self._detect_answers_template_matching(gray, binary)
            logging.info(f"Template matching found {len(answers_method1)} answers: {answers_method1}")

            # Method 2: Contour-based approach for checkbox detection
            answers_method2 = self._detect_answers_contour_based(gray, binary)
            logging.info(f"Contour-based found {len(answers_method2)} answers: {answers_method2}")

            # Method 3: Grid-based approach for structured forms
            answers_method3 = self._detect_answers_grid_based(gray, binary)
            logging.info(f"Grid-based found {len(answers_method3)} answers: {answers_method3}")

            # Combine results from different methods, prioritizing method 3 (grid-based)
            # as it's typically more reliable for structured QCM forms
            answers = {}

            # Start with the most reliable method for structured forms
            if len(answers_method3) >= 5:  # If we found at least 5 answers with grid method
                answers = answers_method3
                logging.info("Using grid-based method results")
            elif len(answers_method2) >= 5:  # Fall back to contour method
                answers = answers_method2
                logging.info("Using contour-based method results")
            elif len(answers_method1) >= 3:  # Last resort is template matching
                answers = answers_method1
                logging.info("Using template matching method results")
            else:
                # Combine all methods if none is clearly better
                answers = {**answers_method1, **answers_method2, **answers_method3}
                logging.info("Using combined results from all methods")

            # Visualize the detected answers on the debug image
            self._visualize_answers(debug_image, answers)
            cv2.imwrite(os.path.join(debug_dir, "debug_result.png"), debug_image)

            return answers

        except Exception as e:
            print(f"Error detecting answers from image: {e}")
            return {}

    def _detect_answers_template_matching(self, gray, binary) -> Dict[int, str]:
        """
        Detect answers using template matching approach
        Good for detecting specific patterns like X marks or filled circles
        """
        try:
            answers = {}
            options = ['A', 'B', 'C', 'D', 'E']

            # Create templates for different mark types
            # X mark template
            x_template = np.zeros((20, 20), dtype=np.uint8)
            cv2.line(x_template, (0, 0), (20, 20), 255, 2)
            cv2.line(x_template, (0, 20), (20, 0), 255, 2)

            # Filled circle template
            circle_template = np.zeros((20, 20), dtype=np.uint8)
            cv2.circle(circle_template, (10, 10), 8, 255, -1)

            # Check mark template
            check_template = np.zeros((20, 20), dtype=np.uint8)
            pts = np.array([[5, 10], [8, 15], [15, 5]])
            cv2.polylines(check_template, [pts], False, 255, 2)

            templates = [x_template, circle_template, check_template]

            # Find potential question rows (horizontal lines or text rows)
            horizontal_projection = np.sum(binary, axis=1)
            row_positions = []

            # Find peaks in horizontal projection (likely question rows)
            for i in range(1, len(horizontal_projection) - 1):
                if horizontal_projection[i] > horizontal_projection[i-1] and horizontal_projection[i] > horizontal_projection[i+1]:
                    if horizontal_projection[i] > np.mean(horizontal_projection) * 1.5:
                        row_positions.append(i)

            # Group nearby rows
            grouped_rows = []
            current_group = [row_positions[0]] if row_positions else []

            for i in range(1, len(row_positions)):
                if row_positions[i] - row_positions[i-1] < 30:  # If rows are close
                    current_group.append(row_positions[i])
                else:
                    if current_group:
                        grouped_rows.append(int(np.mean(current_group)))
                    current_group = [row_positions[i]]

            if current_group:
                grouped_rows.append(int(np.mean(current_group)))

            # For each potential question row
            for q_idx, row_y in enumerate(grouped_rows):
                if q_idx >= 10:  # Limit to 10 questions
                    break

                # Define a region of interest around this row
                roi_height = 40
                roi_y_start = max(0, row_y - roi_height//2)
                roi_y_end = min(gray.shape[0], row_y + roi_height//2)
                roi = binary[roi_y_start:roi_y_end, :]

                # Find potential option positions (divide width into 5 sections)
                width = gray.shape[1]
                option_width = width // 6  # Divide into 6 sections (1 for question, 5 for options)

                best_match_val = 0
                best_match_idx = -1

                # For each potential option position
                for opt_idx in range(5):
                    opt_x_start = option_width + opt_idx * option_width
                    opt_x_end = opt_x_start + option_width

                    option_roi = roi[:, opt_x_start:opt_x_end]

                    # Skip if ROI is too small
                    if option_roi.shape[0] < 10 or option_roi.shape[1] < 10:
                        continue

                    # Try each template
                    for template in templates:
                        # Resize template to different sizes
                        for scale in [0.5, 0.75, 1.0, 1.25, 1.5]:
                            resized_template = cv2.resize(template, (0, 0), fx=scale, fy=scale)

                            # Skip if template is larger than ROI
                            if resized_template.shape[0] > option_roi.shape[0] or resized_template.shape[1] > option_roi.shape[1]:
                                continue

                            # Apply template matching
                            result = cv2.matchTemplate(option_roi, resized_template, cv2.TM_CCOEFF_NORMED)
                            _, max_val, _, _ = cv2.minMaxLoc(result)

                            if max_val > best_match_val and max_val > 0.5:  # Threshold for good match
                                best_match_val = max_val
                                best_match_idx = opt_idx

                # If we found a good match, record the answer
                if best_match_idx >= 0:
                    answers[q_idx + 1] = options[best_match_idx]

            return answers

        except Exception as e:
            print(f"Error in template matching: {e}")
            return {}

    def _detect_answers_contour_based(self, gray, binary) -> Dict[int, str]:
        """
        Detect answers using contour-based approach
        Good for detecting filled checkboxes
        """
        try:
            # Create a debug image for visualization
            debug_image = cv2.cvtColor(binary.copy(), cv2.COLOR_GRAY2BGR)

            # Find contours
            contours, _ = cv2.findContours(binary, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

            # Filter contours to find checkboxes
            checkboxes = []
            for contour in contours:
                x, y, w, h = cv2.boundingRect(contour)
                # Filter by size (adjust these values based on your form)
                if 10 < w < 50 and 10 < h < 50 and 0.7 < w/h < 1.3:
                    # Check if the checkbox is filled (has enough black pixels)
                    roi = binary[y:y+h, x:x+w]
                    black_pixel_count = cv2.countNonZero(roi)
                    if black_pixel_count > (w * h * 0.1):  # At least 10% filled (lowered threshold)
                        checkboxes.append((x, y, w, h))
                        # Draw rectangle around detected checkbox
                        cv2.rectangle(debug_image, (x, y), (x+w, y+h), (0, 255, 0), 2)

            # Save debug image
            debug_dir = "debug"
            os.makedirs(debug_dir, exist_ok=True)
            cv2.imwrite(os.path.join(debug_dir, "detected_checkboxes.png"), debug_image)

            # Sort checkboxes by y-coordinate (row) first, then by x-coordinate (column)
            # This groups them by question
            checkboxes.sort(key=lambda box: (box[1] // 50, box[0]))

            # Group checkboxes into rows (questions)
            rows = []
            current_row = []
            last_y = -100  # Initialize with a value that will trigger a new row

            for box in checkboxes:
                x, y, w, h = box
                # If this box is on a new row (y position differs significantly)
                if abs(y - last_y) > 30:
                    if current_row:
                        rows.append(current_row)
                    current_row = [box]
                else:
                    current_row.append(box)
                last_y = y

            # Add the last row if it exists
            if current_row:
                rows.append(current_row)

            # Create a debug image for visualizing rows
            row_debug_image = cv2.cvtColor(binary.copy(), cv2.COLOR_GRAY2BGR)

            # Process each row to find the marked option
            answers = {}
            options = ['A', 'B', 'C', 'D', 'E']

            for i, row in enumerate(rows):
                if i >= 10:  # Limit to 10 questions
                    break

                # Sort boxes in this row by x-coordinate
                row.sort(key=lambda box: box[0])

                # Draw this row with a unique color
                row_color = (np.random.randint(0, 255), np.random.randint(0, 255), np.random.randint(0, 255))
                for box in row:
                    x, y, w, h = box
                    cv2.rectangle(row_debug_image, (x, y), (x+w, y+h), row_color, 2)
                    # Add question number
                    cv2.putText(row_debug_image, f"Q{i+1}", (x, y-5), cv2.FONT_HERSHEY_SIMPLEX, 0.5, row_color, 1)

                # Find the marked option using multiple detection methods
                max_score = 0
                marked_option = -1
                detection_method = "none"

                for j, box in enumerate(row):
                    if j >= 5:  # Limit to 5 options per question
                        break

                    x, y, w, h = box
                    roi = binary[y:y+h, x:x+w]

                    # Method 1: Check for black pixel density (filled pattern)
                    fill_score = self._detect_filled_pattern(roi) * 100

                    # Method 2: Check specifically for X pattern
                    x_score = self._detect_x_pattern(roi) * 150  # Weight X pattern more

                    # Method 3: Check for check mark pattern
                    check_score = self._detect_check_pattern(roi) * 120

                    # Determine the highest scoring method
                    method_scores = [
                        ("fill", fill_score),
                        ("X", x_score),
                        ("check", check_score)
                    ]

                    best_method, best_score = max(method_scores, key=lambda x: x[1])

                    # Log detailed detection results
                    logging.debug(f"Q{i+1}, Option {options[j]}: fill={fill_score:.1f}, X={x_score:.1f}, check={check_score:.1f}, best={best_method}({best_score:.1f})")

                    # Add score visualization to the debug image
                    score_text = f"{best_score:.1f}"
                    cv2.putText(row_debug_image, score_text, (x, y+h+15), cv2.FONT_HERSHEY_SIMPLEX, 0.4, (255, 255, 255), 1)

                    if best_score > max_score:
                        max_score = best_score
                        marked_option = j
                        detection_method = best_method

                # If an option was marked with a good enough score, record it
                if marked_option >= 0 and marked_option < len(options) and max_score > 10:  # Threshold for detection
                    answers[i + 1] = options[marked_option]

                    # Highlight the selected option in the debug image
                    if marked_option < len(row):
                        x, y, w, h = row[marked_option]
                        cv2.rectangle(row_debug_image, (x, y), (x+w, y+h), (0, 255, 255), 3)

                        # Add answer label
                        answer_text = f"Q{i+1}: {options[marked_option]} ({detection_method})"
                        cv2.putText(row_debug_image, answer_text, (x, y-20), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 255), 2)

                    logging.info(f"Question {i+1}: Selected option {options[marked_option]} (method: {detection_method}, score: {max_score:.1f})")

            # Save the row visualization
            cv2.imwrite(os.path.join(debug_dir, "question_rows.png"), row_debug_image)

            return answers

        except Exception as e:
            print(f"Error in contour-based detection: {e}")
            return {}

    def _detect_x_pattern(self, roi) -> float:
        """
        Detect X pattern in a region of interest
        Returns a score indicating how likely the ROI contains an X mark
        """
        try:
            if roi.size == 0:
                return 0

            height, width = roi.shape

            # Skip if ROI is too small
            if height < 10 or width < 10:
                return 0

            # Save debug image of the ROI
            debug_dir = "debug"
            os.makedirs(debug_dir, exist_ok=True)

            # Create a unique filename based on ROI dimensions
            roi_filename = f"roi_{height}x{width}_{np.random.randint(1000)}.png"
            cv2.imwrite(os.path.join(debug_dir, roi_filename), roi)

            # Create masks for diagonal lines (X pattern)
            mask1 = np.zeros((height, width), dtype=np.uint8)
            mask2 = np.zeros((height, width), dtype=np.uint8)

            # Draw diagonal lines with varying thickness based on ROI size
            thickness = max(1, min(height, width) // 10)
            cv2.line(mask1, (0, 0), (width-1, height-1), 255, thickness)
            cv2.line(mask2, (0, height-1), (width-1, 0), 255, thickness)

            # Save debug images of the masks
            cv2.imwrite(os.path.join(debug_dir, f"mask1_{roi_filename}"), mask1)
            cv2.imwrite(os.path.join(debug_dir, f"mask2_{roi_filename}"), mask2)

            # Count pixels along the diagonals
            diagonal1_pixels = cv2.bitwise_and(roi, mask1)
            diagonal2_pixels = cv2.bitwise_and(roi, mask2)

            # Save debug images of the diagonal pixels
            cv2.imwrite(os.path.join(debug_dir, f"diag1_{roi_filename}"), diagonal1_pixels)
            cv2.imwrite(os.path.join(debug_dir, f"diag2_{roi_filename}"), diagonal2_pixels)

            diagonal1_count = cv2.countNonZero(diagonal1_pixels)
            diagonal2_count = cv2.countNonZero(diagonal2_pixels)

            # Calculate the percentage of the diagonals that are filled
            diagonal1_percentage = diagonal1_count / cv2.countNonZero(mask1) if cv2.countNonZero(mask1) > 0 else 0
            diagonal2_percentage = diagonal2_count / cv2.countNonZero(mask2) if cv2.countNonZero(mask2) > 0 else 0

            # Calculate the average percentage (higher is better)
            x_score = (diagonal1_percentage + diagonal2_percentage) / 2

            # Additional check: Ensure both diagonals have some pixels
            # This helps distinguish X marks from single diagonal lines
            if diagonal1_count > 0 and diagonal2_count > 0:
                # Boost the score if both diagonals have pixels
                x_score *= 1.5

            # Additional check: Calculate the ratio of pixels in the X pattern
            # compared to total pixels in the ROI
            total_pixels = cv2.countNonZero(roi)
            if total_pixels > 0:
                x_pattern_ratio = (diagonal1_count + diagonal2_count) / total_pixels

                # If most of the pixels are in the X pattern, boost the score
                if x_pattern_ratio > 0.5:
                    x_score *= 1.2

                # If very few pixels are in the X pattern, reduce the score
                if x_pattern_ratio < 0.2:
                    x_score *= 0.8

            # Log the results
            logging.debug(f"X pattern detection: score={x_score:.3f}, d1={diagonal1_percentage:.3f}, d2={diagonal2_percentage:.3f}")

            return x_score

        except Exception as e:
            print(f"Error detecting X pattern: {e}")
            return 0

    def _detect_check_pattern(self, roi) -> float:
        """
        Detect check mark (✓) pattern in a region of interest
        Returns a score indicating how likely the ROI contains a check mark
        """
        try:
            if roi.size == 0:
                return 0

            height, width = roi.shape

            # Skip if ROI is too small
            if height < 10 or width < 10:
                return 0

            # Create a mask for check mark pattern (✓)
            mask = np.zeros((height, width), dtype=np.uint8)

            # Define check mark points based on ROI dimensions
            # A check mark is typically a short line going down-right
            # followed by a longer line going up-right
            start_x = int(width * 0.2)
            mid_x = int(width * 0.4)
            end_x = int(width * 0.8)

            start_y = int(height * 0.5)
            mid_y = int(height * 0.7)
            end_y = int(height * 0.3)

            # Draw the check mark
            thickness = max(1, min(height, width) // 10)
            cv2.line(mask, (start_x, start_y), (mid_x, mid_y), 255, thickness)
            cv2.line(mask, (mid_x, mid_y), (end_x, end_y), 255, thickness)

            # Save debug images
            debug_dir = "debug"
            os.makedirs(debug_dir, exist_ok=True)
            roi_filename = f"roi_check_{height}x{width}_{np.random.randint(1000)}.png"
            cv2.imwrite(os.path.join(debug_dir, roi_filename), roi)
            cv2.imwrite(os.path.join(debug_dir, f"mask_check_{roi_filename}"), mask)

            # Count pixels along the check mark
            check_pixels = cv2.bitwise_and(roi, mask)
            cv2.imwrite(os.path.join(debug_dir, f"check_pixels_{roi_filename}"), check_pixels)

            check_count = cv2.countNonZero(check_pixels)

            # Calculate the percentage of the check mark that is filled
            check_percentage = check_count / cv2.countNonZero(mask) if cv2.countNonZero(mask) > 0 else 0

            # Additional check: Calculate the ratio of pixels in the check pattern
            # compared to total pixels in the ROI
            total_pixels = cv2.countNonZero(roi)
            if total_pixels > 0:
                check_pattern_ratio = check_count / total_pixels

                # If most of the pixels are in the check pattern, boost the score
                if check_pattern_ratio > 0.5:
                    check_percentage *= 1.2

                # If very few pixels are in the check pattern, reduce the score
                if check_pattern_ratio < 0.2:
                    check_percentage *= 0.8

            # Log the results
            logging.debug(f"Check pattern detection: score={check_percentage:.3f}")

            return check_percentage

        except Exception as e:
            print(f"Error detecting check pattern: {e}")
            return 0

    def _detect_filled_pattern(self, roi) -> float:
        """
        Detect filled/shaded pattern in a region of interest
        Returns a score indicating how likely the ROI contains a filled mark
        """
        try:
            if roi.size == 0:
                return 0

            height, width = roi.shape

            # Skip if ROI is too small
            if height < 10 or width < 10:
                return 0

            # Calculate the percentage of black pixels in the ROI
            total_pixels = height * width
            black_pixels = cv2.countNonZero(roi)
            fill_percentage = black_pixels / total_pixels if total_pixels > 0 else 0

            # Save debug images
            debug_dir = "debug"
            os.makedirs(debug_dir, exist_ok=True)
            roi_filename = f"roi_fill_{height}x{width}_{np.random.randint(1000)}.png"
            cv2.imwrite(os.path.join(debug_dir, roi_filename), roi)

            # Create a visualization of the fill detection
            # Create a color image to show the fill level
            vis_image = np.zeros((height, width, 3), dtype=np.uint8)
            # Fill with green based on the fill percentage
            vis_image[:, :, 1] = int(255 * fill_percentage)
            cv2.imwrite(os.path.join(debug_dir, f"fill_vis_{roi_filename}"), vis_image)

            # Log the results
            logging.debug(f"Fill pattern detection: score={fill_percentage:.3f}, pixels={black_pixels}/{total_pixels}")

            return fill_percentage

        except Exception as e:
            print(f"Error detecting filled pattern: {e}")
            return 0

    def _visualize_answers(self, image, answers: Dict[int, str]) -> None:
        """
        Visualize detected answers on the image for debugging
        Creates a comprehensive visualization with detected answers highlighted
        """
        try:
            # Create a copy of the image for visualization
            vis_image = image.copy()

            # Get image dimensions
            height, width = vis_image.shape[:2]

            # Create a semi-transparent overlay for highlighting
            overlay = vis_image.copy()

            # Estimate the grid structure (assuming a standard QCM layout)
            num_questions = min(10, len(answers.keys()))
            if num_questions == 0:
                return

            question_spacing = height // (num_questions + 1)
            option_area_width = int(width * 0.8)
            option_spacing = option_area_width // 5  # 5 options (A-E)
            option_start_x = int(width * 0.2)

            # Map option letters to indices
            option_indices = {'A': 0, 'B': 1, 'C': 2, 'D': 3, 'E': 4}

            # Draw grid lines to visualize the structure
            for i in range(1, num_questions + 2):
                y = i * question_spacing
                cv2.line(overlay, (0, y), (width, y), (0, 0, 255), 1)

            for i in range(6):  # 5 options + 1 for question text
                x = option_start_x + i * option_spacing
                cv2.line(overlay, (x, 0), (x, height), (0, 0, 255), 1)

            # Add option labels at the top
            options = ['A', 'B', 'C', 'D', 'E']
            for i, opt in enumerate(options):
                x = option_start_x + i * option_spacing + option_spacing // 2
                cv2.putText(overlay, opt, (x - 10, 30),
                            cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 0, 255), 2)

            # Draw detected answers
            for q_num, answer in answers.items():
                # Calculate the y-coordinate for this question
                q_y = int(q_num) * question_spacing

                # Add question number
                cv2.putText(overlay, f"Q{q_num}", (20, q_y + 5),
                            cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 0), 2)

                # Get the index of the selected option
                if answer in option_indices:
                    opt_idx = option_indices[answer]

                    # Calculate the x-coordinate for this option
                    opt_x = option_start_x + opt_idx * option_spacing + option_spacing // 2

                    # Draw a filled circle at the detected answer
                    cv2.circle(overlay, (opt_x, q_y), 20, (0, 255, 0), -1)

                    # Add answer label inside the circle
                    cv2.putText(overlay, answer, (opt_x - 8, q_y + 8),
                                cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 0), 2)

                    # Add a highlight rectangle around the option area
                    rect_x = option_start_x + opt_idx * option_spacing
                    rect_y = q_y - question_spacing // 2
                    rect_w = option_spacing
                    rect_h = question_spacing
                    cv2.rectangle(overlay, (rect_x, rect_y), (rect_x + rect_w, rect_y + rect_h),
                                 (0, 255, 0), 2)

            # Blend the overlay with the original image
            alpha = 0.6  # Transparency factor
            cv2.addWeighted(overlay, alpha, vis_image, 1 - alpha, 0, vis_image)

            # Add a title and summary
            title = "QCM Detection Results"
            cv2.putText(vis_image, title, (width // 2 - 150, 50),
                        cv2.FONT_HERSHEY_SIMPLEX, 1.0, (0, 0, 0), 2)

            summary = f"Detected {len(answers)} answers"
            cv2.putText(vis_image, summary, (width // 2 - 100, height - 30),
                        cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 0), 2)

            # Save the visualization
            debug_dir = "debug"
            os.makedirs(debug_dir, exist_ok=True)
            cv2.imwrite(os.path.join(debug_dir, "answers_visualization.png"), vis_image)

            # Create a more detailed visualization with answer key
            key_image = np.ones((300, width, 3), dtype=np.uint8) * 255

            # Add title
            cv2.putText(key_image, "Detected Answers", (20, 30),
                        cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 0, 0), 2)

            # Add column headers
            cv2.putText(key_image, "Question", (20, 70),
                        cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 0), 2)
            cv2.putText(key_image, "Answer", (150, 70),
                        cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 0), 2)

            # Add horizontal line
            cv2.line(key_image, (20, 80), (width - 20, 80), (0, 0, 0), 1)

            # Add answers
            y_pos = 110
            for q_num in sorted(answers.keys()):
                answer = answers[q_num]
                cv2.putText(key_image, f"Question {q_num}", (20, y_pos),
                            cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 0, 0), 1)
                cv2.putText(key_image, answer, (150, y_pos),
                            cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 0, 255), 2)
                y_pos += 30

            # Save the answer key
            cv2.imwrite(os.path.join(debug_dir, "answers_key.png"), key_image)

            # Create a combined image (visualization + key)
            combined_height = vis_image.shape[0] + key_image.shape[0]
            combined_image = np.ones((combined_height, width, 3), dtype=np.uint8) * 255
            combined_image[:vis_image.shape[0], :] = vis_image
            combined_image[vis_image.shape[0]:, :] = key_image

            # Save the combined visualization
            cv2.imwrite(os.path.join(debug_dir, "answers_complete.png"), combined_image)

            # Replace the original image with the visualization for the result
            image[:] = vis_image[:]

        except Exception as e:
            logging.error(f"Error visualizing answers: {e}")
            traceback.print_exc()

    def _detect_answers_grid_based(self, gray, binary) -> Dict[int, str]:
        """
        Detect answers using a grid-based approach
        Good for structured QCM forms with a clear grid layout
        """
        try:
            answers = {}
            options = ['A', 'B', 'C', 'D', 'E']

            # Get image dimensions
            height, width = gray.shape

            # Estimate the grid structure (assuming a standard QCM layout)
            # Typically, questions are evenly spaced vertically
            # and options are evenly spaced horizontally

            # Estimate number of questions (rows) - assume max 10 questions
            num_questions = min(10, height // 50)  # Assume each question takes about 50 pixels

            # Estimate vertical spacing between questions
            question_spacing = height // (num_questions + 1)

            # Estimate horizontal spacing between options
            # Assume the first 20% of width is for question text, rest for options
            option_area_width = int(width * 0.8)
            option_spacing = option_area_width // 5  # 5 options (A-E)
            option_start_x = int(width * 0.2)

            # For each question (row)
            for q_idx in range(num_questions):
                # Calculate the y-coordinate for this question
                q_y = (q_idx + 1) * question_spacing

                # Check each option
                max_density = 0
                marked_option = -1

                for opt_idx in range(5):  # 5 options (A-E)
                    # Calculate the x-coordinate for this option
                    opt_x = option_start_x + opt_idx * option_spacing

                    # Define a region of interest around this option
                    roi_size = 20  # Size of the region to check
                    roi_y_start = max(0, q_y - roi_size//2)
                    roi_y_end = min(height, q_y + roi_size//2)
                    roi_x_start = max(0, opt_x - roi_size//2)
                    roi_x_end = min(width, opt_x + roi_size//2)

                    # Extract the ROI
                    roi = binary[roi_y_start:roi_y_end, roi_x_start:roi_x_end]

                    # Calculate the density of black pixels in this ROI
                    if roi.size > 0:  # Ensure ROI is not empty
                        black_pixel_count = cv2.countNonZero(roi)
                        density = black_pixel_count / roi.size

                        if density > max_density and density > 0.2:  # Threshold for marking
                            max_density = density
                            marked_option = opt_idx

                # If we found a marked option, record the answer
                if marked_option >= 0:
                    answers[q_idx + 1] = options[marked_option]

            return answers

        except Exception as e:
            print(f"Error in grid-based detection: {e}")
            return {}



# Create an instance of the service
tesseract_ocr_service = TesseractOCRService()
