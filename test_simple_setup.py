#!/usr/bin/env python3
"""
Simple test script for Auto-Grade Scribe
Tests basic functionality with simplified models
"""

import sys
import os
from pathlib import Path

def test_basic_imports():
    """Test basic Python imports"""
    print("🧪 Testing basic imports...")

    try:
        import fastapi
        import uvicorn
        import sqlalchemy
        import pydantic
        print("  ✅ Core web framework imports successful")
        return True
    except ImportError as e:
        print(f"  ❌ Core imports failed: {e}")
        return False

def test_database_simple():
    """Test simple database setup"""
    print("\n🗄️ Testing simple database setup...")

    try:
        # Add backend to path
        backend_path = Path(__file__).parent / "backend"
        sys.path.insert(0, str(backend_path))

        from database import engine, Base

        # Test connection
        connection = engine.connect()
        connection.close()
        print("  ✅ Database connection successful")

        # Import simplified models
        from models_simple import User, Exam, Student, ExamResult
        print("  ✅ Simplified models imported")

        # Create tables
        Base.metadata.create_all(bind=engine)
        print("  ✅ Database tables created")

        return True

    except Exception as e:
        print(f"  ❌ Database test failed: {e}")
        return False

def test_simple_api():
    """Test simple API creation"""
    print("\n🌐 Testing simple API...")

    try:
        from fastapi import FastAPI

        app = FastAPI(title="Auto Grade Scribe Test", version="1.0.0")

        @app.get("/")
        async def root():
            return {"message": "Auto Grade Scribe API is running"}

        @app.get("/health")
        async def health():
            return {"status": "healthy"}

        print("  ✅ Simple API created successfully")
        return True

    except Exception as e:
        print(f"  ❌ API test failed: {e}")
        return False

def test_directories():
    """Test directory creation"""
    print("\n📁 Testing directories...")

    required_dirs = ["uploads", "results", "temp", "logs"]

    for dir_name in required_dirs:
        dir_path = Path(dir_name)
        if not dir_path.exists():
            try:
                dir_path.mkdir(exist_ok=True)
                print(f"  ✅ Created {dir_name} directory")
            except Exception as e:
                print(f"  ❌ Failed to create {dir_name}: {e}")
                return False
        else:
            print(f"  ✅ {dir_name} directory exists")

    return True

def create_simple_env():
    """Create a simple .env file"""
    print("\n⚙️ Creating simple configuration...")

    env_content = """# Simple configuration for Auto-Grade Scribe
ENVIRONMENT=development
DEBUG=true
DATABASE_URL=sqlite:///./gradegeniusdb.db
SECRET_KEY=simple-secret-key-for-testing
API_HOST=0.0.0.0
API_PORT=8000
"""

    try:
        with open(".env", "w") as f:
            f.write(env_content)
        print("  ✅ Simple .env file created")
        return True
    except Exception as e:
        print(f"  ❌ Failed to create .env: {e}")
        return False

def create_simple_app():
    """Create a simple working app.py"""
    print("\n🚀 Creating simple application...")

    app_content = '''"""
Simple Auto-Grade Scribe Application
Minimal working version for testing
"""

import os
import sys
from pathlib import Path
from fastapi import FastAPI, UploadFile, File, HTTPException
from fastapi.middleware.cors import CORSMiddleware
import uvicorn

# Add current directory to path
sys.path.append(str(Path(__file__).parent))

# Import database
from database import engine, Base
from models_simple import User, Exam, Student, ExamResult

# Create FastAPI app
app = FastAPI(
    title="Auto Grade Scribe - Simple",
    description="Simplified version for testing",
    version="1.0.0"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Create tables on startup
@app.on_event("startup")
async def startup_event():
    Base.metadata.create_all(bind=engine)
    print("Database tables created successfully")

# Basic routes
@app.get("/")
async def root():
    return {
        "message": "Auto Grade Scribe Simple API is running",
        "version": "1.0.0",
        "status": "healthy"
    }

@app.get("/health")
async def health_check():
    try:
        # Test database connection
        connection = engine.connect()
        connection.close()
        return {
            "status": "healthy",
            "database": "connected",
            "timestamp": "2024-01-01T00:00:00Z"
        }
    except Exception as e:
        return {
            "status": "unhealthy",
            "database": "disconnected",
            "error": str(e)
        }

@app.post("/api/upload")
async def upload_file(file: UploadFile = File(...)):
    """Simple file upload endpoint"""
    try:
        # Create uploads directory if it doesn't exist
        uploads_dir = Path("uploads")
        uploads_dir.mkdir(exist_ok=True)

        # Save file
        file_path = uploads_dir / file.filename
        with open(file_path, "wb") as buffer:
            content = await file.read()
            buffer.write(content)

        return {
            "success": True,
            "filename": file.filename,
            "size": len(content),
            "message": "File uploaded successfully"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

if __name__ == "__main__":
    uvicorn.run("simple_app:app", host="0.0.0.0", port=8000, reload=True)
'''

    try:
        with open("backend/simple_app.py", "w") as f:
            f.write(app_content)
        print("  ✅ Simple application created")
        return True
    except Exception as e:
        print(f"  ❌ Failed to create simple app: {e}")
        return False

def main():
    """Run simple setup test"""
    print("🚀 Auto-Grade Scribe Simple Setup Test")
    print("=" * 50)

    tests = [
        ("Basic Imports", test_basic_imports),
        ("Database Setup", test_database_simple),
        ("API Creation", test_simple_api),
        ("Directory Structure", test_directories),
        ("Configuration", create_simple_env),
        ("Simple Application", create_simple_app)
    ]

    passed = 0
    total = len(tests)

    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"  ❌ {test_name} test crashed: {e}")

    print("\n" + "=" * 50)
    print(f"📊 Test Results: {passed}/{total} tests passed")

    if passed == total:
        print("🎉 Simple setup completed successfully!")
        print("\n🚀 To start the simple application:")
        print("   cd backend")
        print("   python simple_app.py")
        print("\n📚 Then visit:")
        print("   - API: http://localhost:8000")
        print("   - Health: http://localhost:8000/health")
        print("   - Upload: http://localhost:8000/api/upload")
    else:
        print("⚠️ Some tests failed. Please install missing dependencies:")
        print("   pip install fastapi uvicorn sqlalchemy pydantic python-dotenv")

    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
