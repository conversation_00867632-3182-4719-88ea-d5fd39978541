#pragma once

// @generated by torchgen/gen.py from Operators.h

#ifdef TORCH_ASSERT_NO_OPERATORS
#error This change adds a dependency on native_functions.yaml,             \
  meaning the file will need to be re-compiled every time an operator      \
  is changed or added. Consider if your change would be better placed in   \
  another file, or if a more specific header might achieve the same goal.  \
  See NOTE: [Tensor vs. TensorBase]
#endif

#if defined(AT_PER_OPERATOR_HEADERS) && defined(TORCH_ASSERT_ONLY_METHOD_OPERATORS)
#error This change adds a dependency on all pytorch operators, meaning the     \
  file will need to be re-compiled every time an operator is changed or added. \
  Consider including a specific operator from <ATen/ops/{my_operator}_ops.h>   \
  and see NOTE [TORCH_ASSERT_ONLY_METHOD_OPERATORS].
#endif

#include <c10/core/SymInt.h>
#include <c10/core/SymIntArrayRef.h>
#include <c10/core/Scalar.h>
#include <c10/core/TensorOptions.h>
#include <c10/core/QScheme.h>
#include <c10/util/OptionalArrayRef.h>
#include <tuple>
#include <vector>

#include <ATen/ops/_adaptive_avg_pool2d_ops.h>
#include <ATen/ops/_adaptive_avg_pool2d_backward_ops.h>
#include <ATen/ops/_adaptive_avg_pool3d_ops.h>
#include <ATen/ops/_adaptive_avg_pool3d_backward_ops.h>
#include <ATen/ops/_add_batch_dim_ops.h>
#include <ATen/ops/_add_relu_ops.h>
#include <ATen/ops/_addmm_activation_ops.h>
#include <ATen/ops/_aminmax_ops.h>
#include <ATen/ops/_amp_foreach_non_finite_check_and_unscale_ops.h>
#include <ATen/ops/_amp_update_scale_ops.h>
#include <ATen/ops/_assert_async_ops.h>
#include <ATen/ops/_assert_tensor_metadata_ops.h>
#include <ATen/ops/_autocast_to_full_precision_ops.h>
#include <ATen/ops/_autocast_to_reduced_precision_ops.h>
#include <ATen/ops/_backward_ops.h>
#include <ATen/ops/_batch_norm_impl_index_ops.h>
#include <ATen/ops/_batch_norm_impl_index_backward_ops.h>
#include <ATen/ops/_cast_Byte_ops.h>
#include <ATen/ops/_cast_Char_ops.h>
#include <ATen/ops/_cast_Double_ops.h>
#include <ATen/ops/_cast_Float_ops.h>
#include <ATen/ops/_cast_Half_ops.h>
#include <ATen/ops/_cast_Int_ops.h>
#include <ATen/ops/_cast_Long_ops.h>
#include <ATen/ops/_cast_Short_ops.h>
#include <ATen/ops/_cdist_backward_ops.h>
#include <ATen/ops/_cdist_forward_ops.h>
#include <ATen/ops/_cholesky_solve_helper_ops.h>
#include <ATen/ops/_choose_qparams_per_tensor_ops.h>
#include <ATen/ops/_coalesce_ops.h>
#include <ATen/ops/_coalesced_ops.h>
#include <ATen/ops/_compute_linear_combination_ops.h>
#include <ATen/ops/_conj_ops.h>
#include <ATen/ops/_conj_copy_ops.h>
#include <ATen/ops/_conj_physical_ops.h>
#include <ATen/ops/_conv_depthwise2d_ops.h>
#include <ATen/ops/_convert_indices_from_coo_to_csr_ops.h>
#include <ATen/ops/_convert_indices_from_csr_to_coo_ops.h>
#include <ATen/ops/_convolution_ops.h>
#include <ATen/ops/_convolution_double_backward_ops.h>
#include <ATen/ops/_convolution_mode_ops.h>
#include <ATen/ops/_copy_from_ops.h>
#include <ATen/ops/_copy_from_and_resize_ops.h>
#include <ATen/ops/_cslt_compress_ops.h>
#include <ATen/ops/_cslt_sparse_mm_ops.h>
#include <ATen/ops/_ctc_loss_ops.h>
#include <ATen/ops/_ctc_loss_backward_ops.h>
#include <ATen/ops/_cudnn_ctc_loss_ops.h>
#include <ATen/ops/_cudnn_init_dropout_state_ops.h>
#include <ATen/ops/_cudnn_rnn_ops.h>
#include <ATen/ops/_cudnn_rnn_backward_ops.h>
#include <ATen/ops/_cudnn_rnn_flatten_weight_ops.h>
#include <ATen/ops/_cufft_clear_plan_cache_ops.h>
#include <ATen/ops/_cufft_get_plan_cache_max_size_ops.h>
#include <ATen/ops/_cufft_get_plan_cache_size_ops.h>
#include <ATen/ops/_cufft_set_plan_cache_max_size_ops.h>
#include <ATen/ops/_cummax_helper_ops.h>
#include <ATen/ops/_cummin_helper_ops.h>
#include <ATen/ops/_debug_has_internal_overlap_ops.h>
#include <ATen/ops/_dimI_ops.h>
#include <ATen/ops/_dimV_ops.h>
#include <ATen/ops/_dim_arange_ops.h>
#include <ATen/ops/_dirichlet_grad_ops.h>
#include <ATen/ops/_efficient_attention_backward_ops.h>
#include <ATen/ops/_efficient_attention_forward_ops.h>
#include <ATen/ops/_efficientzerotensor_ops.h>
#include <ATen/ops/_embedding_bag_ops.h>
#include <ATen/ops/_embedding_bag_backward_ops.h>
#include <ATen/ops/_embedding_bag_dense_backward_ops.h>
#include <ATen/ops/_embedding_bag_forward_only_ops.h>
#include <ATen/ops/_embedding_bag_per_sample_weights_backward_ops.h>
#include <ATen/ops/_embedding_bag_sparse_backward_ops.h>
#include <ATen/ops/_empty_affine_quantized_ops.h>
#include <ATen/ops/_empty_per_channel_affine_quantized_ops.h>
#include <ATen/ops/_euclidean_dist_ops.h>
#include <ATen/ops/_fake_quantize_learnable_per_channel_affine_ops.h>
#include <ATen/ops/_fake_quantize_learnable_per_channel_affine_backward_ops.h>
#include <ATen/ops/_fake_quantize_learnable_per_tensor_affine_ops.h>
#include <ATen/ops/_fake_quantize_learnable_per_tensor_affine_backward_ops.h>
#include <ATen/ops/_fake_quantize_per_tensor_affine_cachemask_tensor_qparams_ops.h>
#include <ATen/ops/_fft_c2c_ops.h>
#include <ATen/ops/_fft_c2r_ops.h>
#include <ATen/ops/_fft_r2c_ops.h>
#include <ATen/ops/_fill_mem_eff_dropout_mask_ops.h>
#include <ATen/ops/_flash_attention_backward_ops.h>
#include <ATen/ops/_flash_attention_forward_ops.h>
#include <ATen/ops/_foobar_ops.h>
#include <ATen/ops/_foreach_abs_ops.h>
#include <ATen/ops/_foreach_acos_ops.h>
#include <ATen/ops/_foreach_add_ops.h>
#include <ATen/ops/_foreach_addcdiv_ops.h>
#include <ATen/ops/_foreach_addcmul_ops.h>
#include <ATen/ops/_foreach_asin_ops.h>
#include <ATen/ops/_foreach_atan_ops.h>
#include <ATen/ops/_foreach_ceil_ops.h>
#include <ATen/ops/_foreach_clamp_max_ops.h>
#include <ATen/ops/_foreach_clamp_min_ops.h>
#include <ATen/ops/_foreach_copy_ops.h>
#include <ATen/ops/_foreach_cos_ops.h>
#include <ATen/ops/_foreach_cosh_ops.h>
#include <ATen/ops/_foreach_div_ops.h>
#include <ATen/ops/_foreach_erf_ops.h>
#include <ATen/ops/_foreach_erfc_ops.h>
#include <ATen/ops/_foreach_exp_ops.h>
#include <ATen/ops/_foreach_expm1_ops.h>
#include <ATen/ops/_foreach_floor_ops.h>
#include <ATen/ops/_foreach_frac_ops.h>
#include <ATen/ops/_foreach_lerp_ops.h>
#include <ATen/ops/_foreach_lgamma_ops.h>
#include <ATen/ops/_foreach_log_ops.h>
#include <ATen/ops/_foreach_log10_ops.h>
#include <ATen/ops/_foreach_log1p_ops.h>
#include <ATen/ops/_foreach_log2_ops.h>
#include <ATen/ops/_foreach_maximum_ops.h>
#include <ATen/ops/_foreach_minimum_ops.h>
#include <ATen/ops/_foreach_mul_ops.h>
#include <ATen/ops/_foreach_neg_ops.h>
#include <ATen/ops/_foreach_norm_ops.h>
#include <ATen/ops/_foreach_pow_ops.h>
#include <ATen/ops/_foreach_reciprocal_ops.h>
#include <ATen/ops/_foreach_round_ops.h>
#include <ATen/ops/_foreach_sigmoid_ops.h>
#include <ATen/ops/_foreach_sign_ops.h>
#include <ATen/ops/_foreach_sin_ops.h>
#include <ATen/ops/_foreach_sinh_ops.h>
#include <ATen/ops/_foreach_sqrt_ops.h>
#include <ATen/ops/_foreach_sub_ops.h>
#include <ATen/ops/_foreach_tan_ops.h>
#include <ATen/ops/_foreach_tanh_ops.h>
#include <ATen/ops/_foreach_trunc_ops.h>
#include <ATen/ops/_foreach_zero_ops.h>
#include <ATen/ops/_functional_assert_async_ops.h>
#include <ATen/ops/_functional_sym_constrain_range_ops.h>
#include <ATen/ops/_functional_sym_constrain_range_for_size_ops.h>
#include <ATen/ops/_fused_adam_ops.h>
#include <ATen/ops/_fused_adamw_ops.h>
#include <ATen/ops/_fused_dropout_ops.h>
#include <ATen/ops/_fused_moving_avg_obs_fq_helper_ops.h>
#include <ATen/ops/_fused_sdp_choice_ops.h>
#include <ATen/ops/_fw_primal_ops.h>
#include <ATen/ops/_fw_primal_copy_ops.h>
#include <ATen/ops/_gather_sparse_backward_ops.h>
#include <ATen/ops/_grid_sampler_2d_cpu_fallback_ops.h>
#include <ATen/ops/_grid_sampler_2d_cpu_fallback_backward_ops.h>
#include <ATen/ops/_has_compatible_shallow_copy_type_ops.h>
#include <ATen/ops/_has_same_storage_numel_ops.h>
#include <ATen/ops/_histogramdd_bin_edges_ops.h>
#include <ATen/ops/_histogramdd_from_bin_cts_ops.h>
#include <ATen/ops/_histogramdd_from_bin_tensors_ops.h>
#include <ATen/ops/_index_put_impl_ops.h>
#include <ATen/ops/_indices_ops.h>
#include <ATen/ops/_indices_copy_ops.h>
#include <ATen/ops/_int_mm_ops.h>
#include <ATen/ops/_is_all_true_ops.h>
#include <ATen/ops/_is_any_true_ops.h>
#include <ATen/ops/_is_zerotensor_ops.h>
#include <ATen/ops/_linalg_check_errors_ops.h>
#include <ATen/ops/_linalg_det_ops.h>
#include <ATen/ops/_linalg_eigh_ops.h>
#include <ATen/ops/_linalg_slogdet_ops.h>
#include <ATen/ops/_linalg_solve_ex_ops.h>
#include <ATen/ops/_linalg_svd_ops.h>
#include <ATen/ops/_local_scalar_dense_ops.h>
#include <ATen/ops/_log_softmax_ops.h>
#include <ATen/ops/_log_softmax_backward_data_ops.h>
#include <ATen/ops/_logcumsumexp_ops.h>
#include <ATen/ops/_lstm_mps_ops.h>
#include <ATen/ops/_lu_with_info_ops.h>
#include <ATen/ops/_make_dep_token_ops.h>
#include <ATen/ops/_make_dual_ops.h>
#include <ATen/ops/_make_dual_copy_ops.h>
#include <ATen/ops/_make_per_channel_quantized_tensor_ops.h>
#include <ATen/ops/_make_per_tensor_quantized_tensor_ops.h>
#include <ATen/ops/_masked_scale_ops.h>
#include <ATen/ops/_masked_softmax_ops.h>
#include <ATen/ops/_masked_softmax_backward_ops.h>
#include <ATen/ops/_mkldnn_reshape_ops.h>
#include <ATen/ops/_mkldnn_transpose_ops.h>
#include <ATen/ops/_mps_convolution_ops.h>
#include <ATen/ops/_mps_convolution_transpose_ops.h>
#include <ATen/ops/_native_batch_norm_legit_ops.h>
#include <ATen/ops/_native_batch_norm_legit_no_training_ops.h>
#include <ATen/ops/_native_multi_head_attention_ops.h>
#include <ATen/ops/_neg_view_ops.h>
#include <ATen/ops/_neg_view_copy_ops.h>
#include <ATen/ops/_nested_from_padded_ops.h>
#include <ATen/ops/_nested_from_padded_and_nested_example_ops.h>
#include <ATen/ops/_nested_select_backward_ops.h>
#include <ATen/ops/_nested_sum_backward_ops.h>
#include <ATen/ops/_nested_tensor_from_mask_ops.h>
#include <ATen/ops/_nested_tensor_from_mask_left_aligned_ops.h>
#include <ATen/ops/_nested_tensor_from_tensor_list_ops.h>
#include <ATen/ops/_nested_tensor_size_ops.h>
#include <ATen/ops/_nested_tensor_softmax_with_shape_ops.h>
#include <ATen/ops/_nested_tensor_storage_offsets_ops.h>
#include <ATen/ops/_nested_tensor_strides_ops.h>
#include <ATen/ops/_nested_view_from_buffer_ops.h>
#include <ATen/ops/_nested_view_from_buffer_copy_ops.h>
#include <ATen/ops/_new_zeros_with_same_feature_meta_ops.h>
#include <ATen/ops/_nnpack_available_ops.h>
#include <ATen/ops/_nnpack_spatial_convolution_ops.h>
#include <ATen/ops/_nnz_ops.h>
#include <ATen/ops/_pack_padded_sequence_ops.h>
#include <ATen/ops/_pack_padded_sequence_backward_ops.h>
#include <ATen/ops/_pad_circular_ops.h>
#include <ATen/ops/_pad_enum_ops.h>
#include <ATen/ops/_pad_packed_sequence_ops.h>
#include <ATen/ops/_pdist_backward_ops.h>
#include <ATen/ops/_pdist_forward_ops.h>
#include <ATen/ops/_pin_memory_ops.h>
#include <ATen/ops/_prelu_kernel_ops.h>
#include <ATen/ops/_prelu_kernel_backward_ops.h>
#include <ATen/ops/_propagate_xla_data_ops.h>
#include <ATen/ops/_remove_batch_dim_ops.h>
#include <ATen/ops/_reshape_alias_ops.h>
#include <ATen/ops/_reshape_alias_copy_ops.h>
#include <ATen/ops/_reshape_copy_ops.h>
#include <ATen/ops/_reshape_from_tensor_ops.h>
#include <ATen/ops/_resize_output_ops.h>
#include <ATen/ops/_rowwise_prune_ops.h>
#include <ATen/ops/_sample_dirichlet_ops.h>
#include <ATen/ops/_saturate_weight_to_fp16_ops.h>
#include <ATen/ops/_scaled_dot_product_attention_math_ops.h>
#include <ATen/ops/_scaled_dot_product_efficient_attention_ops.h>
#include <ATen/ops/_scaled_dot_product_efficient_attention_backward_ops.h>
#include <ATen/ops/_scaled_dot_product_flash_attention_ops.h>
#include <ATen/ops/_scaled_dot_product_flash_attention_backward_ops.h>
#include <ATen/ops/_scaled_mm_ops.h>
#include <ATen/ops/_segment_reduce_backward_ops.h>
#include <ATen/ops/_shape_as_tensor_ops.h>
#include <ATen/ops/_slow_conv2d_backward_ops.h>
#include <ATen/ops/_slow_conv2d_forward_ops.h>
#include <ATen/ops/_sobol_engine_draw_ops.h>
#include <ATen/ops/_sobol_engine_ff_ops.h>
#include <ATen/ops/_sobol_engine_initialize_state_ops.h>
#include <ATen/ops/_sobol_engine_scramble_ops.h>
#include <ATen/ops/_softmax_ops.h>
#include <ATen/ops/_softmax_backward_data_ops.h>
#include <ATen/ops/_sparse_addmm_ops.h>
#include <ATen/ops/_sparse_broadcast_to_ops.h>
#include <ATen/ops/_sparse_broadcast_to_copy_ops.h>
#include <ATen/ops/_sparse_bsc_tensor_unsafe_ops.h>
#include <ATen/ops/_sparse_bsr_tensor_unsafe_ops.h>
#include <ATen/ops/_sparse_compressed_tensor_unsafe_ops.h>
#include <ATen/ops/_sparse_coo_tensor_unsafe_ops.h>
#include <ATen/ops/_sparse_coo_tensor_with_dims_ops.h>
#include <ATen/ops/_sparse_coo_tensor_with_dims_and_tensors_ops.h>
#include <ATen/ops/_sparse_csc_tensor_unsafe_ops.h>
#include <ATen/ops/_sparse_csr_prod_ops.h>
#include <ATen/ops/_sparse_csr_sum_ops.h>
#include <ATen/ops/_sparse_csr_tensor_unsafe_ops.h>
#include <ATen/ops/_sparse_log_softmax_ops.h>
#include <ATen/ops/_sparse_log_softmax_backward_data_ops.h>
#include <ATen/ops/_sparse_mask_projection_ops.h>
#include <ATen/ops/_sparse_mm_ops.h>
#include <ATen/ops/_sparse_mm_reduce_impl_ops.h>
#include <ATen/ops/_sparse_mm_reduce_impl_backward_ops.h>
#include <ATen/ops/_sparse_semi_structured_linear_ops.h>
#include <ATen/ops/_sparse_softmax_ops.h>
#include <ATen/ops/_sparse_softmax_backward_data_ops.h>
#include <ATen/ops/_sparse_sparse_matmul_ops.h>
#include <ATen/ops/_sparse_sum_ops.h>
#include <ATen/ops/_sparse_sum_backward_ops.h>
#include <ATen/ops/_spdiags_ops.h>
#include <ATen/ops/_stack_ops.h>
#include <ATen/ops/_standard_gamma_ops.h>
#include <ATen/ops/_standard_gamma_grad_ops.h>
#include <ATen/ops/_test_ambiguous_defaults_ops.h>
#include <ATen/ops/_test_autograd_multiple_dispatch_ops.h>
#include <ATen/ops/_test_autograd_multiple_dispatch_view_ops.h>
#include <ATen/ops/_test_autograd_multiple_dispatch_view_copy_ops.h>
#include <ATen/ops/_test_check_tensor_ops.h>
#include <ATen/ops/_test_functorch_fallback_ops.h>
#include <ATen/ops/_test_optional_filled_intlist_ops.h>
#include <ATen/ops/_test_optional_floatlist_ops.h>
#include <ATen/ops/_test_optional_intlist_ops.h>
#include <ATen/ops/_test_serialization_subcmul_ops.h>
#include <ATen/ops/_test_string_default_ops.h>
#include <ATen/ops/_test_warn_in_autograd_ops.h>
#include <ATen/ops/_thnn_differentiable_gru_cell_backward_ops.h>
#include <ATen/ops/_thnn_differentiable_lstm_cell_backward_ops.h>
#include <ATen/ops/_thnn_fused_gru_cell_ops.h>
#include <ATen/ops/_thnn_fused_gru_cell_backward_ops.h>
#include <ATen/ops/_thnn_fused_lstm_cell_ops.h>
#include <ATen/ops/_thnn_fused_lstm_cell_backward_ops.h>
#include <ATen/ops/_thnn_fused_lstm_cell_backward_impl_ops.h>
#include <ATen/ops/_to_copy_ops.h>
#include <ATen/ops/_to_cpu_ops.h>
#include <ATen/ops/_to_dense_ops.h>
#include <ATen/ops/_to_sparse_ops.h>
#include <ATen/ops/_to_sparse_bsc_ops.h>
#include <ATen/ops/_to_sparse_bsr_ops.h>
#include <ATen/ops/_to_sparse_csc_ops.h>
#include <ATen/ops/_to_sparse_csr_ops.h>
#include <ATen/ops/_to_sparse_semi_structured_ops.h>
#include <ATen/ops/_transform_bias_rescale_qkv_ops.h>
#include <ATen/ops/_transformer_encoder_layer_fwd_ops.h>
#include <ATen/ops/_trilinear_ops.h>
#include <ATen/ops/_triton_multi_head_attention_ops.h>
#include <ATen/ops/_triton_scaled_dot_attention_ops.h>
#include <ATen/ops/_unique_ops.h>
#include <ATen/ops/_unique2_ops.h>
#include <ATen/ops/_unpack_dual_ops.h>
#include <ATen/ops/_unsafe_index_ops.h>
#include <ATen/ops/_unsafe_index_put_ops.h>
#include <ATen/ops/_unsafe_view_ops.h>
#include <ATen/ops/_upsample_bicubic2d_aa_ops.h>
#include <ATen/ops/_upsample_bicubic2d_aa_backward_ops.h>
#include <ATen/ops/_upsample_bilinear2d_aa_ops.h>
#include <ATen/ops/_upsample_bilinear2d_aa_backward_ops.h>
#include <ATen/ops/_upsample_nearest_exact1d_ops.h>
#include <ATen/ops/_upsample_nearest_exact1d_backward_ops.h>
#include <ATen/ops/_upsample_nearest_exact2d_ops.h>
#include <ATen/ops/_upsample_nearest_exact2d_backward_ops.h>
#include <ATen/ops/_upsample_nearest_exact3d_ops.h>
#include <ATen/ops/_upsample_nearest_exact3d_backward_ops.h>
#include <ATen/ops/_use_cudnn_ctc_loss_ops.h>
#include <ATen/ops/_use_cudnn_rnn_flatten_weight_ops.h>
#include <ATen/ops/_validate_compressed_sparse_indices_ops.h>
#include <ATen/ops/_validate_sparse_bsc_tensor_args_ops.h>
#include <ATen/ops/_validate_sparse_bsr_tensor_args_ops.h>
#include <ATen/ops/_validate_sparse_compressed_tensor_args_ops.h>
#include <ATen/ops/_validate_sparse_coo_tensor_args_ops.h>
#include <ATen/ops/_validate_sparse_csc_tensor_args_ops.h>
#include <ATen/ops/_validate_sparse_csr_tensor_args_ops.h>
#include <ATen/ops/_values_ops.h>
#include <ATen/ops/_values_copy_ops.h>
#include <ATen/ops/_version_ops.h>
#include <ATen/ops/_weight_norm_ops.h>
#include <ATen/ops/_weight_norm_differentiable_backward_ops.h>
#include <ATen/ops/_weight_norm_interface_ops.h>
#include <ATen/ops/_weight_norm_interface_backward_ops.h>
#include <ATen/ops/abs_ops.h>
#include <ATen/ops/absolute_ops.h>
#include <ATen/ops/acos_ops.h>
#include <ATen/ops/acosh_ops.h>
#include <ATen/ops/adaptive_avg_pool1d_ops.h>
#include <ATen/ops/adaptive_avg_pool2d_ops.h>
#include <ATen/ops/adaptive_avg_pool3d_ops.h>
#include <ATen/ops/adaptive_avg_pool3d_backward_ops.h>
#include <ATen/ops/adaptive_max_pool1d_ops.h>
#include <ATen/ops/adaptive_max_pool2d_ops.h>
#include <ATen/ops/adaptive_max_pool2d_backward_ops.h>
#include <ATen/ops/adaptive_max_pool3d_ops.h>
#include <ATen/ops/adaptive_max_pool3d_backward_ops.h>
#include <ATen/ops/add_ops.h>
#include <ATen/ops/addbmm_ops.h>
#include <ATen/ops/addcdiv_ops.h>
#include <ATen/ops/addcmul_ops.h>
#include <ATen/ops/addmm_ops.h>
#include <ATen/ops/addmv_ops.h>
#include <ATen/ops/addr_ops.h>
#include <ATen/ops/adjoint_ops.h>
#include <ATen/ops/affine_grid_generator_ops.h>
#include <ATen/ops/affine_grid_generator_backward_ops.h>
#include <ATen/ops/alias_ops.h>
#include <ATen/ops/alias_copy_ops.h>
#include <ATen/ops/align_as_ops.h>
#include <ATen/ops/align_tensors_ops.h>
#include <ATen/ops/align_to_ops.h>
#include <ATen/ops/all_ops.h>
#include <ATen/ops/allclose_ops.h>
#include <ATen/ops/alpha_dropout_ops.h>
#include <ATen/ops/amax_ops.h>
#include <ATen/ops/amin_ops.h>
#include <ATen/ops/aminmax_ops.h>
#include <ATen/ops/and_ops.h>
#include <ATen/ops/angle_ops.h>
#include <ATen/ops/any_ops.h>
#include <ATen/ops/arange_ops.h>
#include <ATen/ops/arccos_ops.h>
#include <ATen/ops/arccosh_ops.h>
#include <ATen/ops/arcsin_ops.h>
#include <ATen/ops/arcsinh_ops.h>
#include <ATen/ops/arctan_ops.h>
#include <ATen/ops/arctan2_ops.h>
#include <ATen/ops/arctanh_ops.h>
#include <ATen/ops/argmax_ops.h>
#include <ATen/ops/argmin_ops.h>
#include <ATen/ops/argsort_ops.h>
#include <ATen/ops/argwhere_ops.h>
#include <ATen/ops/as_strided_ops.h>
#include <ATen/ops/as_strided_copy_ops.h>
#include <ATen/ops/as_strided_scatter_ops.h>
#include <ATen/ops/asin_ops.h>
#include <ATen/ops/asinh_ops.h>
#include <ATen/ops/atan_ops.h>
#include <ATen/ops/atan2_ops.h>
#include <ATen/ops/atanh_ops.h>
#include <ATen/ops/atleast_1d_ops.h>
#include <ATen/ops/atleast_2d_ops.h>
#include <ATen/ops/atleast_3d_ops.h>
#include <ATen/ops/avg_pool1d_ops.h>
#include <ATen/ops/avg_pool2d_ops.h>
#include <ATen/ops/avg_pool2d_backward_ops.h>
#include <ATen/ops/avg_pool3d_ops.h>
#include <ATen/ops/avg_pool3d_backward_ops.h>
#include <ATen/ops/baddbmm_ops.h>
#include <ATen/ops/bartlett_window_ops.h>
#include <ATen/ops/batch_norm_ops.h>
#include <ATen/ops/batch_norm_backward_elemt_ops.h>
#include <ATen/ops/batch_norm_backward_reduce_ops.h>
#include <ATen/ops/batch_norm_elemt_ops.h>
#include <ATen/ops/batch_norm_gather_stats_ops.h>
#include <ATen/ops/batch_norm_gather_stats_with_counts_ops.h>
#include <ATen/ops/batch_norm_stats_ops.h>
#include <ATen/ops/batch_norm_update_stats_ops.h>
#include <ATen/ops/bernoulli_ops.h>
#include <ATen/ops/bilinear_ops.h>
#include <ATen/ops/binary_cross_entropy_ops.h>
#include <ATen/ops/binary_cross_entropy_backward_ops.h>
#include <ATen/ops/binary_cross_entropy_with_logits_ops.h>
#include <ATen/ops/bincount_ops.h>
#include <ATen/ops/binomial_ops.h>
#include <ATen/ops/bitwise_and_ops.h>
#include <ATen/ops/bitwise_left_shift_ops.h>
#include <ATen/ops/bitwise_not_ops.h>
#include <ATen/ops/bitwise_or_ops.h>
#include <ATen/ops/bitwise_right_shift_ops.h>
#include <ATen/ops/bitwise_xor_ops.h>
#include <ATen/ops/blackman_window_ops.h>
#include <ATen/ops/block_diag_ops.h>
#include <ATen/ops/bmm_ops.h>
#include <ATen/ops/broadcast_tensors_ops.h>
#include <ATen/ops/broadcast_to_ops.h>
#include <ATen/ops/bucketize_ops.h>
#include <ATen/ops/can_cast_ops.h>
#include <ATen/ops/cartesian_prod_ops.h>
#include <ATen/ops/cat_ops.h>
#include <ATen/ops/cauchy_ops.h>
#include <ATen/ops/ccol_indices_ops.h>
#include <ATen/ops/ccol_indices_copy_ops.h>
#include <ATen/ops/cdist_ops.h>
#include <ATen/ops/ceil_ops.h>
#include <ATen/ops/celu_ops.h>
#include <ATen/ops/chain_matmul_ops.h>
#include <ATen/ops/chalf_ops.h>
#include <ATen/ops/channel_shuffle_ops.h>
#include <ATen/ops/cholesky_ops.h>
#include <ATen/ops/cholesky_inverse_ops.h>
#include <ATen/ops/cholesky_solve_ops.h>
#include <ATen/ops/choose_qparams_optimized_ops.h>
#include <ATen/ops/chunk_ops.h>
#include <ATen/ops/clamp_ops.h>
#include <ATen/ops/clamp_max_ops.h>
#include <ATen/ops/clamp_min_ops.h>
#include <ATen/ops/clip_ops.h>
#include <ATen/ops/clone_ops.h>
#include <ATen/ops/coalesce_ops.h>
#include <ATen/ops/col2im_ops.h>
#include <ATen/ops/col_indices_ops.h>
#include <ATen/ops/col_indices_copy_ops.h>
#include <ATen/ops/column_stack_ops.h>
#include <ATen/ops/combinations_ops.h>
#include <ATen/ops/complex_ops.h>
#include <ATen/ops/concat_ops.h>
#include <ATen/ops/concatenate_ops.h>
#include <ATen/ops/conj_ops.h>
#include <ATen/ops/conj_physical_ops.h>
#include <ATen/ops/constant_pad_nd_ops.h>
#include <ATen/ops/contiguous_ops.h>
#include <ATen/ops/conv1d_ops.h>
#include <ATen/ops/conv2d_ops.h>
#include <ATen/ops/conv3d_ops.h>
#include <ATen/ops/conv_depthwise3d_ops.h>
#include <ATen/ops/conv_tbc_ops.h>
#include <ATen/ops/conv_tbc_backward_ops.h>
#include <ATen/ops/conv_transpose1d_ops.h>
#include <ATen/ops/conv_transpose2d_ops.h>
#include <ATen/ops/conv_transpose3d_ops.h>
#include <ATen/ops/convolution_ops.h>
#include <ATen/ops/convolution_backward_ops.h>
#include <ATen/ops/convolution_backward_overrideable_ops.h>
#include <ATen/ops/convolution_overrideable_ops.h>
#include <ATen/ops/copy_ops.h>
#include <ATen/ops/copy_sparse_to_sparse_ops.h>
#include <ATen/ops/copysign_ops.h>
#include <ATen/ops/corrcoef_ops.h>
#include <ATen/ops/cos_ops.h>
#include <ATen/ops/cosh_ops.h>
#include <ATen/ops/cosine_embedding_loss_ops.h>
#include <ATen/ops/cosine_similarity_ops.h>
#include <ATen/ops/count_nonzero_ops.h>
#include <ATen/ops/cov_ops.h>
#include <ATen/ops/cross_ops.h>
#include <ATen/ops/cross_entropy_loss_ops.h>
#include <ATen/ops/crow_indices_ops.h>
#include <ATen/ops/crow_indices_copy_ops.h>
#include <ATen/ops/ctc_loss_ops.h>
#include <ATen/ops/cudnn_affine_grid_generator_ops.h>
#include <ATen/ops/cudnn_affine_grid_generator_backward_ops.h>
#include <ATen/ops/cudnn_batch_norm_ops.h>
#include <ATen/ops/cudnn_batch_norm_backward_ops.h>
#include <ATen/ops/cudnn_convolution_ops.h>
#include <ATen/ops/cudnn_convolution_add_relu_ops.h>
#include <ATen/ops/cudnn_convolution_relu_ops.h>
#include <ATen/ops/cudnn_convolution_transpose_ops.h>
#include <ATen/ops/cudnn_grid_sampler_ops.h>
#include <ATen/ops/cudnn_grid_sampler_backward_ops.h>
#include <ATen/ops/cudnn_is_acceptable_ops.h>
#include <ATen/ops/cummax_ops.h>
#include <ATen/ops/cummaxmin_backward_ops.h>
#include <ATen/ops/cummin_ops.h>
#include <ATen/ops/cumprod_ops.h>
#include <ATen/ops/cumprod_backward_ops.h>
#include <ATen/ops/cumsum_ops.h>
#include <ATen/ops/cumulative_trapezoid_ops.h>
#include <ATen/ops/data_ops.h>
#include <ATen/ops/deg2rad_ops.h>
#include <ATen/ops/dense_dim_ops.h>
#include <ATen/ops/dequantize_ops.h>
#include <ATen/ops/det_ops.h>
#include <ATen/ops/detach_ops.h>
#include <ATen/ops/detach_copy_ops.h>
#include <ATen/ops/diag_ops.h>
#include <ATen/ops/diag_embed_ops.h>
#include <ATen/ops/diagflat_ops.h>
#include <ATen/ops/diagonal_ops.h>
#include <ATen/ops/diagonal_backward_ops.h>
#include <ATen/ops/diagonal_copy_ops.h>
#include <ATen/ops/diagonal_scatter_ops.h>
#include <ATen/ops/diff_ops.h>
#include <ATen/ops/digamma_ops.h>
#include <ATen/ops/dist_ops.h>
#include <ATen/ops/div_ops.h>
#include <ATen/ops/divide_ops.h>
#include <ATen/ops/dot_ops.h>
#include <ATen/ops/dropout_ops.h>
#include <ATen/ops/dsplit_ops.h>
#include <ATen/ops/dstack_ops.h>
#include <ATen/ops/einsum_ops.h>
#include <ATen/ops/elu_ops.h>
#include <ATen/ops/elu_backward_ops.h>
#include <ATen/ops/embedding_ops.h>
#include <ATen/ops/embedding_backward_ops.h>
#include <ATen/ops/embedding_bag_ops.h>
#include <ATen/ops/embedding_dense_backward_ops.h>
#include <ATen/ops/embedding_renorm_ops.h>
#include <ATen/ops/embedding_sparse_backward_ops.h>
#include <ATen/ops/empty_ops.h>
#include <ATen/ops/empty_like_ops.h>
#include <ATen/ops/empty_permuted_ops.h>
#include <ATen/ops/empty_quantized_ops.h>
#include <ATen/ops/empty_strided_ops.h>
#include <ATen/ops/eq_ops.h>
#include <ATen/ops/equal_ops.h>
#include <ATen/ops/erf_ops.h>
#include <ATen/ops/erfc_ops.h>
#include <ATen/ops/erfinv_ops.h>
#include <ATen/ops/exp_ops.h>
#include <ATen/ops/exp2_ops.h>
#include <ATen/ops/expand_ops.h>
#include <ATen/ops/expand_as_ops.h>
#include <ATen/ops/expand_copy_ops.h>
#include <ATen/ops/expm1_ops.h>
#include <ATen/ops/exponential_ops.h>
#include <ATen/ops/eye_ops.h>
#include <ATen/ops/fake_quantize_per_channel_affine_ops.h>
#include <ATen/ops/fake_quantize_per_channel_affine_cachemask_ops.h>
#include <ATen/ops/fake_quantize_per_channel_affine_cachemask_backward_ops.h>
#include <ATen/ops/fake_quantize_per_tensor_affine_ops.h>
#include <ATen/ops/fake_quantize_per_tensor_affine_cachemask_ops.h>
#include <ATen/ops/fake_quantize_per_tensor_affine_cachemask_backward_ops.h>
#include <ATen/ops/fbgemm_linear_fp16_weight_ops.h>
#include <ATen/ops/fbgemm_linear_fp16_weight_fp32_activation_ops.h>
#include <ATen/ops/fbgemm_linear_int8_weight_ops.h>
#include <ATen/ops/fbgemm_linear_int8_weight_fp32_activation_ops.h>
#include <ATen/ops/fbgemm_linear_quantize_weight_ops.h>
#include <ATen/ops/fbgemm_pack_gemm_matrix_fp16_ops.h>
#include <ATen/ops/fbgemm_pack_quantized_matrix_ops.h>
#include <ATen/ops/feature_alpha_dropout_ops.h>
#include <ATen/ops/feature_dropout_ops.h>
#include <ATen/ops/fft_fft_ops.h>
#include <ATen/ops/fft_fft2_ops.h>
#include <ATen/ops/fft_fftfreq_ops.h>
#include <ATen/ops/fft_fftn_ops.h>
#include <ATen/ops/fft_fftshift_ops.h>
#include <ATen/ops/fft_hfft_ops.h>
#include <ATen/ops/fft_hfft2_ops.h>
#include <ATen/ops/fft_hfftn_ops.h>
#include <ATen/ops/fft_ifft_ops.h>
#include <ATen/ops/fft_ifft2_ops.h>
#include <ATen/ops/fft_ifftn_ops.h>
#include <ATen/ops/fft_ifftshift_ops.h>
#include <ATen/ops/fft_ihfft_ops.h>
#include <ATen/ops/fft_ihfft2_ops.h>
#include <ATen/ops/fft_ihfftn_ops.h>
#include <ATen/ops/fft_irfft_ops.h>
#include <ATen/ops/fft_irfft2_ops.h>
#include <ATen/ops/fft_irfftn_ops.h>
#include <ATen/ops/fft_rfft_ops.h>
#include <ATen/ops/fft_rfft2_ops.h>
#include <ATen/ops/fft_rfftfreq_ops.h>
#include <ATen/ops/fft_rfftn_ops.h>
#include <ATen/ops/fill_ops.h>
#include <ATen/ops/fill_diagonal_ops.h>
#include <ATen/ops/fix_ops.h>
#include <ATen/ops/flatten_ops.h>
#include <ATen/ops/flatten_dense_tensors_ops.h>
#include <ATen/ops/flip_ops.h>
#include <ATen/ops/fliplr_ops.h>
#include <ATen/ops/flipud_ops.h>
#include <ATen/ops/float_power_ops.h>
#include <ATen/ops/floor_ops.h>
#include <ATen/ops/floor_divide_ops.h>
#include <ATen/ops/fmax_ops.h>
#include <ATen/ops/fmin_ops.h>
#include <ATen/ops/fmod_ops.h>
#include <ATen/ops/frac_ops.h>
#include <ATen/ops/fractional_max_pool2d_ops.h>
#include <ATen/ops/fractional_max_pool2d_backward_ops.h>
#include <ATen/ops/fractional_max_pool3d_ops.h>
#include <ATen/ops/fractional_max_pool3d_backward_ops.h>
#include <ATen/ops/frexp_ops.h>
#include <ATen/ops/frobenius_norm_ops.h>
#include <ATen/ops/from_file_ops.h>
#include <ATen/ops/full_ops.h>
#include <ATen/ops/full_like_ops.h>
#include <ATen/ops/fused_moving_avg_obs_fake_quant_ops.h>
#include <ATen/ops/gather_ops.h>
#include <ATen/ops/gather_backward_ops.h>
#include <ATen/ops/gcd_ops.h>
#include <ATen/ops/ge_ops.h>
#include <ATen/ops/gelu_ops.h>
#include <ATen/ops/gelu_backward_ops.h>
#include <ATen/ops/geometric_ops.h>
#include <ATen/ops/geqrf_ops.h>
#include <ATen/ops/ger_ops.h>
#include <ATen/ops/glu_ops.h>
#include <ATen/ops/glu_backward_ops.h>
#include <ATen/ops/glu_backward_jvp_ops.h>
#include <ATen/ops/glu_jvp_ops.h>
#include <ATen/ops/gradient_ops.h>
#include <ATen/ops/greater_ops.h>
#include <ATen/ops/greater_equal_ops.h>
#include <ATen/ops/grid_sampler_ops.h>
#include <ATen/ops/grid_sampler_2d_ops.h>
#include <ATen/ops/grid_sampler_2d_backward_ops.h>
#include <ATen/ops/grid_sampler_3d_ops.h>
#include <ATen/ops/grid_sampler_3d_backward_ops.h>
#include <ATen/ops/group_norm_ops.h>
#include <ATen/ops/gru_ops.h>
#include <ATen/ops/gru_cell_ops.h>
#include <ATen/ops/gt_ops.h>
#include <ATen/ops/hamming_window_ops.h>
#include <ATen/ops/hann_window_ops.h>
#include <ATen/ops/hardshrink_ops.h>
#include <ATen/ops/hardshrink_backward_ops.h>
#include <ATen/ops/hardsigmoid_ops.h>
#include <ATen/ops/hardsigmoid_backward_ops.h>
#include <ATen/ops/hardswish_ops.h>
#include <ATen/ops/hardswish_backward_ops.h>
#include <ATen/ops/hardtanh_ops.h>
#include <ATen/ops/hardtanh_backward_ops.h>
#include <ATen/ops/heaviside_ops.h>
#include <ATen/ops/hinge_embedding_loss_ops.h>
#include <ATen/ops/histc_ops.h>
#include <ATen/ops/histogram_ops.h>
#include <ATen/ops/histogramdd_ops.h>
#include <ATen/ops/hsplit_ops.h>
#include <ATen/ops/hspmm_ops.h>
#include <ATen/ops/hstack_ops.h>
#include <ATen/ops/huber_loss_ops.h>
#include <ATen/ops/huber_loss_backward_ops.h>
#include <ATen/ops/hypot_ops.h>
#include <ATen/ops/i0_ops.h>
#include <ATen/ops/igamma_ops.h>
#include <ATen/ops/igammac_ops.h>
#include <ATen/ops/im2col_ops.h>
#include <ATen/ops/imag_ops.h>
#include <ATen/ops/index_ops.h>
#include <ATen/ops/index_add_ops.h>
#include <ATen/ops/index_copy_ops.h>
#include <ATen/ops/index_fill_ops.h>
#include <ATen/ops/index_put_ops.h>
#include <ATen/ops/index_reduce_ops.h>
#include <ATen/ops/index_select_ops.h>
#include <ATen/ops/index_select_backward_ops.h>
#include <ATen/ops/indices_ops.h>
#include <ATen/ops/indices_copy_ops.h>
#include <ATen/ops/infinitely_differentiable_gelu_backward_ops.h>
#include <ATen/ops/inner_ops.h>
#include <ATen/ops/instance_norm_ops.h>
#include <ATen/ops/int_repr_ops.h>
#include <ATen/ops/inverse_ops.h>
#include <ATen/ops/is_coalesced_ops.h>
#include <ATen/ops/is_complex_ops.h>
#include <ATen/ops/is_conj_ops.h>
#include <ATen/ops/is_distributed_ops.h>
#include <ATen/ops/is_floating_point_ops.h>
#include <ATen/ops/is_inference_ops.h>
#include <ATen/ops/is_leaf_ops.h>
#include <ATen/ops/is_neg_ops.h>
#include <ATen/ops/is_nonzero_ops.h>
#include <ATen/ops/is_pinned_ops.h>
#include <ATen/ops/is_same_size_ops.h>
#include <ATen/ops/is_set_to_ops.h>
#include <ATen/ops/is_signed_ops.h>
#include <ATen/ops/is_vulkan_available_ops.h>
#include <ATen/ops/isclose_ops.h>
#include <ATen/ops/isfinite_ops.h>
#include <ATen/ops/isin_ops.h>
#include <ATen/ops/isinf_ops.h>
#include <ATen/ops/isnan_ops.h>
#include <ATen/ops/isneginf_ops.h>
#include <ATen/ops/isposinf_ops.h>
#include <ATen/ops/isreal_ops.h>
#include <ATen/ops/istft_ops.h>
#include <ATen/ops/item_ops.h>
#include <ATen/ops/kaiser_window_ops.h>
#include <ATen/ops/kl_div_ops.h>
#include <ATen/ops/kron_ops.h>
#include <ATen/ops/kthvalue_ops.h>
#include <ATen/ops/l1_loss_ops.h>
#include <ATen/ops/layer_norm_ops.h>
#include <ATen/ops/lcm_ops.h>
#include <ATen/ops/ldexp_ops.h>
#include <ATen/ops/le_ops.h>
#include <ATen/ops/leaky_relu_ops.h>
#include <ATen/ops/leaky_relu_backward_ops.h>
#include <ATen/ops/lerp_ops.h>
#include <ATen/ops/less_ops.h>
#include <ATen/ops/less_equal_ops.h>
#include <ATen/ops/lgamma_ops.h>
#include <ATen/ops/lift_ops.h>
#include <ATen/ops/lift_fresh_ops.h>
#include <ATen/ops/lift_fresh_copy_ops.h>
#include <ATen/ops/linalg_cholesky_ops.h>
#include <ATen/ops/linalg_cholesky_ex_ops.h>
#include <ATen/ops/linalg_cond_ops.h>
#include <ATen/ops/linalg_cross_ops.h>
#include <ATen/ops/linalg_det_ops.h>
#include <ATen/ops/linalg_diagonal_ops.h>
#include <ATen/ops/linalg_eig_ops.h>
#include <ATen/ops/linalg_eigh_ops.h>
#include <ATen/ops/linalg_eigvals_ops.h>
#include <ATen/ops/linalg_eigvalsh_ops.h>
#include <ATen/ops/linalg_householder_product_ops.h>
#include <ATen/ops/linalg_inv_ops.h>
#include <ATen/ops/linalg_inv_ex_ops.h>
#include <ATen/ops/linalg_ldl_factor_ops.h>
#include <ATen/ops/linalg_ldl_factor_ex_ops.h>
#include <ATen/ops/linalg_ldl_solve_ops.h>
#include <ATen/ops/linalg_lstsq_ops.h>
#include <ATen/ops/linalg_lu_ops.h>
#include <ATen/ops/linalg_lu_factor_ops.h>
#include <ATen/ops/linalg_lu_factor_ex_ops.h>
#include <ATen/ops/linalg_lu_solve_ops.h>
#include <ATen/ops/linalg_matmul_ops.h>
#include <ATen/ops/linalg_matrix_exp_ops.h>
#include <ATen/ops/linalg_matrix_norm_ops.h>
#include <ATen/ops/linalg_matrix_power_ops.h>
#include <ATen/ops/linalg_matrix_rank_ops.h>
#include <ATen/ops/linalg_multi_dot_ops.h>
#include <ATen/ops/linalg_norm_ops.h>
#include <ATen/ops/linalg_pinv_ops.h>
#include <ATen/ops/linalg_qr_ops.h>
#include <ATen/ops/linalg_slogdet_ops.h>
#include <ATen/ops/linalg_solve_ops.h>
#include <ATen/ops/linalg_solve_ex_ops.h>
#include <ATen/ops/linalg_solve_triangular_ops.h>
#include <ATen/ops/linalg_svd_ops.h>
#include <ATen/ops/linalg_svdvals_ops.h>
#include <ATen/ops/linalg_tensorinv_ops.h>
#include <ATen/ops/linalg_tensorsolve_ops.h>
#include <ATen/ops/linalg_vander_ops.h>
#include <ATen/ops/linalg_vecdot_ops.h>
#include <ATen/ops/linalg_vector_norm_ops.h>
#include <ATen/ops/linear_ops.h>
#include <ATen/ops/linear_backward_ops.h>
#include <ATen/ops/linspace_ops.h>
#include <ATen/ops/log_ops.h>
#include <ATen/ops/log10_ops.h>
#include <ATen/ops/log1p_ops.h>
#include <ATen/ops/log2_ops.h>
#include <ATen/ops/log_normal_ops.h>
#include <ATen/ops/log_sigmoid_ops.h>
#include <ATen/ops/log_sigmoid_backward_ops.h>
#include <ATen/ops/log_sigmoid_forward_ops.h>
#include <ATen/ops/log_softmax_ops.h>
#include <ATen/ops/logaddexp_ops.h>
#include <ATen/ops/logaddexp2_ops.h>
#include <ATen/ops/logcumsumexp_ops.h>
#include <ATen/ops/logdet_ops.h>
#include <ATen/ops/logical_and_ops.h>
#include <ATen/ops/logical_not_ops.h>
#include <ATen/ops/logical_or_ops.h>
#include <ATen/ops/logical_xor_ops.h>
#include <ATen/ops/logit_ops.h>
#include <ATen/ops/logit_backward_ops.h>
#include <ATen/ops/logspace_ops.h>
#include <ATen/ops/logsumexp_ops.h>
#include <ATen/ops/lshift_ops.h>
#include <ATen/ops/lstm_ops.h>
#include <ATen/ops/lstm_cell_ops.h>
#include <ATen/ops/lstm_mps_backward_ops.h>
#include <ATen/ops/lt_ops.h>
#include <ATen/ops/lu_solve_ops.h>
#include <ATen/ops/lu_unpack_ops.h>
#include <ATen/ops/mH_ops.h>
#include <ATen/ops/mT_ops.h>
#include <ATen/ops/margin_ranking_loss_ops.h>
#include <ATen/ops/masked_fill_ops.h>
#include <ATen/ops/masked_scatter_ops.h>
#include <ATen/ops/masked_select_ops.h>
#include <ATen/ops/masked_select_backward_ops.h>
#include <ATen/ops/matmul_ops.h>
#include <ATen/ops/matmul_backward_ops.h>
#include <ATen/ops/matrix_H_ops.h>
#include <ATen/ops/matrix_exp_ops.h>
#include <ATen/ops/matrix_exp_backward_ops.h>
#include <ATen/ops/matrix_power_ops.h>
#include <ATen/ops/max_ops.h>
#include <ATen/ops/max_pool1d_ops.h>
#include <ATen/ops/max_pool1d_with_indices_ops.h>
#include <ATen/ops/max_pool2d_ops.h>
#include <ATen/ops/max_pool2d_backward_ops.h>
#include <ATen/ops/max_pool2d_with_indices_ops.h>
#include <ATen/ops/max_pool2d_with_indices_backward_ops.h>
#include <ATen/ops/max_pool3d_ops.h>
#include <ATen/ops/max_pool3d_with_indices_ops.h>
#include <ATen/ops/max_pool3d_with_indices_backward_ops.h>
#include <ATen/ops/max_unpool2d_ops.h>
#include <ATen/ops/max_unpool3d_ops.h>
#include <ATen/ops/maximum_ops.h>
#include <ATen/ops/mean_ops.h>
#include <ATen/ops/median_ops.h>
#include <ATen/ops/meshgrid_ops.h>
#include <ATen/ops/min_ops.h>
#include <ATen/ops/minimum_ops.h>
#include <ATen/ops/miopen_batch_norm_ops.h>
#include <ATen/ops/miopen_batch_norm_backward_ops.h>
#include <ATen/ops/miopen_convolution_ops.h>
#include <ATen/ops/miopen_convolution_add_relu_ops.h>
#include <ATen/ops/miopen_convolution_relu_ops.h>
#include <ATen/ops/miopen_convolution_transpose_ops.h>
#include <ATen/ops/miopen_depthwise_convolution_ops.h>
#include <ATen/ops/miopen_rnn_ops.h>
#include <ATen/ops/miopen_rnn_backward_ops.h>
#include <ATen/ops/mish_ops.h>
#include <ATen/ops/mish_backward_ops.h>
#include <ATen/ops/mkldnn_adaptive_avg_pool2d_ops.h>
#include <ATen/ops/mkldnn_adaptive_avg_pool2d_backward_ops.h>
#include <ATen/ops/mkldnn_convolution_ops.h>
#include <ATen/ops/mkldnn_linear_ops.h>
#include <ATen/ops/mkldnn_linear_backward_ops.h>
#include <ATen/ops/mkldnn_linear_backward_input_ops.h>
#include <ATen/ops/mkldnn_linear_backward_weights_ops.h>
#include <ATen/ops/mkldnn_max_pool2d_ops.h>
#include <ATen/ops/mkldnn_max_pool2d_backward_ops.h>
#include <ATen/ops/mkldnn_max_pool3d_ops.h>
#include <ATen/ops/mkldnn_max_pool3d_backward_ops.h>
#include <ATen/ops/mkldnn_reorder_conv2d_weight_ops.h>
#include <ATen/ops/mkldnn_reorder_conv3d_weight_ops.h>
#include <ATen/ops/mkldnn_rnn_layer_ops.h>
#include <ATen/ops/mkldnn_rnn_layer_backward_ops.h>
#include <ATen/ops/mm_ops.h>
#include <ATen/ops/mode_ops.h>
#include <ATen/ops/moveaxis_ops.h>
#include <ATen/ops/movedim_ops.h>
#include <ATen/ops/mps_convolution_backward_ops.h>
#include <ATen/ops/mps_convolution_transpose_backward_ops.h>
#include <ATen/ops/mse_loss_ops.h>
#include <ATen/ops/mse_loss_backward_ops.h>
#include <ATen/ops/msort_ops.h>
#include <ATen/ops/mul_ops.h>
#include <ATen/ops/multi_margin_loss_ops.h>
#include <ATen/ops/multi_margin_loss_backward_ops.h>
#include <ATen/ops/multilabel_margin_loss_ops.h>
#include <ATen/ops/multilabel_margin_loss_backward_ops.h>
#include <ATen/ops/multilabel_margin_loss_forward_ops.h>
#include <ATen/ops/multinomial_ops.h>
#include <ATen/ops/multiply_ops.h>
#include <ATen/ops/mv_ops.h>
#include <ATen/ops/mvlgamma_ops.h>
#include <ATen/ops/nan_to_num_ops.h>
#include <ATen/ops/nanmean_ops.h>
#include <ATen/ops/nanmedian_ops.h>
#include <ATen/ops/nanquantile_ops.h>
#include <ATen/ops/nansum_ops.h>
#include <ATen/ops/narrow_ops.h>
#include <ATen/ops/narrow_copy_ops.h>
#include <ATen/ops/native_batch_norm_ops.h>
#include <ATen/ops/native_batch_norm_backward_ops.h>
#include <ATen/ops/native_channel_shuffle_ops.h>
#include <ATen/ops/native_dropout_ops.h>
#include <ATen/ops/native_dropout_backward_ops.h>
#include <ATen/ops/native_group_norm_ops.h>
#include <ATen/ops/native_group_norm_backward_ops.h>
#include <ATen/ops/native_layer_norm_ops.h>
#include <ATen/ops/native_layer_norm_backward_ops.h>
#include <ATen/ops/native_norm_ops.h>
#include <ATen/ops/ne_ops.h>
#include <ATen/ops/neg_ops.h>
#include <ATen/ops/negative_ops.h>
#include <ATen/ops/nested_to_padded_tensor_ops.h>
#include <ATen/ops/new_empty_ops.h>
#include <ATen/ops/new_empty_strided_ops.h>
#include <ATen/ops/new_full_ops.h>
#include <ATen/ops/new_ones_ops.h>
#include <ATen/ops/new_zeros_ops.h>
#include <ATen/ops/nextafter_ops.h>
#include <ATen/ops/nll_loss_ops.h>
#include <ATen/ops/nll_loss2d_ops.h>
#include <ATen/ops/nll_loss2d_backward_ops.h>
#include <ATen/ops/nll_loss2d_forward_ops.h>
#include <ATen/ops/nll_loss_backward_ops.h>
#include <ATen/ops/nll_loss_forward_ops.h>
#include <ATen/ops/nll_loss_nd_ops.h>
#include <ATen/ops/nonzero_ops.h>
#include <ATen/ops/nonzero_numpy_ops.h>
#include <ATen/ops/nonzero_static_ops.h>
#include <ATen/ops/norm_ops.h>
#include <ATen/ops/norm_except_dim_ops.h>
#include <ATen/ops/normal_ops.h>
#include <ATen/ops/not_equal_ops.h>
#include <ATen/ops/nuclear_norm_ops.h>
#include <ATen/ops/numpy_T_ops.h>
#include <ATen/ops/one_hot_ops.h>
#include <ATen/ops/ones_ops.h>
#include <ATen/ops/ones_like_ops.h>
#include <ATen/ops/or_ops.h>
#include <ATen/ops/orgqr_ops.h>
#include <ATen/ops/ormqr_ops.h>
#include <ATen/ops/outer_ops.h>
#include <ATen/ops/output_nr_ops.h>
#include <ATen/ops/pad_ops.h>
#include <ATen/ops/pad_sequence_ops.h>
#include <ATen/ops/pairwise_distance_ops.h>
#include <ATen/ops/pdist_ops.h>
#include <ATen/ops/permute_ops.h>
#include <ATen/ops/permute_copy_ops.h>
#include <ATen/ops/pin_memory_ops.h>
#include <ATen/ops/pinverse_ops.h>
#include <ATen/ops/pixel_shuffle_ops.h>
#include <ATen/ops/pixel_unshuffle_ops.h>
#include <ATen/ops/poisson_ops.h>
#include <ATen/ops/poisson_nll_loss_ops.h>
#include <ATen/ops/polar_ops.h>
#include <ATen/ops/polygamma_ops.h>
#include <ATen/ops/positive_ops.h>
#include <ATen/ops/pow_ops.h>
#include <ATen/ops/prelu_ops.h>
#include <ATen/ops/prod_ops.h>
#include <ATen/ops/promote_types_ops.h>
#include <ATen/ops/put_ops.h>
#include <ATen/ops/q_per_channel_axis_ops.h>
#include <ATen/ops/q_per_channel_scales_ops.h>
#include <ATen/ops/q_per_channel_zero_points_ops.h>
#include <ATen/ops/q_scale_ops.h>
#include <ATen/ops/q_zero_point_ops.h>
#include <ATen/ops/qr_ops.h>
#include <ATen/ops/qscheme_ops.h>
#include <ATen/ops/quantile_ops.h>
#include <ATen/ops/quantize_per_channel_ops.h>
#include <ATen/ops/quantize_per_tensor_ops.h>
#include <ATen/ops/quantize_per_tensor_dynamic_ops.h>
#include <ATen/ops/quantized_batch_norm_ops.h>
#include <ATen/ops/quantized_gru_cell_ops.h>
#include <ATen/ops/quantized_lstm_cell_ops.h>
#include <ATen/ops/quantized_max_pool1d_ops.h>
#include <ATen/ops/quantized_max_pool2d_ops.h>
#include <ATen/ops/quantized_max_pool3d_ops.h>
#include <ATen/ops/quantized_rnn_relu_cell_ops.h>
#include <ATen/ops/quantized_rnn_tanh_cell_ops.h>
#include <ATen/ops/rad2deg_ops.h>
#include <ATen/ops/rand_ops.h>
#include <ATen/ops/rand_like_ops.h>
#include <ATen/ops/randint_ops.h>
#include <ATen/ops/randint_like_ops.h>
#include <ATen/ops/randn_ops.h>
#include <ATen/ops/randn_like_ops.h>
#include <ATen/ops/random_ops.h>
#include <ATen/ops/randperm_ops.h>
#include <ATen/ops/range_ops.h>
#include <ATen/ops/ravel_ops.h>
#include <ATen/ops/real_ops.h>
#include <ATen/ops/reciprocal_ops.h>
#include <ATen/ops/record_stream_ops.h>
#include <ATen/ops/refine_names_ops.h>
#include <ATen/ops/reflection_pad1d_ops.h>
#include <ATen/ops/reflection_pad1d_backward_ops.h>
#include <ATen/ops/reflection_pad2d_ops.h>
#include <ATen/ops/reflection_pad2d_backward_ops.h>
#include <ATen/ops/reflection_pad3d_ops.h>
#include <ATen/ops/reflection_pad3d_backward_ops.h>
#include <ATen/ops/relu_ops.h>
#include <ATen/ops/relu6_ops.h>
#include <ATen/ops/remainder_ops.h>
#include <ATen/ops/rename_ops.h>
#include <ATen/ops/renorm_ops.h>
#include <ATen/ops/repeat_ops.h>
#include <ATen/ops/repeat_interleave_ops.h>
#include <ATen/ops/replication_pad1d_ops.h>
#include <ATen/ops/replication_pad1d_backward_ops.h>
#include <ATen/ops/replication_pad2d_ops.h>
#include <ATen/ops/replication_pad2d_backward_ops.h>
#include <ATen/ops/replication_pad3d_ops.h>
#include <ATen/ops/replication_pad3d_backward_ops.h>
#include <ATen/ops/requires_grad_ops.h>
#include <ATen/ops/reshape_ops.h>
#include <ATen/ops/reshape_as_ops.h>
#include <ATen/ops/resize_ops.h>
#include <ATen/ops/resize_as_ops.h>
#include <ATen/ops/resize_as_sparse_ops.h>
#include <ATen/ops/resolve_conj_ops.h>
#include <ATen/ops/resolve_neg_ops.h>
#include <ATen/ops/result_type_ops.h>
#include <ATen/ops/retain_grad_ops.h>
#include <ATen/ops/retains_grad_ops.h>
#include <ATen/ops/rnn_relu_ops.h>
#include <ATen/ops/rnn_relu_cell_ops.h>
#include <ATen/ops/rnn_tanh_ops.h>
#include <ATen/ops/rnn_tanh_cell_ops.h>
#include <ATen/ops/roll_ops.h>
#include <ATen/ops/rot90_ops.h>
#include <ATen/ops/round_ops.h>
#include <ATen/ops/row_indices_ops.h>
#include <ATen/ops/row_indices_copy_ops.h>
#include <ATen/ops/row_stack_ops.h>
#include <ATen/ops/rrelu_ops.h>
#include <ATen/ops/rrelu_with_noise_ops.h>
#include <ATen/ops/rrelu_with_noise_backward_ops.h>
#include <ATen/ops/rshift_ops.h>
#include <ATen/ops/rsqrt_ops.h>
#include <ATen/ops/rsub_ops.h>
#include <ATen/ops/scalar_tensor_ops.h>
#include <ATen/ops/scaled_dot_product_attention_ops.h>
#include <ATen/ops/scatter_ops.h>
#include <ATen/ops/scatter_add_ops.h>
#include <ATen/ops/scatter_reduce_ops.h>
#include <ATen/ops/searchsorted_ops.h>
#include <ATen/ops/segment_reduce_ops.h>
#include <ATen/ops/select_ops.h>
#include <ATen/ops/select_backward_ops.h>
#include <ATen/ops/select_copy_ops.h>
#include <ATen/ops/select_scatter_ops.h>
#include <ATen/ops/selu_ops.h>
#include <ATen/ops/set_ops.h>
#include <ATen/ops/set_data_ops.h>
#include <ATen/ops/sgn_ops.h>
#include <ATen/ops/sigmoid_ops.h>
#include <ATen/ops/sigmoid_backward_ops.h>
#include <ATen/ops/sign_ops.h>
#include <ATen/ops/signbit_ops.h>
#include <ATen/ops/silu_ops.h>
#include <ATen/ops/silu_backward_ops.h>
#include <ATen/ops/sin_ops.h>
#include <ATen/ops/sinc_ops.h>
#include <ATen/ops/sinh_ops.h>
#include <ATen/ops/size_ops.h>
#include <ATen/ops/slice_ops.h>
#include <ATen/ops/slice_backward_ops.h>
#include <ATen/ops/slice_copy_ops.h>
#include <ATen/ops/slice_scatter_ops.h>
#include <ATen/ops/slogdet_ops.h>
#include <ATen/ops/slow_conv3d_ops.h>
#include <ATen/ops/slow_conv3d_forward_ops.h>
#include <ATen/ops/slow_conv_dilated2d_ops.h>
#include <ATen/ops/slow_conv_dilated3d_ops.h>
#include <ATen/ops/slow_conv_transpose2d_ops.h>
#include <ATen/ops/slow_conv_transpose3d_ops.h>
#include <ATen/ops/smm_ops.h>
#include <ATen/ops/smooth_l1_loss_ops.h>
#include <ATen/ops/smooth_l1_loss_backward_ops.h>
#include <ATen/ops/soft_margin_loss_ops.h>
#include <ATen/ops/soft_margin_loss_backward_ops.h>
#include <ATen/ops/softmax_ops.h>
#include <ATen/ops/softplus_ops.h>
#include <ATen/ops/softplus_backward_ops.h>
#include <ATen/ops/softshrink_ops.h>
#include <ATen/ops/softshrink_backward_ops.h>
#include <ATen/ops/sort_ops.h>
#include <ATen/ops/sparse_bsc_tensor_ops.h>
#include <ATen/ops/sparse_bsr_tensor_ops.h>
#include <ATen/ops/sparse_compressed_tensor_ops.h>
#include <ATen/ops/sparse_coo_tensor_ops.h>
#include <ATen/ops/sparse_csc_tensor_ops.h>
#include <ATen/ops/sparse_csr_tensor_ops.h>
#include <ATen/ops/sparse_dim_ops.h>
#include <ATen/ops/sparse_mask_ops.h>
#include <ATen/ops/sparse_resize_ops.h>
#include <ATen/ops/sparse_resize_and_clear_ops.h>
#include <ATen/ops/sparse_sampled_addmm_ops.h>
#include <ATen/ops/special_airy_ai_ops.h>
#include <ATen/ops/special_bessel_j0_ops.h>
#include <ATen/ops/special_bessel_j1_ops.h>
#include <ATen/ops/special_bessel_y0_ops.h>
#include <ATen/ops/special_bessel_y1_ops.h>
#include <ATen/ops/special_chebyshev_polynomial_t_ops.h>
#include <ATen/ops/special_chebyshev_polynomial_u_ops.h>
#include <ATen/ops/special_chebyshev_polynomial_v_ops.h>
#include <ATen/ops/special_chebyshev_polynomial_w_ops.h>
#include <ATen/ops/special_digamma_ops.h>
#include <ATen/ops/special_entr_ops.h>
#include <ATen/ops/special_erf_ops.h>
#include <ATen/ops/special_erfc_ops.h>
#include <ATen/ops/special_erfcx_ops.h>
#include <ATen/ops/special_erfinv_ops.h>
#include <ATen/ops/special_exp2_ops.h>
#include <ATen/ops/special_expit_ops.h>
#include <ATen/ops/special_expm1_ops.h>
#include <ATen/ops/special_gammainc_ops.h>
#include <ATen/ops/special_gammaincc_ops.h>
#include <ATen/ops/special_gammaln_ops.h>
#include <ATen/ops/special_hermite_polynomial_h_ops.h>
#include <ATen/ops/special_hermite_polynomial_he_ops.h>
#include <ATen/ops/special_i0_ops.h>
#include <ATen/ops/special_i0e_ops.h>
#include <ATen/ops/special_i1_ops.h>
#include <ATen/ops/special_i1e_ops.h>
#include <ATen/ops/special_laguerre_polynomial_l_ops.h>
#include <ATen/ops/special_legendre_polynomial_p_ops.h>
#include <ATen/ops/special_log1p_ops.h>
#include <ATen/ops/special_log_ndtr_ops.h>
#include <ATen/ops/special_log_softmax_ops.h>
#include <ATen/ops/special_logit_ops.h>
#include <ATen/ops/special_logsumexp_ops.h>
#include <ATen/ops/special_modified_bessel_i0_ops.h>
#include <ATen/ops/special_modified_bessel_i1_ops.h>
#include <ATen/ops/special_modified_bessel_k0_ops.h>
#include <ATen/ops/special_modified_bessel_k1_ops.h>
#include <ATen/ops/special_multigammaln_ops.h>
#include <ATen/ops/special_ndtr_ops.h>
#include <ATen/ops/special_ndtri_ops.h>
#include <ATen/ops/special_polygamma_ops.h>
#include <ATen/ops/special_psi_ops.h>
#include <ATen/ops/special_round_ops.h>
#include <ATen/ops/special_scaled_modified_bessel_k0_ops.h>
#include <ATen/ops/special_scaled_modified_bessel_k1_ops.h>
#include <ATen/ops/special_shifted_chebyshev_polynomial_t_ops.h>
#include <ATen/ops/special_shifted_chebyshev_polynomial_u_ops.h>
#include <ATen/ops/special_shifted_chebyshev_polynomial_v_ops.h>
#include <ATen/ops/special_shifted_chebyshev_polynomial_w_ops.h>
#include <ATen/ops/special_sinc_ops.h>
#include <ATen/ops/special_softmax_ops.h>
#include <ATen/ops/special_spherical_bessel_j0_ops.h>
#include <ATen/ops/special_xlog1py_ops.h>
#include <ATen/ops/special_xlogy_ops.h>
#include <ATen/ops/special_zeta_ops.h>
#include <ATen/ops/split_ops.h>
#include <ATen/ops/split_copy_ops.h>
#include <ATen/ops/split_with_sizes_ops.h>
#include <ATen/ops/split_with_sizes_copy_ops.h>
#include <ATen/ops/sqrt_ops.h>
#include <ATen/ops/square_ops.h>
#include <ATen/ops/squeeze_ops.h>
#include <ATen/ops/squeeze_copy_ops.h>
#include <ATen/ops/sspaddmm_ops.h>
#include <ATen/ops/stack_ops.h>
#include <ATen/ops/std_ops.h>
#include <ATen/ops/std_mean_ops.h>
#include <ATen/ops/stft_ops.h>
#include <ATen/ops/stride_ops.h>
#include <ATen/ops/sub_ops.h>
#include <ATen/ops/subtract_ops.h>
#include <ATen/ops/sum_ops.h>
#include <ATen/ops/sum_to_size_ops.h>
#include <ATen/ops/svd_ops.h>
#include <ATen/ops/swapaxes_ops.h>
#include <ATen/ops/swapdims_ops.h>
#include <ATen/ops/sym_constrain_range_ops.h>
#include <ATen/ops/sym_constrain_range_for_size_ops.h>
#include <ATen/ops/sym_numel_ops.h>
#include <ATen/ops/sym_size_ops.h>
#include <ATen/ops/sym_storage_offset_ops.h>
#include <ATen/ops/sym_stride_ops.h>
#include <ATen/ops/t_ops.h>
#include <ATen/ops/t_copy_ops.h>
#include <ATen/ops/take_ops.h>
#include <ATen/ops/take_along_dim_ops.h>
#include <ATen/ops/tan_ops.h>
#include <ATen/ops/tanh_ops.h>
#include <ATen/ops/tanh_backward_ops.h>
#include <ATen/ops/tensor_split_ops.h>
#include <ATen/ops/tensordot_ops.h>
#include <ATen/ops/thnn_conv2d_ops.h>
#include <ATen/ops/threshold_ops.h>
#include <ATen/ops/threshold_backward_ops.h>
#include <ATen/ops/tile_ops.h>
#include <ATen/ops/to_ops.h>
#include <ATen/ops/to_dense_ops.h>
#include <ATen/ops/to_dense_backward_ops.h>
#include <ATen/ops/to_mkldnn_ops.h>
#include <ATen/ops/to_mkldnn_backward_ops.h>
#include <ATen/ops/to_padded_tensor_ops.h>
#include <ATen/ops/to_sparse_ops.h>
#include <ATen/ops/to_sparse_bsc_ops.h>
#include <ATen/ops/to_sparse_bsr_ops.h>
#include <ATen/ops/to_sparse_csc_ops.h>
#include <ATen/ops/to_sparse_csr_ops.h>
#include <ATen/ops/topk_ops.h>
#include <ATen/ops/trace_ops.h>
#include <ATen/ops/trace_backward_ops.h>
#include <ATen/ops/transpose_ops.h>
#include <ATen/ops/transpose_copy_ops.h>
#include <ATen/ops/trapezoid_ops.h>
#include <ATen/ops/trapz_ops.h>
#include <ATen/ops/triangular_solve_ops.h>
#include <ATen/ops/tril_ops.h>
#include <ATen/ops/tril_indices_ops.h>
#include <ATen/ops/triplet_margin_loss_ops.h>
#include <ATen/ops/triu_ops.h>
#include <ATen/ops/triu_indices_ops.h>
#include <ATen/ops/true_divide_ops.h>
#include <ATen/ops/trunc_ops.h>
#include <ATen/ops/type_as_ops.h>
#include <ATen/ops/unbind_ops.h>
#include <ATen/ops/unbind_copy_ops.h>
#include <ATen/ops/unflatten_ops.h>
#include <ATen/ops/unflatten_dense_tensors_ops.h>
#include <ATen/ops/unfold_ops.h>
#include <ATen/ops/unfold_backward_ops.h>
#include <ATen/ops/unfold_copy_ops.h>
#include <ATen/ops/uniform_ops.h>
#include <ATen/ops/unique_consecutive_ops.h>
#include <ATen/ops/unique_dim_ops.h>
#include <ATen/ops/unique_dim_consecutive_ops.h>
#include <ATen/ops/unsafe_chunk_ops.h>
#include <ATen/ops/unsafe_split_ops.h>
#include <ATen/ops/unsafe_split_with_sizes_ops.h>
#include <ATen/ops/unsqueeze_ops.h>
#include <ATen/ops/unsqueeze_copy_ops.h>
#include <ATen/ops/upsample_bicubic2d_ops.h>
#include <ATen/ops/upsample_bicubic2d_backward_ops.h>
#include <ATen/ops/upsample_bilinear2d_ops.h>
#include <ATen/ops/upsample_bilinear2d_backward_ops.h>
#include <ATen/ops/upsample_linear1d_ops.h>
#include <ATen/ops/upsample_linear1d_backward_ops.h>
#include <ATen/ops/upsample_nearest1d_ops.h>
#include <ATen/ops/upsample_nearest1d_backward_ops.h>
#include <ATen/ops/upsample_nearest2d_ops.h>
#include <ATen/ops/upsample_nearest2d_backward_ops.h>
#include <ATen/ops/upsample_nearest3d_ops.h>
#include <ATen/ops/upsample_nearest3d_backward_ops.h>
#include <ATen/ops/upsample_trilinear3d_ops.h>
#include <ATen/ops/upsample_trilinear3d_backward_ops.h>
#include <ATen/ops/value_selecting_reduction_backward_ops.h>
#include <ATen/ops/values_ops.h>
#include <ATen/ops/values_copy_ops.h>
#include <ATen/ops/vander_ops.h>
#include <ATen/ops/var_ops.h>
#include <ATen/ops/var_mean_ops.h>
#include <ATen/ops/vdot_ops.h>
#include <ATen/ops/view_ops.h>
#include <ATen/ops/view_as_ops.h>
#include <ATen/ops/view_as_complex_ops.h>
#include <ATen/ops/view_as_complex_copy_ops.h>
#include <ATen/ops/view_as_real_ops.h>
#include <ATen/ops/view_as_real_copy_ops.h>
#include <ATen/ops/view_copy_ops.h>
#include <ATen/ops/vsplit_ops.h>
#include <ATen/ops/vstack_ops.h>
#include <ATen/ops/where_ops.h>
#include <ATen/ops/xlogy_ops.h>
#include <ATen/ops/xor_ops.h>
#include <ATen/ops/zero_ops.h>
#include <ATen/ops/zeros_ops.h>
#include <ATen/ops/zeros_like_ops.h>

// Extension writers: do you write wrapper functions? Are you frustrated with
// resolving overloads of operators? Are you frustrated with dealing with
// pointer-to-methods and resolving overloads of pointer-to-methods?? Look no
// further, this is the utility for you.
//
// Given an operator schema: aten::op.overload(...
//
// Use ATEN_FN2(op, overload) to get a *function* version of the operator
// that is guaranteed to not be overloaded. This means that you can safely
// decltype(&ATEN_FN2(op, overload)) it. NB: the 2 means this macro takes 2 args.
//
// Given an operator schema without an overload name: aten::op(...
//
// Use ATEN_FN(op) to get an unambiguous *function* version of the operator.
//
// There is some interesting behavior for out= operations.
// ATEN_FN2(sin, out) gives a function that is *faithful* to the schema;
// that is, the order of arguments is exactly what it looks like in the schema.

#define ATEN_FN2(op_name, overload) at::_ops::op_name##_##overload::call
#define ATEN_FN(op_name) at::_ops::op_name::call

// Separately, ATEN_OP(op) and ATEN_OP2(op, overload) define a class containing compile-time
// metadata about a given aten operator.
// Notable data on the class includes:
// - ATEN_OP2(add, Tensor)::name // returns the string name: "add"
// - ATEN_OP2(add, Tensor)::overload_name // returns the string overload name: "Tensor"
// - ATEN_OP2(add, Tensor)::schema // returns the C++ schema type: at::Tensor (const at::Tensor &, const at::Tensor &, const at::Scalar &)
// - ATEN_OP2(add, Tensor)::schema_str // returns the string jit type: "add.Tensor(Tensor self, Tensor other, *, Scalar alpha=1) -> Tensor"

#define ATEN_OP2(op_name, overload) at::_ops::op_name##_##overload
#define ATEN_OP(op_name) at::_ops::op_name

// WARNING: Please do not call any of the ops in the _ops namespace directly.
// Use the ATEN_FN macros. We do not guarantee stability of the naming
// scheme for the functions in at::_ops

// See Note [The ATen Operators API] for details of the at::_ops namespace

namespace at {
namespace _ops {

} // namespace _ops
} // namespace at
