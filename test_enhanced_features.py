#!/usr/bin/env python3
"""
Script de Test Complet pour Auto-Grade Scribe Enhanced
Teste toutes les nouvelles fonctionnalités améliorées
"""

import asyncio
import aiohttp
import json
import os
import time
from pathlib import Path
from PIL import Image, ImageDraw, ImageFont
import io
import base64

class EnhancedGradingTester:
    """Testeur pour les fonctionnalités améliorées"""
    
    def __init__(self, base_url="http://127.0.0.1:8001"):
        self.base_url = base_url
        self.session = None
        self.test_results = []
    
    async def __aenter__(self):
        self.session = aiohttp.ClientSession()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
    
    async def run_all_tests(self):
        """Exécuter tous les tests"""
        print("🧪 Démarrage des tests Auto-Grade Scribe Enhanced")
        print("=" * 60)
        
        # Tests de base
        await self.test_api_health()
        await self.test_file_upload()
        
        # Tests OCR avancés
        await self.test_enhanced_ocr()
        
        # Tests de correction intelligente
        await self.test_intelligent_grading()
        
        # Tests de révision manuelle
        await self.test_manual_review_workflow()
        
        # Tests de robustesse
        await self.test_error_handling()
        
        # Rapport final
        self.generate_test_report()
    
    async def test_api_health(self):
        """Test de santé de l'API"""
        print("\n🏥 Test de santé de l'API...")
        
        try:
            async with self.session.get(f"{self.base_url}/health") as response:
                data = await response.json()
                
                if response.status == 200 and data.get('status') == 'healthy':
                    self.log_success("API Health", "API accessible et en bonne santé")
                else:
                    self.log_error("API Health", f"Status: {data.get('status')}")
                    
        except Exception as e:
            self.log_error("API Health", f"Erreur de connexion: {str(e)}")
    
    async def test_file_upload(self):
        """Test d'upload de fichier"""
        print("\n📤 Test d'upload de fichier...")
        
        try:
            # Créer un fichier de test
            test_image = self.create_test_exam_image()
            
            # Upload via l'API
            data = aiohttp.FormData()
            data.add_field('file', test_image, filename='test_exam.png', content_type='image/png')
            
            async with self.session.post(f"{self.base_url}/api/upload", data=data) as response:
                result = await response.json()
                
                if response.status == 200 and result.get('success') and result.get('file_id'):
                    self.test_file_id = result['file_id']
                    self.log_success("File Upload", f"Fichier uploadé avec ID: {self.test_file_id}")
                else:
                    self.log_error("File Upload", f"Échec upload: {result}")
                    
        except Exception as e:
            self.log_error("File Upload", f"Erreur: {str(e)}")
    
    async def test_enhanced_ocr(self):
        """Test OCR avancé"""
        print("\n🔍 Test OCR avancé...")
        
        if not hasattr(self, 'test_file_id'):
            self.log_error("Enhanced OCR", "Pas de fichier de test disponible")
            return
        
        try:
            # Test OCR avec différents types de contenu
            content_types = ['mixed', 'handwritten', 'printed']
            
            for content_type in content_types:
                payload = {
                    "file_id": self.test_file_id,
                    "content_type": content_type,
                    "force_reprocess": False
                }
                
                async with self.session.post(
                    f"{self.base_url}/api/v3/ocr/enhanced",
                    json=payload
                ) as response:
                    result = await response.json()
                    
                    if response.status == 200 and result.get('success'):
                        confidence = result.get('confidence', 0)
                        provider = result.get('provider_used', 'unknown')
                        self.log_success(
                            f"Enhanced OCR ({content_type})",
                            f"Provider: {provider}, Confiance: {confidence:.2f}"
                        )
                    else:
                        self.log_error(f"Enhanced OCR ({content_type})", f"Échec: {result}")
                        
        except Exception as e:
            self.log_error("Enhanced OCR", f"Erreur: {str(e)}")
    
    async def test_intelligent_grading(self):
        """Test de correction intelligente"""
        print("\n🧠 Test de correction intelligente...")
        
        if not hasattr(self, 'test_file_id'):
            self.log_error("Intelligent Grading", "Pas de fichier de test disponible")
            return
        
        try:
            # Test avec différents types d'examens
            test_cases = [
                {
                    "exam_type": "qcm",
                    "correct_answers": {
                        "question_1": "A",
                        "question_2": "B",
                        "question_3": "C"
                    }
                },
                {
                    "exam_type": "open_ended",
                    "correct_answers": {
                        "question_1": "La photosynthèse est le processus par lequel les plantes convertissent la lumière en énergie.",
                        "question_2": "L'équation de la photosynthèse est 6CO2 + 6H2O + lumière → C6H12O6 + 6O2"
                    }
                }
            ]
            
            for test_case in test_cases:
                payload = {
                    "file_id": self.test_file_id,
                    "exam_type": test_case["exam_type"],
                    "correct_answers": test_case["correct_answers"],
                    "grading_config": {
                        "partial_credit": True,
                        "fuzzy_threshold": 0.8
                    }
                }
                
                async with self.session.post(
                    f"{self.base_url}/api/v3/grade/intelligent",
                    json=payload
                ) as response:
                    result = await response.json()
                    
                    if response.status == 200 and result.get('success'):
                        final_score = result.get('final_score', {})
                        percentage = final_score.get('percentage', 0)
                        confidence = final_score.get('confidence', 0)
                        requires_review = result.get('requires_manual_review', False)
                        
                        self.log_success(
                            f"Intelligent Grading ({test_case['exam_type']})",
                            f"Score: {percentage:.1f}%, Confiance: {confidence:.2f}, Révision: {requires_review}"
                        )
                    else:
                        self.log_error(f"Intelligent Grading ({test_case['exam_type']})", f"Échec: {result}")
                        
        except Exception as e:
            self.log_error("Intelligent Grading", f"Erreur: {str(e)}")
    
    async def test_manual_review_workflow(self):
        """Test du workflow de révision manuelle"""
        print("\n👨‍🏫 Test du workflow de révision manuelle...")
        
        try:
            # Test du tableau de bord
            async with self.session.get(f"{self.base_url}/api/v3/review/dashboard") as response:
                dashboard = await response.json()
                
                if response.status == 200 and dashboard.get('success'):
                    pending_count = dashboard.get('dashboard', {}).get('pending_reviews', {}).get('total_count', 0)
                    self.log_success("Review Dashboard", f"Examens en attente: {pending_count}")
                else:
                    self.log_error("Review Dashboard", f"Échec: {dashboard}")
            
            # Test de récupération d'examen pour révision
            if hasattr(self, 'test_file_id'):
                async with self.session.get(
                    f"{self.base_url}/api/v3/review/exam/{self.test_file_id}"
                ) as response:
                    exam_data = await response.json()
                    
                    if response.status == 200 and exam_data.get('success'):
                        suggestions = exam_data.get('suggestions', {})
                        priority = suggestions.get('priority_level', 'unknown')
                        self.log_success("Review Exam Data", f"Priorité: {priority}")
                    else:
                        self.log_error("Review Exam Data", f"Échec: {exam_data}")
                
                # Test de soumission de révision
                review_payload = {
                    "question_adjustments": {
                        "question_1": {
                            "new_score": 0.8,
                            "reason": "Partial credit for methodology"
                        }
                    },
                    "overall_comments": "Test review comment",
                    "review_time_minutes": 5
                }
                
                async with self.session.post(
                    f"{self.base_url}/api/v3/review/submit/{self.test_file_id}",
                    json=review_payload
                ) as response:
                    review_result = await response.json()
                    
                    if response.status == 200 and review_result.get('success'):
                        self.log_success("Manual Review Submit", "Révision soumise avec succès")
                    else:
                        self.log_error("Manual Review Submit", f"Échec: {review_result}")
                        
        except Exception as e:
            self.log_error("Manual Review Workflow", f"Erreur: {str(e)}")
    
    async def test_error_handling(self):
        """Test de gestion d'erreurs"""
        print("\n🛡️ Test de gestion d'erreurs...")
        
        try:
            # Test avec fichier inexistant
            payload = {
                "file_id": "fichier-inexistant-12345",
                "content_type": "mixed"
            }
            
            async with self.session.post(
                f"{self.base_url}/api/v3/ocr/enhanced",
                json=payload
            ) as response:
                result = await response.json()
                
                if response.status == 404:
                    self.log_success("Error Handling", "Erreur 404 correctement gérée pour fichier inexistant")
                else:
                    self.log_error("Error Handling", f"Gestion d'erreur inattendue: {result}")
            
            # Test avec données invalides
            invalid_payload = {
                "file_id": "",
                "exam_type": "invalid_type",
                "correct_answers": "not_a_dict"
            }
            
            async with self.session.post(
                f"{self.base_url}/api/v3/grade/intelligent",
                json=invalid_payload
            ) as response:
                result = await response.json()
                
                if response.status >= 400:
                    self.log_success("Error Handling", "Données invalides correctement rejetées")
                else:
                    self.log_error("Error Handling", f"Validation insuffisante: {result}")
                    
        except Exception as e:
            self.log_error("Error Handling", f"Erreur: {str(e)}")
    
    def create_test_exam_image(self):
        """Créer une image d'examen de test"""
        # Créer une image simple avec du texte
        img = Image.new('RGB', (800, 600), color='white')
        draw = ImageDraw.Draw(img)
        
        # Ajouter du texte simulant un examen
        try:
            font = ImageFont.load_default()
        except:
            font = None
        
        text_lines = [
            "EXAMEN DE TEST",
            "",
            "Question 1: Quelle est la capitale de la France?",
            "Réponse: A) Paris",
            "",
            "Question 2: Combien font 2 + 2?",
            "Réponse: B) 4",
            "",
            "Question 3: Quelle est la couleur du ciel?",
            "Réponse: C) Bleu"
        ]
        
        y_position = 50
        for line in text_lines:
            draw.text((50, y_position), line, fill='black', font=font)
            y_position += 30
        
        # Convertir en bytes
        img_buffer = io.BytesIO()
        img.save(img_buffer, format='PNG')
        img_buffer.seek(0)
        
        return img_buffer.getvalue()
    
    def log_success(self, test_name, message):
        """Logger un succès"""
        print(f"  ✅ {test_name}: {message}")
        self.test_results.append({
            'test': test_name,
            'status': 'SUCCESS',
            'message': message
        })
    
    def log_error(self, test_name, message):
        """Logger une erreur"""
        print(f"  ❌ {test_name}: {message}")
        self.test_results.append({
            'test': test_name,
            'status': 'ERROR',
            'message': message
        })
    
    def generate_test_report(self):
        """Générer le rapport de test"""
        print("\n" + "=" * 60)
        print("📊 RAPPORT DE TEST FINAL")
        print("=" * 60)
        
        total_tests = len(self.test_results)
        successful_tests = len([r for r in self.test_results if r['status'] == 'SUCCESS'])
        failed_tests = total_tests - successful_tests
        
        print(f"Total des tests: {total_tests}")
        print(f"✅ Réussis: {successful_tests}")
        print(f"❌ Échoués: {failed_tests}")
        print(f"📈 Taux de réussite: {(successful_tests/total_tests*100):.1f}%")
        
        if failed_tests > 0:
            print("\n🔍 Tests échoués:")
            for result in self.test_results:
                if result['status'] == 'ERROR':
                    print(f"  - {result['test']}: {result['message']}")
        
        print("\n🎯 Fonctionnalités testées:")
        print("  ✓ Upload de fichiers")
        print("  ✓ OCR multi-provider")
        print("  ✓ Correction intelligente")
        print("  ✓ Révision manuelle")
        print("  ✓ Gestion d'erreurs")
        
        # Sauvegarder le rapport
        report_data = {
            'timestamp': time.time(),
            'total_tests': total_tests,
            'successful_tests': successful_tests,
            'failed_tests': failed_tests,
            'success_rate': successful_tests/total_tests*100,
            'results': self.test_results
        }
        
        with open('test_report.json', 'w') as f:
            json.dump(report_data, f, indent=2)
        
        print(f"\n📄 Rapport détaillé sauvegardé: test_report.json")

async def main():
    """Fonction principale"""
    print("🚀 Auto-Grade Scribe Enhanced - Test Suite")
    print("Assurez-vous que l'application est démarrée sur http://127.0.0.1:8001")
    print()
    
    # Attendre que l'utilisateur confirme
    input("Appuyez sur Entrée pour commencer les tests...")
    
    async with EnhancedGradingTester() as tester:
        await tester.run_all_tests()
    
    print("\n🎉 Tests terminés!")

if __name__ == "__main__":
    asyncio.run(main())
