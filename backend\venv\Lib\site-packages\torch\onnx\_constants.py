"""Constant values used in ONNX."""

ONNX_ARCHIVE_MODEL_PROTO_NAME = "__MODEL_PROTO"

ONNX_BASE_OPSET = 9
ONNX_MIN_OPSET = 7
ONNX_MAX_OPSET = 19
ONNX_TORCHSCRIPT_EXPORTER_MAX_OPSET = 17
# ONNX_DEFAULT_OPSET generated by tools/onnx/update_default_opset_version.py
ONNX_DEFAULT_OPSET = 17
ONNX_CONSTANT_FOLDING_MIN_OPSET = 9

PYTORCH_GITHUB_ISSUES_URL = "https://github.com/pytorch/pytorch/issues"

INT64_MAX = 9223372036854775807
INT32_MAX = 2147483647
INT16_MAX = 32767
INT8_MAX = 127
UINT8_MAX = 255

INT64_MIN = -9223372036854775808
INT32_MIN = -2147483648
INT16_MIN = -32768
INT8_MIN = -128
UINT8_MIN = 0
