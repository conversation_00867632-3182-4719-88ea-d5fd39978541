"""
Audit Service for Auto-Grade Scribe
Provides comprehensive audit logging and tracking functionality
"""

import logging
from typing import Dict, Any, Optional
from datetime import datetime
from sqlalchemy.orm import Session

from core.database import AuditLog
from typing import List

logger = logging.getLogger("auto-grade-scribe.audit-service")

class AuditService:
    """Service for audit logging and compliance tracking"""

    def __init__(self):
        logger.info("Audit Service initialized")

    async def log_action(
        self,
        db: Session,
        user_id: Optional[int],
        action: str,
        resource_type: str,
        resource_id: Optional[str] = None,
        old_values: Optional[Dict[str, Any]] = None,
        new_values: Optional[Dict[str, Any]] = None,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None,
        request_method: Optional[str] = None,
        request_path: Optional[str] = None,
        session_id: Optional[str] = None,
        additional_data: Optional[Dict[str, Any]] = None
    ) -> AuditLog:
        """
        Log an action for audit purposes

        Args:
            db: Database session
            user_id: ID of the user performing the action
            action: Action being performed (create, update, delete, login, etc.)
            resource_type: Type of resource (exam, user, grade, etc.)
            resource_id: ID of the resource being acted upon
            old_values: Previous values (for updates)
            new_values: New values (for creates/updates)
            ip_address: IP address of the request
            user_agent: User agent string
            request_method: HTTP method
            request_path: Request path
            session_id: Session identifier
            additional_data: Any additional data to log

        Returns:
            Created AuditLog entry
        """
        try:
            audit_log = AuditLog(
                user_id=user_id,
                action=action,
                resource_type=resource_type,
                resource_id=resource_id,
                old_values=old_values,
                new_values=new_values,
                ip_address=ip_address,
                user_agent=user_agent,
                request_method=request_method,
                request_path=request_path,
                session_id=session_id,
                additional_data=additional_data or {}
            )

            db.add(audit_log)
            db.commit()
            db.refresh(audit_log)

            logger.info(f"Audit log created: {action} on {resource_type} by user {user_id}")
            return audit_log

        except Exception as e:
            logger.error(f"Failed to create audit log: {str(e)}")
            db.rollback()
            raise

    def get_audit_logs(
        self,
        db: Session,
        user_id: Optional[int] = None,
        resource_type: Optional[str] = None,
        resource_id: Optional[str] = None,
        action: Optional[str] = None,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        limit: int = 100,
        offset: int = 0
    ) -> List[AuditLog]:
        """
        Retrieve audit logs with filtering

        Args:
            db: Database session
            user_id: Filter by user ID
            resource_type: Filter by resource type
            resource_id: Filter by resource ID
            action: Filter by action
            start_date: Filter by start date
            end_date: Filter by end date
            limit: Maximum number of results
            offset: Offset for pagination

        Returns:
            List of AuditLog entries
        """
        try:
            query = db.query(AuditLog)

            if user_id:
                query = query.filter(AuditLog.user_id == user_id)

            if resource_type:
                query = query.filter(AuditLog.resource_type == resource_type)

            if resource_id:
                query = query.filter(AuditLog.resource_id == resource_id)

            if action:
                query = query.filter(AuditLog.action == action)

            if start_date:
                query = query.filter(AuditLog.timestamp >= start_date)

            if end_date:
                query = query.filter(AuditLog.timestamp <= end_date)

            return query.order_by(AuditLog.timestamp.desc()).offset(offset).limit(limit).all()

        except Exception as e:
            logger.error(f"Error retrieving audit logs: {str(e)}")
            return []

    def get_user_activity_summary(
        self,
        db: Session,
        user_id: int,
        days: int = 30
    ) -> Dict[str, Any]:
        """
        Get activity summary for a user

        Args:
            db: Database session
            user_id: User ID
            days: Number of days to look back

        Returns:
            Activity summary
        """
        try:
            from datetime import timedelta

            start_date = datetime.utcnow() - timedelta(days=days)

            logs = self.get_audit_logs(
                db, user_id=user_id, start_date=start_date, limit=1000
            )

            # Analyze activity
            activity_by_action = {}
            activity_by_resource = {}
            daily_activity = {}

            for log in logs:
                # Count by action
                activity_by_action[log.action] = activity_by_action.get(log.action, 0) + 1

                # Count by resource type
                activity_by_resource[log.resource_type] = activity_by_resource.get(log.resource_type, 0) + 1

                # Count by day
                day_key = log.timestamp.date().isoformat()
                daily_activity[day_key] = daily_activity.get(day_key, 0) + 1

            return {
                "user_id": user_id,
                "period_days": days,
                "total_actions": len(logs),
                "activity_by_action": activity_by_action,
                "activity_by_resource": activity_by_resource,
                "daily_activity": daily_activity,
                "most_active_day": max(daily_activity.items(), key=lambda x: x[1]) if daily_activity else None,
                "summary_generated_at": datetime.utcnow().isoformat()
            }

        except Exception as e:
            logger.error(f"Error generating activity summary: {str(e)}")
            return {
                "user_id": user_id,
                "error": str(e)
            }

# Create singleton instance
audit_service = AuditService()
