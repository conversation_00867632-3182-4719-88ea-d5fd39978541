"""
Tests for validation utilities
"""

import pytest
from utils.validation import ValidationUtils

class TestValidationUtils:
    """Test cases for ValidationUtils"""
    
    def setup_method(self):
        """Set up test fixtures"""
        self.validation_utils = ValidationUtils()
    
    def test_validate_user_data_valid(self):
        """Test valid user data validation"""
        user_data = {
            "username": "testuser123",
            "email": "<EMAIL>",
            "full_name": "Test User",
            "phone": "+1234567890",
            "department": "Computer Science",
            "institution": "Test University",
            "role": "teacher"
        }
        
        result = self.validation_utils.validate_user_data(user_data)
        
        assert result["valid"] is True
        assert len(result["errors"]) == 0
        assert result["sanitized_data"]["email"] == "<EMAIL>"
    
    def test_validate_user_data_invalid_username(self):
        """Test invalid username validation"""
        user_data = {
            "username": "ab",  # Too short
            "email": "<EMAIL>"
        }
        
        result = self.validation_utils.validate_user_data(user_data)
        
        assert result["valid"] is False
        assert "username" in result["errors"]
    
    def test_validate_user_data_invalid_email(self):
        """Test invalid email validation"""
        user_data = {
            "username": "testuser",
            "email": "invalid-email"
        }
        
        result = self.validation_utils.validate_user_data(user_data)
        
        assert result["valid"] is False
        assert "email" in result["errors"]
    
    def test_validate_student_data_valid(self):
        """Test valid student data validation"""
        student_data = {
            "student_id": "STU123456",
            "first_name": "John",
            "last_name": "Doe",
            "email": "<EMAIL>",
            "class_name": "CS101",
            "academic_year": "2023-2024"
        }
        
        result = self.validation_utils.validate_student_data(student_data)
        
        assert result["valid"] is True
        assert len(result["errors"]) == 0
    
    def test_validate_student_data_missing_required(self):
        """Test student data validation with missing required fields"""
        student_data = {
            "student_id": "STU123456",
            # Missing first_name and last_name
        }
        
        result = self.validation_utils.validate_student_data(student_data)
        
        assert result["valid"] is False
        assert "first_name" in result["errors"]
        assert "last_name" in result["errors"]
    
    def test_validate_exam_data_valid(self):
        """Test valid exam data validation"""
        exam_data = {
            "exam_name": "Midterm Exam",
            "subject": "Mathematics",
            "class_name": "MATH101",
            "academic_year": "2023-2024",
            "exam_type": "qcm"
        }
        
        result = self.validation_utils.validate_exam_data(exam_data)
        
        assert result["valid"] is True
        assert len(result["errors"]) == 0
    
    def test_validate_exam_data_invalid_academic_year(self):
        """Test exam data validation with invalid academic year"""
        exam_data = {
            "exam_name": "Test Exam",
            "academic_year": "2023"  # Invalid format
        }
        
        result = self.validation_utils.validate_exam_data(exam_data)
        
        assert result["valid"] is False
        assert "academic_year" in result["errors"]
    
    def test_validate_grading_data_valid(self):
        """Test valid grading data validation"""
        grading_data = {
            "correct_answers": {
                "1": "A",
                "2": "B",
                "3": "C"
            },
            "grade_scale": "standard",
            "passing_threshold": 60.0
        }
        
        result = self.validation_utils.validate_grading_data(grading_data)
        
        assert result["valid"] is True
        assert len(result["errors"]) == 0
    
    def test_validate_grading_data_empty_answers(self):
        """Test grading data validation with empty correct answers"""
        grading_data = {
            "correct_answers": {},
            "grade_scale": "standard"
        }
        
        result = self.validation_utils.validate_grading_data(grading_data)
        
        assert result["valid"] is False
        assert "correct_answers" in result["errors"]
    
    def test_validate_pagination_valid(self):
        """Test valid pagination validation"""
        result = self.validation_utils.validate_pagination(0, 50)
        
        assert result["valid"] is True
        assert result["skip"] == 0
        assert result["limit"] == 50
        assert len(result["errors"]) == 0
    
    def test_validate_pagination_invalid_values(self):
        """Test pagination validation with invalid values"""
        result = self.validation_utils.validate_pagination(-1, 0)
        
        assert result["valid"] is False
        assert "skip" in result["errors"]
        assert "limit" in result["errors"]
        assert result["skip"] == 0  # Corrected value
        assert result["limit"] == 10  # Corrected value
    
    def test_validate_pagination_large_values(self):
        """Test pagination validation with large values"""
        result = self.validation_utils.validate_pagination(20000, 5000)
        
        assert result["valid"] is False
        assert "skip" in result["errors"]
        assert "limit" in result["errors"]
        assert result["skip"] == 10000  # Capped value
        assert result["limit"] == 1000  # Capped value
    
    def test_sanitize_text_basic(self):
        """Test basic text sanitization"""
        text = "  Hello World  "
        result = self.validation_utils.sanitize_text(text)
        
        assert result == "Hello World"
    
    def test_sanitize_text_with_null_bytes(self):
        """Test text sanitization with null bytes"""
        text = "Hello\x00World"
        result = self.validation_utils.sanitize_text(text)
        
        assert result == "HelloWorld"
    
    def test_sanitize_text_with_length_limit(self):
        """Test text sanitization with length limit"""
        text = "A" * 100
        result = self.validation_utils.sanitize_text(text, max_length=50)
        
        assert len(result) == 50
        assert result == "A" * 50
