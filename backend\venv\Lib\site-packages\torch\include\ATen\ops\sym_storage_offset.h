#pragma once

// @generated by torchgen/gen.py from Function.h

#include <ATen/Context.h>
#include <ATen/DeviceGuard.h>
#include <ATen/TensorUtils.h>
#include <ATen/TracerMode.h>
#include <ATen/core/Generator.h>
#include <ATen/core/Reduction.h>
#include <ATen/core/Tensor.h>
#include <c10/core/Scalar.h>
#include <c10/core/Storage.h>
#include <c10/core/TensorOptions.h>
#include <c10/util/Deprecated.h>
#include <c10/util/Optional.h>



#include <ATen/ops/sym_storage_offset_ops.h>

namespace at {


// aten::sym_storage_offset(Tensor self) -> SymInt
inline c10::SymInt __dispatch_sym_storage_offset(const at::Tensor & self) {
    return at::_ops::sym_storage_offset::call(self);
}

}
