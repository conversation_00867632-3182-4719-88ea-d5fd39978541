#pragma once

// @generated by torchgen/gen.py from Function.h

#include <ATen/Context.h>
#include <ATen/DeviceGuard.h>
#include <ATen/TensorUtils.h>
#include <ATen/TracerMode.h>
#include <ATen/core/Generator.h>
#include <ATen/core/Reduction.h>
#include <ATen/core/Tensor.h>
#include <c10/core/Scalar.h>
#include <c10/core/Storage.h>
#include <c10/core/TensorOptions.h>
#include <c10/util/Deprecated.h>
#include <c10/util/Optional.h>



#include <ATen/ops/unsqueeze_copy_ops.h>

namespace at {


// aten::unsqueeze_copy(Tensor self, int dim) -> Tensor
inline at::Tensor unsqueeze_copy(const at::Tensor & self, int64_t dim) {
    return at::_ops::unsqueeze_copy::call(self, dim);
}

// aten::unsqueeze_copy.out(Tensor self, int dim, *, Tensor(a!) out) -> Tensor(a!)
inline at::Tensor & unsqueeze_copy_out(at::Tensor & out, const at::Tensor & self, int64_t dim) {
    return at::_ops::unsqueeze_copy_out::call(self, dim, out);
}
// aten::unsqueeze_copy.out(Tensor self, int dim, *, Tensor(a!) out) -> Tensor(a!)
inline at::Tensor & unsqueeze_copy_outf(const at::Tensor & self, int64_t dim, at::Tensor & out) {
    return at::_ops::unsqueeze_copy_out::call(self, dim, out);
}

}
