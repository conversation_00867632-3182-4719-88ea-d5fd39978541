#!/usr/bin/env python3
"""
🆓 Auto-Grade Scribe Open-Source v4.0.0 - Script de Démarrage Final
100% Open-Source • Aucune API payante • Données privées
"""

import os
import sys
import subprocess
import time

def main():
    """Démarrage optimisé pour Auto-Grade Scribe Open-Source"""
    print("🆓 Auto-Grade Scribe Open-Source v4.0.0")
    print("=" * 60)
    print("✨ 100% Open-Source • Aucune API payante • Données privées")
    print("=" * 60)

    # Vérifier que nous sommes dans le bon répertoire
    if not os.path.exists("backend"):
        print("❌ Répertoire 'backend' non trouvé")
        print("💡 Assurez-vous d'être dans le répertoire racine du projet")
        input("Appuyez sur Entrée pour quitter...")
        return

    # Installer pydantic-settings si nécessaire
    try:
        import pydantic_settings
        print("✅ pydantic-settings disponible")
    except ImportError:
        print("📦 Installation de pydantic-settings...")
        try:
            subprocess.run([sys.executable, "-m", "pip", "install", "pydantic-settings==2.1.0"],
                         check=True)
            print("✅ pydantic-settings installé")
        except subprocess.CalledProcessError as e:
            print(f"❌ Erreur installation: {e}")
            input("Appuyez sur Entrée pour quitter...")
            return

    # Créer les répertoires
    directories = ["uploads", "results", "temp", "logs", "models"]
    for directory in directories:
        os.makedirs(directory, exist_ok=True)
    print("📁 Répertoires créés")

    # Tester la configuration
    try:
        sys.path.insert(0, 'backend')
        from core.config import settings
        print("✅ Configuration chargée")
        print(f"🌐 Port: {settings.api_port}")
    except Exception as e:
        print(f"❌ Erreur configuration: {e}")
        input("Appuyez sur Entrée pour quitter...")
        return

    # Instructions PostgreSQL
    print("\n🔍 Vérifications PostgreSQL:")
    print("1. Démarrer PostgreSQL:")
    print("   docker-compose -f docker-compose-simple.yml up -d")
    print("2. Vérifier le statut:")
    print("   docker ps | findstr postgres")

    # Démarrer l'application
    print("\n🚀 Démarrage de l'application...")
    print("🌐 Sera disponible sur: http://127.0.0.1:8001")
    print("📚 Documentation API: http://127.0.0.1:8001/docs")
    print("🛑 Appuyez sur Ctrl+C pour arrêter")
    print("\n" + "=" * 55)

    try:
        # Changer vers le répertoire backend
        os.chdir("backend")

        # Démarrer uvicorn
        subprocess.run([
            sys.executable, "-m", "uvicorn",
            "main:app",
            "--host", "0.0.0.0",
            "--port", "8001",
            "--reload",
            "--log-level", "info"
        ])

    except KeyboardInterrupt:
        print("\n🛑 Application arrêtée par l'utilisateur")
    except FileNotFoundError as e:
        print(f"\n❌ Fichier non trouvé: {e}")
        print("💡 Vérifiez que le fichier main.py existe dans backend/")
    except Exception as e:
        print(f"\n❌ Erreur: {e}")
        print("💡 Solutions possibles:")
        print("1. Vérifiez les dépendances: pip install -r backend/requirements_opensource.txt")
        print("2. Vérifiez PostgreSQL: docker ps | findstr postgres")
        print("3. Consultez les logs pour plus de détails")

    input("\nAppuyez sur Entrée pour quitter...")

if __name__ == "__main__":
    main()
