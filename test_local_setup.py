#!/usr/bin/env python3
"""
Test script to verify the local setup of Auto-Grade Scribe
This script tests the core functionality without requiring <PERSON><PERSON>
"""

import sys
import os
import importlib
from pathlib import Path

def test_imports():
    """Test if all required modules can be imported"""
    print("🧪 Testing Python imports...")
    
    required_modules = [
        'fastapi',
        'uvicorn',
        'sqlalchemy',
        'pydantic',
        'PIL',
        'numpy',
        'requests'
    ]
    
    optional_modules = [
        'cv2',
        'pytesseract',
        'google.generativeai',
        'email_validator',
        'magic'
    ]
    
    failed_required = []
    failed_optional = []
    
    # Test required modules
    for module in required_modules:
        try:
            importlib.import_module(module)
            print(f"  ✅ {module}")
        except ImportError as e:
            print(f"  ❌ {module}: {e}")
            failed_required.append(module)
    
    # Test optional modules
    for module in optional_modules:
        try:
            importlib.import_module(module)
            print(f"  ✅ {module} (optional)")
        except ImportError:
            print(f"  ⚠️ {module} (optional - not available)")
            failed_optional.append(module)
    
    if failed_required:
        print(f"\n❌ Required modules failed: {', '.join(failed_required)}")
        return False
    
    if failed_optional:
        print(f"\n⚠️ Optional modules not available: {', '.join(failed_optional)}")
        print("   Some advanced features may not work, but the application will still function.")
    
    print("\n✅ All required modules imported successfully!")
    return True

def test_database():
    """Test database connection"""
    print("\n🗄️ Testing database connection...")
    
    try:
        # Add backend to path
        backend_path = Path(__file__).parent / "backend"
        sys.path.insert(0, str(backend_path))
        
        from database import engine, Base
        
        # Test connection
        connection = engine.connect()
        connection.close()
        print("  ✅ Database connection successful")
        
        # Test table creation
        Base.metadata.create_all(bind=engine)
        print("  ✅ Database tables created/verified")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Database test failed: {e}")
        return False

def test_models():
    """Test model imports"""
    print("\n📊 Testing model imports...")
    
    try:
        backend_path = Path(__file__).parent / "backend"
        sys.path.insert(0, str(backend_path))
        
        from models import User, Exam, ExamResult, Student
        print("  ✅ Core models imported successfully")
        
        # Test enum imports
        from models import UserRole, ExamStatus, ExamType, GradeStatus
        print("  ✅ Enum types imported successfully")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Model test failed: {e}")
        return False

def test_services():
    """Test service imports"""
    print("\n🔧 Testing service imports...")
    
    try:
        backend_path = Path(__file__).parent / "backend"
        sys.path.insert(0, str(backend_path))
        
        # Test core services
        from services.exam_service import exam_service
        print("  ✅ Exam service imported")
        
        from services.grading_service import enhanced_grading_service
        print("  ✅ Grading service imported")
        
        from services.audit_service import audit_service
        print("  ✅ Audit service imported")
        
        # Test utilities
        from utils.validation import validation_utils
        print("  ✅ Validation utils imported")
        
        from utils.file_utils import file_utils
        print("  ✅ File utils imported")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Service test failed: {e}")
        return False

def test_api():
    """Test API startup"""
    print("\n🌐 Testing API startup...")
    
    try:
        backend_path = Path(__file__).parent / "backend"
        sys.path.insert(0, str(backend_path))
        
        from app import app
        print("  ✅ FastAPI app imported successfully")
        
        # Test if app has the expected routes
        routes = [route.path for route in app.routes]
        expected_routes = ["/", "/api/upload", "/api/v2/exams/upload"]
        
        for route in expected_routes:
            if any(route in r for r in routes):
                print(f"  ✅ Route {route} found")
            else:
                print(f"  ⚠️ Route {route} not found")
        
        return True
        
    except Exception as e:
        print(f"  ❌ API test failed: {e}")
        return False

def test_directories():
    """Test required directories"""
    print("\n📁 Testing directory structure...")
    
    required_dirs = ["uploads", "results", "temp", "logs"]
    
    for dir_name in required_dirs:
        dir_path = Path(dir_name)
        if dir_path.exists():
            print(f"  ✅ {dir_name} directory exists")
        else:
            try:
                dir_path.mkdir(exist_ok=True)
                print(f"  ✅ {dir_name} directory created")
            except Exception as e:
                print(f"  ❌ Failed to create {dir_name}: {e}")
                return False
    
    return True

def test_config():
    """Test configuration"""
    print("\n⚙️ Testing configuration...")
    
    try:
        # Check if .env file exists
        env_path = Path(".env")
        if env_path.exists():
            print("  ✅ .env file found")
        else:
            print("  ⚠️ .env file not found (will use defaults)")
        
        # Test config loading
        backend_path = Path(__file__).parent / "backend"
        sys.path.insert(0, str(backend_path))
        
        from config import settings
        print(f"  ✅ Configuration loaded (Environment: {settings.environment})")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Configuration test failed: {e}")
        return False

def main():
    """Run all tests"""
    print("🚀 Auto-Grade Scribe Local Setup Test")
    print("=" * 50)
    
    tests = [
        ("Python Imports", test_imports),
        ("Database", test_database),
        ("Models", test_models),
        ("Services", test_services),
        ("API", test_api),
        ("Directories", test_directories),
        ("Configuration", test_config)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"  ❌ {test_name} test crashed: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Your local setup is ready.")
        print("\n🚀 To start the application, run:")
        print("   .\\start_local.ps1")
        print("\n📚 Then visit http://localhost:8000/docs for API documentation")
    else:
        print("⚠️ Some tests failed. Please check the errors above.")
        print("\n🔧 Common solutions:")
        print("   - Run: pip install -r backend/requirements.txt")
        print("   - Install Tesseract OCR from: https://github.com/UB-Mannheim/tesseract/wiki")
        print("   - Check your Python version (3.8+ required)")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
