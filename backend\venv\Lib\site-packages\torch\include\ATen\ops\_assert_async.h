#pragma once

// @generated by torchgen/gen.py from Function.h

#include <ATen/Context.h>
#include <ATen/DeviceGuard.h>
#include <ATen/TensorUtils.h>
#include <ATen/TracerMode.h>
#include <ATen/core/Generator.h>
#include <ATen/core/Reduction.h>
#include <ATen/core/Tensor.h>
#include <c10/core/Scalar.h>
#include <c10/core/Storage.h>
#include <c10/core/TensorOptions.h>
#include <c10/util/Deprecated.h>
#include <c10/util/Optional.h>



#include <ATen/ops/_assert_async_ops.h>

namespace at {


// aten::_assert_async(Tensor self) -> ()
inline void _assert_async(const at::Tensor & self) {
    return at::_ops::_assert_async::call(self);
}

// aten::_assert_async.msg(Tensor self, str assert_msg) -> ()
inline void _assert_async(const at::Tensor & self, c10::string_view assert_msg) {
    return at::_ops::_assert_async_msg::call(self, assert_msg);
}

}
