#!/usr/bin/env python3
"""
Diagnostic final pour Auto-Grade Scribe
"""

import sys
import os
import traceback

def test_basic_imports():
    """Test des imports de base"""
    print("🔍 Test des imports de base...")
    
    try:
        import fastapi
        print("✅ FastAPI")
    except Exception as e:
        print(f"❌ FastAPI: {e}")
        return False
    
    try:
        import uvicorn
        print("✅ Uvicorn")
    except Exception as e:
        print(f"❌ Uvicorn: {e}")
        return False
    
    try:
        import pydantic_settings
        print("✅ Pydantic Settings")
    except Exception as e:
        print(f"❌ Pydantic Settings: {e}")
        return False
    
    return True

def test_config_import():
    """Test de l'import de configuration"""
    print("\n🔧 Test de la configuration...")
    
    try:
        sys.path.insert(0, 'backend')
        from core.config import settings
        print("✅ Configuration importée")
        print(f"   Port: {settings.api_port}")
        print(f"   Debug: {settings.debug}")
        return True
    except Exception as e:
        print(f"❌ Configuration: {e}")
        traceback.print_exc()
        return False

def test_database_import():
    """Test de l'import de la base de données"""
    print("\n🗄️ Test de la base de données...")
    
    try:
        sys.path.insert(0, 'backend')
        from core.database import Base, User, UploadedFile
        print("✅ Modèles de base de données importés")
        return True
    except Exception as e:
        print(f"❌ Base de données: {e}")
        traceback.print_exc()
        return False

def test_services_import():
    """Test de l'import des services"""
    print("\n🔧 Test des services...")
    
    services = [
        ("enhanced_ocr_service", "services.enhanced_ocr_service"),
        ("intelligent_grading_service", "services.intelligent_grading_service"),
        ("manual_review_service", "services.manual_review_service"),
        ("audit_service", "services.audit_service")
    ]
    
    sys.path.insert(0, 'backend')
    
    for service_name, module_path in services:
        try:
            __import__(module_path)
            print(f"✅ {service_name}")
        except Exception as e:
            print(f"❌ {service_name}: {e}")
            return False
    
    return True

def test_main_app():
    """Test de l'import de l'application principale"""
    print("\n🚀 Test de l'application principale...")
    
    try:
        sys.path.insert(0, 'backend')
        from main import app
        print("✅ Application FastAPI importée")
        print(f"   Type: {type(app)}")
        return True
    except Exception as e:
        print(f"❌ Application: {e}")
        traceback.print_exc()
        return False

def test_uvicorn_start():
    """Test de démarrage avec uvicorn"""
    print("\n🌐 Test de démarrage uvicorn...")
    
    try:
        import subprocess
        import time
        
        # Tester la commande uvicorn
        cmd = [
            sys.executable, "-m", "uvicorn",
            "main:app",
            "--host", "127.0.0.1",
            "--port", "8001",
            "--check"  # Mode vérification seulement
        ]
        
        result = subprocess.run(
            cmd,
            cwd="backend",
            capture_output=True,
            text=True,
            timeout=10
        )
        
        if result.returncode == 0:
            print("✅ Uvicorn peut démarrer l'application")
            return True
        else:
            print(f"❌ Erreur uvicorn: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        print("⚠️ Timeout - mais l'application semble fonctionner")
        return True
    except Exception as e:
        print(f"❌ Test uvicorn: {e}")
        return False

def main():
    """Fonction principale"""
    print("🔍 DIAGNOSTIC FINAL - Auto-Grade Scribe")
    print("=" * 50)
    
    tests = [
        ("Imports de base", test_basic_imports),
        ("Configuration", test_config_import),
        ("Base de données", test_database_import),
        ("Services", test_services_import),
        ("Application principale", test_main_app),
        ("Démarrage Uvicorn", test_uvicorn_start)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ Erreur inattendue: {e}")
            results.append((test_name, False))
    
    # Résumé final
    print("\n" + "=" * 50)
    print("📊 RÉSUMÉ FINAL")
    print("=" * 50)
    
    passed = 0
    for test_name, result in results:
        status = "✅ RÉUSSI" if result else "❌ ÉCHOUÉ"
        print(f"{test_name:25} : {status}")
        if result:
            passed += 1
    
    total = len(results)
    print(f"\nRésultat global: {passed}/{total} tests réussis")
    
    if passed == total:
        print("\n🎉 TOUS LES TESTS RÉUSSIS!")
        print("✅ L'application est prête à démarrer")
        print("\n🚀 Commandes de démarrage:")
        print("   cd backend")
        print("   python -m uvicorn main:app --host 0.0.0.0 --port 8001 --reload")
        print("\n🌐 L'application sera disponible sur:")
        print("   http://127.0.0.1:8001")
        print("   http://127.0.0.1:8001/docs (documentation)")
    else:
        print(f"\n⚠️ {total - passed} test(s) échoué(s)")
        print("💡 Vérifiez les erreurs ci-dessus avant de démarrer")

if __name__ == "__main__":
    main()
