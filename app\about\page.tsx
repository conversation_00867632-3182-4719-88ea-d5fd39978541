'use client';

import Header from '@/components/Header';
import Footer from '@/components/Footer';
import { Button } from '@/components/ui/button';
import Link from 'next/link';
import { ArrowRight, Users, Code, Brain, Award, BookOpen, School } from 'lucide-react';

export default function About() {
  return (
    <div className="flex min-h-screen flex-col">
      <Header />
      
      {/* Hero Section */}
      <section className="py-20 bg-gradient-to-br from-primary/5 to-accent/5">
        <div className="container mx-auto px-4 text-center">
          <h1 className="text-4xl md:text-5xl font-bold mb-6">À propos d'Auto-Grade Scribe</h1>
          <p className="text-xl text-gray-700 dark:text-gray-300 max-w-3xl mx-auto mb-8">
            Une solution innovante pour transformer l'évaluation des examens grâce à l'intelligence artificielle
          </p>
        </div>
      </section>
      
      {/* Mission Section */}
      <section className="py-16 bg-white dark:bg-gray-900">
        <div className="container mx-auto px-4">
          <div className="flex flex-col md:flex-row items-center gap-12">
            <div className="md:w-1/2">
              <h2 className="text-3xl font-bold mb-6">Notre mission</h2>
              <p className="text-lg text-gray-700 dark:text-gray-300 mb-6">
                Auto-Grade Scribe a été créé avec une vision claire : révolutionner le processus d'évaluation des examens pour les enseignants et les institutions éducatives.
              </p>
              <p className="text-lg text-gray-700 dark:text-gray-300 mb-6">
                Notre mission est de libérer les éducateurs des tâches répétitives de correction, leur permettant de se concentrer sur ce qui compte vraiment : l'enseignement et l'accompagnement personnalisé des étudiants.
              </p>
              <p className="text-lg text-gray-700 dark:text-gray-300">
                Nous croyons que la technologie doit être au service de l'éducation, en offrant des outils qui améliorent non seulement l'efficacité, mais aussi la qualité et l'équité des évaluations.
              </p>
            </div>
            <div className="md:w-1/2 grid grid-cols-2 gap-6">
              <div className="bg-primary/5 p-6 rounded-lg">
                <Users className="h-10 w-10 text-primary mb-4" />
                <h3 className="text-xl font-bold mb-2">Pour les enseignants</h3>
                <p className="text-gray-600 dark:text-gray-400">
                  Gain de temps considérable sur les corrections manuelles
                </p>
              </div>
              <div className="bg-primary/5 p-6 rounded-lg">
                <School className="h-10 w-10 text-primary mb-4" />
                <h3 className="text-xl font-bold mb-2">Pour les étudiants</h3>
                <p className="text-gray-600 dark:text-gray-400">
                  Feedback détaillé et personnalisé pour progresser
                </p>
              </div>
              <div className="bg-primary/5 p-6 rounded-lg">
                <Award className="h-10 w-10 text-primary mb-4" />
                <h3 className="text-xl font-bold mb-2">Évaluations équitables</h3>
                <p className="text-gray-600 dark:text-gray-400">
                  Critères d'évaluation constants et objectifs
                </p>
              </div>
              <div className="bg-primary/5 p-6 rounded-lg">
                <BookOpen className="h-10 w-10 text-primary mb-4" />
                <h3 className="text-xl font-bold mb-2">Analyse pédagogique</h3>
                <p className="text-gray-600 dark:text-gray-400">
                  Identification des points forts et axes d'amélioration
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>
      
      {/* Technology Section */}
      <section className="py-16 bg-gray-50 dark:bg-gray-800">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold mb-12 text-center">Notre technologie</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="bg-white dark:bg-gray-900 p-8 rounded-lg shadow-sm">
              <div className="h-14 w-14 rounded-full bg-primary/10 flex items-center justify-center mb-6">
                <Brain className="h-7 w-7 text-primary" />
              </div>
              <h3 className="text-xl font-bold mb-4">Intelligence Artificielle</h3>
              <p className="text-gray-600 dark:text-gray-400 mb-4">
                Nous utilisons des modèles de langage avancés pour comprendre et évaluer les réponses des étudiants avec une précision comparable à celle d'un enseignant.
              </p>
              <ul className="space-y-2 text-gray-600 dark:text-gray-400">
                <li className="flex items-start">
                  <span className="text-primary mr-2">•</span>
                  <span>Modèles LLM (Large Language Models)</span>
                </li>
                <li className="flex items-start">
                  <span className="text-primary mr-2">•</span>
                  <span>Traitement du langage naturel (NLP)</span>
                </li>
                <li className="flex items-start">
                  <span className="text-primary mr-2">•</span>
                  <span>Analyse sémantique contextuelle</span>
                </li>
              </ul>
            </div>
            
            <div className="bg-white dark:bg-gray-900 p-8 rounded-lg shadow-sm">
              <div className="h-14 w-14 rounded-full bg-primary/10 flex items-center justify-center mb-6">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-7 w-7 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 13h6m-3-3v6m5 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
              </div>
              <h3 className="text-xl font-bold mb-4">OCR de pointe</h3>
              <p className="text-gray-600 dark:text-gray-400 mb-4">
                Notre système de reconnaissance optique des caractères est spécialement optimisé pour l'écriture manuscrite, même dans des conditions difficiles.
              </p>
              <ul className="space-y-2 text-gray-600 dark:text-gray-400">
                <li className="flex items-start">
                  <span className="text-primary mr-2">•</span>
                  <span>Reconnaissance d'écriture manuscrite</span>
                </li>
                <li className="flex items-start">
                  <span className="text-primary mr-2">•</span>
                  <span>Prétraitement d'image avancé</span>
                </li>
                <li className="flex items-start">
                  <span className="text-primary mr-2">•</span>
                  <span>Correction automatique des erreurs OCR</span>
                </li>
              </ul>
            </div>
            
            <div className="bg-white dark:bg-gray-900 p-8 rounded-lg shadow-sm">
              <div className="h-14 w-14 rounded-full bg-primary/10 flex items-center justify-center mb-6">
                <Code className="h-7 w-7 text-primary" />
              </div>
              <h3 className="text-xl font-bold mb-4">Architecture moderne</h3>
              <p className="text-gray-600 dark:text-gray-400 mb-4">
                Notre plateforme est construite avec les technologies les plus récentes pour garantir performance, sécurité et évolutivité.
              </p>
              <ul className="space-y-2 text-gray-600 dark:text-gray-400">
                <li className="flex items-start">
                  <span className="text-primary mr-2">•</span>
                  <span>Frontend: Next.js & React</span>
                </li>
                <li className="flex items-start">
                  <span className="text-primary mr-2">•</span>
                  <span>Backend: API REST avec FastAPI</span>
                </li>
                <li className="flex items-start">
                  <span className="text-primary mr-2">•</span>
                  <span>Déploiement: Docker & Cloud</span>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </section>
      
      {/* Team Section */}
      <section className="py-16 bg-white dark:bg-gray-900">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold mb-12 text-center">Notre équipe</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {/* Team members would go here */}
            <div className="text-center">
              <div className="w-32 h-32 rounded-full bg-gray-200 dark:bg-gray-700 mx-auto mb-4"></div>
              <h3 className="text-xl font-bold">Nom Prénom</h3>
              <p className="text-gray-600 dark:text-gray-400">Fondateur & CEO</p>
            </div>
            <div className="text-center">
              <div className="w-32 h-32 rounded-full bg-gray-200 dark:bg-gray-700 mx-auto mb-4"></div>
              <h3 className="text-xl font-bold">Nom Prénom</h3>
              <p className="text-gray-600 dark:text-gray-400">CTO</p>
            </div>
            <div className="text-center">
              <div className="w-32 h-32 rounded-full bg-gray-200 dark:bg-gray-700 mx-auto mb-4"></div>
              <h3 className="text-xl font-bold">Nom Prénom</h3>
              <p className="text-gray-600 dark:text-gray-400">Lead AI Engineer</p>
            </div>
            <div className="text-center">
              <div className="w-32 h-32 rounded-full bg-gray-200 dark:bg-gray-700 mx-auto mb-4"></div>
              <h3 className="text-xl font-bold">Nom Prénom</h3>
              <p className="text-gray-600 dark:text-gray-400">UX Designer</p>
            </div>
          </div>
        </div>
      </section>
      
      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-br from-primary to-accent text-white">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-6">Prêt à essayer Auto-Grade Scribe ?</h2>
          <p className="text-xl mb-8 max-w-2xl mx-auto opacity-90">
            Rejoignez notre communauté d'éducateurs et transformez votre façon d'évaluer les examens.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button size="lg" variant="secondary" asChild>
              <Link href="/login">Créer un compte</Link>
            </Button>
            <Button size="lg" variant="outline" className="bg-white/10 hover:bg-white/20 text-white border-white/20" asChild>
              <Link href="/contact">Nous contacter</Link>
            </Button>
          </div>
        </div>
      </section>
      
      <Footer />
    </div>
  );
}
