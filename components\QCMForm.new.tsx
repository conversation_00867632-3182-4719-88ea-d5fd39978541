"use client"

import React, { useState, useEffect } from 'react';
import { Upload, X, FileText, Check, Loader2, Plus, Minus } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Card, CardContent } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { cn } from '@/lib/utils';
import { useToast } from '@/components/ui/use-toast';
import { useRouter } from 'next/navigation';

// API URL from environment or default
const API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://127.0.0.1:8000';

// Function to test API connectivity
const testApiConnection = async () => {
  try {
    const response = await fetch(`${API_URL}/`);
    if (response.ok) {
      console.log('Backend API is accessible');
      return true;
    }
  } catch (error) {
    console.error('Error connecting to API:', error);
  }
  return false;
};

const QCMForm: React.FC = () => {
  const [isDragging, setIsDragging] = useState(false);
  const [file, setFile] = useState<File | null>(null);
  const [imagePreview, setImagePreview] = useState<string | null>(null);
  const [uploading, setUploading] = useState(false);
  const [progress, setProgress] = useState(0);
  const [processing, setProcessing] = useState(false);
  const [processingStep, setProcessingStep] = useState(0);
  const [questions, setQuestions] = useState([{ id: 1, answer: 'A' }]);
  const [apiConnected, setApiConnected] = useState<boolean | null>(null);
  const { toast } = useToast();
  const router = useRouter();

  // Check API connectivity when component mounts
  useEffect(() => {
    const checkConnection = async () => {
      const isConnected = await testApiConnection();
      setApiConnected(isConnected);

      if (!isConnected) {
        toast({
          title: "Problème de connexion",
          description: "Impossible de se connecter au serveur backend. Veuillez vérifier que le serveur est démarré.",
          variant: "destructive"
        });
      }
    };

    checkConnection();
  }, [toast]);

  const handleDragEnter = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(false);
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (!isDragging) setIsDragging(true);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(false);

    const droppedFiles = Array.from(e.dataTransfer.files);
    if (droppedFiles.length > 0) {
      handleFile(droppedFiles[0]);
    }
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      handleFile(e.target.files[0]);
    }
  };

  const handleFile = (newFile: File) => {
    // Only accept images
    if (!newFile.type.includes('image/')) {
      toast({
        title: "Type de fichier invalide",
        description: "Seules les images sont acceptées pour les QCM",
        variant: "destructive"
      });
      return;
    }

    // Create image preview
    const reader = new FileReader();
    reader.onload = (e) => {
      if (e.target?.result) {
        setImagePreview(e.target.result as string);
      }
    };
    reader.readAsDataURL(newFile);

    setFile(newFile);

    // Show success toast
    toast({
      title: "Image téléchargée",
      description: "L'image a été téléchargée avec succès. Vous pouvez maintenant entrer les réponses correctes.",
      variant: "default"
    });
  };

  const removeFile = () => {
    setFile(null);
    setImagePreview(null);
  };

  const addQuestion = () => {
    const newId = questions.length > 0 ? Math.max(...questions.map(q => q.id)) + 1 : 1;
    setQuestions([...questions, { id: newId, answer: 'A' }]);
  };

  const removeQuestion = (id: number) => {
    if (questions.length > 1) {
      setQuestions(questions.filter(q => q.id !== id));
    }
  };

  const updateQuestionAnswer = (id: number, answer: string) => {
    setQuestions(questions.map(q => q.id === id ? { ...q, answer } : q));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!file) {
      toast({
        title: "Fichier manquant",
        description: "Veuillez télécharger une image de QCM",
        variant: "destructive"
      });
      return;
    }

    setUploading(true);
    let uploadedFileId = null;

    try {
      // Upload file
      const formData = new FormData();
      formData.append('file', file);

      const response = await fetch(`${API_URL}/api/upload`, {
        method: 'POST',
        body: formData
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.detail || `Upload failed with status: ${response.status}`);
      }

      const data = await response.json();
      uploadedFileId = data.file_id;
      setProgress(100);

      // Set processing state after upload
      setUploading(false);
      if (uploadedFileId) {
        setProcessing(true); // Show processing indicator
        setProcessingStep(1); // Preprocessing step
        setProgress(0); // Reset progress for processing step

        // Simulate preprocessing progress
        const preprocessingTimer = setTimeout(() => {
          setProcessingStep(2); // OCR step

          // Prepare correct answers in the format expected by the backend
          const correctAnswers = questions.reduce((acc, q) => {
            acc[q.id.toString()] = q.answer;
            return acc;
          }, {} as Record<string, string>);

          // Call QCM grading API with a slight delay to show the processing steps
          setTimeout(async () => {
            try {
              console.log(`Sending QCM grading request to ${API_URL}/api/grade/enhanced-qcm/noauth with file_id: ${uploadedFileId}`);

              setProcessingStep(3); // Analysis step

              const gradeResponse = await fetch(`${API_URL}/api/grade/enhanced-qcm/noauth`, {
                method: 'POST',
                headers: {
                  'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                  file_id: uploadedFileId,
                  correct_answers: correctAnswers
                })
              });

              console.log(`QCM grade response status: ${gradeResponse.status}`);

              // Get response data
              let responseData = {};
              try {
                responseData = await gradeResponse.json();
                console.log("QCM grade response data:", responseData);
              } catch (e) {
                console.error("Failed to parse QCM grade response:", e);
                throw new Error("Erreur lors de l'analyse de la réponse du serveur");
              }

              // Check if response is OK
              if (!gradeResponse.ok) {
                throw new Error(
                  responseData.detail ||
                  `QCM grading failed with status: ${gradeResponse.status}`
                );
              }

              // Final processing step
              setProcessingStep(4);

              // Short delay before showing success and redirecting
              setTimeout(() => {
                // Grading successful
                toast({
                  title: "Correction terminée",
                  description: "Le QCM a été analysé et corrigé avec succès.",
                  variant: "default"
                });

                setProcessing(false); // Hide processing indicator
                setProcessingStep(0); // Reset processing step

                // Navigate to results page
                router.push(`/results/${uploadedFileId}`);
              }, 1000);

            } catch (error) {
              console.error('API error:', error);

              // Get more detailed error information
              let errorMessage = "Une erreur est survenue lors de l'analyse du QCM";

              if (error instanceof Error) {
                errorMessage = error.message;
                console.error('Error details:', error.stack);
              }

              // Show more helpful error message
              toast({
                title: "Échec du traitement",
                description: `${errorMessage}. Veuillez vérifier que le serveur backend est en cours d'exécution et accessible.`,
                variant: "destructive"
              });

              // Add a second toast with troubleshooting tips
              setTimeout(() => {
                toast({
                  title: "Conseils de dépannage",
                  description: "Assurez-vous que le serveur backend est démarré avec 'uvicorn app:app --reload --host 0.0.0.0 --port 8000'",
                  variant: "default"
                });
              }, 1000);

              setProcessing(false);
              setProcessingStep(0);
            }
          }, 1000);
        }, 1000);
      } else {
        throw new Error("Upload succeeded but no file ID was returned.");
      }

    } catch (error) {
      console.error('Processing error:', error);
      toast({
        title: "Échec du traitement",
        description: error instanceof Error ? error.message : "Une erreur inconnue est survenue",
        variant: "destructive"
      });
    } finally {
      setUploading(false);
      setProcessing(false);
      setProgress(0);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="w-full space-y-6">
      <div className="space-y-4">
        <Label className="text-base font-medium">Image du QCM</Label>
        <div
          className={cn(
            "border-2 border-dashed rounded-lg p-8 text-center transition-colors",
            isDragging ? "border-primary bg-primary/5" : "border-gray-300 dark:border-gray-700",
          )}
          onDragEnter={handleDragEnter}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onDrop={handleDrop}
        >
          <div className="flex flex-col items-center justify-center space-y-4">
            <div className="p-4 bg-secondary rounded-full">
              <Upload className="h-8 w-8 text-primary" />
            </div>
            <div>
              <h3 className="text-lg font-medium">Glissez-déposez l'image du QCM</h3>
              <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                Téléchargez une image claire de la feuille de réponses
              </p>
            </div>
            <div>
              <label htmlFor="qcm-file-upload">
                <Button type="button" variant="outline" className="mt-2" onClick={() => document.getElementById('qcm-file-upload')?.click()}>
                  Sélectionner une image
                </Button>
                <input
                  id="qcm-file-upload"
                  type="file"
                  accept="image/*"
                  onChange={handleFileChange}
                  className="hidden"
                />
              </label>
            </div>
            <div className="text-xs text-gray-500 dark:text-gray-400">
              Images jusqu'à 10MB (JPG, PNG, etc.)
            </div>
          </div>
        </div>
      </div>

      {file && (
        <div className="space-y-4">
          <Card className="overflow-hidden">
            <CardContent className="p-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className="p-2 bg-secondary rounded-md">
                    <FileText className="h-5 w-5 text-primary" />
                  </div>
                  <div>
                    <p className="font-medium truncate max-w-[200px] sm:max-w-[400px]">{file.name}</p>
                    <p className="text-xs text-gray-500">{(file.size / 1024 / 1024).toFixed(2)} MB</p>
                  </div>
                </div>
                <Button variant="ghost" size="sm" onClick={removeFile} disabled={uploading || processing}>
                  <X className="h-4 w-4" />
                </Button>
              </div>
            </CardContent>
          </Card>

          {imagePreview && (
            <div className="mt-4">
              <Label className="text-base font-medium mb-2 block">Aperçu de l'image</Label>
              <div className="border rounded-md overflow-hidden">
                <img
                  src={imagePreview}
                  alt="Aperçu du QCM"
                  className="w-full object-contain max-h-[300px]"
                />
              </div>
              <p className="text-xs text-muted-foreground mt-2">
                Vérifiez que l'image est claire et que les réponses sont bien visibles
              </p>
            </div>
          )}
        </div>
      )}

      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <Label className="text-base font-medium">Réponses correctes</Label>
          <Button
            type="button"
            variant="outline"
            size="sm"
            onClick={addQuestion}
            className="flex items-center gap-1"
          >
            <Plus className="h-4 w-4" />
            <span>Ajouter une question</span>
          </Button>
        </div>

        <div className="space-y-3">
          {questions.map((question) => (
            <div key={question.id} className="flex items-center gap-3">
              <div className="w-16">
                <Label htmlFor={`question-${question.id}`} className="text-sm">
                  Question {question.id}
                </Label>
              </div>
              <Select
                value={question.answer}
                onValueChange={(value) => updateQuestionAnswer(question.id, value)}
              >
                <SelectTrigger className="w-24">
                  <SelectValue placeholder="Réponse" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="A">A</SelectItem>
                  <SelectItem value="B">B</SelectItem>
                  <SelectItem value="C">C</SelectItem>
                  <SelectItem value="D">D</SelectItem>
                  <SelectItem value="E">E</SelectItem>
                </SelectContent>
              </Select>
              <Button
                type="button"
                variant="ghost"
                size="sm"
                onClick={() => removeQuestion(question.id)}
                disabled={questions.length <= 1}
              >
                <Minus className="h-4 w-4 text-gray-500" />
              </Button>
            </div>
          ))}
        </div>
      </div>

      <div>
        {uploading || processing ? (
          <div className="space-y-4">
            <Progress value={uploading ? progress : (processingStep / 4) * 100} />
            <p className="text-sm text-center text-gray-500">
              {uploading
                ? `Téléchargement... ${Math.round(progress)}%`
                : 'Analyse du QCM en cours... Cette opération peut prendre quelques instants'}
            </p>

            {processing && (
              <div className="bg-secondary/50 rounded-lg p-4 border">
                <h4 className="font-medium text-center mb-4">Processus de correction</h4>
                <div className="space-y-3">
                  <div className="flex items-center">
                    <div className={`w-6 h-6 rounded-full flex items-center justify-center mr-2 ${
                      processingStep >= 1 ? "bg-green-100" : "bg-gray-100"
                    }`}>
                      {processingStep > 1 ? (
                        <Check className="h-3 w-3 text-green-600" />
                      ) : processingStep === 1 ? (
                        <Loader2 className="h-3 w-3 animate-spin text-primary" />
                      ) : (
                        <span className="h-3 w-3 text-gray-400">1</span>
                      )}
                    </div>
                    <p className={`text-sm ${processingStep >= 1 ? "text-foreground" : "text-muted-foreground"}`}>
                      Prétraitement de l'image
                    </p>
                  </div>

                  <div className="flex items-center">
                    <div className={`w-6 h-6 rounded-full flex items-center justify-center mr-2 ${
                      processingStep >= 2 ? "bg-green-100" : "bg-gray-100"
                    }`}>
                      {processingStep > 2 ? (
                        <Check className="h-3 w-3 text-green-600" />
                      ) : processingStep === 2 ? (
                        <Loader2 className="h-3 w-3 animate-spin text-primary" />
                      ) : (
                        <span className="h-3 w-3 text-gray-400">2</span>
                      )}
                    </div>
                    <p className={`text-sm ${processingStep >= 2 ? "text-foreground" : "text-muted-foreground"}`}>
                      Détection des cases et des marques
                    </p>
                  </div>

                  <div className="flex items-center">
                    <div className={`w-6 h-6 rounded-full flex items-center justify-center mr-2 ${
                      processingStep >= 3 ? "bg-green-100" : "bg-gray-100"
                    }`}>
                      {processingStep > 3 ? (
                        <Check className="h-3 w-3 text-green-600" />
                      ) : processingStep === 3 ? (
                        <Loader2 className="h-3 w-3 animate-spin text-primary" />
                      ) : (
                        <span className="h-3 w-3 text-gray-400">3</span>
                      )}
                    </div>
                    <p className={`text-sm ${processingStep >= 3 ? "text-foreground" : "text-muted-foreground"}`}>
                      Analyse des réponses par IA
                    </p>
                  </div>

                  <div className="flex items-center">
                    <div className={`w-6 h-6 rounded-full flex items-center justify-center mr-2 ${
                      processingStep >= 4 ? "bg-green-100" : "bg-gray-100"
                    }`}>
                      {processingStep > 4 ? (
                        <Check className="h-3 w-3 text-green-600" />
                      ) : processingStep === 4 ? (
                        <Loader2 className="h-3 w-3 animate-spin text-primary" />
                      ) : (
                        <span className="h-3 w-3 text-gray-400">4</span>
                      )}
                    </div>
                    <p className={`text-sm ${processingStep >= 4 ? "text-foreground" : "text-muted-foreground"}`}>
                      Génération des résultats et du feedback
                    </p>
                  </div>
                </div>
              </div>
            )}
          </div>
        ) : (
          <>
            <Button
              type="submit"
              className="w-full"
              disabled={!file || questions.length === 0 || apiConnected === false}
            >
              {apiConnected === false
                ? "Serveur backend non disponible"
                : "Analyser et corriger le QCM"}
            </Button>

            {apiConnected === false && (
              <p className="text-sm text-center text-red-500 mt-2">
                Le serveur backend n'est pas accessible. Veuillez démarrer le serveur avec la commande:<br />
                <code className="bg-gray-100 dark:bg-gray-800 px-2 py-1 rounded text-xs">
                  uvicorn app:app --reload --host 0.0.0.0 --port 8000
                </code>
              </p>
            )}
          </>
        )}
      </div>
    </form>
  );
};

export default QCMForm;
