"""
Service de Correction Intelligent pour Auto-Grade Scribe avec Solutions Open-Source
Utilise des modèles open-source pour une correction avancée avec fuzzy matching et évaluation contextuelle
"""

import os
import logging
import asyncio
import re
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime
from difflib import SequenceMatcher
import json

logger = logging.getLogger("auto-grade-scribe.intelligent-grading")

class IntelligentGradingService:
    """Service de correction intelligent avec IA"""

    def __init__(self):
        self.fuzzy_threshold = 0.7
        self.partial_credit_threshold = 0.5
        self.ai_providers = {}

        # Initialiser les providers d'IA open-source
        self._init_sentence_transformers()
        self._init_google_ai()
        self._init_huggingface_models()

        logger.info("Intelligent Grading Service initialized with open-source models")

    def _init_sentence_transformers(self):
        """Initialiser Sentence Transformers pour la similarité sémantique (gratuit)"""
        try:
            from sentence_transformers import SentenceTransformer

            # Modèles recommandés pour la similarité textuelle
            self.ai_providers['sentence_transformers'] = {
                'available': True,
                'models': {
                    'similarity': 'all-MiniLM-L6-v2',  # Rapide et efficace
                    'multilingual': 'paraphrase-multilingual-MiniLM-L12-v2',  # Support multilingue
                    'semantic': 'all-mpnet-base-v2'  # Haute qualité
                }
            }
            logger.info("Sentence Transformers initialized for semantic similarity")
        except ImportError:
            logger.warning("Sentence Transformers not available")
            self.ai_providers['sentence_transformers'] = {'available': False}

    def _init_huggingface_models(self):
        """Initialiser les modèles Hugging Face pour l'analyse de texte (gratuit)"""
        try:
            from transformers import pipeline

            # Modèles pour différentes tâches
            self.ai_providers['huggingface'] = {
                'available': True,
                'models': {
                    'sentiment': 'cardiffnlp/twitter-roberta-base-sentiment-latest',
                    'text_classification': 'microsoft/DialoGPT-medium',
                    'question_answering': 'distilbert-base-cased-distilled-squad',
                    'text_similarity': 'sentence-transformers/all-MiniLM-L6-v2'
                }
            }
            logger.info("Hugging Face models initialized for text analysis")
        except ImportError:
            logger.warning("Transformers library not available")
            self.ai_providers['huggingface'] = {'available': False}

    def _init_google_ai(self):
        """Initialiser Google AI pour la correction (optionnel)"""
        try:
            import google.generativeai as genai
            api_key = os.getenv('GOOGLE_API_KEY')
            if api_key:
                genai.configure(api_key=api_key)
                self.ai_providers['google'] = {
                    'available': True,
                    'model': genai.GenerativeModel('gemini-pro'),
                    'name': 'gemini-pro'
                }
                logger.info("Google AI initialized for intelligent grading")
            else:
                logger.warning("Google AI API key not found")
                self.ai_providers['google'] = {'available': False}
        except ImportError:
            logger.warning("Google AI not available")
            self.ai_providers['google'] = {'available': False}

    async def grade_exam_intelligent(
        self,
        exam_id: str,
        extracted_text: str,
        correct_answers: Dict[str, Any],
        exam_type: str = 'qcm',
        grading_config: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Corriger un examen de manière intelligente

        Args:
            exam_id: ID de l'examen
            extracted_text: Texte extrait par OCR
            correct_answers: Réponses correctes
            exam_type: Type d'examen ('qcm', 'open_ended', 'mixed')
            grading_config: Configuration de correction

        Returns:
            Résultats de correction détaillés
        """
        try:
            start_time = datetime.now()

            # Configuration par défaut
            config = grading_config or {}

            # Analyser le texte extrait pour identifier les réponses
            student_answers = await self._extract_student_answers(extracted_text, exam_type)

            # Corriger selon le type d'examen
            if exam_type == 'qcm':
                grading_result = await self._grade_qcm_intelligent(
                    student_answers, correct_answers, config
                )
            elif exam_type == 'open_ended':
                grading_result = await self._grade_open_ended_intelligent(
                    student_answers, correct_answers, config
                )
            else:  # mixed
                grading_result = await self._grade_mixed_intelligent(
                    student_answers, correct_answers, config
                )

            # Calculer le score final
            final_score = self._calculate_final_score(grading_result, config)

            # Générer un feedback détaillé
            feedback = await self._generate_detailed_feedback(
                grading_result, student_answers, correct_answers
            )

            processing_time = (datetime.now() - start_time).total_seconds()

            return {
                'success': True,
                'exam_id': exam_id,
                'exam_type': exam_type,
                'student_answers': student_answers,
                'correct_answers': correct_answers,
                'grading_result': grading_result,
                'final_score': final_score,
                'feedback': feedback,
                'processing_time': processing_time,
                'requires_manual_review': final_score.get('confidence', 1.0) < 0.7,
                'timestamp': datetime.now().isoformat()
            }

        except Exception as e:
            logger.error(f"Error in intelligent grading: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'exam_id': exam_id,
                'requires_manual_review': True
            }

    async def _extract_student_answers(
        self,
        extracted_text: str,
        exam_type: str
    ) -> Dict[str, Any]:
        """Extraire les réponses de l'étudiant du texte OCR"""
        try:
            if exam_type == 'qcm':
                return await self._extract_qcm_answers(extracted_text)
            elif exam_type == 'open_ended':
                return await self._extract_open_ended_answers(extracted_text)
            else:  # mixed
                return await self._extract_mixed_answers(extracted_text)

        except Exception as e:
            logger.error(f"Error extracting student answers: {str(e)}")
            return {}

    async def _extract_qcm_answers(self, text: str) -> Dict[str, str]:
        """Extraire les réponses QCM"""
        answers = {}

        # Patterns pour détecter les réponses QCM
        patterns = [
            r'(?:Question|Q)\s*(\d+)\s*[:\-]?\s*([A-E])',  # Question 1: A
            r'(\d+)\s*[:\-\.]?\s*([A-E])',                 # 1. A
            r'([A-E])\s*(?:pour|for)\s*(?:question|Q)\s*(\d+)',  # A pour question 1
        ]

        for pattern in patterns:
            matches = re.finditer(pattern, text, re.IGNORECASE)
            for match in matches:
                if len(match.groups()) == 2:
                    q_num, answer = match.groups()
                    answers[f"question_{q_num}"] = answer.upper()

        # Si aucun pattern ne fonctionne, utiliser l'IA open-source
        if not answers and self.ai_providers.get('huggingface', {}).get('available'):
            answers = await self._extract_answers_with_opensource_ai(text, 'qcm')

        return answers

    async def _extract_open_ended_answers(self, text: str) -> Dict[str, str]:
        """Extraire les réponses ouvertes"""
        answers = {}

        # Diviser le texte en sections par question
        question_patterns = [
            r'(?:Question|Q)\s*(\d+)\s*[:\-]?\s*(.*?)(?=(?:Question|Q)\s*\d+|$)',
            r'(\d+)\s*[:\-\.]?\s*(.*?)(?=\d+\s*[:\-\.]|$)',
        ]

        for pattern in question_patterns:
            matches = re.finditer(pattern, text, re.IGNORECASE | re.DOTALL)
            for match in matches:
                q_num, answer_text = match.groups()
                answers[f"question_{q_num}"] = answer_text.strip()

        # Utiliser l'IA open-source si nécessaire
        if not answers and self.ai_providers.get('huggingface', {}).get('available'):
            answers = await self._extract_answers_with_opensource_ai(text, 'open_ended')

        return answers

    async def _extract_mixed_answers(self, text: str) -> Dict[str, str]:
        """Extraire les réponses mixtes (QCM + ouvertes)"""
        # Combiner les deux méthodes
        qcm_answers = await self._extract_qcm_answers(text)
        open_answers = await self._extract_open_ended_answers(text)

        # Fusionner les résultats
        all_answers = {**qcm_answers, **open_answers}

        return all_answers

    async def _extract_answers_with_opensource_ai(self, text: str, exam_type: str) -> Dict[str, str]:
        """Utiliser l'IA open-source pour extraire les réponses"""
        try:
            # Utiliser des patterns avancés avec regex pour l'extraction
            answers = {}

            if exam_type == 'qcm':
                # Patterns plus sophistiqués pour QCM
                advanced_patterns = [
                    r'(?:Question|Q|Réponse)\s*(\d+)\s*[:\-\.]?\s*([A-E])',
                    r'(\d+)\s*[:\-\.]?\s*([A-E])\b',
                    r'([A-E])\s*(?:pour|for|to|question)\s*(\d+)',
                    r'(?:Answer|Ans)\s*(\d+)\s*[:\-]?\s*([A-E])'
                ]

                for pattern in advanced_patterns:
                    matches = re.finditer(pattern, text, re.IGNORECASE)
                    for match in matches:
                        groups = match.groups()
                        if len(groups) == 2:
                            q_num, answer = groups
                            # Vérifier si c'est dans l'ordre question-réponse ou réponse-question
                            if answer.upper() in 'ABCDE':
                                answers[f"question_{q_num}"] = answer.upper()
                            elif q_num.upper() in 'ABCDE':
                                answers[f"question_{answer}"] = q_num.upper()

            elif exam_type == 'open_ended':
                # Utiliser la similarité sémantique pour identifier les sections
                if self.ai_providers.get('sentence_transformers', {}).get('available'):
                    answers = await self._extract_with_semantic_analysis(text)
                else:
                    # Fallback vers patterns regex avancés
                    section_patterns = [
                        r'(?:Question|Q)\s*(\d+)\s*[:\-]?\s*(.*?)(?=(?:Question|Q)\s*\d+|$)',
                        r'(\d+)\s*[:\-\.]?\s*(.*?)(?=\d+\s*[:\-\.]|$)',
                        r'(?:Réponse|Answer)\s*(\d+)\s*[:\-]?\s*(.*?)(?=(?:Réponse|Answer)\s*\d+|$)'
                    ]

                    for pattern in section_patterns:
                        matches = re.finditer(pattern, text, re.IGNORECASE | re.DOTALL)
                        for match in matches:
                            q_num, answer_text = match.groups()
                            if len(answer_text.strip()) > 5:  # Filtrer les réponses trop courtes
                                answers[f"question_{q_num}"] = answer_text.strip()

            return answers

        except Exception as e:
            logger.error(f"Error extracting answers with open-source AI: {str(e)}")
            return {}

    async def _extract_with_semantic_analysis(self, text: str) -> Dict[str, str]:
        """Utiliser l'analyse sémantique pour extraire les réponses"""
        try:
            from sentence_transformers import SentenceTransformer

            # Charger le modèle de similarité
            model_name = self.ai_providers['sentence_transformers']['models']['similarity']
            model = SentenceTransformer(model_name)

            # Diviser le texte en phrases
            sentences = re.split(r'[.!?]+', text)
            sentences = [s.strip() for s in sentences if len(s.strip()) > 10]

            # Mots-clés pour identifier les questions et réponses
            question_keywords = ["question", "q", "what", "how", "why", "when", "where"]
            answer_keywords = ["answer", "response", "because", "the result", "solution"]

            # Encoder les phrases
            sentence_embeddings = model.encode(sentences)
            question_embeddings = model.encode(question_keywords)
            answer_embeddings = model.encode(answer_keywords)

            # Identifier les sections de questions et réponses par similarité
            answers = {}
            current_question = None

            for i, sentence in enumerate(sentences):
                # Calculer la similarité avec les mots-clés de question
                question_similarity = max([
                    model.encode([sentence]).dot(q_emb.reshape(-1, 1)).max()
                    for q_emb in question_embeddings
                ])

                # Calculer la similarité avec les mots-clés de réponse
                answer_similarity = max([
                    model.encode([sentence]).dot(a_emb.reshape(-1, 1)).max()
                    for a_emb in answer_embeddings
                ])

                # Identifier les numéros de question
                question_match = re.search(r'(?:question|q)\s*(\d+)', sentence, re.IGNORECASE)
                if question_match:
                    current_question = question_match.group(1)
                elif current_question and answer_similarity > question_similarity:
                    answers[f"question_{current_question}"] = sentence
                    current_question = None

            return answers

        except Exception as e:
            logger.error(f"Error in semantic analysis: {str(e)}")
            return {}

    async def _grade_qcm_intelligent(
        self,
        student_answers: Dict[str, str],
        correct_answers: Dict[str, str],
        config: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Corriger un QCM de manière intelligente"""
        results = {}
        total_questions = len(correct_answers)
        correct_count = 0

        for question_id, correct_answer in correct_answers.items():
            student_answer = student_answers.get(question_id, '').strip().upper()
            correct_answer = correct_answer.strip().upper()

            # Correction exacte
            if student_answer == correct_answer:
                results[question_id] = {
                    'student_answer': student_answer,
                    'correct_answer': correct_answer,
                    'is_correct': True,
                    'score': 1.0,
                    'confidence': 1.0,
                    'feedback': 'Correct'
                }
                correct_count += 1

            # Fuzzy matching pour les erreurs de reconnaissance OCR
            elif student_answer and self._fuzzy_match(student_answer, correct_answer) > 0.8:
                results[question_id] = {
                    'student_answer': student_answer,
                    'correct_answer': correct_answer,
                    'is_correct': True,
                    'score': 0.9,  # Légère pénalité pour incertitude OCR
                    'confidence': 0.8,
                    'feedback': 'Correct (avec correction OCR)'
                }
                correct_count += 0.9

            else:
                results[question_id] = {
                    'student_answer': student_answer,
                    'correct_answer': correct_answer,
                    'is_correct': False,
                    'score': 0.0,
                    'confidence': 1.0 if student_answer else 0.5,  # Faible confiance si pas de réponse
                    'feedback': 'Incorrect' if student_answer else 'Pas de réponse détectée'
                }

        return {
            'type': 'qcm',
            'results': results,
            'summary': {
                'total_questions': total_questions,
                'correct_answers': correct_count,
                'percentage': (correct_count / total_questions * 100) if total_questions > 0 else 0
            }
        }

    async def _grade_open_ended_intelligent(
        self,
        student_answers: Dict[str, str],
        correct_answers: Dict[str, str],
        config: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Corriger des questions ouvertes avec IA"""
        results = {}

        for question_id, correct_answer in correct_answers.items():
            student_answer = student_answers.get(question_id, '').strip()

            if not student_answer:
                results[question_id] = {
                    'student_answer': student_answer,
                    'correct_answer': correct_answer,
                    'score': 0.0,
                    'confidence': 0.5,
                    'feedback': 'Pas de réponse détectée',
                    'requires_manual_review': True
                }
                continue

            # Utiliser l'IA open-source pour évaluer la réponse
            ai_evaluation = await self._evaluate_open_answer_with_opensource_ai(
                student_answer, correct_answer, question_id
            )

            results[question_id] = {
                'student_answer': student_answer,
                'correct_answer': correct_answer,
                'score': ai_evaluation.get('score', 0.0),
                'confidence': ai_evaluation.get('confidence', 0.5),
                'feedback': ai_evaluation.get('feedback', 'Évaluation automatique'),
                'ai_reasoning': ai_evaluation.get('reasoning', ''),
                'requires_manual_review': ai_evaluation.get('confidence', 0.5) < 0.7
            }

        return {
            'type': 'open_ended',
            'results': results,
            'summary': {
                'total_questions': len(correct_answers),
                'avg_score': sum(r['score'] for r in results.values()) / len(results) if results else 0,
                'requires_review_count': sum(1 for r in results.values() if r.get('requires_manual_review', False))
            }
        }

    async def _evaluate_open_answer_with_opensource_ai(
        self,
        student_answer: str,
        correct_answer: str,
        question_id: str
    ) -> Dict[str, Any]:
        """Évaluer une réponse ouverte avec l'IA open-source"""
        try:
            # Utiliser Sentence Transformers pour la similarité sémantique
            if self.ai_providers.get('sentence_transformers', {}).get('available'):
                semantic_score = await self._calculate_semantic_similarity(student_answer, correct_answer)
            else:
                semantic_score = 0.0

            # Utiliser fuzzy matching pour la similarité textuelle
            textual_similarity = self._fuzzy_match(student_answer, correct_answer)

            # Analyser la longueur et la structure
            length_score = self._analyze_answer_length(student_answer, correct_answer)

            # Analyser les mots-clés importants
            keyword_score = self._analyze_keywords(student_answer, correct_answer)

            # Combiner les scores avec des poids
            weights = {
                'semantic': 0.4,
                'textual': 0.3,
                'length': 0.1,
                'keywords': 0.2
            }

            final_score = (
                semantic_score * weights['semantic'] +
                textual_similarity * weights['textual'] +
                length_score * weights['length'] +
                keyword_score * weights['keywords']
            )

            # Calculer la confiance basée sur la cohérence des scores
            score_variance = self._calculate_score_variance([
                semantic_score, textual_similarity, length_score, keyword_score
            ])
            confidence = max(0.3, 1.0 - score_variance)

            # Générer un feedback détaillé
            feedback = self._generate_detailed_feedback(
                final_score, semantic_score, textual_similarity, keyword_score
            )

            return {
                'score': min(1.0, max(0.0, final_score)),
                'confidence': confidence,
                'feedback': feedback,
                'reasoning': f'Analyse multi-critères: sémantique={semantic_score:.2f}, textuelle={textual_similarity:.2f}, mots-clés={keyword_score:.2f}',
                'details': {
                    'semantic_similarity': semantic_score,
                    'textual_similarity': textual_similarity,
                    'length_score': length_score,
                    'keyword_score': keyword_score
                }
            }

        except Exception as e:
            logger.error(f"Error in open-source AI evaluation: {str(e)}")
            # Fallback vers fuzzy matching simple
            similarity = self._fuzzy_match(student_answer, correct_answer)
            return {
                'score': similarity,
                'confidence': 0.4,
                'feedback': f'Évaluation de secours (similarité: {similarity:.1%})',
                'reasoning': f'Erreur IA open-source: {str(e)}'
            }

    async def _calculate_semantic_similarity(self, text1: str, text2: str) -> float:
        """Calculer la similarité sémantique avec Sentence Transformers"""
        try:
            from sentence_transformers import SentenceTransformer
            from sklearn.metrics.pairwise import cosine_similarity
            import numpy as np

            # Charger le modèle
            model_name = self.ai_providers['sentence_transformers']['models']['semantic']
            model = SentenceTransformer(model_name)

            # Encoder les textes
            embeddings = model.encode([text1, text2])

            # Calculer la similarité cosinus
            similarity = cosine_similarity([embeddings[0]], [embeddings[1]])[0][0]

            return float(similarity)

        except Exception as e:
            logger.error(f"Error calculating semantic similarity: {str(e)}")
            return 0.0

    def _analyze_answer_length(self, student_answer: str, correct_answer: str) -> float:
        """Analyser la longueur de la réponse"""
        try:
            student_len = len(student_answer.split())
            correct_len = len(correct_answer.split())

            if correct_len == 0:
                return 1.0 if student_len == 0 else 0.5

            # Score basé sur la proportion de longueur
            length_ratio = student_len / correct_len

            # Optimal entre 0.5 et 1.5 fois la longueur de la réponse correcte
            if 0.5 <= length_ratio <= 1.5:
                return 1.0
            elif length_ratio < 0.5:
                return length_ratio * 2  # Pénalité pour réponse trop courte
            else:
                return max(0.3, 1.5 / length_ratio)  # Pénalité pour réponse trop longue

        except Exception as e:
            logger.error(f"Error analyzing answer length: {str(e)}")
            return 0.5

    def _analyze_keywords(self, student_answer: str, correct_answer: str) -> float:
        """Analyser la présence de mots-clés importants"""
        try:
            import re
            from collections import Counter

            # Nettoyer et tokeniser
            def clean_and_tokenize(text):
                # Supprimer la ponctuation et convertir en minuscules
                cleaned = re.sub(r'[^\w\s]', ' ', text.lower())
                # Supprimer les mots vides courants
                stop_words = {'le', 'la', 'les', 'un', 'une', 'des', 'de', 'du', 'et', 'ou', 'mais', 'donc', 'car', 'ni', 'or',
                             'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 'is', 'are'}
                words = [word for word in cleaned.split() if len(word) > 2 and word not in stop_words]
                return words

            student_words = clean_and_tokenize(student_answer)
            correct_words = clean_and_tokenize(correct_answer)

            if not correct_words:
                return 1.0 if not student_words else 0.5

            # Compter les mots communs
            student_counter = Counter(student_words)
            correct_counter = Counter(correct_words)

            # Calculer le score de recouvrement
            common_words = set(student_words) & set(correct_words)

            if not common_words:
                return 0.0

            # Score basé sur la fréquence des mots importants
            score = 0.0
            total_weight = 0.0

            for word in common_words:
                # Poids basé sur la fréquence dans la réponse correcte
                weight = correct_counter[word]
                score += weight * min(student_counter[word], correct_counter[word]) / correct_counter[word]
                total_weight += weight

            return score / total_weight if total_weight > 0 else 0.0

        except Exception as e:
            logger.error(f"Error analyzing keywords: {str(e)}")
            return 0.0

    def _calculate_score_variance(self, scores: List[float]) -> float:
        """Calculer la variance des scores pour estimer la confiance"""
        try:
            import statistics
            if len(scores) < 2:
                return 0.0
            return statistics.variance(scores)
        except Exception:
            return 0.5

    def _generate_detailed_feedback(
        self,
        final_score: float,
        semantic_score: float,
        textual_similarity: float,
        keyword_score: float
    ) -> str:
        """Générer un feedback détaillé basé sur les scores"""
        try:
            feedback_parts = []

            if final_score >= 0.8:
                feedback_parts.append("Excellente réponse")
            elif final_score >= 0.6:
                feedback_parts.append("Bonne réponse")
            elif final_score >= 0.4:
                feedback_parts.append("Réponse partielle")
            else:
                feedback_parts.append("Réponse insuffisante")

            # Détails spécifiques
            if semantic_score >= 0.7:
                feedback_parts.append("le sens est bien compris")
            elif semantic_score >= 0.4:
                feedback_parts.append("le sens est partiellement compris")
            else:
                feedback_parts.append("le sens n'est pas bien saisi")

            if keyword_score >= 0.6:
                feedback_parts.append("les termes clés sont présents")
            elif keyword_score >= 0.3:
                feedback_parts.append("quelques termes clés manquent")
            else:
                feedback_parts.append("les termes clés sont absents")

            return ", ".join(feedback_parts) + "."

        except Exception as e:
            logger.error(f"Error generating feedback: {str(e)}")
            return "Évaluation automatique effectuée."

    async def _grade_mixed_intelligent(
        self,
        student_answers: Dict[str, str],
        correct_answers: Dict[str, str],
        config: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Corriger un examen mixte (QCM + questions ouvertes)"""
        # Séparer les questions QCM des questions ouvertes
        qcm_answers = {}
        open_answers = {}
        qcm_correct = {}
        open_correct = {}

        for q_id, answer in student_answers.items():
            if len(answer.strip()) <= 3 and answer.strip().upper() in 'ABCDE':
                qcm_answers[q_id] = answer
                if q_id in correct_answers:
                    qcm_correct[q_id] = correct_answers[q_id]
            else:
                open_answers[q_id] = answer
                if q_id in correct_answers:
                    open_correct[q_id] = correct_answers[q_id]

        # Corriger séparément
        qcm_results = await self._grade_qcm_intelligent(qcm_answers, qcm_correct, config)
        open_results = await self._grade_open_ended_intelligent(open_answers, open_correct, config)

        # Combiner les résultats
        combined_results = {**qcm_results['results'], **open_results['results']}

        return {
            'type': 'mixed',
            'results': combined_results,
            'qcm_summary': qcm_results['summary'],
            'open_summary': open_results['summary'],
            'overall_summary': {
                'total_questions': len(correct_answers),
                'qcm_questions': len(qcm_correct),
                'open_questions': len(open_correct)
            }
        }

    def _fuzzy_match(self, text1: str, text2: str) -> float:
        """Calculer la similarité entre deux textes"""
        return SequenceMatcher(None, text1.lower(), text2.lower()).ratio()

    def _calculate_final_score(
        self,
        grading_result: Dict[str, Any],
        config: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Calculer le score final"""
        results = grading_result.get('results', {})

        if not results:
            return {
                'score': 0.0,
                'percentage': 0.0,
                'grade': 'F',
                'confidence': 0.0
            }

        # Calculer le score total
        total_score = sum(r.get('score', 0.0) for r in results.values())
        max_score = len(results)
        percentage = (total_score / max_score * 100) if max_score > 0 else 0

        # Calculer la confiance moyenne
        confidences = [r.get('confidence', 0.5) for r in results.values()]
        avg_confidence = sum(confidences) / len(confidences) if confidences else 0.5

        # Déterminer la note littérale
        grade = self._percentage_to_letter_grade(percentage)

        return {
            'score': total_score,
            'max_score': max_score,
            'percentage': percentage,
            'grade': grade,
            'confidence': avg_confidence
        }

    def _percentage_to_letter_grade(self, percentage: float) -> str:
        """Convertir un pourcentage en note littérale"""
        if percentage >= 90:
            return 'A'
        elif percentage >= 80:
            return 'B'
        elif percentage >= 70:
            return 'C'
        elif percentage >= 60:
            return 'D'
        else:
            return 'F'

    async def _generate_detailed_feedback(
        self,
        grading_result: Dict[str, Any],
        student_answers: Dict[str, str],
        correct_answers: Dict[str, str]
    ) -> Dict[str, Any]:
        """Générer un feedback détaillé"""
        try:
            results = grading_result.get('results', {})

            feedback = {
                'overall': '',
                'by_question': {},
                'strengths': [],
                'areas_for_improvement': [],
                'recommendations': []
            }

            correct_count = sum(1 for r in results.values() if r.get('is_correct', False))
            total_count = len(results)

            # Feedback général
            if correct_count / total_count >= 0.8:
                feedback['overall'] = 'Excellent travail ! Vous maîtrisez bien le sujet.'
            elif correct_count / total_count >= 0.6:
                feedback['overall'] = 'Bon travail avec quelques points à améliorer.'
            else:
                feedback['overall'] = 'Il y a des lacunes importantes à combler.'

            # Feedback par question
            for q_id, result in results.items():
                feedback['by_question'][q_id] = result.get('feedback', '')

            # Utiliser l'IA open-source pour un feedback plus détaillé si disponible
            if self.ai_providers.get('sentence_transformers', {}).get('available'):
                ai_feedback = await self._generate_opensource_ai_feedback(grading_result, student_answers, correct_answers)
                feedback.update(ai_feedback)

            return feedback

        except Exception as e:
            logger.error(f"Error generating feedback: {str(e)}")
            return {
                'overall': 'Feedback automatique non disponible',
                'by_question': {},
                'error': str(e)
            }

    async def _generate_opensource_ai_feedback(
        self,
        grading_result: Dict[str, Any],
        student_answers: Dict[str, str],
        correct_answers: Dict[str, str]
    ) -> Dict[str, Any]:
        """Générer un feedback avec l'IA open-source"""
        try:
            results = grading_result.get('results', {})

            # Analyser les performances par question
            strengths = []
            areas_for_improvement = []
            recommendations = []

            correct_count = 0
            total_count = len(results)

            for q_id, result in results.items():
                score = result.get('score', 0)
                confidence = result.get('confidence', 0.5)

                if score >= 0.8:
                    correct_count += 1
                    if confidence >= 0.8:
                        strengths.append(f"Excellente maîtrise de la question {q_id.replace('question_', '')}")
                elif score >= 0.5:
                    areas_for_improvement.append(f"Compréhension partielle de la question {q_id.replace('question_', '')}")
                    recommendations.append(f"Revoir les concepts liés à la question {q_id.replace('question_', '')}")
                else:
                    areas_for_improvement.append(f"Difficulté avec la question {q_id.replace('question_', '')}")
                    recommendations.append(f"Étudier en profondeur le sujet de la question {q_id.replace('question_', '')}")

            # Feedback global basé sur les performances
            performance_ratio = correct_count / total_count if total_count > 0 else 0

            if performance_ratio >= 0.8:
                overall = "Excellente performance ! Vous démontrez une solide compréhension du sujet."
                strengths.append("Maîtrise globale du sujet")
            elif performance_ratio >= 0.6:
                overall = "Bonne performance avec quelques points à améliorer."
                strengths.append("Bonne compréhension générale")
                recommendations.append("Consolider les points faibles identifiés")
            elif performance_ratio >= 0.4:
                overall = "Performance moyenne. Il y a des lacunes importantes à combler."
                areas_for_improvement.append("Compréhension générale à améliorer")
                recommendations.extend([
                    "Revoir les concepts fondamentaux",
                    "Pratiquer davantage d'exercices"
                ])
            else:
                overall = "Performance faible. Une révision approfondie est nécessaire."
                areas_for_improvement.extend([
                    "Compréhension de base insuffisante",
                    "Méthode de travail à revoir"
                ])
                recommendations.extend([
                    "Reprendre les bases du cours",
                    "Demander de l'aide supplémentaire",
                    "Organiser des sessions d'étude"
                ])

            # Analyser les patterns d'erreurs avec la similarité sémantique
            if self.ai_providers.get('sentence_transformers', {}).get('available'):
                semantic_analysis = await self._analyze_semantic_patterns(student_answers, correct_answers)
                if semantic_analysis:
                    recommendations.extend(semantic_analysis)

            return {
                'strengths': list(set(strengths)),  # Supprimer les doublons
                'areas_for_improvement': list(set(areas_for_improvement)),
                'recommendations': list(set(recommendations)),
                'overall': overall
            }

        except Exception as e:
            logger.error(f"Error generating open-source AI feedback: {str(e)}")
            return {}

    async def _analyze_semantic_patterns(
        self,
        student_answers: Dict[str, str],
        correct_answers: Dict[str, str]
    ) -> List[str]:
        """Analyser les patterns sémantiques pour des recommandations spécifiques"""
        try:
            recommendations = []

            # Analyser la cohérence des réponses
            if len(student_answers) > 1:
                answer_texts = list(student_answers.values())

                # Calculer la cohérence sémantique entre les réponses
                coherence_scores = []
                for i in range(len(answer_texts)):
                    for j in range(i + 1, len(answer_texts)):
                        similarity = await self._calculate_semantic_similarity(
                            answer_texts[i], answer_texts[j]
                        )
                        coherence_scores.append(similarity)

                if coherence_scores:
                    avg_coherence = sum(coherence_scores) / len(coherence_scores)

                    if avg_coherence < 0.3:
                        recommendations.append("Améliorer la cohérence entre les réponses")
                    elif avg_coherence > 0.7:
                        recommendations.append("Bonne cohérence dans les réponses")

            # Analyser la complexité du vocabulaire
            for q_id, student_answer in student_answers.items():
                if q_id in correct_answers:
                    correct_answer = correct_answers[q_id]

                    # Analyser la longueur et la complexité
                    student_words = len(student_answer.split())
                    correct_words = len(correct_answer.split())

                    if student_words < correct_words * 0.3:
                        recommendations.append("Développer davantage les réponses")
                    elif student_words > correct_words * 2:
                        recommendations.append("Être plus concis et précis")

            return recommendations

        except Exception as e:
            logger.error(f"Error analyzing semantic patterns: {str(e)}")
            return []

# Instance globale
intelligent_grading_service = IntelligentGradingService()


class GradingPipelineService:
    """Service de pipeline de correction robuste avec gestion d'erreurs"""

    def __init__(self):
        self.ocr_service = None  # Sera initialisé avec enhanced_ocr_service
        self.grading_service = intelligent_grading_service
        self.max_retries = 3
        self.timeout_seconds = 300  # 5 minutes

        logger.info("Grading Pipeline Service initialized")

    async def process_exam_complete(
        self,
        exam_id: str,
        file_path: str,
        correct_answers: Dict[str, Any],
        exam_type: str = 'qcm',
        config: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Pipeline complet de traitement d'examen avec gestion d'erreurs robuste
        """
        pipeline_start = datetime.now()

        try:
            # Étape 1: Validation des entrées
            validation_result = await self._validate_inputs(exam_id, file_path, correct_answers)
            if not validation_result['valid']:
                return {
                    'success': False,
                    'error': 'Validation failed',
                    'details': validation_result['errors'],
                    'stage': 'validation'
                }

            # Étape 2: OCR avec retry
            ocr_result = await self._ocr_with_retry(file_path, exam_type)
            if not ocr_result['success']:
                return {
                    'success': False,
                    'error': 'OCR failed',
                    'details': ocr_result,
                    'stage': 'ocr'
                }

            # Étape 3: Correction intelligente
            grading_result = await self._grade_with_retry(
                exam_id, ocr_result['text'], correct_answers, exam_type, config
            )
            if not grading_result['success']:
                return {
                    'success': False,
                    'error': 'Grading failed',
                    'details': grading_result,
                    'stage': 'grading'
                }

            # Étape 4: Sauvegarde en base de données
            db_result = await self._save_results_with_retry(exam_id, ocr_result, grading_result)

            processing_time = (datetime.now() - pipeline_start).total_seconds()

            return {
                'success': True,
                'exam_id': exam_id,
                'ocr_result': ocr_result,
                'grading_result': grading_result,
                'database_saved': db_result['success'],
                'processing_time': processing_time,
                'pipeline_stages': {
                    'validation': 'completed',
                    'ocr': 'completed',
                    'grading': 'completed',
                    'database': 'completed' if db_result['success'] else 'failed'
                },
                'timestamp': datetime.now().isoformat()
            }

        except Exception as e:
            logger.error(f"Pipeline error for exam {exam_id}: {str(e)}")
            return {
                'success': False,
                'error': f'Pipeline error: {str(e)}',
                'exam_id': exam_id,
                'processing_time': (datetime.now() - pipeline_start).total_seconds(),
                'stage': 'pipeline'
            }

    async def _validate_inputs(
        self,
        exam_id: str,
        file_path: str,
        correct_answers: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Valider les entrées du pipeline"""
        errors = []

        # Valider exam_id
        if not exam_id or not isinstance(exam_id, str):
            errors.append("Invalid exam_id")

        # Valider file_path
        if not file_path or not os.path.exists(file_path):
            errors.append(f"File not found: {file_path}")

        # Valider correct_answers
        if not correct_answers or not isinstance(correct_answers, dict):
            errors.append("Invalid correct_answers format")

        # Valider la taille du fichier
        if file_path and os.path.exists(file_path):
            file_size = os.path.getsize(file_path)
            if file_size > 50 * 1024 * 1024:  # 50MB
                errors.append("File too large (max 50MB)")
            elif file_size == 0:
                errors.append("Empty file")

        return {
            'valid': len(errors) == 0,
            'errors': errors
        }

    async def _ocr_with_retry(self, file_path: str, exam_type: str) -> Dict[str, Any]:
        """OCR avec retry automatique"""
        last_error = None

        for attempt in range(self.max_retries):
            try:
                logger.info(f"OCR attempt {attempt + 1}/{self.max_retries} for {file_path}")

                # Utiliser le service OCR avancé
                if self.ocr_service:
                    result = await self.ocr_service.extract_text_multi_provider(
                        file_path, exam_type
                    )
                else:
                    # Fallback vers OCR simple
                    result = await self._simple_ocr_fallback(file_path)

                if result.get('success') and result.get('text'):
                    logger.info(f"OCR successful on attempt {attempt + 1}")
                    return result

                last_error = result.get('error', 'Unknown OCR error')

            except Exception as e:
                last_error = str(e)
                logger.warning(f"OCR attempt {attempt + 1} failed: {last_error}")

            # Attendre avant le retry
            if attempt < self.max_retries - 1:
                await asyncio.sleep(2 ** attempt)  # Backoff exponentiel

        return {
            'success': False,
            'error': f'OCR failed after {self.max_retries} attempts: {last_error}',
            'text': '',
            'confidence': 0.0
        }

    async def _simple_ocr_fallback(self, file_path: str) -> Dict[str, Any]:
        """OCR de secours simple avec Tesseract"""
        try:
            import pytesseract
            from PIL import Image

            image = Image.open(file_path)
            text = pytesseract.image_to_string(image, lang='eng+fra')

            return {
                'success': True,
                'text': text.strip(),
                'confidence': 0.6,  # Confiance modérée pour fallback
                'provider_used': 'tesseract_fallback'
            }

        except Exception as e:
            return {
                'success': False,
                'error': f'Fallback OCR failed: {str(e)}',
                'text': '',
                'confidence': 0.0
            }

    async def _grade_with_retry(
        self,
        exam_id: str,
        extracted_text: str,
        correct_answers: Dict[str, Any],
        exam_type: str,
        config: Optional[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """Correction avec retry automatique"""
        last_error = None

        for attempt in range(self.max_retries):
            try:
                logger.info(f"Grading attempt {attempt + 1}/{self.max_retries} for exam {exam_id}")

                result = await self.grading_service.grade_exam_intelligent(
                    exam_id, extracted_text, correct_answers, exam_type, config
                )

                if result.get('success'):
                    logger.info(f"Grading successful on attempt {attempt + 1}")
                    return result

                last_error = result.get('error', 'Unknown grading error')

            except Exception as e:
                last_error = str(e)
                logger.warning(f"Grading attempt {attempt + 1} failed: {last_error}")

            # Attendre avant le retry
            if attempt < self.max_retries - 1:
                await asyncio.sleep(1)

        return {
            'success': False,
            'error': f'Grading failed after {self.max_retries} attempts: {last_error}',
            'exam_id': exam_id
        }

    async def _save_results_with_retry(
        self,
        exam_id: str,
        ocr_result: Dict[str, Any],
        grading_result: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Sauvegarde en base avec retry"""
        last_error = None

        for attempt in range(self.max_retries):
            try:
                logger.info(f"Database save attempt {attempt + 1}/{self.max_retries} for exam {exam_id}")

                # Ici on sauvegarderait en base de données
                # Pour l'instant, simulation
                await asyncio.sleep(0.1)  # Simulation d'opération DB

                logger.info(f"Database save successful on attempt {attempt + 1}")
                return {'success': True}

            except Exception as e:
                last_error = str(e)
                logger.warning(f"Database save attempt {attempt + 1} failed: {last_error}")

            # Attendre avant le retry
            if attempt < self.max_retries - 1:
                await asyncio.sleep(0.5)

        return {
            'success': False,
            'error': f'Database save failed after {self.max_retries} attempts: {last_error}'
        }

# Instance globale du pipeline
grading_pipeline_service = GradingPipelineService()
