"""
Service de Correction Intelligent pour Auto-Grade Scribe
Utilise l'IA pour une correction avancée avec fuzzy matching et évaluation contextuelle
"""

import os
import logging
import asyncio
import re
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime
from difflib import SequenceMatcher
import json

logger = logging.getLogger("auto-grade-scribe.intelligent-grading")

class IntelligentGradingService:
    """Service de correction intelligent avec IA"""

    def __init__(self):
        self.fuzzy_threshold = 0.7
        self.partial_credit_threshold = 0.5
        self.ai_providers = {}

        # Initialiser les providers d'IA
        self._init_openai()
        self._init_google_ai()

        logger.info("Intelligent Grading Service initialized")

    def _init_openai(self):
        """Initialiser OpenAI pour la correction intelligente"""
        try:
            import openai
            api_key = os.getenv('OPENAI_API_KEY')
            if api_key:
                self.ai_providers['openai'] = {
                    'available': True,
                    'client': openai.OpenAI(api_key=api_key),
                    'model': 'gpt-4-turbo-preview'
                }
                logger.info("OpenAI initialized for intelligent grading")
            else:
                logger.warning("OpenAI API key not found")
                self.ai_providers['openai'] = {'available': False}
        except ImportError:
            logger.warning("OpenAI not available")
            self.ai_providers['openai'] = {'available': False}

    def _init_google_ai(self):
        """Initialiser Google AI pour la correction"""
        try:
            import google.generativeai as genai
            api_key = os.getenv('GOOGLE_API_KEY')
            if api_key:
                genai.configure(api_key=api_key)
                self.ai_providers['google'] = {
                    'available': True,
                    'model': genai.GenerativeModel('gemini-pro'),
                    'name': 'gemini-pro'
                }
                logger.info("Google AI initialized for intelligent grading")
            else:
                logger.warning("Google AI API key not found")
                self.ai_providers['google'] = {'available': False}
        except ImportError:
            logger.warning("Google AI not available")
            self.ai_providers['google'] = {'available': False}

    async def grade_exam_intelligent(
        self,
        exam_id: str,
        extracted_text: str,
        correct_answers: Dict[str, Any],
        exam_type: str = 'qcm',
        grading_config: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Corriger un examen de manière intelligente

        Args:
            exam_id: ID de l'examen
            extracted_text: Texte extrait par OCR
            correct_answers: Réponses correctes
            exam_type: Type d'examen ('qcm', 'open_ended', 'mixed')
            grading_config: Configuration de correction

        Returns:
            Résultats de correction détaillés
        """
        try:
            start_time = datetime.now()

            # Configuration par défaut
            config = grading_config or {}

            # Analyser le texte extrait pour identifier les réponses
            student_answers = await self._extract_student_answers(extracted_text, exam_type)

            # Corriger selon le type d'examen
            if exam_type == 'qcm':
                grading_result = await self._grade_qcm_intelligent(
                    student_answers, correct_answers, config
                )
            elif exam_type == 'open_ended':
                grading_result = await self._grade_open_ended_intelligent(
                    student_answers, correct_answers, config
                )
            else:  # mixed
                grading_result = await self._grade_mixed_intelligent(
                    student_answers, correct_answers, config
                )

            # Calculer le score final
            final_score = self._calculate_final_score(grading_result, config)

            # Générer un feedback détaillé
            feedback = await self._generate_detailed_feedback(
                grading_result, student_answers, correct_answers
            )

            processing_time = (datetime.now() - start_time).total_seconds()

            return {
                'success': True,
                'exam_id': exam_id,
                'exam_type': exam_type,
                'student_answers': student_answers,
                'correct_answers': correct_answers,
                'grading_result': grading_result,
                'final_score': final_score,
                'feedback': feedback,
                'processing_time': processing_time,
                'requires_manual_review': final_score.get('confidence', 1.0) < 0.7,
                'timestamp': datetime.now().isoformat()
            }

        except Exception as e:
            logger.error(f"Error in intelligent grading: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'exam_id': exam_id,
                'requires_manual_review': True
            }

    async def _extract_student_answers(
        self,
        extracted_text: str,
        exam_type: str
    ) -> Dict[str, Any]:
        """Extraire les réponses de l'étudiant du texte OCR"""
        try:
            if exam_type == 'qcm':
                return await self._extract_qcm_answers(extracted_text)
            elif exam_type == 'open_ended':
                return await self._extract_open_ended_answers(extracted_text)
            else:  # mixed
                return await self._extract_mixed_answers(extracted_text)

        except Exception as e:
            logger.error(f"Error extracting student answers: {str(e)}")
            return {}

    async def _extract_qcm_answers(self, text: str) -> Dict[str, str]:
        """Extraire les réponses QCM"""
        answers = {}

        # Patterns pour détecter les réponses QCM
        patterns = [
            r'(?:Question|Q)\s*(\d+)\s*[:\-]?\s*([A-E])',  # Question 1: A
            r'(\d+)\s*[:\-\.]?\s*([A-E])',                 # 1. A
            r'([A-E])\s*(?:pour|for)\s*(?:question|Q)\s*(\d+)',  # A pour question 1
        ]

        for pattern in patterns:
            matches = re.finditer(pattern, text, re.IGNORECASE)
            for match in matches:
                if len(match.groups()) == 2:
                    q_num, answer = match.groups()
                    answers[f"question_{q_num}"] = answer.upper()

        # Si aucun pattern ne fonctionne, utiliser l'IA
        if not answers and self.ai_providers.get('openai', {}).get('available'):
            answers = await self._extract_answers_with_ai(text, 'qcm')

        return answers

    async def _extract_open_ended_answers(self, text: str) -> Dict[str, str]:
        """Extraire les réponses ouvertes"""
        answers = {}

        # Diviser le texte en sections par question
        question_patterns = [
            r'(?:Question|Q)\s*(\d+)\s*[:\-]?\s*(.*?)(?=(?:Question|Q)\s*\d+|$)',
            r'(\d+)\s*[:\-\.]?\s*(.*?)(?=\d+\s*[:\-\.]|$)',
        ]

        for pattern in question_patterns:
            matches = re.finditer(pattern, text, re.IGNORECASE | re.DOTALL)
            for match in matches:
                q_num, answer_text = match.groups()
                answers[f"question_{q_num}"] = answer_text.strip()

        # Utiliser l'IA si nécessaire
        if not answers and self.ai_providers.get('openai', {}).get('available'):
            answers = await self._extract_answers_with_ai(text, 'open_ended')

        return answers

    async def _extract_mixed_answers(self, text: str) -> Dict[str, str]:
        """Extraire les réponses mixtes (QCM + ouvertes)"""
        # Combiner les deux méthodes
        qcm_answers = await self._extract_qcm_answers(text)
        open_answers = await self._extract_open_ended_answers(text)

        # Fusionner les résultats
        all_answers = {**qcm_answers, **open_answers}

        return all_answers

    async def _extract_answers_with_ai(self, text: str, exam_type: str) -> Dict[str, str]:
        """Utiliser l'IA pour extraire les réponses"""
        try:
            if not self.ai_providers.get('openai', {}).get('available'):
                return {}

            client = self.ai_providers['openai']['client']

            prompts = {
                'qcm': """
                Analyze this exam text and extract all student answers for multiple choice questions.
                Return a JSON object with question numbers as keys and selected answers (A, B, C, D, E) as values.
                Example: {"question_1": "A", "question_2": "B"}

                Text: {text}
                """,
                'open_ended': """
                Analyze this exam text and extract all student answers for open-ended questions.
                Return a JSON object with question numbers as keys and the full answer text as values.
                Example: {"question_1": "The answer is...", "question_2": "Because..."}

                Text: {text}
                """
            }

            prompt = prompts.get(exam_type, prompts['qcm']).format(text=text)

            response = client.chat.completions.create(
                model=self.ai_providers['openai']['model'],
                messages=[
                    {"role": "system", "content": "You are an expert at analyzing exam papers and extracting student answers."},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.1
            )

            # Parser la réponse JSON
            response_text = response.choices[0].message.content

            # Extraire le JSON de la réponse
            json_match = re.search(r'\{.*\}', response_text, re.DOTALL)
            if json_match:
                return json.loads(json_match.group())

            return {}

        except Exception as e:
            logger.error(f"Error extracting answers with AI: {str(e)}")
            return {}

    async def _grade_qcm_intelligent(
        self,
        student_answers: Dict[str, str],
        correct_answers: Dict[str, str],
        config: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Corriger un QCM de manière intelligente"""
        results = {}
        total_questions = len(correct_answers)
        correct_count = 0

        for question_id, correct_answer in correct_answers.items():
            student_answer = student_answers.get(question_id, '').strip().upper()
            correct_answer = correct_answer.strip().upper()

            # Correction exacte
            if student_answer == correct_answer:
                results[question_id] = {
                    'student_answer': student_answer,
                    'correct_answer': correct_answer,
                    'is_correct': True,
                    'score': 1.0,
                    'confidence': 1.0,
                    'feedback': 'Correct'
                }
                correct_count += 1

            # Fuzzy matching pour les erreurs de reconnaissance OCR
            elif student_answer and self._fuzzy_match(student_answer, correct_answer) > 0.8:
                results[question_id] = {
                    'student_answer': student_answer,
                    'correct_answer': correct_answer,
                    'is_correct': True,
                    'score': 0.9,  # Légère pénalité pour incertitude OCR
                    'confidence': 0.8,
                    'feedback': 'Correct (avec correction OCR)'
                }
                correct_count += 0.9

            else:
                results[question_id] = {
                    'student_answer': student_answer,
                    'correct_answer': correct_answer,
                    'is_correct': False,
                    'score': 0.0,
                    'confidence': 1.0 if student_answer else 0.5,  # Faible confiance si pas de réponse
                    'feedback': 'Incorrect' if student_answer else 'Pas de réponse détectée'
                }

        return {
            'type': 'qcm',
            'results': results,
            'summary': {
                'total_questions': total_questions,
                'correct_answers': correct_count,
                'percentage': (correct_count / total_questions * 100) if total_questions > 0 else 0
            }
        }

    async def _grade_open_ended_intelligent(
        self,
        student_answers: Dict[str, str],
        correct_answers: Dict[str, str],
        config: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Corriger des questions ouvertes avec IA"""
        results = {}

        for question_id, correct_answer in correct_answers.items():
            student_answer = student_answers.get(question_id, '').strip()

            if not student_answer:
                results[question_id] = {
                    'student_answer': student_answer,
                    'correct_answer': correct_answer,
                    'score': 0.0,
                    'confidence': 0.5,
                    'feedback': 'Pas de réponse détectée',
                    'requires_manual_review': True
                }
                continue

            # Utiliser l'IA pour évaluer la réponse
            ai_evaluation = await self._evaluate_open_answer_with_ai(
                student_answer, correct_answer, question_id
            )

            results[question_id] = {
                'student_answer': student_answer,
                'correct_answer': correct_answer,
                'score': ai_evaluation.get('score', 0.0),
                'confidence': ai_evaluation.get('confidence', 0.5),
                'feedback': ai_evaluation.get('feedback', 'Évaluation automatique'),
                'ai_reasoning': ai_evaluation.get('reasoning', ''),
                'requires_manual_review': ai_evaluation.get('confidence', 0.5) < 0.7
            }

        return {
            'type': 'open_ended',
            'results': results,
            'summary': {
                'total_questions': len(correct_answers),
                'avg_score': sum(r['score'] for r in results.values()) / len(results) if results else 0,
                'requires_review_count': sum(1 for r in results.values() if r.get('requires_manual_review', False))
            }
        }

    async def _evaluate_open_answer_with_ai(
        self,
        student_answer: str,
        correct_answer: str,
        question_id: str
    ) -> Dict[str, Any]:
        """Évaluer une réponse ouverte avec l'IA"""
        try:
            if not self.ai_providers.get('openai', {}).get('available'):
                # Fallback vers fuzzy matching simple
                similarity = self._fuzzy_match(student_answer, correct_answer)
                return {
                    'score': similarity,
                    'confidence': 0.6,
                    'feedback': f'Similarité: {similarity:.1%}',
                    'reasoning': 'Évaluation par similarité textuelle'
                }

            client = self.ai_providers['openai']['client']

            prompt = f"""
            Evaluate this student's answer to an exam question.

            Question ID: {question_id}
            Correct Answer: {correct_answer}
            Student Answer: {student_answer}

            Please provide:
            1. A score from 0.0 to 1.0 (0 = completely wrong, 1 = perfect)
            2. Your confidence in this evaluation (0.0 to 1.0)
            3. Brief feedback explaining the score
            4. Reasoning for your evaluation

            Consider:
            - Factual accuracy
            - Completeness of the answer
            - Understanding demonstrated
            - Partial credit for partially correct answers

            Respond in JSON format:
            {{
                "score": 0.0-1.0,
                "confidence": 0.0-1.0,
                "feedback": "brief explanation",
                "reasoning": "detailed reasoning"
            }}
            """

            response = client.chat.completions.create(
                model=self.ai_providers['openai']['model'],
                messages=[
                    {"role": "system", "content": "You are an expert teacher evaluating student exam answers."},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.1
            )

            # Parser la réponse JSON
            response_text = response.choices[0].message.content
            json_match = re.search(r'\{.*\}', response_text, re.DOTALL)

            if json_match:
                evaluation = json.loads(json_match.group())
                return {
                    'score': float(evaluation.get('score', 0.0)),
                    'confidence': float(evaluation.get('confidence', 0.5)),
                    'feedback': evaluation.get('feedback', 'Évaluation IA'),
                    'reasoning': evaluation.get('reasoning', '')
                }

            # Fallback si parsing échoue
            return {
                'score': 0.5,
                'confidence': 0.3,
                'feedback': 'Erreur d\'évaluation IA',
                'reasoning': 'Impossible de parser la réponse IA'
            }

        except Exception as e:
            logger.error(f"Error in AI evaluation: {str(e)}")
            # Fallback vers fuzzy matching
            similarity = self._fuzzy_match(student_answer, correct_answer)
            return {
                'score': similarity,
                'confidence': 0.4,
                'feedback': f'Évaluation de secours (similarité: {similarity:.1%})',
                'reasoning': f'Erreur IA: {str(e)}'
            }

    async def _grade_mixed_intelligent(
        self,
        student_answers: Dict[str, str],
        correct_answers: Dict[str, str],
        config: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Corriger un examen mixte (QCM + questions ouvertes)"""
        # Séparer les questions QCM des questions ouvertes
        qcm_answers = {}
        open_answers = {}
        qcm_correct = {}
        open_correct = {}

        for q_id, answer in student_answers.items():
            if len(answer.strip()) <= 3 and answer.strip().upper() in 'ABCDE':
                qcm_answers[q_id] = answer
                if q_id in correct_answers:
                    qcm_correct[q_id] = correct_answers[q_id]
            else:
                open_answers[q_id] = answer
                if q_id in correct_answers:
                    open_correct[q_id] = correct_answers[q_id]

        # Corriger séparément
        qcm_results = await self._grade_qcm_intelligent(qcm_answers, qcm_correct, config)
        open_results = await self._grade_open_ended_intelligent(open_answers, open_correct, config)

        # Combiner les résultats
        combined_results = {**qcm_results['results'], **open_results['results']}

        return {
            'type': 'mixed',
            'results': combined_results,
            'qcm_summary': qcm_results['summary'],
            'open_summary': open_results['summary'],
            'overall_summary': {
                'total_questions': len(correct_answers),
                'qcm_questions': len(qcm_correct),
                'open_questions': len(open_correct)
            }
        }

    def _fuzzy_match(self, text1: str, text2: str) -> float:
        """Calculer la similarité entre deux textes"""
        return SequenceMatcher(None, text1.lower(), text2.lower()).ratio()

    def _calculate_final_score(
        self,
        grading_result: Dict[str, Any],
        config: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Calculer le score final"""
        results = grading_result.get('results', {})

        if not results:
            return {
                'score': 0.0,
                'percentage': 0.0,
                'grade': 'F',
                'confidence': 0.0
            }

        # Calculer le score total
        total_score = sum(r.get('score', 0.0) for r in results.values())
        max_score = len(results)
        percentage = (total_score / max_score * 100) if max_score > 0 else 0

        # Calculer la confiance moyenne
        confidences = [r.get('confidence', 0.5) for r in results.values()]
        avg_confidence = sum(confidences) / len(confidences) if confidences else 0.5

        # Déterminer la note littérale
        grade = self._percentage_to_letter_grade(percentage)

        return {
            'score': total_score,
            'max_score': max_score,
            'percentage': percentage,
            'grade': grade,
            'confidence': avg_confidence
        }

    def _percentage_to_letter_grade(self, percentage: float) -> str:
        """Convertir un pourcentage en note littérale"""
        if percentage >= 90:
            return 'A'
        elif percentage >= 80:
            return 'B'
        elif percentage >= 70:
            return 'C'
        elif percentage >= 60:
            return 'D'
        else:
            return 'F'

    async def _generate_detailed_feedback(
        self,
        grading_result: Dict[str, Any],
        student_answers: Dict[str, str],
        correct_answers: Dict[str, str]
    ) -> Dict[str, Any]:
        """Générer un feedback détaillé"""
        try:
            results = grading_result.get('results', {})

            feedback = {
                'overall': '',
                'by_question': {},
                'strengths': [],
                'areas_for_improvement': [],
                'recommendations': []
            }

            correct_count = sum(1 for r in results.values() if r.get('is_correct', False))
            total_count = len(results)

            # Feedback général
            if correct_count / total_count >= 0.8:
                feedback['overall'] = 'Excellent travail ! Vous maîtrisez bien le sujet.'
            elif correct_count / total_count >= 0.6:
                feedback['overall'] = 'Bon travail avec quelques points à améliorer.'
            else:
                feedback['overall'] = 'Il y a des lacunes importantes à combler.'

            # Feedback par question
            for q_id, result in results.items():
                feedback['by_question'][q_id] = result.get('feedback', '')

            # Utiliser l'IA pour un feedback plus détaillé si disponible
            if self.ai_providers.get('openai', {}).get('available'):
                ai_feedback = await self._generate_ai_feedback(grading_result, student_answers, correct_answers)
                feedback.update(ai_feedback)

            return feedback

        except Exception as e:
            logger.error(f"Error generating feedback: {str(e)}")
            return {
                'overall': 'Feedback automatique non disponible',
                'by_question': {},
                'error': str(e)
            }

    async def _generate_ai_feedback(
        self,
        grading_result: Dict[str, Any],
        student_answers: Dict[str, str],
        correct_answers: Dict[str, str]
    ) -> Dict[str, Any]:
        """Générer un feedback avec l'IA"""
        try:
            client = self.ai_providers['openai']['client']

            # Préparer le contexte pour l'IA
            context = {
                'grading_result': grading_result,
                'student_answers': student_answers,
                'correct_answers': correct_answers
            }

            prompt = f"""
            Based on this exam grading result, provide detailed educational feedback for the student.

            Context: {json.dumps(context, indent=2)}

            Please provide:
            1. Specific strengths demonstrated
            2. Areas that need improvement
            3. Concrete recommendations for study
            4. Encouraging but honest overall assessment

            Respond in JSON format:
            {{
                "strengths": ["strength1", "strength2"],
                "areas_for_improvement": ["area1", "area2"],
                "recommendations": ["rec1", "rec2"],
                "overall": "overall assessment"
            }}
            """

            response = client.chat.completions.create(
                model=self.ai_providers['openai']['model'],
                messages=[
                    {"role": "system", "content": "You are an experienced teacher providing constructive feedback."},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.3
            )

            response_text = response.choices[0].message.content
            json_match = re.search(r'\{.*\}', response_text, re.DOTALL)

            if json_match:
                return json.loads(json_match.group())

            return {}

        except Exception as e:
            logger.error(f"Error generating AI feedback: {str(e)}")
            return {}

# Instance globale
intelligent_grading_service = IntelligentGradingService()


class GradingPipelineService:
    """Service de pipeline de correction robuste avec gestion d'erreurs"""

    def __init__(self):
        self.ocr_service = None  # Sera initialisé avec enhanced_ocr_service
        self.grading_service = intelligent_grading_service
        self.max_retries = 3
        self.timeout_seconds = 300  # 5 minutes

        logger.info("Grading Pipeline Service initialized")

    async def process_exam_complete(
        self,
        exam_id: str,
        file_path: str,
        correct_answers: Dict[str, Any],
        exam_type: str = 'qcm',
        config: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Pipeline complet de traitement d'examen avec gestion d'erreurs robuste
        """
        pipeline_start = datetime.now()

        try:
            # Étape 1: Validation des entrées
            validation_result = await self._validate_inputs(exam_id, file_path, correct_answers)
            if not validation_result['valid']:
                return {
                    'success': False,
                    'error': 'Validation failed',
                    'details': validation_result['errors'],
                    'stage': 'validation'
                }

            # Étape 2: OCR avec retry
            ocr_result = await self._ocr_with_retry(file_path, exam_type)
            if not ocr_result['success']:
                return {
                    'success': False,
                    'error': 'OCR failed',
                    'details': ocr_result,
                    'stage': 'ocr'
                }

            # Étape 3: Correction intelligente
            grading_result = await self._grade_with_retry(
                exam_id, ocr_result['text'], correct_answers, exam_type, config
            )
            if not grading_result['success']:
                return {
                    'success': False,
                    'error': 'Grading failed',
                    'details': grading_result,
                    'stage': 'grading'
                }

            # Étape 4: Sauvegarde en base de données
            db_result = await self._save_results_with_retry(exam_id, ocr_result, grading_result)

            processing_time = (datetime.now() - pipeline_start).total_seconds()

            return {
                'success': True,
                'exam_id': exam_id,
                'ocr_result': ocr_result,
                'grading_result': grading_result,
                'database_saved': db_result['success'],
                'processing_time': processing_time,
                'pipeline_stages': {
                    'validation': 'completed',
                    'ocr': 'completed',
                    'grading': 'completed',
                    'database': 'completed' if db_result['success'] else 'failed'
                },
                'timestamp': datetime.now().isoformat()
            }

        except Exception as e:
            logger.error(f"Pipeline error for exam {exam_id}: {str(e)}")
            return {
                'success': False,
                'error': f'Pipeline error: {str(e)}',
                'exam_id': exam_id,
                'processing_time': (datetime.now() - pipeline_start).total_seconds(),
                'stage': 'pipeline'
            }

    async def _validate_inputs(
        self,
        exam_id: str,
        file_path: str,
        correct_answers: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Valider les entrées du pipeline"""
        errors = []

        # Valider exam_id
        if not exam_id or not isinstance(exam_id, str):
            errors.append("Invalid exam_id")

        # Valider file_path
        if not file_path or not os.path.exists(file_path):
            errors.append(f"File not found: {file_path}")

        # Valider correct_answers
        if not correct_answers or not isinstance(correct_answers, dict):
            errors.append("Invalid correct_answers format")

        # Valider la taille du fichier
        if file_path and os.path.exists(file_path):
            file_size = os.path.getsize(file_path)
            if file_size > 50 * 1024 * 1024:  # 50MB
                errors.append("File too large (max 50MB)")
            elif file_size == 0:
                errors.append("Empty file")

        return {
            'valid': len(errors) == 0,
            'errors': errors
        }

    async def _ocr_with_retry(self, file_path: str, exam_type: str) -> Dict[str, Any]:
        """OCR avec retry automatique"""
        last_error = None

        for attempt in range(self.max_retries):
            try:
                logger.info(f"OCR attempt {attempt + 1}/{self.max_retries} for {file_path}")

                # Utiliser le service OCR avancé
                if self.ocr_service:
                    result = await self.ocr_service.extract_text_multi_provider(
                        file_path, exam_type
                    )
                else:
                    # Fallback vers OCR simple
                    result = await self._simple_ocr_fallback(file_path)

                if result.get('success') and result.get('text'):
                    logger.info(f"OCR successful on attempt {attempt + 1}")
                    return result

                last_error = result.get('error', 'Unknown OCR error')

            except Exception as e:
                last_error = str(e)
                logger.warning(f"OCR attempt {attempt + 1} failed: {last_error}")

            # Attendre avant le retry
            if attempt < self.max_retries - 1:
                await asyncio.sleep(2 ** attempt)  # Backoff exponentiel

        return {
            'success': False,
            'error': f'OCR failed after {self.max_retries} attempts: {last_error}',
            'text': '',
            'confidence': 0.0
        }

    async def _simple_ocr_fallback(self, file_path: str) -> Dict[str, Any]:
        """OCR de secours simple avec Tesseract"""
        try:
            import pytesseract
            from PIL import Image

            image = Image.open(file_path)
            text = pytesseract.image_to_string(image, lang='eng+fra')

            return {
                'success': True,
                'text': text.strip(),
                'confidence': 0.6,  # Confiance modérée pour fallback
                'provider_used': 'tesseract_fallback'
            }

        except Exception as e:
            return {
                'success': False,
                'error': f'Fallback OCR failed: {str(e)}',
                'text': '',
                'confidence': 0.0
            }

    async def _grade_with_retry(
        self,
        exam_id: str,
        extracted_text: str,
        correct_answers: Dict[str, Any],
        exam_type: str,
        config: Optional[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """Correction avec retry automatique"""
        last_error = None

        for attempt in range(self.max_retries):
            try:
                logger.info(f"Grading attempt {attempt + 1}/{self.max_retries} for exam {exam_id}")

                result = await self.grading_service.grade_exam_intelligent(
                    exam_id, extracted_text, correct_answers, exam_type, config
                )

                if result.get('success'):
                    logger.info(f"Grading successful on attempt {attempt + 1}")
                    return result

                last_error = result.get('error', 'Unknown grading error')

            except Exception as e:
                last_error = str(e)
                logger.warning(f"Grading attempt {attempt + 1} failed: {last_error}")

            # Attendre avant le retry
            if attempt < self.max_retries - 1:
                await asyncio.sleep(1)

        return {
            'success': False,
            'error': f'Grading failed after {self.max_retries} attempts: {last_error}',
            'exam_id': exam_id
        }

    async def _save_results_with_retry(
        self,
        exam_id: str,
        ocr_result: Dict[str, Any],
        grading_result: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Sauvegarde en base avec retry"""
        last_error = None

        for attempt in range(self.max_retries):
            try:
                logger.info(f"Database save attempt {attempt + 1}/{self.max_retries} for exam {exam_id}")

                # Ici on sauvegarderait en base de données
                # Pour l'instant, simulation
                await asyncio.sleep(0.1)  # Simulation d'opération DB

                logger.info(f"Database save successful on attempt {attempt + 1}")
                return {'success': True}

            except Exception as e:
                last_error = str(e)
                logger.warning(f"Database save attempt {attempt + 1} failed: {last_error}")

            # Attendre avant le retry
            if attempt < self.max_retries - 1:
                await asyncio.sleep(0.5)

        return {
            'success': False,
            'error': f'Database save failed after {self.max_retries} attempts: {last_error}'
        }

# Instance globale du pipeline
grading_pipeline_service = GradingPipelineService()
